package net.summerfarm.mall.controller.search;

import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.common.util.IPUtil;
import net.summerfarm.mall.common.util.IpWhiteListUtil;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.facade.usercenter.MerchantQueryFacade;
import net.summerfarm.mall.model.input.product.ProductSearchInput;
import net.summerfarm.mall.model.vo.ProductInfoVO;
import net.summerfarm.mall.service.InventoryService;
import net.xianmu.common.result.CommonResult;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAndExtendResp;

@RestController
@RequestMapping("/search")
@Slf4j
public class SearchController {

    @Autowired
    private InventoryService inventoryService;

    @Autowired
    private MerchantQueryFacade merchantQueryFacade;

    /**
     * 新搜索接口，允许ip白名单访问、允许指定areaNo（白名单用户）
     * @return
     */
    @ApiOperation(value = "首页商品查询",httpMethod = "GET",response = ProductInfoVO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex",value = "页数",paramType = "path",defaultValue = "1",required = true),
            @ApiImplicitParam(name = "pageSize",value = "数量",paramType = "path",defaultValue = "10",required = true),
            @ApiImplicitParam(name = "frontCategoryId",value = "前台类目id",paramType = "query"),
            @ApiImplicitParam(name = "pdName",value = "商品名称",paramType = "query"),
            @ApiImplicitParam(name = "mId",value = "商户id",paramType = "query"),
            @ApiImplicitParam(name = "addOn",value = "商品性质",paramType = "query"),
            @ApiImplicitParam(name = "queryStr", value = "查询pdName或sku", paramType = "query"),
            @ApiImplicitParam(name = "areaNo", value = "指定areaNo查询", paramType = "query")
    })
    @RequestMapping(value = "/product/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public CommonResult<PageInfo<ProductInfoVO>> product(@PathVariable int pageIndex, @PathVariable int pageSize, ProductSearchInput input) {
        String ip = IPUtil.getIpAddress(RequestHolder.getRequest());
        if (!IpWhiteListUtil.check(ip)) {
            input.setMId(null);
        }
        
        MerchantStoreAndExtendResp merchant = merchantQueryFacade.getMerchantExtendsByMid(RequestHolder.getMId());
        if (merchant == null|| !Objects.equals(1, merchant.getMockLoginFlag())) {
            log.warn("该账号不允许mock login:{}", RequestHolder.getMId());
            input.setAreaNo(RequestHolder.getMerchantAreaNo());
        }
        
        return CommonResult.ok(inventoryService.selectHomeProductVoV2(pageIndex, pageSize, input));
    }
}
