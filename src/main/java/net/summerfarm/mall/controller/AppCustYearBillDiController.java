package net.summerfarm.mall.controller;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.mall.common.config.DynamicConfig;
import net.summerfarm.mall.facade.market.AppCustYearBillDiFacade;
import net.summerfarm.mall.facade.market.assembler.AppCustYearBillDiAssembler;
import net.summerfarm.mall.model.vo.AppCustYearBillDiVO;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.marketing.center.client.bill.resp.AppCustYearBillDiResultResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0
 * @Title 客户年账单表
 * @Description 客户年账单表功能模块
 * @date 2024-05-30 10:27:32
 */
@RestController
@RequestMapping(value = "/annual/statement")
@Slf4j
public class AppCustYearBillDiController {

    @Autowired
    private AppCustYearBillDiFacade appCustYearBillDiFacade;

    @Resource
    private DynamicConfig dynamicConfig;
    private static final Long SPECIAL_MERCHANT_ID = -1L;

    /**
     * 获取详情
     *
     * @return AppCustYearBillDiVO
     */
    @PostMapping(value = "/query/detail/{mid}")
    public CommonResult<AppCustYearBillDiVO> detail(@PathVariable Long mid) {
        List<Long> annualStatementMerchantIdList = dynamicConfig.getAnnualStatementMerchantIdList();
        log.info("当前灰度mid配置：{}， mid：{}", JSON.toJSONString(annualStatementMerchantIdList), mid);
        if(annualStatementMerchantIdList.contains(SPECIAL_MERCHANT_ID) || annualStatementMerchantIdList.contains(mid)) {
            AppCustYearBillDiResultResp detail = appCustYearBillDiFacade.getDetail(mid);
            if(null == detail) {
                // 这里通过抛DefaultServiceException，前端不会提示报错弹窗
                throw new DefaultServiceException("暂无年度账单");
            }
            return CommonResult.ok(AppCustYearBillDiAssembler.toAppCustYearBillDiVO(detail));
        }
        throw new DefaultServiceException("灰度阶段，暂无年度账单");
    }

}

