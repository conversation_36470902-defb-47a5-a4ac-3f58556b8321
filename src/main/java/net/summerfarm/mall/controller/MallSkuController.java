package net.summerfarm.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Iterators;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.common.util.qiNiu.UploadTokenFactory;
import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.common.datacollect.UnCollectByAspect;
import net.summerfarm.mall.common.util.IPUtil;
import net.summerfarm.mall.common.util.IpWhiteListUtil;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.common.util.WeChatUtils;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.model.domain.MerchantExt;
import net.summerfarm.mall.model.dto.login.MerchantLoginDto;
import net.summerfarm.mall.model.input.DataStorageInput;
import net.summerfarm.mall.model.input.DataStorageQueryInput;
import net.summerfarm.mall.model.input.DistributionRulesGetInput;
import net.summerfarm.mall.model.input.product.*;
import net.summerfarm.mall.model.vo.*;
import net.summerfarm.mall.model.vo.product.CommodityInventoryVO;
import net.summerfarm.mall.service.*;
import net.xianmu.common.result.CommonResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Map;

/**
 * @Package: net.summerfarm.controller
 * @Description: 商城
 * @author: <EMAIL>
 * @Date: 2016/10/8
 */
@Slf4j
@RestController
@RequestMapping(value = "mall/sku")
public class MallSkuController {

    @Resource
    private MerchantService merchantService;
    @Resource
    private SkuQueryService skuQueryService;
    @Resource
    private InventoryService inventoryService;
    @Resource
    private CategoryService categoryService;
    @Resource
    private ArrivalNoticeService arrivalNoticeService;
    @Resource
    private CollocationService collocationService;
    @Resource
    AreaService areaService;
    @Resource
    private DeliveryService deliveryService;
    @Resource
    private DistributionRulesService distributionRulesService;

    @NacosValue("${mall.product.list.pageSize:6}")
    private int defaultPageSize;

    @NacosValue(value = "${search.switch:1}", autoRefreshed = true)
    private int searchSwitch;


    /**
     * 搜索
     *
     * @return
     */
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public CommonResult<PageInfo<ProductInfoVO>> productPage(@RequestBody ProductQueryInput input) {
        if (input.getPageSize () <= defaultPageSize) {
            input.setPageSize (defaultPageSize);
            log.info ("已替换成defaultPageSize：{}", defaultPageSize);
        }
        if(searchSwitch == 1){
            return CommonResult.ok (skuQueryService.selectHomeProductVo(input));
        }else{
            String ip = IPUtil.getIpAddress(RequestHolder.getRequest());
            ProductSearchInput i = new ProductSearchInput ();
            if (!IpWhiteListUtil.check(ip)) {
                i.setMId(null);
            }
            i.setPageIndex (input.getPageIndex ());
            i.setPageSize (input.getPageSize ());
            if (input.getPageSize () <= defaultPageSize) {
                input.setPageSize ( defaultPageSize);
                log.info("已替换成defaultPageSize：{}", defaultPageSize);
            }
            i.setPdName (input.getTitleSuggest ());
            return CommonResult.ok(inventoryService.selectHomeProductVoV2(i.getPageIndex (), i.getPageSize (), i));
        }
    }

    /**
     * 搜索 筛选项
     *
     * @return
     */
    @RequestMapping(value = "/condition", method = RequestMethod.POST)
    public CommonResult<List<SearchConditionVO>> condition(@RequestBody ProductQueryInput input) {
        String pdName = input.getTitleSuggest ();
        Integer areaNo = RequestHolder.getMerchantArea().getAreaNo();
        String key = areaNo + pdName;
        MerchantSubject merchantSubject = RequestHolder.getMerchantSubject ();
        if(merchantSubject!=null){
            Integer adminId = merchantSubject.getAdminId ();
            key  = adminId + key;
        }
        return CommonResult.ok (skuQueryService.selectQueryCondition(key));
    }
    /**
     *
     *  可能想买
     * @return
     */
    @RequestMapping(value = "/wantBuy", method = RequestMethod.POST)
    public CommonResult<List<String>> wantBuy(@RequestBody ProductQueryInput input) {
        return CommonResult.ok (merchantService.wantBuy(input));
    }

    /**
     *
     *  mimi榜单
     * @return
     */
    @RequestMapping(value = "/hotList", method = RequestMethod.POST)
    public CommonResult<List<ProductInfoVO>> hotList(@RequestBody HotListSearchInput input) {
        return CommonResult.ok (inventoryService.getHotList(input));
    }

}
