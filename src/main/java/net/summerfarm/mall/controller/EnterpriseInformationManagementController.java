package net.summerfarm.mall.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.service.EnterpriseInformationManagementService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @title: 商户信息管理控制类
 * @date 2022/4/2616:52
 */
@Api(tags = "商户信息管理")
@RequiresAuthority
@RestController
@RequestMapping(value = "/enterprise_information")
public class EnterpriseInformationManagementController {

    @Resource
    private EnterpriseInformationManagementService enterpriseInformationManagementService;
    @Resource
    private RedissonClient redissonClient;

    @ApiOperation(value = "天眼查查询信息", httpMethod = "GET")
    @RequestMapping(value = "/selectTianYanCha", method = RequestMethod.GET)
    @RequiresAuthority
    public AjaxResult selectTianYanCha(String name, Integer pageNum) {
        
        Long mId = RequestHolder.getMId();
        RLock redissonLock = redissonClient.getLock("lock:tianYanCha:" + name + mId);
        try {
            boolean flag = redissonLock.tryLock(0L, 3L, TimeUnit.SECONDS);
            if (!flag) {
                return AjaxResult.getErrorWithMsg("操作频率过快，请稍后操作");
            }

            return enterpriseInformationManagementService.selectTianYanCha(name,pageNum);
        } catch (InterruptedException e) {
            throw new DefaultServiceException(1, "操作频率过快，请稍后操作");
        } finally {
            if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()){
                redissonLock.unlock();
            }
        }
    }

}
