package net.summerfarm.mall.controller;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.service.MemberService;
import net.summerfarm.mall.service.TrolleyService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @Package: net.summerfarm.trade.controller
 * @Description: 购物车
 * @author: <EMAIL>
 * @Date: 2016/10/13
 */
@Api(tags = "购物车控制类")
@RequiresAuthority
@RestController
@RequestMapping(value = "/trolley")
@Deprecated
public class TrolleyController{

    @Resource
    private TrolleyService trolleyService;

    @Resource
    private MemberService memberService;

    /**
     * 剩余加单次数
     * @return
     */
    @ApiOperation(value = "剩余加单次数",httpMethod = "GET")
    @RequiresAuthority
    @RequestMapping(value = "/out-times")
    public AjaxResult getOutTimes() {
        int vip = memberService.calculGrade(RequestHolder.getMId());
        int totalOutTimes = memberService.getOutTimesByGrade(vip);
        int outTimes = trolleyService.getOutTimes(RequestHolder.getMId());
        JSONObject json = new JSONObject();
        json.put("vip", vip);
        json.put("outTimes", outTimes);
        json.put("totalOutTimes", totalOutTimes);
        json.put("outTimesFee", Global.OUT_TIMES_FEE);
        return AjaxResult.getOK(json);
    }
}
