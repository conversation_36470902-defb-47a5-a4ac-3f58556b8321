package net.summerfarm.mall.controller;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.facade.fms.SellingEntityQueryFacade;
import net.summerfarm.mall.model.vo.agreement.SellingEntityAgreementInfo;
import net.summerfarm.mall.model.vo.agreement.UserAgreementInfoVO;
import net.summerfarm.mall.service.sellingEntity.SellingEntityService;
import net.xianmu.common.result.CommonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户协议相关接口
 * <AUTHOR>
 * @Date 2025/3/17 10:06
 * @Version 1.0
 */
@Slf4j
@RestController
public class AgreementController {

    @Autowired
    private SellingEntityService sellingEntityService;

    @Autowired
    private SellingEntityQueryFacade sellingEntityQueryFacade;

    /**
     * 查询【销售主体】协议内容
     * @return 协议内容
     */
    @PostMapping("/sellingEntityAgreement/query/agreementContent")
    public CommonResult<SellingEntityAgreementInfo> queryAgreementContent() {
        return CommonResult.ok(new SellingEntityAgreementInfo(sellingEntityQueryFacade.queryAllSellingEntityInfo()));
    }

    /**
     * 查询是否需要弹窗
     * @return 是否需要弹窗
     */
    @PostMapping("/sellingEntityAgreement/query/needPopup")
    public CommonResult<Boolean> queryNeedPopup(){
        return CommonResult.ok(sellingEntityService.queryNeedPopup());
    }

    /**
     * 同意新销售主体协议
     * @return 是否成功
     */
    @PostMapping("/sellingEntityAgreement/agree")
    public CommonResult<Boolean> agreeNewSellingEntityAgreement(){
        sellingEntityService.agreeNewSellingEntityAgreement(RequestHolder.getMId());
        return CommonResult.ok(true);
    }

    /**
     * 查询用户协议版本
     * @return 用户协议版本
     */
    @PostMapping("/sellingEntityAgreement/query/version")
    public CommonResult<UserAgreementInfoVO> queryUserAgreementVersion(){
        Long mId = RequestHolder.getMId();
        Integer agreementVersion = sellingEntityService.queryUserAgreementVersion(mId);
        log.info("【用户协议版本查询结果】用户mId>>> {} >>> {}", mId, agreementVersion);
        return CommonResult.ok(new UserAgreementInfoVO(agreementVersion));
    }


}
