package net.summerfarm.mall.controller;

import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.model.input.TimingDeliveryInput;
import net.summerfarm.mall.model.vo.TimingDeliveryVO;
import net.summerfarm.mall.model.vo.TimingProductVO;
import net.summerfarm.mall.model.vo.product.TimingOrderProductQueryVO;
import net.summerfarm.mall.service.InventoryService;
import net.summerfarm.mall.service.TimingDeliveryService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Package: net.summerfarm.controller
 * @Description:  省心送
 * @author: <EMAIL>
 * @Date: 2016/12/7
 */
@Api(tags = "省心送控制类")
@RequestMapping(value = "/timing-delivery")
@RestController
public class TimingDeliveryController  {

    @Resource
    private InventoryService inventoryService;
    @Resource
    private TimingDeliveryService timingDeliveryService;

    @ApiOperation(value = "省心送商品分页展示(预售复用)",httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex",value = "页数",paramType = "path",defaultValue = "1",required = true),
            @ApiImplicitParam(name = "pageSize",value = "数量",paramType = "path",defaultValue = "10",required = true),
            @ApiImplicitParam(name = "type",value = "类型，0省心送，1预售",paramType = "query",required = true)
    })
    @RequestMapping(value = "/sku/{pageIndex}/{pageSize}",method = RequestMethod.GET)
    public CommonResult<PageInfo<TimingProductVO>> selectTimingSku(@PathVariable int pageIndex, @PathVariable int pageSize, TimingDeliveryInput timingDeliveryInput) {
        timingDeliveryInput.setPageIndex(pageIndex);
        timingDeliveryInput.setPageSize(pageSize);
        return inventoryService.selectTimingSkuGray(timingDeliveryInput);
    }

    /**
     * 省心送商品详情
     * @return
     */
    @ApiOperation(value = "省心送商品详情",httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sku",value = "sku编号",paramType = "path",required = true),
            @ApiImplicitParam(name = "ruleId",value = "组合包id",paramType = "query",required = true)
    })
    @RequiresAuthority
    @RequestMapping(value = "/sku/{sku}",method = RequestMethod.GET)
    public AjaxResult selectDetail(@PathVariable String sku, Integer ruleId) {
        return inventoryService.selectTimingSkuDetail(sku,null, ruleId);
    }

    /**
     * 我的省心送
     * @param pageIndex
     * @param pageSize
     * @param orderStatus
     * @return
     */
    @ApiOperation(value = "我的省心送",httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex",value = "页数",paramType = "path",defaultValue = "1",required = true),
            @ApiImplicitParam(name = "pageSize",value = "数量",paramType = "path",defaultValue = "10",required = true),
            @ApiImplicitParam(name = "orderStatus",value = "订单状态",paramType = "query")
    })
    @RequiresAuthority
    @RequestMapping(value = "/order/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public CommonResult<PageInfo<TimingDeliveryVO>> timingOrder(@PathVariable int pageIndex, @PathVariable int pageSize, Short orderStatus) {
        return timingDeliveryService.selectTimingOrder(pageIndex, pageSize, orderStatus);
    }


    /**
     * 省心送本周推荐top3
     * @return
     */
    @RequiresAuthority
    @PostMapping(value = "/query/top")
    public CommonResult<List<TimingProductVO>> topRecommend() {
        return timingDeliveryService.topRecommend(RequestHolder.getMerchantAreaNo());
    }

    /**
     * 获取商家还剩配送次数的省心送订单
     * @return
     */
    @RequiresAuthority
    @PostMapping(value = "/query/earliest/order")
    public CommonResult<TimingDeliveryVO> earliestNotCompleteOrder() {
        return timingDeliveryService.earliestNotCompleteOrder();
    }

    /**
     * 获取用户省心送商品接口
     * @param timingOrderProductQueryVO
     * @return
     */
    @RequiresAuthority
    @RequestMapping(value = "/query/timing-order-products", method = RequestMethod.POST)
    public CommonResult<PageInfo<TimingDeliveryVO>> timingOrderProducts(@RequestBody TimingOrderProductQueryVO timingOrderProductQueryVO) {
        return timingDeliveryService.selectTimingOrder(timingOrderProductQueryVO.getPageIndex(), timingOrderProductQueryVO.getPageSize(), timingOrderProductQueryVO.getOrderStatus());
    }


}
