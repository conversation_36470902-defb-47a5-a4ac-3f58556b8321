package net.summerfarm.mall.controller;

import com.github.pagehelper.PageInfo;
import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.model.domain.Coupon;
import net.summerfarm.mall.model.dto.market.banner.BannerDetailDTO;
import net.summerfarm.mall.model.dto.market.topicpage.*;
import net.summerfarm.mall.model.vo.ProductInfoVO;
import net.summerfarm.mall.service.BannerService;
import net.summerfarm.mall.service.TopicPageService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.CommonResultList;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.concurrent.TimeUnit;

/**
 * 专题页控制类（CMS系统）
 *
 * @author: <EMAIL>
 * @create: 2023/6/27
 */
@RequestMapping("/topic/page")
@RestController
public class TopicPageController {

    @Resource
    private TopicPageService topicPageService;

    @Resource
    private BannerService bannerService;

    @Resource
    private RedissonClient redissonClient;


    /**
     * 获取专题页组件框架
     *
     * @param topicPageId 专题页id，非必填
     * @return
     */
    @RequiresAuthority
    @PostMapping("/query/frame")
    public CommonResult<TopicPageDTO> showFrame(Long topicPageId) {
        CommonResult<TopicPageDTO> result = topicPageService.showFrame(topicPageId);
        return result;
    }

    /**
     * 分页获取专题页商品列表
     *
     * @param launchReq
     * @return
     */
    @RequiresAuthority
    @PostMapping("/query/sku")
    public CommonResult<PageInfo<ProductInfoVO>> listTopicSku(
            @RequestBody TopicLaunchReq launchReq) {
        return topicPageService.listTopicSku(launchReq);
    }

    /**
     * 分页获取专题页优惠券列表
     *
     * @param launchReq
     * @return
     */
    @RequiresAuthority
    @PostMapping("/query/coupon")
    public CommonResult<PageInfo<Coupon>> listTopicCoupon(@RequestBody TopicLaunchReq launchReq) {
        return topicPageService.listTopicCoupon(launchReq);
    }

    /**
     * 获取跳转明细
     *
     * @param linkReq
     * @return
     */
    @RequiresAuthority
    @PostMapping("/query/link")
    public CommonResult<BannerDetailDTO> getLinkDetail(@RequestBody TopicDetailLinkReq linkReq) {
        return bannerService.getLinkDetail(linkReq.getLinkType(), linkReq.getLinkBizId());
    }

    /**
     * 一键领取优惠券组件的优惠券
     *
     * @param launchId 组件投放id
     * @return
     */
    @RequiresAuthority
    @PostMapping("/upsert/coupon/one-click")
    public CommonResult<Integer> oneClickCoupon(Long launchId) {
        //幂等性处理
        RLock redissonLock = redissonClient.getLock(
                "lock:coupon-one-click:" + launchId + "-" + RequestHolder.getMId());
        try {
            boolean tryLock = redissonLock.tryLock(0L, 30L, TimeUnit.SECONDS);
            if (!tryLock) {
                throw new BizException("您的操作过于频繁，请稍后再试！");
            }
            CommonResult<Integer> result = topicPageService.oneClickReceiveCoupon(launchId);
            return result;
        } catch (InterruptedException e) {
            throw new BizException("一键领券失败，请重试！");
        } finally {
            if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()) {
                redissonLock.unlock();
            }
        }
    }

    /**
     * 获取商品低价位专题页前四个商品信息
     *
     * @param request
     * @return
     */
    @RequiresAuthority
    @PostMapping("/query/low-level")
    public CommonResultList<ProductInfoVO> lowLevelSkus(@RequestBody @Valid TopicLowLevelSkuReq request) {
        return CommonResultList.ok(topicPageService.lowLevelSkus(request));
    }

}
