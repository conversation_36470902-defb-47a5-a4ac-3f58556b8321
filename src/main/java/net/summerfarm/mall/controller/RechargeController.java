package net.summerfarm.mall.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.model.domain.Orders;
import net.summerfarm.mall.service.RechargeRecordService;
import net.summerfarm.mall.service.RechargeService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;

@Api(tags = "充值余额管理")
@RestController
@RequestMapping(value = "/recharge")
public class RechargeController {

    @Resource
    private RechargeRecordService rechargeRecordService;
    @Resource
    private RechargeService rechargeService;

    @ApiOperation(value = "用户余额",httpMethod = "GET",tags = "充值余额管理")
    @RequiresAuthority
    @RequestMapping(value = "/amount",method = RequestMethod.GET)
    public AjaxResult selectRechargeAmount(){
        return rechargeService.selectRechargeAmount(RequestHolder.getMId());
    }

    @ApiOperation(value = "余额变动记录",httpMethod = "GET",tags = "充值余额管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex",value = "页数",paramType = "path",required = true),
            @ApiImplicitParam(name = "pageSize",value = "数量",paramType = "path",required = true)
    })
    @RequiresAuthority
    @RequestMapping(value = "/recharge/record/{pageIndex}/{pageSize}",method = RequestMethod.GET)
    public AjaxResult selectRechargeRecordList(@PathVariable int pageIndex,@PathVariable int pageSize, Integer type, String yearMonth){
        return rechargeRecordService.selectRechargeRecordList(pageIndex,pageSize, type, yearMonth);
    }

    @RequiresAuthority
    @GetMapping("/gear")
    public AjaxResult selectRechargeGear(){
        return rechargeService.selectRechargeGear();
    }

    @RequiresAuthority
    @PostMapping("/recharge")
    public AjaxResult recharge(Integer configId, BigDecimal customAmount) {
        Orders recharge = rechargeService.recharge(configId, customAmount);
        return AjaxResult.getOK(recharge);
    }
}
