package net.summerfarm.mall.controller;

import java.util.List;
import javax.annotation.Resource;
import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.model.dto.market.exchange.ExchangeBuyDTO;
import net.summerfarm.mall.model.vo.ProductInfoVO;
import net.summerfarm.mall.service.ExchangeBuyService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: <EMAIL>
 * @create: 2022/9/15
 */
@RestController
@RequestMapping("/exchange")
public class ExchangeBuyController {

    @Resource
    private ExchangeBuyService exchangeBuyService;

    @RequiresAuthority
    @RequestMapping(value = "/query/info", method = RequestMethod.POST)
    public CommonResult<ExchangeBuyDTO> getExchangeBuyInfo() {
        CommonResult<ExchangeBuyDTO> result = exchangeBuyService.getExchangeBuyInfo();
        return result;
    }

    @RequiresAuthority
    @RequestMapping(value = "/query/list", method = RequestMethod.POST)
    public CommonResult<List<ProductInfoVO>> listAllItems() {
        CommonResult<List<ProductInfoVO>> result = exchangeBuyService.listAllItems();
        return result;
    }

    @RequiresAuthority
    @RequestMapping(value = "/query/guide-show", method = RequestMethod.POST)
    public CommonResult<Boolean> guideShow() {
        return exchangeBuyService.guideShow();
    }

}
