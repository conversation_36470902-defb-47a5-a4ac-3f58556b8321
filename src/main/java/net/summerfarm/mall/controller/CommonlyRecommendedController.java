package net.summerfarm.mall.controller;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.model.dto.product.BusinessSwitchDTO;
import net.summerfarm.mall.model.dto.product.CommonlyRecommendedQueryDTO;
import net.summerfarm.mall.service.CommonlyRecommendedService;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping("/home")
public class CommonlyRecommendedController {

    @Resource
    CommonlyRecommendedService commonlyRecommendedService;

    /**
     * 首页常用推荐
     * @return
     */
    @RequiresAuthority
    @RequestMapping(value = "/query/commonly-recommended", method = RequestMethod.POST)
    public AjaxResult commonlyRecommended(@Valid @RequestBody CommonlyRecommendedQueryDTO queryDTO, BindingResult bindingResult) {
        if(bindingResult.hasErrors()){
            String errorMsg = bindingResult.getAllErrors().get(0).getDefaultMessage();
            return AjaxResult.getErrorWithMsg(errorMsg);
        }
        return commonlyRecommendedService.listViewGray(queryDTO);
    }

    /**
     * 常用推荐new标签展示
     * @return
     */
    @RequiresAuthority
    @RequestMapping(value = "/query/business-switch", method = RequestMethod.POST)
    public AjaxResult businessSwitch(@RequestBody BusinessSwitchDTO switchDTO) {
        return commonlyRecommendedService.businessSwitch(switchDTO);
    }

}
