package net.summerfarm.mall.controller;

import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.enums.RedissonLockKey;
import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.model.converter.order.OrderInputConverter;
import net.summerfarm.mall.model.input.order.OrderPageInput;
import net.summerfarm.mall.model.input.order.PlaceOrderInput;
import net.summerfarm.mall.model.input.order.PreOrderInput;
import net.summerfarm.mall.model.vo.MerchantCouponVO;
import net.summerfarm.mall.model.vo.OrderVO;
import net.summerfarm.mall.model.vo.neworder.OrderNewResultVO;
import net.summerfarm.mall.model.vo.neworder.OrderSimpleVO;
import net.summerfarm.mall.model.vo.neworder.PlaceOrderResultVO;
import net.summerfarm.mall.model.vo.order.OrderDetailVO;
import net.summerfarm.mall.model.vo.order.PlaceOrderVO;
import net.summerfarm.mall.service.OrderNewService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 新-订单管理
 *
 * @author: <EMAIL>
 * @create: 2023/9/28
 */
@Slf4j
@RestController()
@RequestMapping("/order")
public class OrderNewController {

    @Resource
    private OrderNewService orderNewService;

    @Resource
    private RedissonClient redissonClient;

    /**
     * 预下单接口v3
     *
     * @param input 预下单数据
     */
    @RequiresAuthority
    @PostMapping("/query/pre-order/v3")
    public CommonResult<OrderNewResultVO> preOrder(@RequestBody PreOrderInput input) {
        //PreOrderInput转换成PlaceOrderVO
        PlaceOrderVO placeOrderVO = OrderInputConverter.toPlaceOrderVO(input);
        placeOrderVO.setTakePriceFlag(Boolean.FALSE);
        OrderNewResultVO resultVO = orderNewService.preOrder(placeOrderVO);
        return CommonResult.ok(resultVO);
    }

    /**
     * 下单接口v3
     *
     * @param input 下单数据
     */
    @RequiresAuthority
    @PostMapping("/upsert/place-order/v3")
    public CommonResult<PlaceOrderResultVO> placeOrder(@RequestBody PlaceOrderInput input) {
        Long mId = RequestHolder.getMId();
        if (Objects.isNull(mId)) {
            throw new BizException("请先登录");
        }
        //同一mId并发下单控制
        RLock redissonLock = redissonClient.getLock(RedissonLockKey.ORDER + mId);
        try {
            boolean flag = redissonLock.tryLock(0L, 30L, TimeUnit.SECONDS);
            if (!flag) {
                throw new BizException(1, "请重新下单");
            }
            //PlaceOrderInput转换成PlaceOrderVO
            PlaceOrderVO placeOrderVO = OrderInputConverter.toPlaceOrderVO(input);
            PlaceOrderResultVO resultVO = orderNewService.placeOrder(placeOrderVO);
            return CommonResult.ok(resultVO);
        } catch (InterruptedException e) {
            log.error("锁获取异常", e);
            throw new BizException(1, "请重新下单");
        } finally {
            if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()) {
                redissonLock.unlock();
            }
        }
    }

    /**
     * 订单列表页
     *
     * @param input
     * @return
     */
    @RequiresAuthority
    @PostMapping(value = "/query/page")
    public CommonResult<PageInfo<OrderVO>> getOrders(@RequestBody OrderPageInput input) {
        return orderNewService.queryOrderPage(input);
    }

    /**
     * 订单详情
     *
     * @param orderNo 子订单号
     * @return
     */
    @RequiresAuthority
    @PostMapping(value = "/query/detail/orderNo")
    public CommonResult<OrderDetailVO> orderDetails(String orderNo) {
        return orderNewService.getOrderDetail(orderNo);
    }

    /**
     * 获取子订单信息（如有）
     *
     * @param masterOrderNo 主订单号
     * @return 子订单信息
     */
    @RequiresAuthority
    @PostMapping(value = "/query/merge")
    public CommonResult<List<OrderSimpleVO>> queryOrderByMasterOrderNo(String masterOrderNo) {
        return orderNewService.queryOrderByMasterOrderNo(masterOrderNo);
    }

    /**
     * 查询当前订单的满返券
     *
     * @param masterOrderNo 主订单号
     * @return 满返劵信息
     */
    @RequiresAuthority
    @PostMapping(value = "/query/full_return")
    public CommonResult<List<MerchantCouponVO>> queryFullReturn(String masterOrderNo) {
        return CommonResult.ok(orderNewService.queryFullReturn(masterOrderNo));
    }
}
