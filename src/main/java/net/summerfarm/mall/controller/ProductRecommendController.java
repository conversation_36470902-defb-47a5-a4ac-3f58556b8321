package net.summerfarm.mall.controller;

import javax.annotation.Resource;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.service.ProductRecommendService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2020-09-14
 * @description 商品推荐（商品排序处理为人工推荐）
 */
@Slf4j
@RestController
@RequestMapping("/recommend")
public class ProductRecommendController {

    @NacosValue("${mall.product.list.pageSize:6}")
    private int defaultPageSize;

    @Resource
    private ProductRecommendService productRecommendService;


    @GetMapping("/tab")
    public AjaxResult sortTab() {
        return AjaxResult.getOK(productRecommendService.listAllTab());
    }

    /**
     * 推荐
     * @param pageIndex
     * @param pageSize
     * @param skuStr
     * @param type
     * @param tabId
     * @return
     */
    @GetMapping("{pageIndex}/{pageSize}")
    @RequiresAuthority
    public AjaxResult recommend(@PathVariable int pageIndex, @PathVariable int pageSize, String skuStr,
            @RequestParam(defaultValue = "0") Integer type, Integer tabId) {
        if (pageSize <= defaultPageSize) {
            log.info("即将替换成：defaultPageSize={}", defaultPageSize);
            pageSize = defaultPageSize;
        }
        return AjaxResult.getOK(productRecommendService.recommendV2(pageIndex, pageSize, skuStr, type, tabId));
    }

}
