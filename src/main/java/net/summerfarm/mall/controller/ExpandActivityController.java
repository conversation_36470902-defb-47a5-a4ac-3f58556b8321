package net.summerfarm.mall.controller;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.service.ExpandActivityService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


/**
 * 拓展购买
 * <AUTHOR>
 * @since 2022-08-18
 */
@RestController
@Slf4j
@RequestMapping("/expand_activity")
public class ExpandActivityController {

    @Resource
    ExpandActivityService expandActivityService;



    /**
     * 拓展购买商城首页弹窗接口
     */
    @RequiresAuthority
    @RequestMapping(value = "/pop_ups", method = RequestMethod.GET)
    public AjaxResult popUps(){
        return expandActivityService.popUps();
    }

    /**
     * 拓展购买商城列表接口
     * @return
     */
    @RequiresAuthority
    @RequestMapping(value = "/list/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult discountZoneList(@PathVariable int pageIndex, @PathVariable int pageSize) {
        return expandActivityService.listView(pageIndex,pageSize);
    }

    /**
     * 拓展购买商城活动详情接口
     * @return
     */
    @RequiresAuthority
    @RequestMapping(value = "/activity_details", method = RequestMethod.GET)
    public AjaxResult activityDetails() {
        return expandActivityService.activityDetails(null);
    }

    /**
     * 拓展购买支付成功调用接口
     * @return
     */
    @RequiresAuthority
    @RequestMapping(value = "/pay_success/{orderNo}", method = RequestMethod.GET)
    public AjaxResult expandActivityCallBack(@PathVariable String orderNo) {
        return expandActivityService.payCallBackInfo(orderNo);
    }

}
