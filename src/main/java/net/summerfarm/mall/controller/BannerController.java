package net.summerfarm.mall.controller;

import com.github.pagehelper.PageInfo;
import java.util.List;
import javax.annotation.Resource;
import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.model.dto.market.banner.BannerDTO;
import net.summerfarm.mall.model.dto.market.banner.BannerDetailDTO;
import net.summerfarm.mall.model.vo.ProductInfoVO;
import net.summerfarm.mall.service.BannerService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 资源位管理
 *
 * @author: <EMAIL>
 * @create: 2023/6/2
 */
@RequestMapping("/banner")
@RestController
public class BannerController {

    @Resource
    private BannerService bannerService;

    /**
     * 获取所有资源位信息
     *
     * @return
     */
    @PostMapping("/query/list")
    @RequiresAuthority
    public CommonResult<List<BannerDTO>> listAllBanner() {
        return bannerService.listAllBanner();
    }

    /**
     * 资源位详情
     *
     * @return
     */
    @PostMapping("/query/detail")
    @RequiresAuthority
    public CommonResult<BannerDetailDTO> bannerDetail(@RequestParam Integer id) {
        return bannerService.bannerDetail(id);
    }

    /**
     * 分页获取临保品（固定资源位）
     *
     * @return
     */
    @PostMapping("/query/expired")
    @RequiresAuthority
    public CommonResult<PageInfo<ProductInfoVO>> listExpiredSku(Integer pageNo, Integer pageSize) {
        return bannerService.listExpiredSku(pageNo, pageSize);
    }
}
