package net.summerfarm.mall.controller;

import net.summerfarm.mall.common.datacollect.UnCollectByAspect;
import net.summerfarm.mall.payments.common.enums.NotifyUrl;
import net.summerfarm.mall.payments.common.pojo.din.notify.DinNotifyDTO;
import net.summerfarm.mall.payments.common.pojo.fireface.FireFaceNotifyDTO;
import net.summerfarm.mall.payments.notify.CmbNotifyService;
import net.summerfarm.mall.payments.notify.DinNotifyService;
import net.summerfarm.mall.payments.notify.FireFaceNotifyService;
import net.summerfarm.mall.payments.notify.WeixNotifyService;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-07-05
 * @description 支付回调接口
 */
@RestController
public class PayNotifyController {
    @Resource
    private WeixNotifyService weixNotifyService;
    @Resource
    private CmbNotifyService cmbNotifyService;
    @Resource
    private FireFaceNotifyService fireFaceNotifyService;
    @Resource
    private DinNotifyService dinNotifyService;

    @UnCollectByAspect
    @RequestMapping(value = NotifyUrl.WEIX_NOTIFY_URL, method = RequestMethod.POST)
    public String weixPayNotify() throws IOException {
        return weixNotifyService.weixPayNotify();
    }

    @UnCollectByAspect
    @RequestMapping(value = NotifyUrl.WEIX_NOTIFY_URL_V2, method = RequestMethod.POST)
    public String weixPayNotifyV2() throws IOException {
        return weixNotifyService.weixPayNotifyV2();
    }


    @UnCollectByAspect
    @RequestMapping(value = NotifyUrl.WEIX_REFUND_URL, method = RequestMethod.POST)
    public String weixRefundNotify() throws Exception {
        return weixNotifyService.weixRefundNotify();
    }

    @UnCollectByAspect
    @RequestMapping(value = NotifyUrl.CMD_NOTIFY_URL, method = RequestMethod.POST, produces = {"application/json"})
    public Map<String, String> cmbPayNotify(@RequestParam Map<String, String> notifyMap){
        return cmbNotifyService.cmbPayNotify(notifyMap, true);
    }

    @UnCollectByAspect
    @RequestMapping(value = NotifyUrl.CMD_NOTIFY_URL_V2, method = RequestMethod.POST, produces = {"application/json"})
    public Map<String, String> cmbPayNotifyV2(@RequestParam Map<String, String> notifyMap){
        return cmbNotifyService.cmbPayNotifyV2(notifyMap, true);
    }

    @UnCollectByAspect
    @RequestMapping(value = NotifyUrl.CMD_REFUND_URL, method = RequestMethod.POST, produces = {"application/json"})
    public Map<String, String> cmbNotifyNotify(@RequestParam Map<String, String> notifyMap){
        return cmbNotifyService.cmbRefundNotify(notifyMap, true);
    }

    /**
     * 火脸支付回调
     * @param notifyDTO
     * @return
     */
    @UnCollectByAspect
    @RequestMapping(value = "/pay-notify/fire-face/{companyAccountId}", method = RequestMethod.POST, produces = {"application/json"})
    public String fireFacePayNotify(@PathVariable("companyAccountId") Integer companyAccountId, @RequestBody FireFaceNotifyDTO notifyDTO) {
        return fireFaceNotifyService.payNotify(companyAccountId, notifyDTO);
    }


    @UnCollectByAspect
    @RequestMapping(value = "/refund-notify/fire-face/{companyAccountId}", method = RequestMethod.POST, produces = {"application/json"})
    public String fireFaceRefundNotify(@PathVariable("companyAccountId") Integer companyAccountId, @RequestBody FireFaceNotifyDTO notifyDTO) throws Exception {
        return fireFaceNotifyService.fireFaceRefundNotify(companyAccountId, notifyDTO);
    }

    /**
     * 智付支付回调
     * @param dinNotifyDTO
     * @return
     */
    @UnCollectByAspect
    @RequestMapping(value = "/pay-notify/din", method = RequestMethod.POST, consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public String dinPayNotify(@ModelAttribute DinNotifyDTO dinNotifyDTO) {
        return dinNotifyService.payNotify(dinNotifyDTO);
    }

    @UnCollectByAspect
    @RequestMapping(value = "/refund-notify/din", method = RequestMethod.POST, consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public String dinRefundNotify(@ModelAttribute DinNotifyDTO dinNotifyDTO) {
        return dinNotifyService.refundNotify(dinNotifyDTO);
    }

}
