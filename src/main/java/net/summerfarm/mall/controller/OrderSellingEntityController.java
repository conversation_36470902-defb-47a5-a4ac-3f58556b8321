package net.summerfarm.mall.controller;

import net.summerfarm.mall.service.OrderSellingEntityService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description: 订单销售主体控制器
 * @author: <PERSON>
 * @date: 2025-03-13
 **/
@RestController
@RequestMapping("/order-selling-entity")
public class OrderSellingEntityController {

    @Resource
    private OrderSellingEntityService orderSellingEntityService;

    /**
     * 根据订单号查询销售实体
     * @param orderNos
     * @return
     */
    @RequestMapping("/query/by-order-nos")
    public CommonResult<List<String>> querySellingEntityByNos(@RequestBody List<String> orderNos) {
        return CommonResult.ok(orderSellingEntityService.querySellingEntityByNos(orderNos));
    }
}
