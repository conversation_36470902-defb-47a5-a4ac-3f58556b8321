package net.summerfarm.mall.controller;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.model.vo.DeliveryEvaluationVO;
import net.summerfarm.mall.service.DeliveryPlanService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 评价中心
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping(value = "/evaluation")
public class EvaluationController {

    @Resource
    private DeliveryPlanService deliveryPlanService;

    /**
     * 待评价数量
     */
    @RequestMapping(value = "/query/evaluation-count", method = RequestMethod.POST)
    @RequiresAuthority
    public CommonResult getEvaluationCount() {
        return deliveryPlanService.getEvaluationCount();
    }

    /**
     * 快速评价
     */
    @RequestMapping(value = "/upsert/rapid-evaluation", method = RequestMethod.POST)
    @RequiresAuthority
    public CommonResult rapidEvaluation(@RequestBody DeliveryEvaluationVO deliveryEvaluationVO) {
        return deliveryPlanService.rapidEvaluation(deliveryEvaluationVO);
    }

    /**
     * 评价中心-待评价列表
     */
    @RequestMapping(value = "/query/evaluation-list", method = RequestMethod.POST)
    @RequiresAuthority
    public CommonResult evaluationList(@RequestBody DeliveryEvaluationVO deliveryEvaluationVO) {
        return deliveryPlanService.evaluationList(deliveryEvaluationVO);
    }


}
