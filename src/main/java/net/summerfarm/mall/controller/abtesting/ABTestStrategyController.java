package net.summerfarm.mall.controller.abtesting;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.recommend.strategy.model.UserExperimentVariantDTO;
import net.summerfarm.recommend.strategy.service.UserExperimentingService;
import net.xianmu.common.result.CommonResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/abStrategy")
@Slf4j
@Api(tags = "用户AB实验控制类")
public class ABTestStrategyController {

    @NacosValue(value = "${abStrategy.allowed.areaNoList:}", autoRefreshed = true)
    private Set<Integer> allowedAreaNoList;

    @NacosValue(value = "${abStrategy.isAllAllowed:true}", autoRefreshed = true)
    private boolean isAllAllowed;

    @Resource
    private UserExperimentingService userExperimentingService;

    @PostMapping("/experiments")
    @RequiresAuthority
    @ApiOperation("每次用户进入小程序或者H5商城时，前端请求本接口用以确认用户进入了哪些实验以及分桶信息")
    public CommonResult<List<UserExperimentVariantDTO>> experiments(@RequestParam(required = false) String variantId) {
        if (!isUserInAllowedAreaNo()) {
            return CommonResult.ok(Collections.EMPTY_LIST);
        }
        return CommonResult.ok(userExperimentingService.getExperimentVariant(RequestHolder.getMId(), RequestHolder.getMerchantAreaNo()));
    }

    @RequestMapping(value = "/userExperiments", method = {RequestMethod.GET, RequestMethod.POST})
    @RequiresAuthority
    @ApiOperation("每次用户进入小程序或者H5商城时，前端请求本接口用以确认门店进入了哪些实验以及分桶信息")
    public CommonResult<Map<String, UserExperimentVariantDTO>> experimentsMap(@RequestParam(required = false, defaultValue = "") String variantId) {
        if (!isUserInAllowedAreaNo()) {
            return CommonResult.ok(Collections.EMPTY_MAP);
        }
        List<UserExperimentVariantDTO> userExperimentVariantDTOList = userExperimentingService.getExperimentVariant(RequestHolder.getMId(), RequestHolder.getMerchantAreaNo());
        if (CollectionUtils.isEmpty(userExperimentVariantDTOList)) {
            log.info("门店没有匹配上任何AB实验:{}", RequestHolder.getMId());
            return CommonResult.ok(Collections.EMPTY_MAP);
        }
        Map<String, UserExperimentVariantDTO> result = userExperimentVariantDTOList.stream().collect(Collectors.toMap(UserExperimentVariantDTO::getExperimentId, exp -> {
            if (StringUtils.isNotBlank(variantId)) {
                log.warn("实验:{} 被前端指定了固定分组:{}", exp.getExperimentId(), variantId);
                exp.setVariantId(variantId);
            }
            return exp;
        }, (e1, e2) -> e1));
        return CommonResult.ok(result);
    }

    private boolean isUserInAllowedAreaNo() {
        Long mId = RequestHolder.getMId();
        if (isAllAllowed) {
            log.info("AB实验已经全部开启");
            return isAllAllowed;
        }
        if (CollectionUtils.isEmpty(allowedAreaNoList)) {
            log.warn("未配置abStrategy.allowed.areaNoList");
            return false;
        }

        Integer areaNo = RequestHolder.getMerchantAreaNo();

        boolean isAllowed = allowedAreaNoList.contains(areaNo);
        if (isAllowed) {
            log.info("用户即将进入AB实验, areaNo:{},mId:{}", areaNo, mId);
        } else {
            log.info("用户不参与AB实验, areaNo:{},mId:{}", areaNo, mId);
        }
        return isAllowed;
    }
}
