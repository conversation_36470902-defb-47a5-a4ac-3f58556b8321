package net.summerfarm.mall.controller;

import net.summerfarm.mall.model.vo.FeedBackVO;
import net.summerfarm.mall.model.vo.FollowUpEvaluationVO;
import net.summerfarm.mall.service.FollowUpRecordService;
import net.xianmu.common.result.CommonResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
@RestController
@RequestMapping("/follow-up-record")
public class FollowUpController {
    @Resource
    FollowUpRecordService followUpRecordService;
    /**
     * 用户提交反馈
     *
     * @param feedBackVO 提交反馈参数
     * @return
     */
    @PostMapping("/feedback")
    public CommonResult feedback(@Validated @RequestBody FeedBackVO feedBackVO) {
        return followUpRecordService.feedback(feedBackVO);
    }


    /**
     * 销售评价
     *
     * @param followUpEvaluationVO 提交评价参数
     * @return
     */
    @PostMapping("/upsert/evaluation-save")
    public CommonResult<Boolean> evaluationSave(@Validated @RequestBody FollowUpEvaluationVO followUpEvaluationVO) {
        return followUpRecordService.evaluationSave(followUpEvaluationVO);
    }

}
