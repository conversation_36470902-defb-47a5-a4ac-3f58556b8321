package net.summerfarm.mall.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.common.redis.KeyConstant;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.mapper.MerchantMapper;
import net.summerfarm.mall.model.domain.Merchant;
import net.summerfarm.mall.model.vo.DeliveryEvaluationVO;
import net.summerfarm.mall.service.AfterSaleOrderService;
import net.summerfarm.mall.service.MerchantSubAccountService;
import net.xianmu.common.result.CommonResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2019-08-08
 * @description
 */
@Api(tags = "子账号控制类")
@RestController
@RequestMapping(value = "/sub-account")
public class MerchantSubAccountController {
    @Resource
    private MerchantSubAccountService accountService;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private AfterSaleOrderService afterSaleOrderService;

    private static final Logger logger = LoggerFactory.getLogger(MerchantSubAccountController.class);

    @ApiOperation(value = "提交子账号信息", httpMethod = "POST", response = Merchant.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "contact", value = "子账号联系方式", type = "query", required = true),
            @ApiImplicitParam(name = "phone", value = "子账号手机号", type = "query", required = true),
            @ApiImplicitParam(name = "mergeKey", value = "邀请口令", type = "query", required = true)
    })
    @RequestMapping(value = "/submit", method = RequestMethod.POST)
    public AjaxResult submitAccountInfo(String contact, String phone,
                                        String mergeKey,
                                        HttpServletRequest request) {
        return accountService.submit(contact, phone, mergeKey, request);
    }

    @ApiOperation(value = "重新提交子账号信息", httpMethod = "POST", response = Merchant.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "accountId", value = "子账号id", type = "query", required = true),
            @ApiImplicitParam(name = "contact", value = "子账号联系方式", type = "query"),
            @ApiImplicitParam(name = "phone", value = "子账号手机号", type = "query"),
            @ApiImplicitParam(name = "mergeKey", value = "口令", type = "query")
    })
    @RequestMapping(value = "/reSubmit", method = RequestMethod.POST)
    public AjaxResult reSubmitAccountInfo(Long accountId, String contact, String phone, String mergeKey) {
        return accountService.reSubmit(accountId, contact, phone, mergeKey);
    }

    @ApiOperation(value = "查询账号信息", httpMethod = "GET", response = Merchant.class)
    @RequiresAuthority
    @RequestMapping(value = "/account-list", method = RequestMethod.GET)
    public AjaxResult getAccountList() {
        return accountService.selectAccountList(RequestHolder.getMId(), RequestHolder.getAccountId());
    }

    @ApiOperation(value = "查询账号详细信息", httpMethod = "GET", response = Merchant.class)
    @ApiImplicitParam(name = "accountId", value = "子账号id", type = "query")
    @RequestMapping(value = "/account-info", method = RequestMethod.GET)
    public AjaxResult getAccountInfo(Long accountId) {
        return accountService.selectAccountInfo(accountId);
    }

    @ApiOperation(value = "设置新店长", httpMethod = "POST", response = Merchant.class)
    @ApiImplicitParam(name = "accountId", value = "子账号id", type = "query")
    @RequiresAuthority
    @RequestMapping(value = "/change", method = RequestMethod.POST)
    public AjaxResult changeManager(Long accountId) {
        return accountService.changeManger(accountId);
    }

    @ApiOperation(value = "移除店员", httpMethod = "POST", response = Merchant.class)
    @ApiImplicitParam(name = "accountId", value = "子账号id", type = "query")
    @RequiresAuthority
    @RequestMapping(value = "/del", method = RequestMethod.POST)
    public AjaxResult deleteAccount(Long accountId) {
        return accountService.deleteAccount(accountId);
    }

    @ApiOperation(value = "子账号审核", httpMethod = "POST", response = Merchant.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "accountId", value = "子账号id", type = "query"),
            @ApiImplicitParam(name = "auditFlag", value = "审核标识：0、不通过 1、通过", type = "query")
    })
    @RequiresAuthority
    @RequestMapping(value = "/audit", method = RequestMethod.POST)
    public AjaxResult auditAccount(Long accountId, Integer auditFlag) {
        return accountService.auditAccount(accountId, auditFlag);
    }

    @ApiOperation(value = "根据邀请口令查询店铺名称", httpMethod = "GET", response = Merchant.class)
    @ApiImplicitParam(name = "mergeKey", value = "邀请口令", type = "query")
    @RequestMapping(value = "/merchant", method = RequestMethod.GET)
    public AjaxResult selectMerchant(String mergeKey) {
        return accountService.selectMerchantName(mergeKey);
    }

    @ApiOperation(value = "弹窗已读回执", httpMethod = "POST", response = Merchant.class)
    @ApiImplicitParam(name = "type", value = "弹窗类型", required = true)
    @RequestMapping(value = "/read", method = RequestMethod.POST)
    public AjaxResult firstPopView(String type) {
        if ("firstLoginPop".equals(type)) {
            Merchant merchant = new Merchant();
            merchant.setmId(RequestHolder.getMId());
            merchant.setFirstLoginPop(1);
            merchantMapper.updateByPrimaryKeySelective(merchant);
            return AjaxResult.getOK();
        } else if ("changePop".equals(type)) {
            Merchant merchant = new Merchant();
            merchant.setmId(RequestHolder.getMId());
            merchant.setChangePop(1);
            merchantMapper.updateByPrimaryKeySelective(merchant);
            return AjaxResult.getOK();
        } else {
            return accountService.readFirstPopView();
        }
    }

    @ApiOperation(value = "修改账号联系人名称", httpMethod = "POST", response = Merchant.class)
    @ApiImplicitParam(name = "contact", value = "新联系人名称", type = "query")
    @RequestMapping(value = "/editContact", method = RequestMethod.POST)
    public AjaxResult editContact(String contact) {
        return accountService.editContact(contact);
    }

    @ApiOperation(value = "检验邀请口令", httpMethod = "POST", response = Merchant.class)
    @ApiImplicitParam(name = "mergeKey", value = "新联系人名称", type = "query")
    @RequestMapping(value = "/invalidMergeKey", method = RequestMethod.GET)
    public AjaxResult invalidMergeKey(String mergeKey) {
        return accountService.invalidMergeKey(mergeKey);
    }

    @ApiOperation(value = "查询子账号状态是否正常", httpMethod = "GET", tags = "子账号管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "accountId", value = "accountId", paramType = "query")
    })
    @RequiresAuthority
    @RequestMapping(value = "/status", method = RequestMethod.GET)
    public AjaxResult getAccountStatus(Long accountId) {
        return accountService.selectAccountStatus(accountId);
    }

    /**
     * 省心送退款弹窗通知新接口
     */
    @RequestMapping(value = "/query/timing-refund-view-new", method = RequestMethod.POST)
    @RequiresAuthority
    public CommonResult timingRefundViewNew() {
        return afterSaleOrderService.timingRefundView();
    }

    /**
     * 超过48小时未设置配送计划弹窗提醒
     */
    @RequestMapping(value = "/query/timing-not-plan-view", method = RequestMethod.POST)
    @RequiresAuthority
    public CommonResult timingNotPlanView() {
        return afterSaleOrderService.timingNotPlanView();
    }

    /**
     * 省心送历史订单弹窗
     */
    @RequestMapping(value = "/query/timing-old-order-view", method = RequestMethod.POST)
    @RequiresAuthority
    public CommonResult timingOldOrderView() {
        return afterSaleOrderService.timingOldOrderView();
    }

}
