package net.summerfarm.mall.controller;

import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.model.input.MerchantCancelReq;
import net.summerfarm.mall.model.vo.MerchantCancelVO;
import net.summerfarm.mall.service.MerchantCancelService;
import net.xianmu.common.result.CommonResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-mall
 * @description 门店注销
 * @date 2023/4/20 19:13:41
 */
@RestController
@RequestMapping("/merchant/cancel")
public class MerchantCancelController {

    @Resource
    private MerchantCancelService merchantCancelService;


    /**
     * 申请注销
     * @param merchantCancelReq
     * @return MerchantCancelVO
     */
    @PostMapping(value = "/upsert/insert")
    @RequiresAuthority
    public CommonResult<MerchantCancelVO> insert(@RequestBody @Validated MerchantCancelReq merchantCancelReq) {
        return CommonResult.ok(merchantCancelService.insert(merchantCancelReq));
    }

    /**
     * 更新门店注销状态
     * @param merchantCancelReq
     * @return MerchantCancelVO
     */
    @PostMapping(value = "/upsert/update-status")
    @RequiresAuthority
    public CommonResult<Boolean> updateStatus(@RequestBody MerchantCancelReq merchantCancelReq) {
        return CommonResult.ok(merchantCancelService.updateStatus(merchantCancelReq));
    }

    /**
     * 校验门店是否是主账号
     * @return MerchantCancelVO
     */
    @PostMapping(value = "/upsert/check")
    @RequiresAuthority
    public CommonResult<Boolean> checkMasterAccount(@RequestBody MerchantCancelReq merchantCancelReq) {
        return CommonResult.ok(merchantCancelService.checkMasterAccount(merchantCancelReq));
    }

    /**
     * 获取门店注销申请记录--待注销
     * @return MerchantCancelVO
     */
    @PostMapping(value = "/query/detail")
    @RequiresAuthority
    public CommonResult<MerchantCancelVO> getDetail() {
        return CommonResult.ok(merchantCancelService.getDetail());
    }
}
