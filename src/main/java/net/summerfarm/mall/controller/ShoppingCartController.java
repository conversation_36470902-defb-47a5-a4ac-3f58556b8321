package net.summerfarm.mall.controller;

import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.model.input.ShoppingCartDeleteInput;
import net.summerfarm.mall.model.input.ShoppingCartGetInput;
import net.summerfarm.mall.model.input.ShoppingCartInsertInput;
import net.summerfarm.mall.model.input.ShoppingCartOneMoreInput;
import net.summerfarm.mall.model.vo.ShoppingCartOneMoreVO;
import net.summerfarm.mall.model.vo.ShoppingCartVO;
import net.summerfarm.mall.service.ShoppingCartService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-mall
 * @description 购物车
 * @date 2023/9/27 15:55:24
 */
@RestController
@RequestMapping(value = "/shopping/cart")
public class ShoppingCartController {

    @Resource
    private ShoppingCartService shoppingCartService;

    @Resource
    private RedissonClient redissonClient;

    /**
     * 新增购物车信息
     * @param input
     * @return Boolean
     */
    @PostMapping(value = "/upsert/insert")
    @RequiresAuthority
    public CommonResult<Long> insert(@RequestBody @Validated ShoppingCartInsertInput input) {
        //幂等性处理
        RLock redissonLock = redissonClient.getLock("lock:shopping-cart:" + input.getSku() + "-" + RequestHolder.getMId());
        try {
            boolean tryLock = redissonLock.tryLock(0L, 10L, TimeUnit.SECONDS);
            if (!tryLock) {
                throw new BizException("您的操作过于频繁，请稍后再试！");
            }
            return CommonResult.ok(shoppingCartService.insert(input));
        } catch (InterruptedException e) {
            throw new DefaultServiceException(1, "获取锁失败，请重试！");
        } finally {
            if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()){
                redissonLock.unlock();
            }
        }
    }

    /**
     * 查询购物车
     * @param
     * @return Boolean
     */
    @PostMapping(value = "/query/get-all")
    @RequiresAuthority
    public CommonResult<List<ShoppingCartVO>> getAll(@RequestBody ShoppingCartGetInput input) {
        return CommonResult.ok(shoppingCartService.getAll(input));
    }

    /**
     * 删除购物车信息-支持批量删除
     * @param input
     * @return Boolean
     */
    @PostMapping(value = "/upsert/delete")
    @RequiresAuthority
    public CommonResult<Boolean> delete(@RequestBody @Validated ShoppingCartDeleteInput input) {
        return CommonResult.ok(shoppingCartService.delete(input));
    }

    /**
     * 查询购物车数量
     * @param
     * @return Integer
     */
    @PostMapping(value = "/query/count")
    @RequiresAuthority
    public CommonResult<Integer> count(@RequestBody ShoppingCartGetInput input) {
        return CommonResult.ok(shoppingCartService.count(input));
    }

    /**
     * 再来一单
     * @param
     * @return List<String>
     */
    @PostMapping(value = "/upsert/one-more")
    @RequiresAuthority
    public CommonResult<ShoppingCartOneMoreVO> oneMoreOrder(@RequestBody @Validated ShoppingCartOneMoreInput input) {
        //幂等性处理
        RLock redissonLock = redissonClient.getLock("lock:shopping-cart:" + input.getOrderNo() + "-" + RequestHolder.getMId());
        try {
            boolean tryLock = redissonLock.tryLock(0L, 10L, TimeUnit.SECONDS);
            if (!tryLock) {
                throw new BizException("您的操作过于频繁，请稍后再试！");
            }
            return CommonResult.ok(shoppingCartService.oneMoreOrder(input));
        } catch (InterruptedException e) {
            throw new DefaultServiceException(1, "获取锁失败，请重试！");
        } finally {
            if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()){
                redissonLock.unlock();
            }
        }
    }
}
