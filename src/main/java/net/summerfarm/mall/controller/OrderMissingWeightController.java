package net.summerfarm.mall.controller;

import net.summerfarm.mall.service.OrderMissingWeightService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @description:
 * @author: George
 * @date: 2024-09-23
 **/
@RestController
@RequestMapping(value = "/order-missing-weight")
public class OrderMissingWeightController {

    @Resource
    private OrderMissingWeightService orderMissingWeightService;

    /**
     * 自动售后
     * @return
     */
    @PostMapping("/{orderNo}")
    public CommonResult<Void> autoAfterSale4OrderMissingWeight(@PathVariable("orderNo") String orderNo) {
        orderMissingWeightService.autoAfterSale4OrderMissingWeightLite(orderNo);
        return CommonResult.ok();
    }
}
