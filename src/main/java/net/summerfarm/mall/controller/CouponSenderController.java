package net.summerfarm.mall.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.enums.coupon.CouponSenderSetupSenderTypeEnum;
import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.model.bo.coupon.CouponSenderBO;
import net.summerfarm.mall.model.domain.Coupon;
import net.summerfarm.mall.model.domain.CouponSenderRelation;
import net.summerfarm.mall.model.dto.market.coupon.SkuCouponDTO;
import net.summerfarm.mall.model.dto.market.coupon.SkuCouponReqDTO;
import net.summerfarm.mall.model.input.CouponReceiveReq;
import net.summerfarm.mall.model.vo.MerchantCouponVO;
import net.summerfarm.mall.model.vo.coupon.AutoReceiveCoupon;
import net.summerfarm.mall.service.CouponSenderService;
import net.summerfarm.mall.service.strategy.coupon.CouponSenderContext;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Api(tags = "优惠劵领取页")
@RestController
@RequestMapping(value = "/sender-setup")
public class CouponSenderController {
    @Resource
    CouponSenderService couponSenderService;

    @Resource
    CouponSenderContext couponSenderContext;

    @Resource
    private RedissonClient redissonClient;

    @ApiOperation(value = "优惠券展示页查询",httpMethod = "GET",response = MerchantCouponVO.class)
    @RequiresAuthority
    @RequestMapping(value = "/coupon/list")
    public CommonResult<List<Coupon>> list() {
        return couponSenderService.list(true, RequestHolder.getMId());
    }

    /**
     * 优惠券领取
     * @param couponSenderRelation
     * @return
     */
    @RequiresAuthority
    @RequestMapping(value = "/coupon/receive",method = RequestMethod.POST)
    public AjaxResult receive(@RequestBody @Validated CouponReceiveReq couponSenderRelation) {
        //幂等性处理
        RLock redissonLock = redissonClient.getLock("lock:coupon-receive:" + couponSenderRelation.getCouponId() + "-" + RequestHolder.getMId());
        try {
            boolean tryLock = redissonLock.tryLock(0L, 10L, TimeUnit.SECONDS);
            if (!tryLock) {
                throw new BizException("您的操作过于频繁，请稍后再试！");
            }
            CouponSenderBO couponSenderBO = new CouponSenderBO();
            couponSenderBO.setCouponId(couponSenderRelation.getCouponId());
            couponSenderBO.setCouponSenderId(couponSenderRelation.getCouponSenderId());
            couponSenderBO.setMId(RequestHolder.getMId());
            couponSenderBO.setSenderType(CouponSenderSetupSenderTypeEnum.USER_RECEIVE);
            couponSenderBO.setTriggerTime(LocalDateTime.now());
            if (Objects.equals(couponSenderRelation.getIsFromSender(), false)) {
                couponSenderContext.receiveNew(couponSenderBO);
            } else {
                couponSenderContext.sendCoupon(couponSenderBO);
            }
            return AjaxResult.getOK();
        } catch (InterruptedException e) {
            throw new DefaultServiceException(1, "获取锁失败，请重试！");
        } finally {
            if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()){
                redissonLock.unlock();
            }
        }
    }

    /**
     * 自动领券
     * @return
     */
    @RequiresAuthority
    @PostMapping(value = "/add/automatic-coupon")
    public CommonResult takeAllocationPrice(@RequestBody @Validated AutoReceiveCoupon autoReceiveCoupon) {
        //幂等性处理
        RLock redissonLock = redissonClient.getLock("lock:coupon-receive:" + autoReceiveCoupon.getCouponId() + "-" + RequestHolder.getMId());
        try {
            boolean tryLock = redissonLock.tryLock(0L, 10L, TimeUnit.SECONDS);
            if (!tryLock) {
                throw new BizException("您的操作过于频繁，请稍后再试！");
            }
            CouponSenderBO couponSenderBO = new CouponSenderBO();
            couponSenderBO.setCouponId(autoReceiveCoupon.getCouponId());
            couponSenderBO.setCouponSenderId(autoReceiveCoupon.getCouponSenderId());
            couponSenderBO.setMId(RequestHolder.getMId());
            couponSenderBO.setSenderType(CouponSenderSetupSenderTypeEnum.USER_RECEIVE);
            couponSenderBO.setTriggerTime(LocalDateTime.now());
            couponSenderContext.sendCoupon(couponSenderBO);
            return CommonResult.ok();
        } catch (InterruptedException e) {
            throw new DefaultServiceException(1, "获取锁失败，请重试！");
        } finally {
            if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()) {
                redissonLock.unlock();
            }
        }
    }
}
