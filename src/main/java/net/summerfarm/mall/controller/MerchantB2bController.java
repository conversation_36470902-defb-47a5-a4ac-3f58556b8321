package net.summerfarm.mall.controller;

import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.mapper.MerchantMapper;
import net.summerfarm.mall.model.domain.Merchant;
import net.summerfarm.mall.model.dto.merchant.MerchantB2bDTO;
import net.summerfarm.mall.model.vo.MerchantSubject;
import net.summerfarm.mall.model.vo.merchant.UpdateMerchantPopupStatusVO;
import net.summerfarm.mall.service.MerchantB2bService;
import net.xianmu.common.result.CommonResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * b2b门店
 */
@RestController
@RequestMapping("/merchant/b2b")
public class MerchantB2bController {
    @Resource
    MerchantB2bService merchantB2bService;
    @Resource
    MerchantMapper merchantMapper;
    /**
     * 查询门店p2p配置详情（主要是设置弹窗）
     *
     * @return MerchantCancelVO
     */
    @PostMapping(value = "/query")
    @RequiresAuthority
    public CommonResult<MerchantB2bDTO> query() {
        MerchantB2bDTO dto = merchantB2bService.query(RequestHolder.getMerchantSubject());
        return CommonResult.ok(dto);
    }

    /**
     * 更新门店p2p微信状态 包微信接口和后续操作
     *
     * @return MerchantCancelVO
     */
    @PostMapping(value = "/query/b2b")
    @RequiresAuthority
    public CommonResult<MerchantB2bDTO> queryB2bWX() {
        MerchantB2bDTO dto = merchantB2bService.queryB2bWX(RequestHolder.getMerchantSubject());
        return CommonResult.ok(dto);
    }

    /**
     * 更新门店弹框状态
     *
     * @return boolean
     */
    @PostMapping(value = "/upsert/update-status")
    @RequiresAuthority
    public CommonResult<Boolean> updatePopupStatus(@RequestBody @Validated UpdateMerchantPopupStatusVO updateMerchantPopupStatusVO) {
        MerchantSubject merchantSubject = RequestHolder.getMerchantSubject();
        return CommonResult.ok(merchantB2bService.updatePopupStatus(merchantSubject, updateMerchantPopupStatusVO));
    }
//
    /**
     * 预录入
     *
     * @return boolean
     */
    @PostMapping(value = "/upsert/fore-entering")
    @RequiresAuthority
    public CommonResult<Boolean> foreEntering() {
        MerchantSubject merchantSubject = RequestHolder.getMerchantSubject();
        return CommonResult.ok(merchantB2bService.foreEntering(merchantSubject.getAccount().getAccountId(), merchantMapper.selectOneByMid(merchantSubject.getMerchantId())));
    }
}
