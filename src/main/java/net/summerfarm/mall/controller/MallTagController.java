package net.summerfarm.mall.controller;

import java.util.List;
import javax.annotation.Resource;
import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.model.dto.market.malltag.MallAllTagDTO;
import net.summerfarm.mall.model.dto.market.malltag.MallTagReqDTO;
import net.summerfarm.mall.model.dto.market.malltag.SkuMallTagInfoDTO;
import net.summerfarm.mall.service.MallTagService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * sku标签
 *
 * @author: <EMAIL>
 * @create: 2023/4/17
 */
@RestController
@RequestMapping("/mall/tag")
public class MallTagController {

    @Resource
    private MallTagService mallTagService;

    /**
     * 批量获取sku标签信息
     *
     * @param reqDTO
     * @return
     */
    @RequiresAuthority
    @RequestMapping(value = "/query/all", method = RequestMethod.POST)
    public CommonResult<List<SkuMallTagInfoDTO>> listAllTags(@RequestBody MallTagReqDTO reqDTO) {
        return mallTagService.listAllTags(reqDTO);
    }

}
