package net.summerfarm.mall.controller;

import com.github.pagehelper.PageInfo;
import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.common.config.DynamicConfig;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.enums.MerchantSizeEnum;
import net.summerfarm.mall.model.input.MerchantCancelReq;
import net.summerfarm.mall.model.input.MerchantJobQueryInput;
import net.summerfarm.mall.model.input.MerchantJobUpdateInput;
import net.summerfarm.mall.model.vo.MerchantCancelVO;
import net.summerfarm.mall.model.vo.MerchantJobVO;
import net.summerfarm.mall.model.vo.SkuJobVO;
import net.summerfarm.mall.service.MerchantJobService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-mall
 * @description 门店任务
 * @date 2023/4/20 19:13:41
 */
@RestController
@RequestMapping("/merchant/job")
public class MerchantJobController {

    @Resource
    private MerchantJobService merchantJobService;
    @Resource
    private DynamicConfig dynamicConfig;


    /**
     * 福利中心-列表
     * @return MerchantCancelVO
     */
    @PostMapping(value = "/query/page")
    @RequiresAuthority
    public CommonResult<PageInfo<MerchantJobVO>> getPage(@RequestBody MerchantJobQueryInput input) {
        return CommonResult.ok(merchantJobService.getPage(input));
    }


    /**
     * 详情
     * @return MerchantCancelVO
     */
    @PostMapping(value = "/query/detail")
    @RequiresAuthority
    public CommonResult<MerchantJobVO> getDetail(@RequestBody MerchantJobQueryInput input) {
        return CommonResult.ok(merchantJobService.getDetail(input));
    }



    /**
     * 福利中心-历史任务列表
     * @return MerchantCancelVO
     */
    @PostMapping(value = "/query/history/page")
    @RequiresAuthority
    public CommonResult<PageInfo<MerchantJobVO>> getHistoryPage(@RequestBody MerchantJobQueryInput input) {
        return CommonResult.ok(merchantJobService.getHistoryPage(input));
    }


    /**
     * 支付完成后，查询该订单可能会达成的任务
     * @return MerchantCancelVO
     */
    @PostMapping(value = "/query/task-list-may-be-achieved")
    @RequiresAuthority
    public CommonResult<List<MerchantJobVO>> queryTaskListMayBeAchieved(@RequestBody MerchantJobQueryInput input) {
        return CommonResult.ok(merchantJobService.queryTaskListMayBeAchieved(input));
    }

    /**
     * 查询sku列表是否存在对应的拓品任务
     * @return MerchantCancelVO
     */
    @PostMapping(value = "/query/task-list-by-skus")
    @RequiresAuthority
    public CommonResult<List<SkuJobVO>> queryTaskListBySkus(@RequestBody MerchantJobQueryInput input) {
        return CommonResult.ok(merchantJobService.queryTaskListBySkus(input));
    }


    /**
     * 查询sku列表是否存在对应的拓品任务(走缓存-用于首页列表、品类列表等场景)
     * @return MerchantCancelVO
     */
    @PostMapping(value = "/query/cache-task-list-by-skus")
    @RequiresAuthority
    public CommonResult<List<SkuJobVO>> queryCacheTaskListBySkus(@RequestBody MerchantJobQueryInput input) {
        return CommonResult.ok(merchantJobService.queryCacheTaskListBySkus(input));
    }


    /**
     * 判断当前用户是否开放了任务
     * @return
     */
    @GetMapping(value = "/query/check-job-enabled")
    @RequiresAuthority
    public CommonResult<Boolean> checkJobEnabledForCurrentUser() {
        List<Integer> openAreaNo = dynamicConfig.getMerchantJobOpenAreaNo();
        if(MerchantSizeEnum.MAJOR_CUSTOMER.getValue().equals(RequestHolder.getSize())) {
            return CommonResult.ok(false);
        }
        return CommonResult.ok(openAreaNo.contains(-1) || openAreaNo.contains(RequestHolder.getMerchantAreaNo()));
    }


    /**
     * 查询当前商户累计的金额
     * @return
     */
    @GetMapping(value = "/query/merchant-job-reward")
    @RequiresAuthority
    public CommonResult<BigDecimal> queryMerchantJobReward() {
        return CommonResult.ok(merchantJobService.queryMerchantJobReward(RequestHolder.getMId()));
    }


    /**
     * 查询当前商户最近一条即将过期的任务
     * @return
     */
    @GetMapping(value = "/query/merchant-job-be-about-to-expire")
    @RequiresAuthority
    public CommonResult<MerchantJobVO> queryMerchantJobBeAboutToExpire() {
        return CommonResult.ok(merchantJobService.queryMerchantJobBeAboutToExpire(RequestHolder.getMId()));
    }


}
