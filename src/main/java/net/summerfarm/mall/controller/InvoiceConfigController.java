package net.summerfarm.mall.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.contexts.Global;
import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.model.domain.InvoiceConfig;
import net.summerfarm.mall.model.input.DefaultFlagInput;
import net.summerfarm.mall.model.vo.invoice.InvoiceConfigVO;
import net.summerfarm.mall.service.EnterpriseInformationService;
import net.summerfarm.mall.service.InvoiceConfigService;
import net.summerfarm.mall.service.MerchantService;
import net.summerfarm.mall.service.TianYanChaService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 财务票据相关控制类
 *
 * @date 2021/11/17 15:56
 */
@Api(tags = "财务税票抬头配置类")
@RestController
@RequiresAuthority
@RequestMapping(value = "/invoice-config")
public class InvoiceConfigController {

    @Resource
    private InvoiceConfigService invoiceConfigService;

    @Resource
    private EnterpriseInformationService enterpriseInformationService;

    @Resource
    private MerchantService merchantService;

    @Resource
    private TianYanChaService tianYanChaService;


    @ApiOperation(value = "获取用户工商信息", httpMethod = "GET")
    @RequestMapping(value = "/business", method = RequestMethod.GET)
    @RequiresAuthority
    public AjaxResult selectBusiness(Long id) {
        return invoiceConfigService.selectBusiness(id);
    }

    @ApiOperation(value = "查询已保存的工商名称", httpMethod = "GET")
    @RequestMapping(value = "/businessAll", method = RequestMethod.GET)
    @RequiresAuthority
    public AjaxResult selectAll(String name) {
        if(org.apache.commons.lang3.StringUtils.isBlank(name)){
            return AjaxResult.getOK();
        }
        return enterpriseInformationService.selectAll(name);
    }

    @ApiOperation(value = "商城提交工商名称", httpMethod = "POST")
    @RequestMapping(value = "/businessSubmitted", method = RequestMethod.POST)
    @RequiresAuthority
    public AjaxResult businessSubmitted(@RequestBody InvoiceConfig invoiceConfig) {
        return invoiceConfigService.businessSubmitted(invoiceConfig);
    }

    @ApiOperation(value = "查询发票抬头是否展示字段", httpMethod = "GET")
    @RequestMapping(value = "/select", method = RequestMethod.GET)
    @RequiresAuthority
    public AjaxResult select(Long mId) {
        return merchantService.select(mId);
    }

    @ApiOperation(value = "天眼查查询信息", httpMethod = "GET")
    @RequestMapping(value = "/selectTianYanCha", method = RequestMethod.GET)
    @RequiresAuthority
    public AjaxResult selectTianYanCha(String name, Long mId) {
        String query = tianYanChaService.restrictedQuery(mId);
        if (!StringUtils.isEmpty(query)) {
            return AjaxResult.getErrorWithMsg(query);
        }
        return invoiceConfigService.selectTianYanCha(name);
    }

    @RequiresAuthority
    @PostMapping(value = "/list-merchant")
    @ApiOperation(value = "发票配置信息", httpMethod = "POST", tags = "财务税票抬头配置类")
    public CommonResult<List<InvoiceConfig>> invoiceConfig() {
        return invoiceConfigService.invoiceConfigList();
    }

    @RequiresAuthority
    @PostMapping(value = "/upsert/default-invoice")
    @ApiOperation(value = "设为默认抬头", httpMethod = "POST", tags = "财务税票抬头配置类")
    public CommonResult<Void> defaultInvoice(@RequestBody DefaultFlagInput input) {
        return invoiceConfigService.defaultInvoiceConfig(input);
    }

    @RequiresAuthority
    @PostMapping(value = "/upsert/del-invoice")
    @ApiOperation(value = "删除默认抬头", httpMethod = "POST", tags = "财务税票抬头配置类")
    public CommonResult<Void> delInvoice(@RequestBody DefaultFlagInput input) {
        return invoiceConfigService.delInvoice(input);
    }
}
