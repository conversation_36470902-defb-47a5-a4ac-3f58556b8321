package net.summerfarm.mall.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.common.util.JsonUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.common.util.validation.groups.Update;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.enums.SubAccountType;
import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.common.sms.SMSSender;
import net.summerfarm.mall.common.sms.SMSSenderFactory;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.constant.MerchantConstants;
import net.summerfarm.mall.constant.validation.group.MerchantInfoComplete;
import net.summerfarm.mall.contexts.SpringContextUtil;
import net.summerfarm.mall.mapper.InvitecodeMapper;
import net.summerfarm.mall.model.SMS;
import net.summerfarm.mall.model.domain.Contact;
import net.summerfarm.mall.model.domain.Invitecode;
import net.summerfarm.mall.model.domain.Merchant;
import net.summerfarm.mall.model.domain.MerchantSubAccount;
import net.summerfarm.mall.model.dto.login.LoginMerchantCacheDTO;
import net.summerfarm.mall.model.dto.login.MerchantLoginDto;
import net.summerfarm.mall.model.dto.merchant.MerchantRebindDTO;
import net.summerfarm.mall.model.dto.wechat.WechatUserPhoneQueryDto;
import net.summerfarm.mall.service.FenceService;
import net.summerfarm.mall.service.MerchantService;
import net.summerfarm.mall.service.MerchantSubAccountService;
import net.summerfarm.mall.wechat.service.WeChatService;
import net.summerfarm.mall.wechat.service.WechatMsgService;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Package: net.summerfarm.customer.controller
 * @Description: 注册
 * @author: <EMAIL>
 * @Date: 2016/9/30
 */
@Api(tags = "注册控制类")
@RestController
@RequestMapping(value = "/register")
public class RegisterController {

    private static final Logger logger = LoggerFactory.getLogger(RegisterController.class);
    @Resource
    private MerchantService merchantService;
    @Resource
    private WechatMsgService wechatMsgService;
    @Resource
    private InvitecodeMapper invitecodeMapper;
    @Resource
    private MerchantSubAccountService subAccountService;
    @Resource
    private SMSSenderFactory smsSenderFactory;
    @Resource
    RedisTemplate<String, String> redisTemplate;
    @Resource
    private WeChatService weChatService;
    @Resource
    private FenceService fenceService;

    /**
     * 上传用户信息
     * TODO 对用户名称及地址做格式校验
     *
     * @param merchant
     * @param bindingResult
     * @return
     */
    @ApiOperation(value = "上传用户信息", httpMethod = "GET")
    @RequestMapping(value = "/VerifyBL")
    public AjaxResult verifyBL(@Validated(value = Add.class) Merchant merchant,
                               BindingResult bindingResult, HttpServletRequest request) {
        LoginMerchantCacheDTO loginMerchantCache = RequestHolder.getLoginMerchantCache();
        if (Objects.isNull(loginMerchantCache)) {
            throw new DefaultServiceException(ResultConstant.LOGIN_FIRST);
        }
        String phone = loginMerchantCache.getPhone();
        if (bindingResult.hasErrors()) {
            return AjaxResult.getError(bindingResult.getFieldError().getDefaultMessage());
        }
        if (StringUtils.isNotBlank(RequestHolder.getUnionid())) {
            merchant.setUnionid(RequestHolder.getUnionid());
        }
        if (StringUtils.isNotBlank(RequestHolder.getOpenId())) {
            merchant.setOpenid(RequestHolder.getOpenId());
        }
        if (StringUtils.isNotBlank(RequestHolder.getMpOpenId())) {
            merchant.setMpOpenid(RequestHolder.getMpOpenId());
        }
        merchant.setCrmRegisterFlag(false);
        return merchantService.verifyBL(merchant, true);
    }

    /**
     * 修改微信号(有原先的手机号)
     *
     * @return
     */
    @ApiOperation(value = "修改微信号(有原先的手机号)", httpMethod = "GET")
    @ApiImplicitParam(name = "mcontact", value = "联系人名称", paramType = "query", required = true)
    @RequestMapping(value = "/updateOpenId")
    public AjaxResult updateOpenId(String mcontact) {
        return merchantService.updateOpenId(mcontact, false);
    }


    /**
     * 修改微信号(没有原先的手机号)
     * @return
     */
    @ApiOperation(value = "修改微信号(没有原先的手机号)", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "mname", value = "原店铺名称", paramType = "query", required = true),
            @ApiImplicitParam(name = "oldPhone", value = "原注册手机号码", paramType = "query", required = true),
            @ApiImplicitParam(name = "newContact", value = "新账号名称", paramType = "query", required = true)
    })
    @RequestMapping(value = "/updateOpenIdNot")
    public AjaxResult updateOpenIdNot(String mname, String oldPhone, String newContact) {
        String newPhone = RequestHolder.getLoginMerchantCache().getPhone();
        String openid = RequestHolder.getOpenId();
        String unionid = RequestHolder.getUnionid();
        if (StringUtils.isBlank(mname, oldPhone, newContact, newPhone, openid)) {
            return AjaxResult.getError(ResultConstant.DEFAULT_FAILED, "审核信息请填写完整");
        }
        return merchantService.updateOpenIdNot(mname, openid, unionid, oldPhone, newContact, newPhone, false);
    }



    /**
     * POP重新绑定已有门店(有原先的手机号)
     * @return
     */
    @RequestMapping(value = "/reBind", method = RequestMethod.POST)
    public AjaxResult reBind(@RequestBody MerchantRebindDTO merchantRebindDTO) {
        String newContact = merchantRebindDTO.getNewContact();
        return merchantService.updateOpenId(newContact, true);

    }


    /**
     *  POP重新绑定已有门店(没有原先的手机号)
     * @return
     */
    @RequestMapping(value = "/reBindWithoutOldPhone", method = RequestMethod.POST)
    public AjaxResult reBindWithOutOldPhone(@RequestBody MerchantRebindDTO merchantRebindDTO) {
        String newPhone = RequestHolder.getLoginMerchantCache().getPhone();
        String openid = RequestHolder.getOpenId();
        String unionid = RequestHolder.getUnionid();
        String mname = merchantRebindDTO.getMname();
        String oldPhone = merchantRebindDTO.getOldPhone();
        String newContact = merchantRebindDTO.getNewContact();
        if (StringUtils.isBlank(mname, oldPhone, newContact, newPhone, openid)) {
            logger.error("请求参数缺失：merchantRebindDTO：{}, loginMerchantCacheDTO:{}", JSON.toJSONString(merchantRebindDTO), JSON.toJSONString(RequestHolder.getLoginMerchantCache()));
            return AjaxResult.getError(ResultConstant.DEFAULT_FAILED, "审核信息请填写完整");
        }
        return merchantService.updateOpenIdNot(mname, openid, unionid, oldPhone, newContact, newPhone, true);
    }



    /**
     * 校验验证码
     *
     * @param yzm
     * @return
     */
    @ApiOperation(value = "校验验证码", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "yzm", value = "验证码：login", paramType = "query", required = true),
            @ApiImplicitParam(name = "type", value = "验证码类型:login", paramType = "query", defaultValue = "login", required = true)
    })
    @RequestMapping(value = "/VerifyYZM")
    public AjaxResult verifyYZM(String yzm, String type,
                                HttpServletRequest request) {
        String token = request.getHeader("token");
        return merchantService.verifyYZM(yzm, type, token);
    }


    /**
     * 获取验证码v2
     *
     * @param mobile 手机号
     * @return 操作结果
     */
    @ApiOperation(value = "获取验证码", httpMethod = "GET")
    @ApiImplicitParam(name = "mobile", value = "手机号", paramType = "query", required = true)
    @RequestMapping(value = "/VerificationCodeV2")
    public AjaxResult verificationCodeV2(String mobile, boolean popMerchant, HttpServletRequest request) {
        //子账号不可更换账号绑定
        MerchantSubAccount account = subAccountService.selectOne("phone", mobile);
        if (account != null && SubAccountType.STAFF.ordinal() == account.getType()) {
            return AjaxResult.getErrorWithMsg("您输入的号码为子账号，不可重新绑定");
        }

        String code = SMSSender.buildRandom(6) + "";
        //此处必须先发短信，确保短信发送成功的才能去改内部中code的值，否则会出现短信内存验证码不一致情况
        Long sceneId = popMerchant ? MerchantConstants.POP_SMS_SCENE_ID : MerchantConstants.XIAN_MU_SMS_SCENE_ID;
        SMS sms = new SMS();
        sms.setPhone(mobile);
        sms.setSceneId(sceneId);
        sms.setArgs(Arrays.asList(code));
        boolean success = smsSenderFactory.getSMSSender().sendSMS(sms);
        Map<String, Object> map = new HashMap<>();
        map.put("code", success ? 1 : 0);
        map.put("msg", success ? "成功" : "失败");

        // 非正式环境返回验证码
        if (!SpringContextUtil.isProduct()) {
            map.put("code", "localhost");
            map.put("msg", code);
        }

        LoginMerchantCacheDTO loginMerchantCache = RequestHolder.getLoginMerchantCache();
        if (Objects.isNull(loginMerchantCache)) {
            throw new DefaultServiceException(ResultConstant.LOGIN_FIRST);
        }
        loginMerchantCache.setPhone(mobile);
        loginMerchantCache.setCode(code);
        String token = request.getHeader("token");
        logger.info("验证码获取,当前token:{}", token);
        RequestHolder.setLoginMerchantCache(token, loginMerchantCache);

        logger.info("mobile:{},code:{},success:{}", mobile, code, map);
        return AjaxResult.getOK(map);
    }


    /**
     * 获取验证码
     *
     * @param mobile 手机号
     * @return 操作结果
     */
    @ApiOperation(value = "获取验证码", httpMethod = "GET")
    @ApiImplicitParam(name = "mobile", value = "手机号", paramType = "query", required = true)
    @RequestMapping(value = "/VerificationCode")
    public AjaxResult verificationCode(String mobile, HttpServletRequest request) {
        //子账号不可更换账号绑定
        MerchantSubAccount account = subAccountService.selectOne("phone", mobile);
        if (account != null && SubAccountType.STAFF.ordinal() == account.getType()) {
            return AjaxResult.getErrorWithMsg("您输入的号码为子账号，不可重新绑定");
        }

        String code = SMSSender.buildRandom(6) + "";
        //此处必须先发短信，确保短信发送成功的才能去改内部中code的值，否则会出现短信内存验证码不一致情况

        SMS sms = new SMS();
        sms.setPhone(mobile);
        sms.setSceneId(2L);
        sms.setArgs(Arrays.asList(code));
        boolean success = smsSenderFactory.getSMSSender().sendSMS(sms);
        Map<String, Object> map = new HashMap<>();
        map.put("code", success ? 1 : 0);
        map.put("msg", success ? "成功" : "失败");

        // 非正式环境返回验证码
        if (!SpringContextUtil.isProduct()) {
            map.put("code", "localhost");
            map.put("msg", code);
        }

        LoginMerchantCacheDTO loginMerchantCache = RequestHolder.getLoginMerchantCache();
        if (Objects.isNull(loginMerchantCache)) {
            throw new DefaultServiceException(ResultConstant.LOGIN_FIRST);
        }
        loginMerchantCache.setPhone(mobile);
        loginMerchantCache.setCode(code);
        String token = request.getHeader("token");
        logger.info("验证码获取,当前token:{}", token);
        RequestHolder.setLoginMerchantCache(token, loginMerchantCache);

        logger.info("mobile:{},code:{},success:{}", mobile, code, map);
        return AjaxResult.getOK(map);
    }

    /**
     * 校验用户手机号是否可用
     *
     * @param phone
     * @return
     */
    @ApiOperation(value = "校验用户手机号是否可用", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "phone", value = "手机号", paramType = "query", required = true),
    })
    @RequestMapping(value = "/Phone")
    public AjaxResult phone(String phone) {
        logger.info("phone：{}, redis缓存信息：{}, ThreadLocal 缓存信息：{}", phone, JSON.toJSONString(RequestHolder.getLoginMerchantCache()), JsonUtil.toJson(RequestHolder.getMerchantSubject()));
        if (StringUtils.isMobile(phone)) {
            MerchantSubAccount subAccount = subAccountService.selectOneNew("phone", phone);
            Merchant merchant = merchantService.preRegister(phone);
            if (Objects.nonNull(merchant)) {
                logger.info("预注册用户");
                return AjaxResult.getOK(merchant);
            }
            if (subAccount == null) {
                logger.info("手机号校验通过");
                this.setPhoneToCache(phone);
                return AjaxResult.getOK();
            }
            if (RequestHolder.getLoginMerchantCache() == null) {
                logger.info("缓存失效");
                throw new DefaultServiceException(0, ResultConstant.LOGIN_FIRST);
            }
            // 这一段不知道是兼容啥逻辑的 --2024-09-14
            if (Objects.equals(RequestHolder.getOpenId(),subAccount.getOpenid()) || Objects.equals(RequestHolder.getMpOpenId(),subAccount.getMpOpenid())) {
                logger.info("当前openid和数据库一致");
                return AjaxResult.getOK();
            } else {
                logger.info("当前手机号已注册.phone:{}", phone);
                return AjaxResult.getErrorWithMsg("当前手机号已注册");
            }

        }
        logger.info("手机号格式校验不通过，phone:{}", phone);
        return AjaxResult.getError();
    }


    private void setPhoneToCache(String phone){
        LoginMerchantCacheDTO loginMerchantCache = RequestHolder.getLoginMerchantCache();
        HttpServletRequest request = RequestHolder.getRequest();
        if (Objects.isNull(loginMerchantCache)) {
            logger.info("loginMerchantCache not exist！");
            throw new DefaultServiceException(ResultConstant.LOGIN_FIRST);
        }
        loginMerchantCache.setPhone(phone);
        String token = request.getHeader("token");
        logger.info("设置手机号缓存,当前token:{}", token);
        RequestHolder.setLoginMerchantCache(token, loginMerchantCache);
    }

    /**
     * 校验邀请码
     *
     * @param invitecode
     * @return
     */
    @ApiOperation(value = "校验邀请码", httpMethod = "GET")
    @ApiImplicitParam(name = "invitecode", value = "邀请码", paramType = "query", required = true)
    @RequestMapping(value = "/InviteCode")
    public AjaxResult inviteCode(String invitecode) {
        invitecode = invitecode.trim();
        if (StringUtils.isMobile(invitecode)) {
            return AjaxResult.getOK();
        }
        Invitecode num = invitecodeMapper.select(invitecode);
        if (num != null) {
            return AjaxResult.getOK();
        }
        return AjaxResult.getErrorWithMsg("请输入正确的邀请码");
    }

    @ApiOperation(value = "移除注册信息", httpMethod = "DELETE")
    @RequestMapping(value = "/del")
    public AjaxResult del() {
        return merchantService.del();
    }


    @ApiOperation(value = "认领店铺", httpMethod = "POST")
    @RequestMapping(value = "/claim/{merchantLeadsId}", method = RequestMethod.POST)
    public AjaxResult claimMerchant(@PathVariable String merchantLeadsId,
                                    String yzm, String type,
                                    HttpServletRequest request) {
        String token = request.getHeader("token");
        return merchantService.claimMerchant(merchantLeadsId, yzm, token, type);
    }

    @ApiOperation(value = "茶百道账号导入店铺注册", httpMethod = "GET")
    @GetMapping("/save/cbd")
    public AjaxResult registerToCBD(Merchant merchant) {
        return merchantService.registerToCBD(merchant);
    }



    /**
     * 完善信息
     * @return
     */
    @RequiresAuthority
    @RequestMapping(value = {"/complete-information"},method = RequestMethod.POST)
    public CommonResult<Void> completeInformation(@RequestBody @Validated(value = MerchantInfoComplete.class) Merchant merchant) {
        return merchantService.completeInformation(merchant);
    }


    /**
     * 审核不通过，返回待提交核验
     * @return
     */
    @RequestMapping(value = {"/go-back-pending-submit"},method = RequestMethod.POST)
    public CommonResult<Void> goBackPendingSubmit(@RequestBody Merchant merchant) {
        return merchantService.goBackPendingSubmit(merchant.getmId());
    }


    /**
     * 通过微信查询用户的手机号
     * @return
     */
    @RequestMapping(value = {"/query/wechat-phone"},method = RequestMethod.POST)
    public CommonResult<String> queryWechatPhone(@RequestBody WechatUserPhoneQueryDto dto) {
        return weChatService.queryWechatPhone(dto.getCode(), dto.getChannelCode());
    }


    /**
     * 校验地址是否可用
     *
     * @return
     */
    @RequestMapping(value = "/checkCityAreaFence", method = RequestMethod.POST)
    public AjaxResult checkCityAreaFence(@RequestBody Contact contact) {
        return fenceService.checkCityAreaFence(contact);
    }
    
}
