package net.summerfarm.mall.controller;

import java.util.List;
import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.model.vo.FrontCategoryVO;
import net.summerfarm.mall.service.FrontCategoryService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 前台类目
 * <AUTHOR>
 * @version 1.0.0
 * @date 2020-06-01
 * @description
 */
@RestController
@RequestMapping("/front-category")
public class FrontCategoryController {
    @Resource
    private FrontCategoryService frontCategoryService;

    @GetMapping
    @RequiresAuthority
    public CommonResult<List<FrontCategoryVO>> selectFrontCategory() {
        return frontCategoryService.selectFrontCategory();
    }

    /**
     * 省心送前台类目
     * @return
     */
    @RequiresAuthority
    @PostMapping(value = "/query/timing-delivery")
    public CommonResult<List<FrontCategoryVO>> listTimingFrontCategory() {
        return frontCategoryService.listTimingFrontCategory();
    }
}
