package net.summerfarm.mall.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.enums.RedissonLockKey;
import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.common.util.DateUtils;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.model.domain.DeliveryPlan;
import net.summerfarm.mall.model.input.TimeFrameInput;
import net.summerfarm.mall.model.vo.*;
import net.summerfarm.mall.model.vo.deliveryplan.DeliveryPlanOperationVO;
import net.summerfarm.mall.model.vo.deliveryplan.DeliveryPlanQueryVO;
import net.summerfarm.mall.service.DeliveryPlanService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import org.jdom.JDOMException;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Package: net.summerfarm.customer.controller
 * @Description: 配送计划管理
 * @author: <EMAIL>
 * @Date: 2016/12/21
 */
@Api(tags = "配送计划管理控制类")
@Slf4j
@RequiresAuthority
@RestController
@RequestMapping(value = "/delivery-plan")
public class DeliveryPlanController {
    @Resource
    private DeliveryPlanService deliveryPlanService;
    @Resource
    private RedissonClient redissonClient;

    /**
     * 查询未配送的配送计划
     * @param contactId
     * @return
     */
    @ApiOperation(value = "查询未配送的配送计划",httpMethod = "GET",response = TimingOrderVO.class)
    @ApiImplicitParam(name = "contactId",value = "地址编号",paramType = "query",required = true)
    @RequestMapping(value = "/wait_delivery", method = RequestMethod.GET)
    public AjaxResult selectWait(Long contactId) {
        return deliveryPlanService.selectWait(contactId);
    }
    /**
     * 查询对应订单的配送计划
     * @param orderNo
     * @return
     */
    @ApiOperation(value = "查询对应订单的配送计划",httpMethod = "GET",response = TimingOrderVO.class)
    @ApiImplicitParam(name = "orderNo",value = "订单编号",paramType = "path",required = true)
    @RequestMapping(value = "/{orderNo}", method = RequestMethod.GET)
    public AjaxResult<TimingOrderVO> select(@PathVariable String orderNo) {
        return deliveryPlanService.select(orderNo);
    }

    @PostMapping(value = "/query/time-frame")
    public CommonResult<TimeFrameVO> timeFrameNew(@Valid @RequestBody TimeFrameInput input){
        return CommonResult.ok(deliveryPlanService.timeFrameNew(input));
    }

    @ApiOperation(value = "提交配送评价",httpMethod = "POST")
//    @RequiresAuthority
    @RequestMapping(value = "/evaluation/save", method = RequestMethod.POST)
    public CommonResult evaluationSave(@Validated(value = Add.class) @RequestBody DeliveryEvaluationVO deliveryEvaluationVO) throws IOException, JDOMException {
        return deliveryPlanService.evaluationSave(deliveryEvaluationVO);
    }

    @ApiOperation(value = "配送评价详情",httpMethod = "POST",response = DeliveryEvaluationVO.class)
    @ApiImplicitParam(name = "deliveryPlanId",value = "配送计划id",paramType = "path",required = true)
    @RequiresAuthority
    @RequestMapping(value = "/evaluation/detail", method = RequestMethod.POST)
    public CommonResult<DeliveryEvaluationVO> evaluationDetail(@RequestBody  DeliveryEvaluationVO deliveryEvaluationVO) throws IOException, JDOMException {
        return deliveryPlanService.evaluationDetail(deliveryEvaluationVO.getDeliveryPlanId());
    }

    /**
     * 月维度查询用户配送计划列表
     * @return
     */
    @RequiresAuthority
    @PostMapping(value = "/query/delivery-plan-monthly")
    public CommonResult deliveryPlanMonthly(@RequestBody DeliveryPlanQueryVO deliveryPlanQueryVO) {
        return deliveryPlanService.selectDeliveryPlanMonthly(RequestHolder.getMId(),deliveryPlanQueryVO.getQueryMonth());
    }

    /**
     * 批量操作省心送配送计划
     * @return
     */
    @RequestMapping(value = "/upsert/batch-operation", method = RequestMethod.POST)
    public CommonResult batchOperation(@RequestBody DeliveryPlanOperationVO deliveryPlanOperationVO) {
        RLock redissonLock = redissonClient.getLock(RedissonLockKey.DELIVERY_PLAN + RequestHolder.getMId());
        try {
            boolean flag = redissonLock.tryLock(0L, 30L, TimeUnit.SECONDS);
            if (!flag) {
                return CommonResult.fail(ResultStatusEnum.BAD_REQUEST);
            }

            return deliveryPlanService.batchOperation(deliveryPlanOperationVO, RequestHolder.getMId(), RequestHolder.getMerchantArea());
        } catch (InterruptedException e) {
            log.error("锁获取异常", e);
            throw new BizException("您的操作过于频繁，请稍后再试！");
        } finally {
            if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()){
                redissonLock.unlock();
            }
        }
    }

    /**
     * 单个配送计划新增
     * @return
     */
    @RequestMapping(value = "/insert", method = RequestMethod.POST)
    public CommonResult insert(@RequestBody @Validated DeliveryPlanOperationVO deliveryPlanOperationVO) {
        RLock redissonLock = redissonClient.getLock(RedissonLockKey.DELIVERY_PLAN + deliveryPlanOperationVO.getOrderNo());
        try {
            boolean flag = redissonLock.tryLock(0L, 30L, TimeUnit.SECONDS);
            if (!flag) {
                return CommonResult.fail(ResultStatusEnum.BAD_REQUEST);
            }

            return deliveryPlanService.insert(deliveryPlanOperationVO, RequestHolder.getMId(), RequestHolder.getMerchantArea(), deliveryPlanOperationVO.getOrderNo());
        } catch (InterruptedException e) {
            log.error("锁获取异常", e);
            throw new BizException("您的操作过于频繁，请稍后再试！");
        } finally {
            if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()){
                redissonLock.unlock();
            }
        }
    }

    /**
     * 单个修改配送计划
     * @return
     */
    @RequestMapping(value = "/upsert", method = RequestMethod.POST)
    public CommonResult upsert(@RequestBody @Validated DeliveryPlanOperationVO deliveryPlanOperationVO) {
        RLock redissonLock = redissonClient.getLock(RedissonLockKey.DELIVERY_PLAN + deliveryPlanOperationVO.getOrderNo());
        try {
            boolean flag = redissonLock.tryLock(0L, 30L, TimeUnit.SECONDS);
            if (!flag) {
                return CommonResult.fail(ResultStatusEnum.BAD_REQUEST);
            }

            return deliveryPlanService.upsert(deliveryPlanOperationVO, RequestHolder.getMId(), RequestHolder.getMerchantArea(), deliveryPlanOperationVO.getOrderNo());
        } catch (InterruptedException e) {
            log.error("锁获取异常", e);
            throw new BizException("您的操作过于频繁，请稍后再试！");
        } finally {
            if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()){
                redissonLock.unlock();
            }
        }
    }

    /**
     * 单个配送计划删除
     * @return
     */
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public CommonResult delete(@RequestBody @Validated DeliveryPlanOperationVO deliveryPlanOperationVO) {
        RLock redissonLock = redissonClient.getLock(RedissonLockKey.DELIVERY_PLAN + deliveryPlanOperationVO.getOrderNo());
        try {
            boolean flag = redissonLock.tryLock(0L, 30L, TimeUnit.SECONDS);
            if (!flag) {
                return CommonResult.fail(ResultStatusEnum.BAD_REQUEST);
            }

            return deliveryPlanService.delete(deliveryPlanOperationVO, RequestHolder.getMId(), RequestHolder.getMerchantArea(), deliveryPlanOperationVO.getOrderNo());
        } catch (InterruptedException e) {
            log.error("锁获取异常", e);
            throw new BizException("您的操作过于频繁，请稍后再试！");
        } finally {
            if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()){
                redissonLock.unlock();
            }
        }
    }


}
