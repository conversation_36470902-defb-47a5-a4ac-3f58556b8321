package net.summerfarm.mall.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.model.vo.invoice.*;
import net.summerfarm.mall.service.FinancialInvoiceService;
import net.summerfarm.manage.client.invoice.dto.req.FinancialInvoiceQueryReq;
import net.summerfarm.manage.client.invoice.dto.req.FinancialInvoiceReq;
import net.summerfarm.manage.client.invoice.dto.req.FinancialOrderQueryReq;
import net.summerfarm.manage.client.invoice.dto.res.CalculateInvoiceAmountVO;
import net.summerfarm.manage.client.invoice.dto.res.FinancialInvoiceDetailRes;
import net.summerfarm.manage.client.invoice.dto.res.FinancialOrderDTO;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 财务票据控制类
 *
 * <AUTHOR>
 * @date 2023/2/10 17:28
 */
@Api(tags = "财务税票类")
@RestController
@RequestMapping(value = "/financial-invoice")
public class FinancialInvoiceController {
	@Resource
	FinancialInvoiceService financialInvoiceService;

	/**
	 * 待开票订单列表
	 */
	@PostMapping(value = "/query/orderList")
	public CommonResult<PageInfo<FinancialOrderDTO>> selectOrderList(@RequestBody FinancialOrderQueryReq req) {
		return financialInvoiceService.selectOrderList(req);
	}

	/**
	 * 开票列表
	 */
	@PostMapping(value = "/invoiceQuery/{pageIndex}/{pageSize}")
	@RequiresAuthority()
	public CommonResult<PageInfo<FinancialInvoiceVO>> invoiceQuery(@PathVariable Integer pageIndex, @PathVariable Integer pageSize) {
		return financialInvoiceService.invoiceList(pageIndex,pageSize);
	}

	/**
	 * 申请开票
	 */
	@PostMapping(value = "/invoiceSave")
	@RequiresAuthority()
	public CommonResult<Long> invoiceSave(@RequestBody FinancialInvoiceReq req) {
		if (ObjectUtil.equal(req.getInvoiceType(), 0)&&ObjectUtil.equal(req.getInvoiceType(),1)){
			return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,"入参无发票类型，请刷新页面，无效请联系管理员解决");
		}
		if (CollectionUtil.isEmpty(req.getFinancialInvoiceOrderDTOList())){
			return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,"未选中任何订单，请勾选后提交,无效请联系管理员解决");
		}

		return financialInvoiceService.invoiceSave(req);
	}

	/**
	 * 开票订单详情
	 */
	@PostMapping(value = "/query/invoice-detail")
	@RequiresAuthority()
	public CommonResult<FinancialInvoiceDetailRes> invoiceDetail(@RequestBody FinancialInvoiceReq req) {
		return financialInvoiceService.invoiceDetail(req);
	}

	/**
	 * 根据开票id获取对应的订单列表
	 */
	@PostMapping(value = "/query/invoice-order-list")
	@RequiresAuthority()
	public CommonResult<List<FinancialOrderDTO>> invoiceOrderList(@RequestBody FinancialOrderQueryReq req) {
		return financialInvoiceService.invoiceOrderList(req);
	}

	/**
	 * 计算开票金额
	 */
	@PostMapping(value = "/query/calculate-invoice-amount")
	@RequiresAuthority()
	public CommonResult<CalculateInvoiceAmountVO> calculateInvoiceAmount(@RequestBody FinancialInvoiceReq req) {
		return financialInvoiceService.calculateInvoiceAmount(req);
	}

	/**
	 * 作废开票
	 */
	@PostMapping(value = "/cancel")
	@RequiresAuthority()
	public CommonResult<Long> cancel(@RequestBody FinancialInvoiceQueryReq req) {
		return financialInvoiceService.cancel(req);
	}
}
