package net.summerfarm.mall.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.model.domain.DiscountCard;
import net.summerfarm.mall.model.domain.Orders;
import net.summerfarm.mall.model.vo.DiscountCardToMerchantVO;
import net.summerfarm.mall.service.DiscountCardService;
import net.summerfarm.mall.service.OrderService;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2020-05-15
 * @description
 */
@Api(tags = "商品优惠卡接口")
@RequiresAuthority
@RestController
@RequestMapping(value = "/discount-card")
public class DiscountCardController {
    @Resource
    private DiscountCardService discountCardService;
    @Resource
    private OrderService orderService;

    @ApiOperation(value = "购买优惠卡", httpMethod = "GET", response = DiscountCardToMerchantVO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "discountCardId", value = "奶油卡id", paramType = "query", required = true),
    })
    @RequestMapping(value = "/buy", method = RequestMethod.POST)
    public AjaxResult buyDiscountCard(Integer discountCardId) {
        Orders orders = orderService.createDiscountCardOrder(discountCardId);
        return AjaxResult.getOK(orders);
    }

    @ApiOperation(value = "查询用户自己优惠卡", httpMethod = "GET", response = DiscountCardToMerchantVO.class)
    @RequestMapping(value = "/merchant/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult selectMerchantDiscountCard(@PathVariable int pageIndex, @PathVariable int pageSize, Integer status) {
        Long mId = RequestHolder.getMId();
        return discountCardService.selectDiscountCardByCondition(pageIndex,pageSize,status,mId);
    }

    @ApiOperation(value = "查询可以购买卡信息 ", httpMethod = "GET", response = DiscountCard.class)
    @RequestMapping(value = "/allCard",method = RequestMethod.GET)
    public AjaxResult selectDiscountCard(){
        return AjaxResult.getOK(discountCardService.selectDiscountCard());
    }

}
