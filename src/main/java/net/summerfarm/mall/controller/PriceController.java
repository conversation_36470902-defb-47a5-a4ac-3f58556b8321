package net.summerfarm.mall.controller;

import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.model.vo.order.PlaceOrderVO;
import net.summerfarm.mall.model.vo.price.TakeActualPriceVO;
import net.summerfarm.mall.service.OrderCalcService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController()
@RequestMapping("/price")
public class PriceController {

    @Resource
    private OrderCalcService orderCalcService;


    /**
     * 获取到手价-非购物车勾选不需要均摊
     * @return
     */
    @RequiresAuthority
    @PostMapping(value = "/query/take-actual-price")
    public CommonResult<List<TakeActualPriceVO>> takeActualPrice(@RequestBody PlaceOrderVO placeOrderVO) {
        //获取到手价明细标记，此标记为true代表获取明细并且不均摊优惠
        placeOrderVO.setTakePriceFlag(Boolean.TRUE);
        return CommonResult.ok(orderCalcService.takePriceHandler(placeOrderVO));
    }

    /**
     * 获取到手价-需要均摊
     * @return
     */
    @RequiresAuthority
    @PostMapping(value = "/query/take-allocation-price")
    public CommonResult<List<TakeActualPriceVO>> takeAllocationPrice(@RequestBody PlaceOrderVO placeOrderVO) {
        //获取到手价明细标记，此标记为FALSE代表不获取明细并且均摊优惠
        placeOrderVO.setTakePriceFlag(Boolean.FALSE);
        return CommonResult.ok(orderCalcService.takePriceHandler(placeOrderVO));
    }

}
