package net.summerfarm.mall.controller.health;

import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.model.domain.Area;
import net.summerfarm.mall.service.AreaService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用来检测应用是否已经初始化完成（能够请求数据库且写入数据到Redis）
 */
@RestController
@RequestMapping("/appHealth")
@Slf4j
public class AppHealthController {

    @Resource
    private AreaService areaService;

    @Resource
    private HttpServletResponse response;

    @GetMapping("/isAvailable")
    @ApiOperation(value = "检测系统是否就绪-是否可以放流量进来", httpMethod = "GET")
    public String isAvailable(@RequestParam(required = false, defaultValue = "1001") int areaNo) {
        try {
            Area area = areaService.selectAreaWithCache(areaNo);
            log.info("查询到的area:{}, area_no:{}", area, areaNo);
            List<Area> updatedAreaList = areaService.getUpdatedAreaList(TimeUnit.HOURS.toMillis(12L));
            log.info("查询到的updatedAreaList:{}", updatedAreaList);
            if (CollectionUtils.isNotEmpty(updatedAreaList)) {
                updatedAreaList.stream().forEach(areaInList -> {
                    areaService.updateAreaCache(areaInList.getAreaNo());
                });
            }
            return "OK";
        } catch (Exception e) {
            log.error("监控检测失败:{}", areaNo, e);
            response.setStatus(HttpServletResponse.SC_SERVICE_UNAVAILABLE);
            return e.getMessage();
        }
    }
}
