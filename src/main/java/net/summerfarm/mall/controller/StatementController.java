package net.summerfarm.mall.controller;

import io.swagger.annotations.Api;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.enums.PayChannelEnum;
import net.summerfarm.mall.common.util.IPUtil;
import net.summerfarm.mall.common.util.IpWhiteListUtil;
import net.summerfarm.mall.payments.request.PaymentRequestService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

@Api(tags = "账单类")
@RestController
public class StatementController {
    private static final Logger logger = LoggerFactory.getLogger(StatementController.class);

    @Resource
    private PaymentRequestService cmbPaymentRequestService;
    @Resource
    private PaymentRequestService weixPaymentRequestService;

    @GetMapping("/statement")
    public AjaxResult getStatement(@RequestParam String payType, @RequestParam Integer accountId, @RequestParam String date,
                                   HttpServletRequest request)throws UnsupportedEncodingException {
        String ip = IPUtil.getIpAddress(request);
        payType = URLDecoder.decode(payType, "UTF8");
        if(IpWhiteListUtil.check(ip)) {
            if (payType.equals(PayChannelEnum.WEIX.getDesc())) {
                return AjaxResult.getOK(weixPaymentRequestService.queryStatement(accountId, date));
            } else if (payType.equals(PayChannelEnum.CMB.getDesc())) {
                return AjaxResult.getOK(cmbPaymentRequestService.queryStatement(accountId, date));
            } else {
                return AjaxResult.getError();
            }
        } else {
            logger.error("ip is not in the whitelist");
            return AjaxResult.getError();
        }
    }
}
