package net.summerfarm.mall.controller;

import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.model.input.payment.PaymentInput;
import net.summerfarm.mall.model.vo.payment.PayResultVO;
import net.summerfarm.mall.payments.model.PaymentRequest;
import net.summerfarm.mall.payments.model.PaymentResponse;
import net.summerfarm.mall.payments.request.PaymentHandler;
import net.summerfarm.mall.payments.service.PaymentUnionService;
import net.summerfarm.payment.routing.model.vo.PaymentUsableChannelVO;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 支付相关接口
 *
 * <AUTHOR>
 * @date 2023/10/10  11:25
 */
@RestController
@RequiresAuthority
@RequestMapping(value = "/payment")
public class PaymentController {

    @Resource
    private PaymentHandler paymentHandler;
    @Resource
    private PaymentUnionService paymentUnionService;

    /**
     * 支付接口
     *
     * @param input 支付请求参数
     * @return 支付响应结果
     */
    @PostMapping(value = "/pay")
    public CommonResult<PayResultVO> pay(PaymentInput input) {
        return paymentHandler.payV2(input);
    }

    /**
     * 支付接口
     * 用于统一路由的支付接口
     * @param request 支付请求参数
     * @return 支付响应结果
     */
    @PostMapping(value = "/union/pay")
    public CommonResult<PaymentResponse> unionPay(@RequestBody PaymentRequest request) {
        return paymentUnionService.unionPay(request);
    }

    /**
     * 查询可用支付渠道
     * @return
     */
    @PostMapping(value = "/query/usable-payment-channels")
    public CommonResult<PaymentUsableChannelVO> queryUsablePaymentChannels() {
        return paymentUnionService.queryUsablePaymentChannels();
    }

    /**
     * 查询默认支付方式
     * @return
     */
    @RequiresAuthority
    @PostMapping("/default-method")
    public CommonResult<Integer> getDefaultPaymentMethod() {
        return CommonResult.ok(paymentUnionService.getDefaultPaymentMethod());
    }
}
