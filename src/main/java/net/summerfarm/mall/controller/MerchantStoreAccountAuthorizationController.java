package net.summerfarm.mall.controller;

import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.facade.usercenter.MerchantQueryFacade;
import net.summerfarm.mall.model.dto.merchant.MerchantStoreAccountAuthorizationDTO;
import net.summerfarm.mall.model.dto.merchant.MerchantStoreAccountBindAuthorizationDTO;
import net.summerfarm.mall.service.MerchantStoreAccountAuthorizationRecordService;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAndExtendResp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @description: 绑定/解绑授权(b2b)
 * @author: George
 * @date: 2025-01-13
 **/
@RestController
@RequestMapping("/merchant/store/account/authorization")
public class MerchantStoreAccountAuthorizationController {

    private static final Logger log = LoggerFactory.getLogger(MerchantStoreAccountAuthorizationController.class);
    @Resource
    private MerchantStoreAccountAuthorizationRecordService merchantStoreAccountAuthorizationRecordService;
    @Resource
    private MerchantQueryFacade merchantQueryFacade;

    /**
     * 绑定/解绑授权
     *
     * @param dto
     * @return
     */
    @PostMapping("/bind")
    @RequiresAuthority
    public CommonResult<Boolean> bind(@RequestBody MerchantStoreAccountBindAuthorizationDTO dto) {
        return CommonResult.ok(merchantStoreAccountAuthorizationRecordService.bind(dto));
    }

    /**
     * 查询授权状态
     *
     * @return
     */
    @PostMapping("/query-status")
    @RequiresAuthority
    public CommonResult<MerchantStoreAccountAuthorizationDTO> queryAuthorizationStatus() {
        return CommonResult.ok(merchantStoreAccountAuthorizationRecordService.queryStatus(RequestHolder.getMpOpenId()));
    }

    /**
     * 同步授权状态
     * 可能存在解绑行为 此接口用于同步状态
     *
     * @return
     */
    @PostMapping("/sync-status")
    @RequiresAuthority
    public CommonResult syncAuthorizationStatus() {
        merchantStoreAccountAuthorizationRecordService.syncAuthorizationStatus();
        return CommonResult.ok();
    }


    /**
     * 预录入接口
     */
    @PostMapping("/pre-entry")
    @RequiresAuthority
    public CommonResult<Boolean> preEntry(@RequestParam(required = false) Long accountId) {
        try {
            if (accountId == null) {
                accountId = RequestHolder.getAccountId();
                log.info("为当前登录的用户进行预录入");
            } else {
                MerchantStoreAndExtendResp merchantStoreAndExtendResp = merchantQueryFacade.getMerchantExtendsByMid(RequestHolder.getMId());
                if (null == merchantStoreAndExtendResp || merchantStoreAndExtendResp.getMockLoginFlag() == null || 1 != merchantStoreAndExtendResp.getMockLoginFlag()) {
                    return CommonResult.fail(ResultStatusEnum.UNAUTHORIZED, "当前登录的用户不支持模拟登录，不允许为其他accountId预录入");
                }
                log.info("{}正在为其他accountId:{}预录入", RequestHolder.getMerchantSubject().getPhone(), accountId);
            }
            boolean result = merchantStoreAccountAuthorizationRecordService.createB2bStore(accountId);
            return CommonResult.ok(result);
        } catch (Exception e) {
            log.error("预录入接口异常", e);
        }
        return CommonResult.ok(false);
    }

}
