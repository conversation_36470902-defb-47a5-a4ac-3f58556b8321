package net.summerfarm.mall.controller;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.model.input.LuckDrawActivityInsertReq;
import net.summerfarm.mall.model.input.LuckDrawActivityReq;
import net.summerfarm.mall.model.vo.LuckDrawActivityVO;
import net.summerfarm.mall.service.LuckDrawActivityService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-mall
 * @description 红包雨/每日抽奖活动
 * @date 2023/5/12 15:35:41
 */
@RestController
@RequestMapping("/luck/draw/activity")
@Slf4j
public class LuckDrawActivityController {

    @Resource
    private LuckDrawActivityService luckDrawActivityService;

    @Resource
    private RedissonClient redissonClient;

    /**
     * 开始抽奖
     * @param luckDrawActivityInsertReq
     * @return LuckDrawActivityVO
     */
    @PostMapping(value = "/upsert/start")
    @RequiresAuthority
    public CommonResult<LuckDrawActivityVO> start(@RequestBody @Validated LuckDrawActivityInsertReq luckDrawActivityInsertReq) {
        //幂等性处理
        RLock redissonLock = redissonClient.getLock("lock:luck-draw:" + luckDrawActivityInsertReq.getActivityId() + "-" + RequestHolder.getMId());
        try {
            boolean tryLock = redissonLock.tryLock(0L, 10L, TimeUnit.SECONDS);
            if (!tryLock) {
                throw new BizException("您的操作过于频繁，请稍后再试！");
            }
            return CommonResult.ok(luckDrawActivityService.start(luckDrawActivityInsertReq));
        } catch (InterruptedException e) {
            log.error("LuckDrawActivityController[]start[]tryLock[]error[]cause:{}", e.getMessage());
            throw new DefaultServiceException(1, "获取锁失败，请重试！");
        } finally {
            if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()){
                redissonLock.unlock();
            }
        }
    }

    /**
     * 查询活动信息-根据活动ID
     * @param luckDrawActivityReq
     * @return LuckDrawActivityVO
     */
    @PostMapping(value = "/query/detail")
    @RequiresAuthority
    public CommonResult<LuckDrawActivityVO> detail(@RequestBody @Validated LuckDrawActivityReq luckDrawActivityReq) {
        return CommonResult.ok(luckDrawActivityService.detail(luckDrawActivityReq));
    }

    /**
     * 查询活动信息-根据活动类型
     * @param luckDrawActivityReq
     * @return LuckDrawActivityVO
     */
    @PostMapping(value = "/query/info")
    @RequiresAuthority
    public CommonResult<LuckDrawActivityVO> info(@RequestBody @Validated LuckDrawActivityReq luckDrawActivityReq) {
        return CommonResult.ok(luckDrawActivityService.info(luckDrawActivityReq));
    }

    /**
     * 查询活动抽奖记录-根据活动ID
     * @param luckDrawActivityReq
     * @return LuckDrawActivityVO
     */
    @PostMapping(value = "/query/prize")
    @RequiresAuthority
    public CommonResult<LuckDrawActivityVO> prize(@RequestBody LuckDrawActivityReq luckDrawActivityReq) {
        return CommonResult.ok(luckDrawActivityService.prizeRecord(luckDrawActivityReq));
    }
}
