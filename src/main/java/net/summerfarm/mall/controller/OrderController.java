package net.summerfarm.mall.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.enums.RedissonLockKey;
import net.summerfarm.enums.order.OrderConfirmResultEnum;
import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.common.util.DateUtils;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.constant.CommonRedisKey;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.enums.DeliveryStatusEnum;
import net.summerfarm.mall.mapper.ContactMapper;
import net.summerfarm.mall.model.domain.Contact;
import net.summerfarm.mall.model.dto.order.DeliveryPointDTO;
import net.summerfarm.mall.model.input.OrderBillExportReq;
import net.summerfarm.mall.model.input.order.OrderTimeRangeInput;
import net.summerfarm.mall.model.vo.*;
import net.summerfarm.mall.service.*;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.redis.support.lock.annotation.XmLock;
import org.jdom.JDOMException;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @Package: net.summerfarm.controller
 * @Description: 订单相关
 * @author: <EMAIL>
 * @Date: 2016/9/30
 */
@Api(tags = "订单管理控制类")
@Slf4j
@RestController
@RequestMapping(value = "/order")
public class OrderController {

    @Resource
    private OrderService orderService;
    @Resource
    private ContactService contactService;
    @Resource
    private ContactMapper contactMapper;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private TimingDeliveryService timingDeliveryService;
    @Resource
    private DeliveryService deliveryService;

    @Resource
    private MasterOrderService masterOrderService;

    /**
     * 确认收货
     *
     * @param orderNo
     * @return
     */
    @ApiOperation(value = "用户确认收货",httpMethod = "GET")
    @ApiImplicitParam(name = "orderNo",value = "订单编号",paramType = "query",required = true)
    @RequiresAuthority
    @RequestMapping(value = "/UserConfirm")
    @XmLock(prefixKey = CommonRedisKey.OrderLock.ORDER_CONFIRM_RECEIPT, key = "{orderNo}")
    public AjaxResult userConfirm(String orderNo,Integer deliveryStatus) {
        if (deliveryStatus == null){
            return AjaxResult.getError("订单配送状态未获取到");
        }else {
            if (!deliveryStatus.equals(DeliveryStatusEnum.COMPLETE_DELIVERY.getStatus())){
                return AjaxResult.getError("订单配送尚未完成，无法确认收货");
            }
        }
        String result = orderService.userConfirm(orderNo);
        if(OrderConfirmResultEnum.SUCCESS.getResult().equals(result)){
            return AjaxResult.getOK();
        }
        return AjaxResult.getErrorWithMsg(result);
    }

    /**
     * 修改默认地址或删除
     *
     * @return
     */
    @ApiOperation(value = "修改默认地址或删除",httpMethod = "POST")
    @RequiresAuthority
    @RequestMapping(value = "/UpdateContact", method = RequestMethod.POST)
    public AjaxResult updateContact(Contact contact) {
        contact.setmId(RequestHolder.getMId());
        return contactService.updateContact(contact);
    }

    /**
     * 新增地址或修改地址
     *
     * @return
     */
    @ApiOperation(value = "新增地址或删除地址",httpMethod = "POST")
    @RequiresAuthority
    @RequestMapping(value = "/InsertContact", method = RequestMethod.POST)
    public AjaxResult insertContact(Contact contact) {
        contact.setmId(RequestHolder.getMId());
        return contactService.insertContact(contact);
    }

    /**
     * 下单前补全地址
     *
     * @return
     */
    @ApiOperation(value = "下单前补全地址",httpMethod = "POST")
    @RequiresAuthority
    @RequestMapping(value = "/complete/address", method = RequestMethod.POST)
    public CommonResult<Void> completeAddress(@RequestBody Contact contact) {
        return contactService.completeAddress(contact);
    }


    /**
     * 获得地址
     *
     * @return
     */
    @ApiOperation(value = "获得地址",httpMethod = "GET")
    @ApiImplicitParam(name = "status",value = "地址审核状态:1正常或审核通过、2删除、3待审核、4审核不通过",paramType = "query",defaultValue = "1",required = false)
    @RequiresAuthority
    @RequestMapping(value = "/GetContact")
    public AjaxResult<List<Contact>> getContact(Integer status, Integer defaultFlag, Long contactId) {
        return contactService.getContact(status, defaultFlag, contactId);
    }

    /**
     * 获得默认地址
     *
     * @return
     */
    @RequiresAuthority
    @RequestMapping(value = "/query/default-contact", method = RequestMethod.POST)
    public CommonResult<Contact> getDefaultContact() {
        return CommonResult.ok(contactService.getMerchantDefaultContact(RequestHolder.getMId()));
    }

    /**
     * 根据下单时间查询当前用户的历史订单地址
     *
     * @param orderTimeRangeInput 下单时间区间请求参数
     *
     * @return {@link List}<{@link Contact}>
     */
    @RequiresAuthority
    @RequestMapping(value = "/query/queryContactByOrderTime", method = RequestMethod.POST)
    public CommonResult<List<Contact>> queryContactByOrderTime(@RequestBody OrderTimeRangeInput orderTimeRangeInput) {
        return CommonResult.ok(contactService.queryMerchantContactByOrderTime(RequestHolder.getMId(),
                orderTimeRangeInput.getStartOrderTime(), orderTimeRangeInput.getEndOrderTime()));
    }


    /**
     * 获得配送时间
     *
     * @return
     */
    @ApiOperation(value = "获得配送时间",httpMethod = "GET")
    @ApiImplicitParam(name = "outTimes",value = "是否超时加单：0否，1是",paramType = "query",defaultValue = "0")
    @RequiresAuthority
    @RequestMapping(value = "/GetDeliveryDate")
    public AjaxResult getCompletionDeliveryDate(Integer outTimes,Long contactId) {
        return deliveryService.getCompletionDeliveryDate(outTimes, contactId);
    }

    /**
     * 用户订单列表
     * 查询用户订单订单信息
     *
     * @param pageIndex
     * @param pageSize
     * @param orderStatus
     * @return
     */
    @ApiOperation(value = "查询用户订单订单信息",httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex",value = "页数",paramType = "path",defaultValue = "1",required = true),
            @ApiImplicitParam(name = "pageSize",value = "数量",paramType = "path",defaultValue = "10",required = true),
            @ApiImplicitParam(name = "orderStatus",value = "订单状态：1：待支付订单,2：待配送,3：待收货,4：前置仓到货待提货订单," +
                    "5：前置仓确认客户已提货订单,6：商户已确认收货完成订单,7：申请退款订单,8：已退款订单,9：支付失败订单," +
                    "10：支付中断超时关闭订单,11：已撤销订单",paramType = "query",defaultValue = "6",required = true)
    })
    @RequiresAuthority
    @RequestMapping(value = "/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult getOrders(@PathVariable int pageIndex, @PathVariable int pageSize, Short orderStatus) {
        return orderService.getOrders(pageIndex, pageSize, orderStatus);
    }

    /**
     * 订单详情
     * @param orderNo
     * @return
     */
    @ApiOperation(value = "订单详情", httpMethod = "GET")
    @RequiresAuthority
    @RequestMapping(value = "/detail/{orderNo}", method = RequestMethod.GET)
    public AjaxResult orderDetails(@PathVariable String orderNo) {
        return orderService.orderDetails(orderNo);
    }

    /**
     * 提交省心送订单
     *
     * @return
     */
    @ApiOperation(value = "提交省心送订单",httpMethod = "POST")
    @RequiresAuthority
    @RequestMapping(value = "/timing", method = RequestMethod.POST)
    public AjaxResult placeTimingOrder(@Validated @RequestBody PlaceTimingOrderVO ptoVO, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            return AjaxResult.getError(bindingResult.getFieldError().getDefaultMessage());
        }

        //获取mId
        Long mId = RequestHolder.getMId();

        //同一mId并发下单控制
        RLock redissonLock = redissonClient.getLock(RedissonLockKey.TIMING + mId);
        try {
            boolean flag = redissonLock.tryLock(0L, 30L, TimeUnit.SECONDS);
            if (!flag) {
                throw new BizException(1, "请重新下单");
            }

            return timingDeliveryService.distributeTimingOrder(ptoVO);
        } catch (InterruptedException e) {
            log.error("锁获取异常", e);
            throw new DefaultServiceException(1, "请重新下单");
        } finally {
            if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()){
                redissonLock.unlock();
            }
        }

    }

    /**
     * 订单详情
     *
     * @param orderNo
     * @return
     */
    @ApiOperation(value = "支付订单详情",httpMethod = "GET")
    @ApiImplicitParam(name = "orderNo",value = "订单编号",paramType = "path",defaultValue = "01153691988135944",required = true)
    @RequestMapping(value = "/{orderNo}", method = RequestMethod.GET)
    @RequiresAuthority
    public AjaxResult orderDetail(@PathVariable String orderNo) {
        return orderService.selectOrderDetail(orderNo);
    }

    /**
     * 取消待支付订单
     *
     * @param orderNo
     * @return
     */
    @ApiOperation(value = "取消待支付订单",httpMethod = "POST")
    @ApiImplicitParam(name = "orderNo",value = "订单编号",paramType = "path",required = true)
    @RequiresAuthority
    @RequestMapping(value = "/cancel/{orderNo}", method = RequestMethod.POST)
    public AjaxResult cancel(@PathVariable String orderNo) throws IOException, JDOMException {
        return orderService.cancelOrder(orderNo);
    }

    /**
    * 用户的订单数量
    */
    @RequiresAuthority
    @RequestMapping(value = "/quantity", method = RequestMethod.GET)
    public AjaxResult sumNoPayOrders(){
        return orderService.sumNoPayOrders();
    }

    @RequiresAuthority
    @ApiOperation(value="省心送物品购买后，确认订单页面", httpMethod = "POST")
    @RequestMapping(value = "/checkTimingOrder", method = RequestMethod.POST)
    public AjaxResult checkTimingDeliveryOrder(@Validated @RequestBody PlaceTimingOrderVO ptoVO, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            return AjaxResult.getError(bindingResult.getFieldError().getDefaultMessage());
        }
        return timingDeliveryService.checkTimingDeliveryOrder(ptoVO);
    }

    /**
     * 获得司机配送实时点位
     * @return
     */
    @RequiresAuthority
    @PostMapping(value = "/delivery-point")
    public AjaxResult getDeliveryPoint(@RequestBody DeliveryPointDTO deliveryPointDTO) {
        return orderService.getDeliveryPoint(deliveryPointDTO);
    }

    /**
     * 获得司机配送实时点位
     * @return
     */
    @RequiresAuthority
    @PostMapping(value = "/query/order-delivery")
    public CommonResult<OrderDeliveryVO> getOrderDelivery() {
        return orderService.getOrderDelivery();
    }

    /**
     * 账单导出 - 单店、现结门店
     * @return
     */
    @RequiresAuthority
    @PostMapping(value = "/export-async/bill-export")
    public CommonResult<Boolean> billExport(@RequestBody @Validated OrderBillExportReq orderBillExportReq) {
        return CommonResult.ok(orderService.billExport(orderBillExportReq));
    }

    /**
     * 新关单接口
     * @param masterOrderNo 主单编号
     * @return 关单是否成功
     */
    @RequiresAuthority
    @PostMapping(value = "/close-order/v2")
    public CommonResult<Boolean> closeOrderV3(String masterOrderNo) {
        return masterOrderService.closeOrder(masterOrderNo, RequestHolder.getMId(), RequestHolder.isMajor(), RequestHolder.isMajorDirect());
    }
}
