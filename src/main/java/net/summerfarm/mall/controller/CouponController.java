package net.summerfarm.mall.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.model.domain.Activity;
import net.summerfarm.mall.model.dto.market.coupon.SkuCouponDTO;
import net.summerfarm.mall.model.dto.market.coupon.SkuCouponReqDTO;
import net.summerfarm.mall.model.vo.MerchantCouponVO;
import net.summerfarm.mall.model.vo.coupon.FixCouponDTO;
import net.summerfarm.mall.service.ActivityService;
import net.summerfarm.mall.service.MerchantCouponService;
import net.summerfarm.mall.service.impl.MerchantCouponServiceImpl;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @Package: net.summerfarm.activity.controller
 * @Description:
 * @author: <EMAIL>
 * @Date: 2016/10/9
 */
@Api(tags = "优惠券控制类")
@RestController
@RequestMapping(value = "/activity")
public class CouponController {

    @Resource
    private MerchantCouponService merchantCouponService;

    @Resource
    private ActivityService activityService;

    /**
     * 优惠券展示页
     *
     * @return
     */
    @ApiOperation(value = "优惠券展示页查询",httpMethod = "GET",response = MerchantCouponVO.class)
    @RequiresAuthority
    @RequestMapping(value = "/queryCoupon/{pageIndex}/{pageSize}")
    public AjaxResult queryCoupon(@PathVariable int pageIndex, @PathVariable int pageSize,Integer status,Integer type) {
        return merchantCouponService.selectCoupon(pageIndex,pageSize,status,RequestHolder.getMId(),type);
    }

    /**
     * 可用优惠券数量
     *
     * @return
     */
    @ApiOperation(value = "可用优惠券数量",httpMethod = "GET")
    @RequiresAuthority
    @RequestMapping(value = "coupon-count", method = RequestMethod.GET)
    public AjaxResult countCoupon() {
        return merchantCouponService.countUnused(RequestHolder.getMId());
    }

    @ApiOperation(value = "活动列表查询",httpMethod = "GET",response = Activity.class)
    @RequiresAuthority
    @RequestMapping(value = "/activity-list", method = RequestMethod.GET)
    public AjaxResult activityList(Integer type) {
        return activityService.activityList(RequestHolder.getMerchantAreaNo(),type);
    }

    /**
     * 商城首页提供特价专区接口
     * @return
     */
    @RequiresAuthority
    @RequestMapping(value = "/specialOffer", method = RequestMethod.GET)
    public AjaxResult discountZone() {
        return activityService.discountZoneSku();
    }

    /**
     * 商城首页提供特价专区列表
     * @return
     */
    @RequiresAuthority
    @RequestMapping(value = "/specialOffer/list/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult discountZoneList(@PathVariable int pageIndex, @PathVariable int pageSize) {
        return activityService.discountZoneSkuList(pageIndex,pageSize);
    }


}
