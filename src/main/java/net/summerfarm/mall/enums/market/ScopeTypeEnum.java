package net.summerfarm.mall.enums.market;

import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: <EMAIL>
 * @create: 2022/12/8
 */
@Getter
@AllArgsConstructor
public enum ScopeTypeEnum {

    ALL(0, "全部"),
    MERCHANT_POOL(1, "人群包"),
    AREA(2, "运营城市"),
    LARGE_AREA(3, "运营大区");

    private int code;

    private String value;

    public static ScopeTypeEnum getByCode(Integer code) {
        for (ScopeTypeEnum typeEnum : ScopeTypeEnum.values()) {
            if (Objects.equals(code, typeEnum.code)) {
                return typeEnum;
            }
        }
        return null;
    }
}
