package net.summerfarm.mall.enums.market;

import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 营销活动类型
 * @author: <EMAIL>
 * @create: 2022/12/8
 */
@Getter
@AllArgsConstructor
public enum ActivityTypeEnum {

    SPECIAL_PRICE(0, "特价活动"),
    NEAR_EXPIRED(1, "临保活动"),
    EXCHANGE_BUY(2, "购物车换购"),
    EXPAND_BUY(3, "拓展购买"),
    PANIC_BUY(4, "秒杀"),
    PARTNER_SHIP_BUY(5, "多人拼团"),
    FULL_REDUCE(6, "满减"),
    FULL_RETURN(7, "满返"),
    PRE_SALE(8, "预售"),
    TIMING_BUY(9, "省心送");

    private int code;

    private String value;

    public static ActivityTypeEnum getByCode(Integer code) {
        for (ActivityTypeEnum typeEnum : ActivityTypeEnum.values()) {
            if (Objects.equals(code, typeEnum.code)) {
                return typeEnum;
            }
        }
        return null;
    }


}
