package net.summerfarm.mall.enums.market.timing;

import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 省心送开始配送类型枚举类
 *
 * @author: <EMAIL>
 * @create: 2023/5/13
 */
@Getter
@AllArgsConstructor
public enum DeliveryStartTypeEnum {


    NEXT(0, "下一个配送日"),

    APPOINT(1, "指定开始日期"),

    AFTER_ORDER(2, "下单日期+N"),
    ;

    private int code;

    private String value;

    public static DeliveryStartTypeEnum getByCode(Integer code) {
        for (DeliveryStartTypeEnum typeEnum : DeliveryStartTypeEnum.values()) {
            if (Objects.equals(code, typeEnum.code)) {
                return typeEnum;
            }
        }
        return null;
    }

}
