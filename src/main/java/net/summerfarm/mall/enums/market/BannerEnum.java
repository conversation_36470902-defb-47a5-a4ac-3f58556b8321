package net.summerfarm.mall.enums.market;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-01-26
 * @description
 */
public class BannerEnum {
    @Getter
    @AllArgsConstructor
    public enum Type {
        HOME_BANNER("homeBanner", "首页banner"),
        TIMING("timing","省心送banner"),
        POP("pop", "弹窗"),
        CARD("card", "商品列表卡片"),
        LARGE_ACTIVITY("largeActivity", "大型活动"),
        MEDIUM_ACTIVITY("mediumActivity", "中型活动"),
        ACTIVITY_1("activity1", "活动位1"),
        ACTIVITY_2("activity2", "活动位2"),
        ACTIVITY_3("activity3", "活动位3"),
        ACTIVITY_4("activity4", "活动位4"),
        ACTIVITY_5("activity5", "活动位5"),
        ACTIVITY_6("activity6", "活动位6"),
        BOTTOM_TITLE("bottomTitle", "省心送底部hot"),
        RECOMMEND_WORD("recommendWord", "搜索推荐词"),
        SHADING_WORD("shadingWord", "搜索底纹词"),
        SEARCH_PICTURE("searchPicture", "搜索图片资源位"),
        CATEGORY_PICTURE("categoryPicture", "分类图片资源位"),
        LOW_COMMODITY_LEVEL("lowCommodityLevel", "低价商品位"),
        ;


        private final String type;
        private final String desc;
    }

    @Getter
    public enum Status {
        /**
         * 0-展示
         */
        SHOW,
        /**
         * 1-不展示
         */
        UN_SHOW
    }

    @Getter
    @AllArgsConstructor
    public enum SHOW_RULE {
        CITY_TYPE(0,"按城市"),
        CIRCLE_TYPE(1,"按圈人");


        private final Integer value;
        private final String desc;
    }

    @Getter
    @AllArgsConstructor
    /**
     * 跳转类型枚举
     */
    public enum LinkTypeEnum {

        NO(0,"无跳转"),

        ITEM(1,"商品"),

        TIMING_DELIVERY(2, "省心送"),

        LUCK_DRAW(3, "抽奖"),

        SPECIAL(4, "专题"),

        CHANNEL(5, "频道"),

        LIVE_ROOM(6, "直播间");

        private Integer value;

        private String desc;
    }

    @Getter
    @AllArgsConstructor
    /**
     * 频道跳转类型枚举
     */
    public enum LinkChannelEnum {

        HOME(0,"首页"),

        CLASSIFICATION(1,"分类页"),

        TIMING_DELIVERY(2, "省心送"),

        TROLLEY(3, "购物车"),

        COUPON_CENTER(4, "领券中心"),

        DISCOUNT_CARD(5, "优惠卡"),

        INVITE(6, "邀请好友");

        private Integer value;

        private String desc;
    }

    @Getter
    @AllArgsConstructor
    /**
     * 展示形式枚举
     */
    public enum DisplayFormatEnum {

        IMAGE(0,"图片"),

        WORD(1,"文字"),

        ITEM_CARD(2,"商品卡片"),
        ;

        private Integer value;

        private String desc;
    }
}
