package net.summerfarm.mall.enums.market;

import com.google.common.collect.Lists;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: <EMAIL>
 * @create: 2023/3/6
 */
@Getter
@AllArgsConstructor
public enum PlatformEnum {

    MALL(0, "商城渠道"),
    LIVE_BROADCAST(1, "直播渠道"),
    MALL_AND_LIVE(2, "商城、直播同渠道"),
    ;

    private int code;

    private String value;

    /**
     * 判断是否包含直播渠道
     * @param code
     * @return
     */
    public static Boolean containLive(int code) {
        List<PlatformEnum> list = Lists.newArrayList(LIVE_BROADCAST, MALL_AND_LIVE);
        for (PlatformEnum platformEnum : list) {
            if (code == platformEnum.code) {
                return true;
            }
        }
        return false;
    }
}
