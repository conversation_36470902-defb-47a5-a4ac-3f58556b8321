package net.summerfarm.mall.enums.market;

import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: <EMAIL>
 * @create: 2023/4/19
 */
@Getter
@AllArgsConstructor
public enum MallTagEnum {

    NEW_PRODUCT(100, "新品"),

    WILL_EXPIRE(101, "临保"),

    BROKEN(102, "破袋"),

    UNPACK(103, "拆包"),

    EXT_ACTIVITY(104, "活动"),

    TIMING_DELIVER(105, "省心送"),

    SPECIAL_OFFER(200, "特价"),

    LADDER_PRICE(201, "阶梯价"),

    FULL_REDUCE(202, "满减"),

    FULL_RETURN(203, "满返"),

    COUPON(204, "优惠券"),

    EXCHANGE(205, "换购"),

    DISCOUNT_CARD(206, "权益卡"),

    TIMING_SPECIAL_OFFER(207, "省心送特价"),
    ;

    private int code;

    private String value;

    public static Integer getCodeByName(String tagName) {
        for (MallTagEnum tagEnum : MallTagEnum.values()) {
            if (Objects.equals(tagEnum.getValue(), tagName)) {
                return tagEnum.getCode();
            }
        }
        return null;
    }

}
