package net.summerfarm.mall.enums.market;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * @author: <EMAIL>
 * @create: 2023/9/8
 */
@Getter
@AllArgsConstructor
public enum ReceiveCouponErrorEnum {

    NOT_EXIST(0, "优惠券不存在！"),

    INVALID(1, "抱歉您来晚啦，优惠券已过期！"),

    EXIST_UNUSED(2, "您已领取过当前优惠券！"),

    OVER_LIMIT(3, "抱歉，优惠券领取已达上限！"),

    END(4, "抱歉您来晚啦，优惠券已抢光！");

    private int code;

    private String value;

    public static final List<ReceiveCouponErrorEnum> SORT_INDEX = Lists.newArrayList(EXIST_UNUSED,
            OVER_LIMIT, END, INVALID, NOT_EXIST);

}
