package net.summerfarm.mall.enums;

/**
 * @Description:  共枚举
 * @Author: lzh
 * @Time: 2023/4/11 0010 09:59
 * @ModifyBy:
 */

public enum DistributionRulesProductTypeEnum {
    /**
     * 商品类目  1：全部商品 2：乳制品商品  3：非乳制品商品
     */
    ALL(1,"全部商品"),
    LACTATION(2,"乳制品商品"),
    NON_DAIRY(3,"非乳制品商品")
    ;
    private Integer code;

    private String value;

    DistributionRulesProductTypeEnum(Integer code, String value){
        this.code = code;
        this.value = value;
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static String getValueByKey(Integer key){
        for (DistributionRulesProductTypeEnum c : DistributionRulesProductTypeEnum.values()) {
            if (c.getCode() .equals(key)) {
                return c.value;
            }
        }
        return null;
    }

}
