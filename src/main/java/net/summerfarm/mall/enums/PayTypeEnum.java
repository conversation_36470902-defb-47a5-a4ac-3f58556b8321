package net.summerfarm.mall.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 下单支付类型
 *
 * <AUTHOR>
 * @Date 2023/4/12 17:44
 */
@Getter
@AllArgsConstructor
public enum PayTypeEnum {
    BILL(1,"账期"),
    CASH(2,"现结"),
    HELP_BILL(3, "未收款代下单"),
    HELP_CASH(4, "已收款代下单");

    Integer type;
    String desc;

    public static PayTypeEnum getHelpOrderType(Integer code) {
        for (PayTypeEnum type : PayTypeEnum.values()) {
            if (type.getType().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
