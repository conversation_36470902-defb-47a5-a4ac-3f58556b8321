package net.summerfarm.mall.enums;

/**
 * @Description:  满返-返券规则
 * @Author: lzh
 * @Time: 2023/4/11 0010 09:59
 * @ModifyBy:
 */

public enum MarketRuleCouponRuleEnum {
    /**
     * 返券规则  1-确认收货后（默认）  2-支付完成后
     */
    CONFIRM_RECEIPT(1,"确认收货后"),
    PAYMENT_COMPLETION(2,"支付完成后")
    ;
    private Integer code;

    private String value;

    MarketRuleCouponRuleEnum(Integer code, String value){
        this.code = code;
        this.value = value;
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static String getValueByKey(Integer key){
        for (MarketRuleCouponRuleEnum c : MarketRuleCouponRuleEnum.values()) {
            if (c.getCode() .equals(key)) {
                return c.value;
            }
        }
        return null;
    }

}
