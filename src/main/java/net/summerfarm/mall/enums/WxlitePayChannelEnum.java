package net.summerfarm.mall.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 运营服务区是否使用招行支付
 * <AUTHOR>
 * @date 2023/6/12  17:28
 */
@Getter
@AllArgsConstructor
public enum WxlitePayChannelEnum {
    /**
     * 0-默认设置，即小程序使用微信收款
     */
    OFF(0, "默认配置"),
    /**
     * 1-招行支付，即小程序使用招行收款
     */
    CMB(1, "招行支付");


    /**
     * 运营服务区配置小程序是否是否招行收款
     */
    private Integer wxlitePayChannel;

    /**
     * 备注
     */
    private String desc;

}
