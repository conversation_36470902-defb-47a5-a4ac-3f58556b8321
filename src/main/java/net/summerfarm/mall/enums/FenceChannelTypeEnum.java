package net.summerfarm.mall.enums;

import net.summerfarm.mall.common.util.RequestHolder;

/**
 * @Description:  共枚举
 * @Author: lzh
 * @Time: 2023/4/11 0010 09:59
 * @ModifyBy:
 */

public enum FenceChannelTypeEnum {
    /**
     * 如 是否删除  0 否 1 是   是否关注  0 否 1 是
     */
    NORMAL("1000","鲜沐平台客户"),
    MAJOR("2000","鲜沐大客户"),
    SAAS("3000","SaaS客户")
    ;
    private String code;

    private String value;

    FenceChannelTypeEnum(String code, String value){
        this.code = code;
        this.value = value;
    }


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static String getValueByKey(String key){
        for (FenceChannelTypeEnum c : FenceChannelTypeEnum.values()) {
            if (c.getCode() .equals(key)) {
                return c.value;
            }
        }
        return null;
    }

    public static String getChannelCode(){
        if (MerchantSizeEnum.MAJOR_CUSTOMER.getValue().equals(RequestHolder.getSize())) {
            return MAJOR.getCode();
        } else if (MerchantSizeEnum.SINGLE_SHOP.getValue().equals(RequestHolder.getSize())) {
            return NORMAL.getCode();
        }
        return null;
    }

}
