package net.summerfarm.mall.enums;

/**
 * @Description: 配送评价状态枚举
 * @Date: 2022/5/14 15:42
 * @Author: huang<PERSON><PERSON><PERSON>ong
 */
public enum DeliveryEvaluationStatusEnum {

    EVALUATION_WAITING(0,"未评价"),
    EVALUATION_FINISH(1,"已评价"),
    EVALUATION_WITHOUT(2,"不需要评价"),
    ;
    private Integer status;
    private String statusDesc;

    DeliveryEvaluationStatusEnum(Integer status, String statusDesc) {
        this.status = status;
        this.statusDesc = statusDesc;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getStatusDesc() {
        return statusDesc;
    }

    public void setStatusDesc(String statusDesc) {
        this.statusDesc = statusDesc;
    }
}
