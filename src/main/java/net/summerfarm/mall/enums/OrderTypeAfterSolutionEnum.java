package net.summerfarm.mall.enums;

/**
 * <AUTHOR>
 * @Description 售后处理订单类型枚举类
 * @date 2022/8/5 14:54
 * @Version 1.0
 */
public enum OrderTypeAfterSolutionEnum {
    NORMAL(0,"普通订单"),
    TIMING(1,"省心送订单");

    private Integer type;
    private String description;

    private OrderTypeAfterSolutionEnum(Integer type, String description) {
        this.type = type;
        this.description = description;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
