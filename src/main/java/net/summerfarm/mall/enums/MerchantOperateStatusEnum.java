package net.summerfarm.mall.enums;

public enum MerchantOperateStatusEnum {

    /**
     * 正常
     */
    NORMAL(0, "正常"),

    /**
     * 倒闭
     */
    CLOSED(1, "倒闭"),

    /**
     * 待提交核验
     */
    PENDING_SUBMISSION(2, "待提交核验"),

    /**
     * 待核验
     */
    PENDING_VERIFICATION(3, "待核验"),

    /**
     * 核验拒绝
     */
    VERIFICATION_REJECTED(4, "核验拒绝");

    private final Integer code;
    private final String description;

    MerchantOperateStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
    
}