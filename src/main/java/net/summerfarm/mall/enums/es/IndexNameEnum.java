package net.summerfarm.mall.enums.es;

/**
 *
 * <AUTHOR> <a href="mailto:<EMAIL>"><EMAIL></a>
 * @since 2022/7/24 12:06 下午
 */
public enum IndexNameEnum {

    /**
     * 鲜沐商城首页商品列表数据索引
     */
    XIANMU_AREA_SKU_INFO("xianmu_area_sku_info", "鲜沐商城首页商品列表数据索引"),

    /**
     * 鲜沐商品搜索建议词索引
     */
    XIANMU_PRODUCT_SUGGEST_WORD("xianmu_product_suggest_word", "鲜沐商品搜索建议词索引");
    /**
     * 索引名称
     */
    private final String name;

    /**
     * 索引名称说明
     */
    private final String description;

    IndexNameEnum(String name, String description) {
        this.name = name;
        this.description = description;
    }

    public String getName() {
        return name;
    }
}
