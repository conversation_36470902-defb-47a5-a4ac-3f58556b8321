package net.summerfarm.mall.enums.es;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *
 * <AUTHOR> <a href="mailto:<EMAIL>"><EMAIL></a>
 * @since 2022/7/24 12:23 下午
 */
@Getter
@AllArgsConstructor
public enum XianmuAreaSkuInfoFieldEnum {

    /**
     * 品牌名称
     */
    brandName("brandName","品牌名称"),

    /**
     * 后台类目id
     */
    categoryId("categoryId","后台类目id"),

    /**
     * pdId
     */
    pdId("pdId","pdId"),

    /**
     * sku名称
     */
    skuName("skuName", "sku名称"),

    /**
     * 商品名称
     */
    pdName("pdName","商品名称"),

    /**
     * 商品名称括号内容
     */
    pdNameExt("pdNameExt", "商品名称括号内容"),

    /**
     * 商品名称去除括号内容
     */
    pdNamePure("pdNamePure", "商品名称去除括号内容"),

    /**
     * 商品描述
     */
    pddetail("pddetail","商品描述"),

    /**
     * 商品介绍
     */
    slogan("slogan","商品介绍"),

    /**
     * 商品介绍
     */
    otherSlogan("otherSlogan","商品介绍"),

    /**
     * 保质期时长
     */
    qualityTime("qualityTime","保质期时长"),

    /**
     * 保质期时长单位
     */
    qualityTimeUnit("qualityTimeUnit","保质期时长单位"),

    /**
     * SPU生命周期：-1、上新中 0、有效 1、已删除
     */
    productsOutdated("productsOutdated","SPU生命周期：-1、上新中 0、有效 1、已删除"),

    /**
     * 商品图片（SPU）
     */
    picturePath("picturePath","商品图片（SPU）"),

    /**
     * 商品规格信息
     */
    weight("weight","商品规格信息"),

    /**
     * 商品包装
     */
    unit("unit", "商品包装"),

    /**
     * 商品编号
     */
    sku("sku","商品编号"),

    /**
     * SKU商品图片
     */
    skuPic("skuPic","SKU商品图片"),

    /**
     * 售卖规格(inventory.base_sale_unit )
     */
    baseSaleUnit("baseSaleUnit","售卖规格"),

    /**
     * 最小起售量
     */
    baseSaleQuantity("baseSaleQuantity","最小起售量"),

    /**
     * SKU生命周期：-1、上新处理中 0、使用中 1、已删除
     */
    outdated("outdated","SKU生命周期：-1、上新处理中 0、使用中 1、已删除"),

    /**
     * 介绍
     */
    info("info","介绍"),

    /**
     * 区域sku优先级（area_sku.pd_priority）
     */
    pdPriority("pdPriority","区域sku优先级（area_sku.pd_priority）"),

    /**
     * 销售模式(area_sku.sales_mode)
     */
    salesMode("salesMode","销售模式"),

    /**
     * 限购数量(area_sku.limited_quantity )
     */
    limitedQuantity("limitedQuantity","限购数量"),

    /**
     * 上下架状态
     */
    onSale("onSale","上下架状态"),

    /**
     * 运营区域编号
     */
    areaNo("areaNo","运营区域编号"),

    /**
     * 是否是大客户专享
     */
    mType("mType", "是否是大客户专享"),

    /**
     * 毛利率
     */
    interestRate("interestRate","毛利率"),

    /**
     * 核心客户是否售罄
     */
    coreSaleOut("coreSaleOut","核心客户是否售罄"),

    /**
     * 普通客户是否售罄
     */
    saleOut("saleOut","普通客户是否售罄"),

    /**
     * 核心客户库存量
     */
    coreStoreQuantity("coreStoreQuantity","核心客户库存量"),

    /**
     * 普通客户库存量
     */
    storeQuantity("storeQuantity","普通客户库存量"),

    /**
     * 是否展示预告
     */
    showAdvance("showAdvance","是否展示预告"),

    /**
     * 预告信息
     */
    advance("advance","预告信息"),

    /**
     * 库存仓编号
     */
    warehouseNo("warehouseNo", "库存仓编号"),

    /**
     * 属性值（数组形式）
     */
    propertyValues("propertyValues", "属性值（数组形式）"),

    /**
     * 是否展示
     */
    show("show", "是否展示"),

    /**
     * inventory.extType
     */
    extType("extType", "商品类型"),

    /**
     * 固定排序标识
     */
    fixFlag("fixFlag", "固定排序标识"),

    /**
     * 固定排序值
     */
    fixNum("fixNum", "固定排序值"),

    /**
     * 关键属性（排除品牌）
     */
    keyProperty("keyProperty", "关键属性（排除品牌）"),

    /**
     * 商品二级性质
     */
    subType("subType", "商品二级性质"),
    ;

    /**
     * 字段名称
     */
    private final String fieldName;

    /**
     * 字段描述
     */
    private final String desciption;
}