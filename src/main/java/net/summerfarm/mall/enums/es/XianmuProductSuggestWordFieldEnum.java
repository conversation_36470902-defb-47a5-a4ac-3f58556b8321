package net.summerfarm.mall.enums.es;

/**
 *
 * <AUTHOR> <a href="mailto:<EMAIL>"><EMAIL></a>
 * @since 2022/7/24 12:23 下午
 */
public enum XianmuProductSuggestWordFieldEnum {

    /**
     * 建议词
     */
    SUGGEST_WORD("suggest_word", "建议词");

    /**
     * 字段名称
     */
    private final String fieldName;

    /**
     * 字段描述
     */
    private final String desciption;

    XianmuProductSuggestWordFieldEnum(String fieldName, String desciption) {
        this.fieldName = fieldName;
        this.desciption = desciption;
    }

    public String getFieldName() {
        return fieldName;
    }

    public String getDesciption() {
        return desciption;
    }
}
