package net.summerfarm.mall.enums;

/**
 * @Description:  共枚举
 * @Author: lzh
 * @Time: 2023/4/11 0010 09:59
 * @ModifyBy:
 */

public enum ShoppingCartEfficacyEnum {
    /**
     * 是否失效 0:否 1:是
     */
    NO(0,"否"),
    YES(1,"是")
    ;
    private Integer code;

    private String value;

    ShoppingCartEfficacyEnum(Integer code, String value){
        this.code = code;
        this.value = value;
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static String getValueByKey(Integer key){
        for (ShoppingCartEfficacyEnum c : ShoppingCartEfficacyEnum.values()) {
            if (c.getCode() .equals(key)) {
                return c.value;
            }
        }
        return null;
    }

}
