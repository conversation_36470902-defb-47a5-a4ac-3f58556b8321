package net.summerfarm.mall.enums;

/**
 * <AUTHOR>
 * @date 2021/07/22
 */
public enum UpdateContactEnum {
    UPDATE_DEFAULT("1","设置默认地址"),
    DELETE_DEFAULT("2","删除默认地址"),
    UPDATE_CONTACT("3","更新收货人"),
    UPDATE_MCONTACT("4","更新联系人");

    private String type;
    private String typeName;

    UpdateContactEnum() {
    }

    UpdateContactEnum(String type, String typeName) {
        this.type = type;
        this.typeName = typeName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }
}
