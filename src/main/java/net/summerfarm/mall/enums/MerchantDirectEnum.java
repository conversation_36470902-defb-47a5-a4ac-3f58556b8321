package net.summerfarm.mall.enums;

/**
 * 客户类型枚举类
 * <AUTHOR>
 */
public enum  MerchantDirectEnum {

    //账期
    ACCOUNT_PERIOD(1,"账期"),
    //立即支付
    PAY_IMMEDIATELY(2,"现结");

    private Integer type;
    private String description;

    private MerchantDirectEnum(Integer type, String description) {
        this.type = type;
        this.description = description;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
