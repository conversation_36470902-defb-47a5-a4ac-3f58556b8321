package net.summerfarm.mall.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @author: <EMAIL>
 * @create: 2023/3/16
 */
@Getter
@AllArgsConstructor
public enum MajorPriceTypeEnum {

    /**
     * 商城价
     */
    MALL_PRICE(0, "商城价"),

    /**
     * 指定价
     */
    SPECIFIED_PRICE(1, "指定价"),

    /**
     * 毛利率
     */
    RATE_PRICE(2, "毛利率"),

    MALL_PRICE_ADD_RATE(3, "商城价上浮"),

    MALL_PRICE_SUB_RATE(4, "商城价下浮"),

    MALL_PRICE_ADD_PRICE(5, "商城价加价"),

    MALL_PRICE_SUB_PRICE(6, "商城价减价"),
    ;

    public static final List<Integer> MALL_RELATED = new ArrayList<> (Arrays.asList(
            MajorPriceTypeEnum.MALL_PRICE.getCode(),
            MajorPriceTypeEnum.MALL_PRICE_ADD_RATE.getCode(),
            MajorPriceTypeEnum.MALL_PRICE_SUB_RATE.getCode(),
            MajorPriceTypeEnum.MALL_PRICE_ADD_PRICE.getCode(),
            MajorPriceTypeEnum.MALL_PRICE_SUB_PRICE.getCode()
    ));
    private Integer code;

    private String value;
    // 静态方法，根据code获取对应的枚举实例
    public static MajorPriceTypeEnum fromCode(Integer code) {
        for (MajorPriceTypeEnum type : MajorPriceTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("No enum constant with code " + code);
    }
}
