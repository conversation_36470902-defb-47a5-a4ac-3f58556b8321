package net.summerfarm.mall.enums;

/**
 * <AUTHOR>
 * @Description 售后退运费标识枚举类
 * @date 2022/8/30 14:52
 * @Version 1.0
 */
public enum RefundFreightEnums {
    //未退
    NOT_REFUNDED(0,"未退运费"),
    //已退
    RETURNED(1,"已退运费");

    private Integer status;
    private String description;

    RefundFreightEnums(Integer status, String description){
        this.status=status;
        this.description=description;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
