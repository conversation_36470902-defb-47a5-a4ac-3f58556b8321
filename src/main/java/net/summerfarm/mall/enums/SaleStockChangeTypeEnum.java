package net.summerfarm.mall.enums;

/**
 *  销售库存变动类型枚举
 */

public enum SaleStockChangeTypeEnum implements StockChangeType {
    PLACE_ORDER("生成订单"),
    SALE_OUT("销售出库"),
    CANCEL_ORDER("取消订单"),
    TIMING_PLAN_CREATE("省心送配送计划"),
    TIMING_PLAN_DEL("后台删除配送计划"),
    AFTER_SALE_RETURN("用户退单"),
    ALL_RETURN("用户整单退"),
    RETURN("用户退单"),
    REFUNDS("补发售后"),
    PANIC_BUY("秒杀下单"),
    MANUAL_CLOSED("手动关单"),
    TRUST_STORE_NO_CHANGE("库存使用切换"),
    CANCEL_AFTER_ORDER("取消售后订单"),
    MALL_TIMING_PLAN_DEL("商城删除配送计划");

    private final String typeName;

    SaleStockChangeTypeEnum(String typeName) {
        this.typeName = typeName;
    }

    @Override
    public String getTypeName() {
        return typeName;
    }
}
