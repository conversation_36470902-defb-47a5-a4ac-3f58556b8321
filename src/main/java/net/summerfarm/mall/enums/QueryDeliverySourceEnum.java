package net.summerfarm.mall.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * Description: <br/>
 * date: 2022/9/7 10:31<br/>
 *
 * <AUTHOR> />
 */
@Getter
public enum QueryDeliverySourceEnum {

    MANAGE(1, "后台"),
    MALL(2, "商城"),;

    private Integer code;
    private String desc;

    QueryDeliverySourceEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Map<Integer,String> map  = new HashMap<Integer,String>();

    static{
        QueryDeliverySourceEnum[] values = QueryDeliverySourceEnum.values();
        for (QueryDeliverySourceEnum queryDeliverySourceEnum : values) {
            map.put(queryDeliverySourceEnum.getCode(),queryDeliverySourceEnum.getDesc());
        }
    }
}
