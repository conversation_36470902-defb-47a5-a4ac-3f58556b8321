package net.summerfarm.mall.enums;

/**
 * @Package: net.summerfarm.common.enums
 * @Description: 精准送时间范围
 * @author: <EMAIL>
 * @Date: 2017/4/18
 */
public enum TimeFrameEnum {

    ZERO(0,"8:00-9:00"),
    FIRST(1,"9:00-10:00"),
    SECOND(2,"10:00-11:00"),
    THIRD(3,"11:00-12:00"),
    FOURTH(4,"12:00-14:00");

    TimeFrameEnum(int id, String timeFrame) {
        this.timeFrame = timeFrame;
        this.id = id;
    }

    @Override
    public String toString() {
        return "{" +
                "id=" + id +
                ", timeFrame='" + timeFrame + '\'' +
                '}';
    }

    private int id;

    private String timeFrame;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getTimeFrame() {
        return timeFrame;
    }

    public void setTimeFrame(String timeFrame) {
        this.timeFrame = timeFrame;
    }
}
