package net.summerfarm.mall.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 样品申请状态
 *
 * <AUTHOR>
 * @date 2023/01/18
 */
@Getter
@AllArgsConstructor
public enum SampleApplyStatus {
    PASS(0, "通过"),
    FEEDBACK(1, "已反馈"),
    CANCEL(2, "取消"),
    INIT(3, "初始化"),
    NOT_PASS(4, "不通过"),
    ;

    private Integer value;
    /**
     * 描述
     */
    private String content;
}
