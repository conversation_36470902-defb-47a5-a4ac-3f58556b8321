package net.summerfarm.mall.enums;

/**
 * @Description: 单店账单导出状态
 * @Author: lzh
 * @Time: 2023/06/08 0010 09:59
 * @ModifyBy:
 */

public enum OrderExportStatusEnum {
    /**
     * 0-待发送  1-发送成功  2-发送失败
     */
    WAITING(0,"待发送"),
    SUCCESS(1,"发送成功"),
    FAIL(2,"发送失败")
    ;
    private Integer code;

    private String value;

    OrderExportStatusEnum(Integer code, String value){
        this.code = code;
        this.value = value;
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static String getValueByKey(Integer key){
        for (OrderExportStatusEnum c : OrderExportStatusEnum.values()) {
            if (c.getCode() .equals(key)) {
                return c.value;
            }
        }
        return null;
    }

}
