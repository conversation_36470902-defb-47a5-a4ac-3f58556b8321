package net.summerfarm.mall.enums;

/**
 * @Description:  卡劵领取类型
 * @Author: lzh
 * @Time: 2023/4/11 0010 09:59
 * @ModifyBy:
 */

public enum CouponSkuScopeEnum {

    ALL(1,"全部商品可用"),
    ALL_EXCEPTIONALITY(2,"全部商品可用（除部分特殊商品）"),
    PARTIAL_CATEGORY(3,"部分品类可用"),
    PARTIAL_CATEGORY_EXCEPTIONALITY(4,"部分品类可用（除部分特殊商品）"),
    PARTIAL_SKU(5,"部分商品可用"),
    ;
    private Integer code;

    private String value;

    CouponSkuScopeEnum(Integer code, String value){
        this.code = code;
        this.value = value;
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static String getValueByKey(Integer key){
        for (CouponSkuScopeEnum c : CouponSkuScopeEnum.values()) {
            if (c.getCode() .equals(key)) {
                return c.value;
            }
        }
        return null;
    }

}
