package net.summerfarm.mall.enums;

/**
 * 除销售出库外的其他冻结库存类型
 */
public enum OtherStockChangeTypeEnum implements StockChangeType {

    STOCK_ALLOCATION_AUDIT("库存调拨-审核通过"),
    OPEN_SYNC("同步开关开启"),
    DAMAGE_OUT("货损出库"),
    STOCK_ALLOCATION_DELIVERING("库存调拨-运输中"),
    ALLOCATION_DAMAGE_OUT_TASK("调拨货损"),
    ALLOCATION_END_IN("调拨回库"),
    STOCK_TAKING("库存盘点"),
    STOCK_TAKING_OUT("盘亏出库"),
    AREA_CHANGE_STORE("城市仓切换"),
    REFUNDS("补货售后");

    private final String typeName;

    OtherStockChangeTypeEnum(String typeName) {
        this.typeName = typeName;
    }

    public String getTypeName() {
        return typeName;
    }
}
