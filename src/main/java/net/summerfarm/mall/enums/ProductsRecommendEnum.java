package net.summerfarm.mall.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2020-12-03
 * @description
 */
@Getter
@AllArgsConstructor
public enum ProductsRecommendEnum {
    /**
     * 0-首页
     */
    HOME(0, "u2i", "product_recommend_mid"),
    /**
     * 1-商品详情
     */
//    PRODUCT_INFO(1, "ui2i", "product_recommend_ui2i"),
    /**
     * 2-购物车
     */
    TROLLEY(2, "i2i", "product_recommend_sku"),

    COLLOCATION(3,"u2i","collocation_recommend_sku"),
    /**
     * 4-首页V2
     */
    HOME_V2(4, "u2i", "product_recommend_mid_v2"),

    PRODUCT_INFO_V2(5, "top_selling", "product_recommend_top_selling"),

    ;

    /**
     * 推荐类型
     */
    private final Integer type;
    /**
     * 推荐策略
     */
    private final String strategy;
    /**
     * redis key
     */
    private final String redisKey;
}
