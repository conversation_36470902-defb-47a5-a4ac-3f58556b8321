package net.summerfarm.mall.enums;

/**
 * <AUTHOR>
 * @Description TODO
 * @date 2022/8/8 23:18
 * @Version 1.0
 */
public enum AfterRefundTypeEnum {
    /**
     * 切仓
     */
    SWITCH_WAREHOUSE("切仓库存不足"),

    /**
     * 其他原因
     */
    OTHER("其他");

    private String description;

    private AfterRefundTypeEnum(String description){
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
