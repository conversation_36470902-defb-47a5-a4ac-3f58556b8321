package net.summerfarm.mall.enums;

public enum AddressCompletionStatusEnum {
    UNCOMPLETED(0, "未完善"),
    COMPLETED(1, "已完善");

    private final Integer code;
    private final String description;

    AddressCompletionStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static AddressCompletionStatusEnum fromCode(Integer code) {
        for (AddressCompletionStatusEnum status : AddressCompletionStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("No matching constant for [" + code + "]");
    }
}
