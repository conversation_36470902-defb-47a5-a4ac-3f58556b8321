package net.summerfarm.mall.enums;

import lombok.Data;
import lombok.Getter;

/**
 * <AUTHOR>
 * @descripton
 * @date 2024/6/19 14:50
 */
public interface MerchantLeadsEnum {


    enum MerchantTypeEnum {
        POP(1, "pop客户"),
        XIAN_MU(0, "鲜沐客户");
        @Getter
        private Integer code;
        @Getter
        private String value;

        MerchantTypeEnum(Integer code, String value) {
            this.code = code;
            this.value = value;
        }
    }
}
