package net.summerfarm.mall.enums;

/**
 * <AUTHOR>
 * @Description TODO
 * @date 2022/8/8 16:47
 * @Version 1.0
 */
public enum AfterHandleRemarkEnum {

    /**
     * 凭证原因
     */
    CERTIFICATE("凭证不符合要求,请重新上传照片"),

    /**
     * 其他原因
     */
    OTHER("其他");

    private String description;

    private AfterHandleRemarkEnum(String description){
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
