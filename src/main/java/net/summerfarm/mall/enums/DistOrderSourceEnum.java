package net.summerfarm.mall.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.summerfarm.enums.OrderTypeEnum;
import net.summerfarm.ofc.client.enums.OfcOrderSourceEnum;
import net.summerfarm.wnc.client.enums.SourceEnum;

@Getter
@AllArgsConstructor
public enum DistOrderSourceEnum {
    /**
     * 城配(200-299)
     */
    XM_MALL(200, "鲜沐商城"),
    XM_AFTER_SALE(201, "鲜沐售后"),
    XM_SAMPLE_APPLY(202, "鲜沐样品申请"),
    XM_MALL_TIMING(203, "鲜沐商城省心送订单"),
    SAAS_MALL(210, "Saas商城"),
    SAAS_AFTER_SALE(211, "Saas售后"),
    POP_MALL(230, "POP商城"),
    POP_AFTER_SALE(231, "POP售后"),
    ;

    private final Integer code;
    private final String name;

    public static Integer getDistOrderAfterSaleSource(Integer businessLine) {
        if (MerchantEnum.BusinessLineEnum.POP.getCode().equals(businessLine)) {
            return DistOrderSourceEnum.POP_AFTER_SALE.getCode();
        } else {
            return DistOrderSourceEnum.XM_AFTER_SALE.getCode();
        }
    }

    public static Integer getDistOrderSource(Integer businessLine) {
        if (MerchantEnum.BusinessLineEnum.POP.getCode().equals(businessLine)) {
            return DistOrderSourceEnum.POP_MALL.getCode();
        } else {
            return DistOrderSourceEnum.XM_MALL.getCode();
        }
    }

    public static Integer getDistOrderSourceByOrderType (Integer orderType) {
        if (OrderTypeEnum.POP.getId().equals(orderType)) {
            return DistOrderSourceEnum.POP_MALL.getCode();
        } else if(OrderTypeEnum.TIMING.getId().equals(orderType)) {
            return DistOrderSourceEnum.XM_MALL_TIMING.getCode();
        } else {
            return DistOrderSourceEnum.XM_MALL.getCode();
        }
    }

    public static Integer getDistOrderAfterSaleSourceByOrderType (Integer orderType) {
        if (OrderTypeEnum.POP.getId().equals(orderType)) {
            return DistOrderSourceEnum.POP_AFTER_SALE.getCode();
        } else {
            return DistOrderSourceEnum.XM_AFTER_SALE.getCode();
        }
    }

    public static SourceEnum getSourceEnumByOrderType (Integer orderType) {
        if (OrderTypeEnum.POP.getId().equals(orderType)) {
            return SourceEnum.POP_MALL;
        } else if (OrderTypeEnum.TIMING.getId().equals(orderType)) {
            return SourceEnum.XM_MALL_TIMING;
        } else {
            return SourceEnum.XM_MALL;
        }
    }

    public static SourceEnum getSourceEnum(Integer businessLine) {
        if (MerchantEnum.BusinessLineEnum.POP.getCode().equals(businessLine)) {
            return SourceEnum.POP_MALL;
        } else {
            return SourceEnum.XM_MALL;
        }
    }

    public static OfcOrderSourceEnum getOfcSourceEnumAfterSaleByOrderType (Integer orderType) {
        if (OrderTypeEnum.POP.getId().equals(orderType)) {
            return OfcOrderSourceEnum.POP_AFTER_SALE;
        } else {
            return OfcOrderSourceEnum.XM_AFTER_SALE;
        }
    }

    public static OfcOrderSourceEnum getOfcSourceEnumByOrderType (Integer orderType) {
        if (OrderTypeEnum.TIMING.getId().equals(orderType)){
            return OfcOrderSourceEnum.XM_MALL_TIMING;
        } else if (OrderTypeEnum.POP.getId().equals(orderType)) {
            return OfcOrderSourceEnum.POP_MALL;
        } else {
            return OfcOrderSourceEnum.XM_MALL;
        }
    }
}