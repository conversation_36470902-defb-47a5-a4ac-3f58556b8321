package net.summerfarm.mall.enums;

/**
 * @Description:  共枚举
 * @Author: lzh
 * @Time: 2023/4/11 0010 09:59
 * @ModifyBy:
 */

public enum MarketRuleHistoryTypeEnum {
    /**
     * 0满返 1 满减
     */
    FULL_RETURN(0,"满返"),
    FULL_REDUCE(1,"满减")
    ;
    private Integer code;

    private String value;

    MarketRuleHistoryTypeEnum(Integer code, String value){
        this.code = code;
        this.value = value;
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static String getValueByKey(Integer key){
        for (MarketRuleHistoryTypeEnum c : MarketRuleHistoryTypeEnum.values()) {
            if (c.getCode() .equals(key)) {
                return c.value;
            }
        }
        return null;
    }

}
