package net.summerfarm.mall.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/21 22:31
 */
public class CouponEnum {

    public enum UsedStatus{
        NOT_USED,
        USED
    }

    public enum Status{
        UNDEFINED,
        EFFECTIVE,
        DELETE
    }

    public enum Type{
        /**
         * 0指固定时间间隔到期
         */
        SPECIFIED_TIME,
        /**
         * 1固定时间点到期
         */
        FIXED_POINT_IN_TIME
    }

    public enum CouponType{
        MERCHANT_SITUATION_COUPON("客情券"),
        MONTH_LIVE_COUPON("鲜沐专属购物券"),
        CATEGORY_COUPON("专属商品优惠券"),
        MERCHANT_SITUATION_CATEGORY_COUPON("鲜沐品类客情券")
        ;

        private String couponName;

        CouponType(String couponName) {
            this.couponName = couponName;
        }

        public String getCouponName() {
            return couponName;
        }
    }
    public enum CouponQuotaChangeType {
        SETTING(0, "设置"),
        DIVISION(1, "划分"),
        COUPON(2, "发券"),
        REWARD(3, "返还"),
        Expand(4, "品类拓宽发券"),
        ;

        private Integer code;
        private String changeType;

        CouponQuotaChangeType(Integer code, String changeType) {
            this.code = code;
            this.changeType = changeType;
        }

        public Integer getCode() {
            return code;
        }

        public String getChangeType() {
            return changeType;
        }

    }


    @AllArgsConstructor
    @Getter
    public enum Group{
        /**
         * 0-活动 7-消费返券 新改枚举 对应 平台活动券
         */
        ACTIVITY(0, "平台活动券"),
        /**
         * 1-售后 新改枚举 对应 售后补偿券
         */
        AFTER_SALE(1, "售后补偿券"),
        /**
         * 2-新人 新改枚举 对应 区域拉新券
         */
        NEW_PEOPLE(2, "区域拉新券"),
        /**
         * 3-权益 新改枚举 对应 会员权益券
         */
        RIGHTS_AND_INTERESTS(3, "会员权益券"),
        /**
         * 4-客情券 新改枚举 对应 销售客情券
         */
        MERCHANT_SITUATION(4, "销售客情券"),
        /**
         * 5-用户召回 新改枚举 对应 销售月活券
         */
        USER_RECALL(5, "销售月活券"),
        /**
         * 6-新品 12-品类召回 14-滞销补贴 15-临保补贴 新改枚举 对应 行业活动券
         */
        NEW_PRODUCTS(6, "行业活动券"),
        /*  *//**
         * 7-消费返券
         *//*
        CONSUMPTION_REBATE(7, "消费返券"),*/
        /**
         * 8-员工福利 新改枚举 对应 员工福利券
         */
        EMPLOYEE_WELFARE(8, "员工福利券"),
        /**
         * 9-囤货补贴 新改枚举 对应 销售囤货券
         */
        STOCKPILE_SUBSIDY(9, "销售囤货券"),
        /**
         * 10-降价补差 新改枚举 对应 区域活动券
         */
        MARK_DOWN_SUBSIDY(10, "区域活动券"),
        /**
         * 11-品类拉新 新改枚举 对应 销售品类券
         */
        PRODUCT_PULL_NEW(11, "销售品类券"),
        /**
         * 12-品类召回
         */
        //PRODUCT_RECALL(12, "品类召回"),
        /**
         * 13-流失风险 新改枚举 对应 销售现货券
         */
        LOSS_RISK(13, "销售现货券"),
        /**
         * 14-滞销补贴
         */
        //UNSALABLE_SUBSIDY(14, "滞销补贴"),
        /**
         * 15-临保补贴
         */
        //WILL_EXPIRED_SUBSIDY(15, "临保补贴"),
        /**
         * 16-特殊情况 新改枚举 对应 功能测试券
         */
        SPECIAL_CONDITION(16, "功能测试券"),
        /**
         * 17-平台补偿券
         */
        PLATFORM_EXPERIENCE_COMPENSATION(17, "平台补偿券"),
        /**
         * 18-配送补偿券
         */
        DISTRIBUTION_COMPENSATION(18, "配送补偿券"),
        /**
         * 19-区域召回券
         */
        REGIONAL_RECALL(19, "区域召回券"),
        /**
         * 20-市场活动劵
         */
        MARKET_ACTIVITY(20, "市场活动劵"),
        /**
         * 21-其他
         */
        OTHER(21, "其他");

        private int code;

        private String desc;

        public static String getGroupName(Integer code) {
            for (Group group : Group.values()) {
                if (Objects.equals(code, group.code)) {
                    return group.desc;
                }
            }
            return "";
        }
    }

    public enum AgioType{
        /**
         * 0未定义
         */
        UNDEFINED,
        /**
         * 1普通商品优惠券
         */
        ORDINARY,
        /**
         * 2普通运费优惠券
         */
        FREIGHT,
        /**
         * 3精准送优惠券
         */
        PRECISE_DELIVERY,
        /**
         * 4红包
         */
        RED_ENVELOPES,
        /**
         * 5商品兑换券
         */
        VOUCHER;
    }

    @AllArgsConstructor
    @Getter
    public enum CouponTypeEnum {

        UNDEFINED(0, "未定义"),

        NORMAL(1, "普通商品优惠券"),

        DELIVERY(2, "普通运费优惠券"),

        PRECISE_DELIVERY(3, "精准送优惠券"),

        RED_PACKET(4, "红包"),

        VOUCHER(5, "商品兑换券")
        ;

        private int code;

        private String value;
    }

    public enum ActivityScope{
        /**
         * 0 - 未定义
         */
        UNDEFINED,
        /**
         * 1 - 仅预售尾款
         */
        PRE_SALE,
        /**
         * 2 - 除预售,省心送,秒杀
         */
        OTHER,
        /**
         * 3 - 全部
         */
        ALL,
        /**
         * 4 - 仅省心送
         */
        TIMING_DELIVERY
    }

}
