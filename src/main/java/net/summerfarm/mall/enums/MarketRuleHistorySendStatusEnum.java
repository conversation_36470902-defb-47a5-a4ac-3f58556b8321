package net.summerfarm.mall.enums;

/**
 * @Description:  共枚举
 * @Author: lzh
 * @Time: 2023/4/11 0010 09:59
 * @ModifyBy:
 */

public enum MarketRuleHistorySendStatusEnum {
    /**
     * 满返发放状态  0：待发放 1：发放中  2：已发放 3：已撤回
     */
    TO_BE_ISSUED(0,"待发放"),
    UNDER_RELEASE(1,"发放中"),
    ISSUED(2,"已发放"),
    WITHDRAWN(3,"已撤回")
    ;
    private Integer code;

    private String value;

    MarketRuleHistorySendStatusEnum(Integer code, String value){
        this.code = code;
        this.value = value;
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static String getValueByKey(Integer key){
        for (MarketRuleHistorySendStatusEnum c : MarketRuleHistorySendStatusEnum.values()) {
            if (c.getCode() .equals(key)) {
                return c.value;
            }
        }
        return null;
    }

}
