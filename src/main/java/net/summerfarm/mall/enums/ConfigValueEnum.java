package net.summerfarm.mall.enums;

public enum ConfigValueEnum {


    COST_PRODUCTS_SENDER("cost_products_sender","普通商品调价发送钉钉消息指定人"),
    GMV_EXCLUDE_ADMIN("gmv_exclude_admin","gmv排除客户编码为121"),
    BD_ACHIEVEMENT_RECEIVE("bd_achievement_receive","bd业绩邮件接收人"),
    GMV_TARGET("gmv_target","gmv目标值"),
    EXPRIED_PRICE_RULE("expried_price_rule","临保活动价格规则"),
    FREE_DAY_AREANO("free_day_areano","免邮城市弹窗提示编号 0全部城市"),
    PANIC_BUY_ORDER_TIME("panic_buy_order_time","秒杀已购时间"),
    NEW_AREA_MONTH("new_area_month","新城市定义"),
    TIMING_CHECK_BEGIN_TIME("TIMING_CHECK_BEGIN_TIME", "省心送设置配送计划校验开始时间"),
    ;
    ConfigValueEnum(String key, String remark) {
        this.key = key;
        this.remark = remark;
    }

    private String key;

    private String remark;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
