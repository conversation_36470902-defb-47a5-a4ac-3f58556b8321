package net.summerfarm.mall.enums;

import lombok.AllArgsConstructor;

/**
 * redis key枚举
 * <AUTHOR>
 * @Date 2022/11/11 15:37
 **/
@AllArgsConstructor
public enum RedisKeyEnum {

    UPDATE_ONLINE_STOCK_BY_STORE_NO("update_online_stock_%s_%s_%s"),

    B2B_POP_UP_DISPLAYED("b2b_pop_up_displayed_%s"),

    AUTO_TRANSFER_TASK("auto_transfer_task_%s_%s_%s")
    ;


    private final String key;

    public String getKey() {
        return this.key;
    }

    public String getKey(Object... extraMsg) {
        return String.format(this.key, extraMsg);
    }

}
