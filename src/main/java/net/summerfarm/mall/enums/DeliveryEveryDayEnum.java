package net.summerfarm.mall.enums;

/**
 * @Description:  共枚举
 * @Author: lzh
 * @Time: 2023/4/11 0010 09:59
 * @ModifyBy:
 */

public enum DeliveryEveryDayEnum {
    /**
     * 日配的标识 0日配，1非日配
     */
    DAYCARE(0,"日配"),
    NON_DAYCARE(1,"非日配")
    ;
    private Integer code;

    private String value;

    DeliveryEveryDayEnum(Integer code, String value){
        this.code = code;
        this.value = value;
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static String getValueByKey(Integer key){
        for (DeliveryEveryDayEnum c : DeliveryEveryDayEnum.values()) {
            if (c.getCode() .equals(key)) {
                return c.value;
            }
        }
        return null;
    }

}
