package net.summerfarm.mall.enums;

public enum PaymentPayType {
    WECHAT_PAY("微信支付"),
    MINI_PROGRAM_PAY("小程序支付"),
    OFFLINE_PAY("线下支付"),
    OFFLINE_ZFB_PAY("线下支付宝"),
    RECHARGE_PAY("余额支付");

    private String payType;

    PaymentPayType(String payType){
        this.payType = payType;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }
}
