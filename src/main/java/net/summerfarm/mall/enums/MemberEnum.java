package net.summerfarm.mall.enums;
import java.math.BigDecimal;
import java.util.Objects;

public enum MemberEnum {
    ORDINARY(0,BigDecimal.valueOf(0),0,0,0),
    ELEMENTARY(1,BigDecimal.valueOf(500),0,1,0),
    INTERMEDIATE(2,BigDecimal.valueOf(2000),50,2,1),
    ADVANCED(3,BigDecimal.valueOf(5000),100,3,3);

    private Integer grade; //等级
    private BigDecimal amount; //达到当前等级所需金额
    private Integer refundAmount; //极速退款额度
    private Integer freePostage; //免邮券数量
    private Integer outTimeOrderTimes; //超时加单次数
//    private Integer accurateTimes; //精准送券数量

    /**
     * 根据等级得到免邮次数
     * @param grade
     * @return
     */
    public static Integer getFreePostageByGrade(Integer grade){
        Integer freePostage = 0;
        for (MemberEnum memberEnum:MemberEnum.values()){
            if (Objects.equals(grade, memberEnum.getGrade())){
                freePostage = memberEnum.getFreePostage();
                break;
            }
        }
        return  freePostage;
    }

    /**
     * 根据等级得到超时加单次数
     * @param grade
     * @return
     */
    public static Integer getOutTimeOrderTimes(Integer grade){
        Integer outTimeOrderTimes = 0;
        for (MemberEnum memberEnum:MemberEnum.values()){
            if (Objects.equals(grade, memberEnum.getGrade())){
                outTimeOrderTimes = memberEnum.getOutTimeOrderTimes();
                break;
            }
        }
        return  outTimeOrderTimes;
    }

    /**
     * 根据等级得到额度
     * @param grade
     * @return
     */
    public static Integer getRefundAmountByGrade(Integer grade){
        Integer refundAmount = 0;
        for (MemberEnum memberEnum:MemberEnum.values()){
            if (Objects.equals(grade, memberEnum.getGrade())){
                refundAmount = memberEnum.getRefundAmount();
                break;
            }
        }
        return  refundAmount;
    }

    /**
     * 根据积分得到当前等级
     * @param amount
     * @return
     */
    public static Integer getGradeByAmount(BigDecimal amount){
        if (amount == null){ return 0;}
        Integer grade = 0;
        for (MemberEnum memberEnum:MemberEnum.values()){
            if (amount.compareTo(memberEnum.getAmount()) > -1){
                grade =  memberEnum.getGrade();
            }
        }
        return  grade;
    }

    public static BigDecimal getAmountByGrade(Integer grade){
        BigDecimal amount = BigDecimal.valueOf(0);
        for (MemberEnum memberEnum:MemberEnum.values()){
            if (Objects.equals(grade, memberEnum.getGrade())){
                amount = memberEnum.getAmount();
                break;
            }
        }
        return amount;
    }

    MemberEnum(Integer grade, BigDecimal amount, Integer refundAmount, Integer freePostage, Integer outTimeOrderTimes){
        this.grade=grade;
        this.amount=amount;
        this.refundAmount=refundAmount;
        this.freePostage=freePostage;
        this.outTimeOrderTimes = outTimeOrderTimes;
    }

    public Integer getGrade() {
        return grade;
    }

    public void setGrade(Integer grade) {
        this.grade = grade;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Integer getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(Integer refundAmount) {
        this.refundAmount = refundAmount;
    }

    public Integer getFreePostage() {
        return freePostage;
    }

    public void setFreePostage(Integer freePostage) {
        this.freePostage = freePostage;
    }

    public Integer getOutTimeOrderTimes() {
        return outTimeOrderTimes;
    }

    public void setOutTimeOrderTimes(Integer outTimeOrderTimes) {
        this.outTimeOrderTimes = outTimeOrderTimes;
    }
}
