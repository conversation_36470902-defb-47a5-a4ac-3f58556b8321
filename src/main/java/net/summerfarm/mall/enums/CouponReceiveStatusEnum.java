package net.summerfarm.mall.enums;

/**
 * @Description:  卡劵领取类型
 * @Author: lzh
 * @Time: 2023/4/11 0010 09:59
 * @ModifyBy:
 */

public enum CouponReceiveStatusEnum {

    IMMEDIATE_CLAIM(1,"立即领取"),
    USED(2,"去使用"),
    SOLD_OUT(3,"已抢光"),
    ALREADY_RECEIVED(4,"已领取")
    ;
    private Integer code;

    private String value;

    CouponReceiveStatusEnum(Integer code, String value){
        this.code = code;
        this.value = value;
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static String getValueByKey(Integer key){
        for (CouponReceiveStatusEnum c : CouponReceiveStatusEnum.values()) {
            if (c.getCode() .equals(key)) {
                return c.value;
            }
        }
        return null;
    }

}
