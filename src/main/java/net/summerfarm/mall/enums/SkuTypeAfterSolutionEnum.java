package net.summerfarm.mall.enums;

/**
 * <AUTHOR>
 * @Description 售后处理SKU类型枚举类
 * @date 2022/8/5 15:05
 * @Version 1.0
 */
public enum SkuTypeAfterSolutionEnum {
    SELF_OPERATED_GOODS(0,"自营商品"),
    AGENT_PRODUCT(1,"代仓商品");

    private Integer type;
    private String description;

    private SkuTypeAfterSolutionEnum(Integer type, String description) {
        this.type = type;
        this.description = description;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
