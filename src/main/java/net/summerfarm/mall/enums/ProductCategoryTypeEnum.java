package net.summerfarm.mall.enums;

import lombok.Getter;

/**
 * category.type
 * <AUTHOR>
 */
@Getter
public enum ProductCategoryTypeEnum {

    /**
     * all
     */
    ALL(1,"all"),
    /**
     * 乳制品
     */
    DAIRY(2,"乳制品"),
    /**
     * 非乳制品
     */
    NOT_DAIRY(3,"非乳制品"),
    /**
     * 水果
     */
    FRUIT(4,"水果"),
    ;

    private final int code;
    private final String des;

    ProductCategoryTypeEnum(Integer code, String des) {
        this.code = code;
        this.des = des;
    }
}
