package net.summerfarm.mall.enums;

/**
 * <AUTHOR>
 * @descripton
 * @date 2024/7/22 18:23
 */
public interface MerchantEnum {

    public enum OperateStatusEnum {

        /**
         * 正常
         */
        NORMAL(0, "正常"),

        /**
         * 倒闭
         */
        CLOSED(1, "倒闭"),

        /**
         * 待提交核验
         */
        PENDING_SUBMISSION(2, "待提交核验"),

        /**
         * 待核验
         */
        PENDING_VERIFICATION(3, "待核验"),

        /**
         * 核验拒绝
         */
        VERIFICATION_REJECTED(4, "核验拒绝");

        private final Integer code;
        private final String description;

        OperateStatusEnum(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        // 可以添加一个根据code获取枚举值的方法（如果需要）
        public static OperateStatusEnum getByCode(int code) {
            for (OperateStatusEnum status : OperateStatusEnum.values()) {
                if (status.getCode() == code) {
                    return status;
                }
            }
            throw new IllegalArgumentException("No matching constant for [" + code + "]");
        }
    }



    public enum BusinessLineEnum {

        /**
         * 鲜沐
         */
        XM(0, "鲜沐"),

        /**
         * pop
         */
        POP(1, "pop");

        private final Integer code;
        private final String description;

        BusinessLineEnum(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

    }

}
