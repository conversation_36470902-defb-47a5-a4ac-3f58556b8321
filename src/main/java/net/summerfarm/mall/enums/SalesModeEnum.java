package net.summerfarm.mall.enums;

/**
 * @Description:  sku限购类型
 * @Author: lzh
 * @Time: 2023/4/11 0010 09:59
 * @ModifyBy:
 */

public enum SalesModeEnum {
    /**
     * 如 是否日限购 0 否 2 是
     */
    NO(0,"否"),
    YES(2,"是")
    ;
    private Integer code;

    private String value;

    SalesModeEnum(Integer code, String value){
        this.code = code;
        this.value = value;
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static String getValueByKey(Integer key){
        for (SalesModeEnum c : SalesModeEnum.values()) {
            if (c.getCode() .equals(key)) {
                return c.value;
            }
        }
        return null;
    }

}
