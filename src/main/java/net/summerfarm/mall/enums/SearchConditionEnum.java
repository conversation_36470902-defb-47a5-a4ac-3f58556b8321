package net.summerfarm.mall.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
public interface SearchConditionEnum{

    @Getter
    @AllArgsConstructor
     enum SearchConditionTypeEnum {
        PRODUCT_FEATURE (1, "商品特色"),
        FIRST_FRONT_CATEGORY (2, "类型"),
        SECOND_FRONT_CATEGORY (3, "品类"),
        BRAND (4, "标品品牌"),
        FRUIT_VARIETY (5, "鲜果品种"),
        PRICE_RANGE (6, "价格区间"),
        FRUIT_GRADE (7, "鲜果等级"),
//        FAT_CONTENT (8, "乳脂含量"),
//        ORIGIN (9, "产地"),
//        STORAGE_AREA (10, "储藏区域"),
        ;
        private Integer type;
        private String description;

        public static SearchConditionTypeEnum of(Integer t) {
            for (SearchConditionTypeEnum type : SearchConditionTypeEnum.values ()) {
                if (type.getType ().equals (t)) {
                    return type;
                }
            }
            return null;
        }
    }

    @Getter
    @AllArgsConstructor
     enum ProductFeatureEnum {
        TIMING (1, "省心送"),
        NEW_PRODUCTS(2, "新品"),
        NEAR_EXPIRATION(3, "临保"),
        ;
        private Integer key;
        private String name;

        public static ProductFeatureEnum of(Integer t) {
            for (ProductFeatureEnum e : ProductFeatureEnum.values ()) {
                if (e.getKey ().equals (t)) {
                    return e;
                }
            }
            return null;
        }
    }
}
