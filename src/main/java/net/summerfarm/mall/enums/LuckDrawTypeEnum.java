package net.summerfarm.mall.enums;

/**
 * @Description:  红包雨、每日抽奖活动
 * @Author: lzh
 * @Time: 2023/4/11 0010 09:59
 * @ModifyBy:
 */

public enum LuckDrawTypeEnum {

    RED_ENVELOPE_RAIN(1,"红包雨"),
    DAILY_DRAW(2,"每日抽奖")
    ;
    private Integer code;

    private String value;

    LuckDrawTypeEnum(Integer code, String value){
        this.code = code;
        this.value = value;
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static String getValueByKey(Integer key){
        for (LuckDrawTypeEnum c : LuckDrawTypeEnum.values()) {
            if (c.getCode() .equals(key)) {
                return c.value;
            }
        }
        return null;
    }

}
