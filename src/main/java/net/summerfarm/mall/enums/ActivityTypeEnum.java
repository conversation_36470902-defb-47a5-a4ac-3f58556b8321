package net.summerfarm.mall.enums;

/**
 * 活动类型枚举
 * <AUTHOR> href="mailto:<EMAIL>>黄棽</a>
 * @since 2022-03-14
 */
public enum ActivityTypeEnum {

    /**
     * 临保活动
     */
    EXPRIED_ACTIVITY(1, "临保活动"),

    /**
     * 普通活动
     */
    NORMAL_ACTIVITY(0, "普通活动");

    /**
     * 活动类型
     */
    private final int type;

    /**
     * 活动类型描述
     */
    private final String description;

    ActivityTypeEnum(int type, String description) {
        this.type = type;
        this.description = description;
    }

    public int getType() {
        return type;
    }

    public String getDescription() {
        return description;
    }
}
