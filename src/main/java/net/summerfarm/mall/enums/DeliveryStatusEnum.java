package net.summerfarm.mall.enums;

/**
 * @Description: 订单配送状态枚举类
 * @Date: 2022/5/14 15:42
 * @Author: huang<PERSON><PERSON><PERSON>ong
 */
public enum DeliveryStatusEnum {

    TO_BE_PICKED(0,"待捡货"),
    IN_DELIVERY(1,"配送中"),
    COMPLETE_DELIVERY(2,"配送完成"),
    TO_BE_WIRED(3,"待排线"),
    NOT_YET(4,"暂无"),
    INTERCEPT(5,"已拦截")
    ;
    private Integer status;
    private String statusDesc;

    DeliveryStatusEnum(Integer status, String statusDesc) {
        this.status = status;
        this.statusDesc = statusDesc;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getStatusDesc() {
        return statusDesc;
    }

    public void setStatusDesc(String statusDesc) {
        this.statusDesc = statusDesc;
    }
}
