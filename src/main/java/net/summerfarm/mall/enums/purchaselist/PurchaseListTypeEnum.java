package net.summerfarm.mall.enums.purchaselist;

/**
 * <AUTHOR>
 */
public enum PurchaseListTypeEnum {
    /**
     * 鲜沐sku
     */
    DEFAULT(0, "鲜沐sku"),

    /**
     * 非平台SKU
     */
    CUSTOM_PRODUCT(1, "非平台");

    /**
     * sku 类型
     */
    private final byte type;

    /**
     * 活动类型描述
     */
    private final String description;

    PurchaseListTypeEnum(int type, String description) {
        this.type = (byte) type;
        this.description = description;
    }

    public byte getType() {
        return type;
    }

    public String getDescription() {
        return description;
    }
}
