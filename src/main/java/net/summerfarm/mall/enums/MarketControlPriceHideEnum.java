package net.summerfarm.mall.enums;

/**
 * @Description:  共枚举
 * @Author: lzh
 * @Time: 2023/4/11 0010 09:59
 * @ModifyBy:
 */

public enum MarketControlPriceHideEnum {
    /**
     * 控价品是否隐藏实付价 2-隐藏  1-不隐藏
     */
    UNHIDE(1,"不隐藏"),
    HIDE(2,"隐藏")
    ;
    private Integer code;

    private String value;

    MarketControlPriceHideEnum(Integer code, String value){
        this.code = code;
        this.value = value;
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static String getValueByKey(Integer key){
        for (MarketControlPriceHideEnum c : MarketControlPriceHideEnum.values()) {
            if (c.getCode() .equals(key)) {
                return c.value;
            }
        }
        return null;
    }

}
