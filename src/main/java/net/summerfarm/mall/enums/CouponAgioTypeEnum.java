package net.summerfarm.mall.enums;

/**
 * @Description:  共枚举
 * @Author: lzh
 * @Time: 2023/4/11 0010 09:59
 * @ModifyBy:
 */

public enum CouponAgioTypeEnum {

    GENERAL_GOODS(1,"普通商品优惠券"),
    GENERAL_FREIGHT(2,"普通运费优惠券"),
    PRECISE_DELIVERY(3,"精准送优惠券"),
    RED_PACKET(4,"红包"),
    COMMODITY_EXCHANGE(5,"商品兑换券"),
    ;
    private Integer code;

    private String value;

    CouponAgioTypeEnum(Integer code, String value){
        this.code = code;
        this.value = value;
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static String getValueByKey(Integer key){
        for (CouponAgioTypeEnum c : CouponAgioTypeEnum.values()) {
            if (c.getCode() .equals(key)) {
                return c.value;
            }
        }
        return null;
    }

}
