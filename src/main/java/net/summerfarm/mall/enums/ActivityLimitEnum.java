package net.summerfarm.mall.enums;

/**
 * @Description:  sku限购类型
 * @Author: lzh
 * @Time: 2023/4/11 0010 09:59
 * @ModifyBy:
 */

public enum ActivityLimitEnum {
    /**
     *
     */
    NUMBER_LIMIT(1,"件数"),
    DAY_LIMIT(2,"日限购")
    ;
    private Integer code;

    private String value;

    ActivityLimitEnum(Integer code, String value){
        this.code = code;
        this.value = value;
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static String getValueByKey(Integer key){
        for (ActivityLimitEnum c : ActivityLimitEnum.values()) {
            if (c.getCode() .equals(key)) {
                return c.value;
            }
        }
        return null;
    }

}
