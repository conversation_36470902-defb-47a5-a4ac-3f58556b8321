package net.summerfarm.mall.enums;

public enum ReviewStatus {
    APPROVED(0,"审核通过"),
    REVIEWING(1,"审核中"),
    REVIEW_NOT_PASS(2,"审核未通过"),
    PULLED_BLACK(3,"账号被拉黑"),

    STORE_CLOSURE(4,"门店已注销")
    ;

    private int islock;
    private String state;

    ReviewStatus(int islock, String state){
        this.islock=islock;
        this.state=state;
    }

    public int getIslock() {
        return islock;
    }

    public void setIslock(int islock) {
        this.islock = islock;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }
}
