package net.summerfarm.mall.enums;

/**
 * @Description:  共枚举
 * @Author: lzh
 * @Time: 2023/4/11 0010 09:59
 * @ModifyBy:
 */

public enum FenceStatusEnum {
    /**
     *
     */
    UNOPENED(-1,"未开放"),
    OPEN(0,"开放"),
    PAUSE(3,"暂停"),
    ;
    private Integer code;

    private String value;

    FenceStatusEnum(Integer code, String value){
        this.code = code;
        this.value = value;
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static String getValueByKey(Integer key){
        for (FenceStatusEnum c : FenceStatusEnum.values()) {
            if (c.getCode() .equals(key)) {
                return c.value;
            }
        }
        return null;
    }

}
