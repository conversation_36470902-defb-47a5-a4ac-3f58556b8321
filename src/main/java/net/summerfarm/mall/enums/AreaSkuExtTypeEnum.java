package net.summerfarm.mall.enums;

/**
 * @Classname AreaSkuEnum
 * @Description 城市售卖信息枚举
 * @Date 2021/11/15 13:42
 * @Created by hx
 */
public enum AreaSkuExtTypeEnum {
    EXT_TYPE_ROUTINE(0,"常规"),
    EXT_TYPE_ACTIVITY(1,"活动"),
    EXT_TYPE_EXPIRED(2,"临保")
    ;


    private Integer extType;
    private String desc;

    AreaSkuExtTypeEnum(Integer extType, String desc) {
        this.extType = extType;
        this.desc = desc;
    }

    public Integer getExtType() {
        return extType;
    }

    public void setExtType(Integer extType) {
        this.extType = extType;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}