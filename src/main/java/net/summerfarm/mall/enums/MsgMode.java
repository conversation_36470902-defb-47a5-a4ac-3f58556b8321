package net.summerfarm.mall.enums;

/**
 * @Package: com.manageSystem.common.message
 * @Description: 消息推送渠道
 * @author: <EMAIL>
 * @Date: 2016/8/24
 */
public enum MsgMode {
    WECHAT_MSG(1,"微信公众号"),
    MSG_CENTER(2,"消息中心");

    MsgMode() {
    }

    MsgMode(int id, String modeName) {
        this.id = id;
        this.modeName = modeName;
    }

    private int id;

    private String modeName;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getModeName() {
        return modeName;
    }

    public void setModeName(String modeName) {
        this.modeName = modeName;
    }


}
