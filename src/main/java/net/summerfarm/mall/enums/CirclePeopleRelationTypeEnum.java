package net.summerfarm.mall.enums;

public enum CirclePeopleRelationTypeEnum {
    PANIC_TYPE(0,"秒杀"),
    SELECTION_TYPE(1,"严选"),
    BANNER_SHOW_TYPE(2,"BANNER"),
    COUPON_TYPE(3,"优惠劵");

    private Integer type;
    private String  typeDesc;



    CirclePeopleRelationTypeEnum(Integer type, String typeDesc) {
        this.type = type;
        this.typeDesc = typeDesc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getTypeDesc() {
        return typeDesc;
    }

    public void setTypeDesc(String typeDesc) {
        this.typeDesc = typeDesc;
    }
}
