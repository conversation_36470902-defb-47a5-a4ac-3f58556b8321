package net.summerfarm.mall.enums;

import com.google.common.collect.Lists;
import net.summerfarm.mall.enums.market.PlatformEnum;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */

public enum OrderPreferentialTypeEnum {
    /**
     * 0-活动
     */
    ACTIVITY,
    /**
     * 1-组合包
     */
    SUIT,
    /**
     * 2-满减
     */
    FULL_REDUCE,
    /**
     * 3- 阶梯价
     */
    LADDER,
    /**
     * 4-黄金卡
     */
    DISCOUNT_CARD,
    /**
     * 5-预售
     */
    ADVANCE,
    /**
     * 6-秒杀
     */
    PANIC_BUY,
    /**
     * 7-搭配购
     */
    COLLOCATION,
    /**
     * 8-多人拼团
     */
    PARTNERSHIP_BUY,
    /**
     * 9-优惠券
     */
    COUPON,
    /**
     * 10-赠品
     */
    GIFT,
    /**
     * 11-代仓
     */
    AGENT,
    /**
     * 12-预付
     */
    PREPAY,
    /**
     * 13-红包
     */
    RED_PACKET,
    /**
     * 14-拓展活动
     */
    EXPAND_ACTIVITY,
    /**
     * 15-换购活动
     */
    EXCHANGE_BUY,

    /**
     * 16-直播专享价
     */
    LIVE_BROADCAST,

    /**
     * 17-直播、商城同享价
     */
    MALL_AND_LIVE,

    /**
     * 18-精准送优惠券
     */
    ACCURATE_COUPON,

    /**
     * 19-运费优惠券
     */
    DELIVERY_COUPON,

    /**
     * 20-满返
     */
    FULL_RETURN,

    ;

    /**
     * 特价活动优惠类型区分
     * @param platform
     * @return
     */
    public static OrderPreferentialTypeEnum getActivityPreference(Integer platform) {
        if (Objects.equals(platform, PlatformEnum.LIVE_BROADCAST.getCode())) {
            return LIVE_BROADCAST;
        }
        if (Objects.equals(platform, PlatformEnum.MALL_AND_LIVE.getCode())) {
            return MALL_AND_LIVE;
        }
        return ACTIVITY;
    }

    /**
     * 是否是商品加格优惠「0-活动、3-阶梯价、16-直播专享价、17-直播、商城同享价」
     * @param type 优惠类型
     * @return 是、否
     */
    public static boolean isSkuPriceDiscount(int type) {
        return type == ACTIVITY.ordinal()
                || type == LADDER.ordinal()
                || type == LIVE_BROADCAST.ordinal()
                || type == MALL_AND_LIVE.ordinal();
    }

    /**
     * 是否是活动优惠「2-满减、14-拓展活动、15-换购活动」
     * @param type 优惠类型
     * @return 是否是活动优惠
     */
    public static boolean isOrderPriceDiscount(Integer type) {
        return type == FULL_REDUCE.ordinal()
                || type == EXPAND_ACTIVITY.ordinal()
                || type == EXCHANGE_BUY.ordinal();
    }

    /**
     * 是否是运费「18-精准送优惠券、19-运费优惠券」
     * @param type 优惠类型
     * @return 是否是活动优惠
     */
    public static boolean isDeliveryDiscount(Integer type) {
        return type == ACCURATE_COUPON.ordinal()
                || type == DELIVERY_COUPON.ordinal();
    }

    /**
     * 是否需要合并优惠金额的优惠
     * @return
     */
    public static boolean isNotNeedMergeDiscount(Integer type) {
        List<Integer> notNeedList = Lists.newArrayList(ACTIVITY.ordinal(), FULL_REDUCE.ordinal(), DISCOUNT_CARD.ordinal(),
                COUPON.ordinal(), RED_PACKET.ordinal(), LIVE_BROADCAST.ordinal(), MALL_AND_LIVE.ordinal(),
                ACCURATE_COUPON.ordinal(), DELIVERY_COUPON.ordinal());
        return notNeedList.contains(type);
    }

    /**
     * 是否是优惠券
     * @param type 优惠类型
     * @return 是否是优惠券
     */
    public static boolean isCoupon(Integer type) {
        return type == OrderPreferentialTypeEnum.COUPON.ordinal()
                || type == OrderPreferentialTypeEnum.RED_PACKET.ordinal()
                || type == OrderPreferentialTypeEnum.DELIVERY_COUPON.ordinal()
                || type == OrderPreferentialTypeEnum.ACCURATE_COUPON.ordinal();
    }
}
