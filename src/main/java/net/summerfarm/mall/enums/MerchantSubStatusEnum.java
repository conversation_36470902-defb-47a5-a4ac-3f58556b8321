package net.summerfarm.mall.enums;

/**
 * @Description:  门店注销申请记录状态
 * @Author: lzh
 * @Time: 2023/4/20 0010 09:59
 * @ModifyBy:
 */

public enum MerchantSubStatusEnum {

    AUDIT(0,"待审核"),
    APPROVE(1,"审核通过"),
    CANCELLED(2,"已注销")
    ;
    private Integer code;

    private String value;

    MerchantSubStatusEnum(Integer code, String value){
        this.code = code;
        this.value = value;
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static String getValueByKey(Integer key){
        for (MerchantSubStatusEnum c : MerchantSubStatusEnum.values()) {
            if (c.getCode() .equals(key)) {
                return c.value;
            }
        }
        return null;
    }

}
