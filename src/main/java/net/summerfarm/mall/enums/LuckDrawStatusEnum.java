package net.summerfarm.mall.enums;

/**
 * @Description:  红包雨、每日抽奖活动
 * @Author: lzh
 * @Time: 2023/4/11 0010 09:59
 * @ModifyBy:
 */

public enum LuckDrawStatusEnum {

    NOT_START(1,"未开始"),
    PREHEATING(2,"预热中"),
    UNDER_WAY(3,"进行中"),
    FINISHED(4,"已结束")
    ;
    private Integer code;

    private String value;

    LuckDrawStatusEnum(Integer code, String value){
        this.code = code;
        this.value = value;
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static String getValueByKey(Integer key){
        for (LuckDrawStatusEnum c : LuckDrawStatusEnum.values()) {
            if (c.getCode() .equals(key)) {
                return c.value;
            }
        }
        return null;
    }

}
