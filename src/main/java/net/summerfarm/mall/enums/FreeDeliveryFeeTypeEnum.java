package net.summerfarm.mall.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: <EMAIL>
 * @create: 2022/7/13
 */
@AllArgsConstructor
public enum FreeDeliveryFeeTypeEnum {

    ALL(0, "全部"),
    AGENT(1, "代仓"),
    SELF_SUPPORT(2, "自营"),
    DIARY_PRODUCT(3, "乳制品"),
    NON_DIARY_PRODUCT(4, "非乳制品");

    @Getter
    private int type;

    @Getter
    private String desc;
}
