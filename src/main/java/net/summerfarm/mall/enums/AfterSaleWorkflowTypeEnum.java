package net.summerfarm.mall.enums;

public enum AfterSaleWorkflowTypeEnum {

    NOT_NEED(0,"未到货"),
    BROKEN(1,"已到货"),
    EXCHANGE(2,"补发"),
    INTERCEPT(3,"拦截"),
    ADVANCE(4,"预售省心送未到货");
    private int type;
    private String value;


    AfterSaleWorkflowTypeEnum(int type, String value){
        this.type = type;
        this.value = value;
    }
    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
