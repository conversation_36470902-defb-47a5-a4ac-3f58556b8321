package net.summerfarm.mall.enums;

/**
 * @Description:  卡劵领取类型
 * @Author: lzh
 * @Time: 2023/4/11 0010 09:59
 * @ModifyBy:
 */

public enum CouponReceiveTypeEnum {

    GRANT(0,"发放（人工）"),
    RECEIVE(1,"手动领取"),
    LUCKY_DRAW(2,"抽奖活动 （被动）"),
    AUTO_RECEIVE(3,"自动领取（新人注册/推荐好友下单）"),
    FULL_RETURN(4,"满返活动（被动）"),
    OTHER(5,"其他（售后等）"),
    ;
    private Integer code;

    private String value;

    CouponReceiveTypeEnum(Integer code, String value){
        this.code = code;
        this.value = value;
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static String getValueByKey(Integer key){
        for (CouponReceiveTypeEnum c : CouponReceiveTypeEnum.values()) {
            if (c.getCode() .equals(key)) {
                return c.value;
            }
        }
        return null;
    }

}
