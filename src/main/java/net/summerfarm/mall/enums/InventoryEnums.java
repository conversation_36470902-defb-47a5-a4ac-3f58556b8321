package net.summerfarm.mall.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/10/16  17:11
 */
public class InventoryEnums {
    /**
     * 商品二级性质，1 自营-代销不入仓、2 自营-代销入仓、3 自营-经销、4 代仓-代仓'
     */
    @Getter
    @AllArgsConstructor
    public enum SubType {
        /**
         * 自营-代销不入仓
         */
        SELF_NOT_INTO_WAREHOUSE(1),
        /**
         * 自营-代销入仓
         */
        SELF_INTO_WAREHOUSE(2),
        /**
         * 自营-经销
         */
        SELF_DISTRIBUTION(3),
        /**
         * 代仓-代仓
         */
        PROXY_AGENT(4),

        /**
         * POP
         */
        POP(5);
        private final Integer subType;
    }
}
