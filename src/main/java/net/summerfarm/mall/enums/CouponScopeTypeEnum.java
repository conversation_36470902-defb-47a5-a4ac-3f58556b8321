package net.summerfarm.mall.enums;

/**
 * @Description:  共枚举
 * @Author: lzh
 * @Time: 2023/4/11 0010 09:59
 * @ModifyBy:
 */

public enum CouponScopeTypeEnum {
    /**
     * 范围类型，1 人群包，2 运营城市，3 运营大区，4 品牌客户
     */
    CROWD(1,"人群包"),
    CITY(2,"运营城市"),
    REGION(3,"运营大区"),
    ADMIN(4,"品牌客户"),
    ;
    private Integer code;

    private String value;

    CouponScopeTypeEnum(Integer code, String value){
        this.code = code;
        this.value = value;
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static String getValueByKey(Integer key){
        for (CouponScopeTypeEnum c : CouponScopeTypeEnum.values()) {
            if (c.getCode() .equals(key)) {
                return c.value;
            }
        }
        return null;
    }

}
