package net.summerfarm.mall.enums;

/**
 * <AUTHOR>
 * @Date 2021/9/8
 * @Description:
 */
public enum SalesEnum {
    add(1,"销量加"),reduce(2,"销量减");
    private Integer type;
    private String msg;

    SalesEnum() {
    }

    SalesEnum(Integer type, String msg) {
        this.type = type;
        this.msg = msg;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
