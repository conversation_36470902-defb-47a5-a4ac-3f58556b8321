package net.summerfarm.mall.enums;

/**
 * <AUTHOR>
 * @date 2022-06-14
 * 预售省心送退款情况描述
 */
public enum  PreTimingRefundTypeEnum {

    ALL(0,"订金+尾款"),
    FINAL_MONEY(1,"只退尾款"),
    DEPOSIT(2,"只退订金");

    private int type;
    private String value;


    PreTimingRefundTypeEnum(int type, String value){
        this.type = type;
        this.value = value;
    }
    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
