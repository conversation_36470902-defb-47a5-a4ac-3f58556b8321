package net.summerfarm.mall.enums;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/5/23 16:36
 */
public class B2BEnum {

    public enum PopupStatus{
        /**
         * 0 未弹出
         */
        INIT,
        /**
         * 1 已弹出
         */
        END
    }

    public enum CoverStatus{
        /**
         * 0 未弹出
         */
        INIT,
        /**
         * 1 已弹出
         */
        END
    }
    public enum CouponStatus{
        /**
         * 0 未发送
         */
        INIT,
        /**
         * 1 发送
         */
        END
    }

    public enum B2BStatus{
        /**
         * 预录入
         */
        INIT,
        /**
         * 1 已关闭
         */
        CLOSE,
        /**
         *  2已完成
         */
        END
    }
}
