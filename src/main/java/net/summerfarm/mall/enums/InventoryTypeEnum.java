package net.summerfarm.mall.enums;

import lombok.Data;

/**
 * SKU类型枚举类
 * <AUTHOR>
 */

public enum InventoryTypeEnum {

    /**
     * 自营商品
     */
    SELF_OPERATED_GOODS(0,"自营"),

    /**
     * 代理商品
     */
    AGENT_PRODUCT(1,"代仓");

    private Integer type;
    private String description;

    private InventoryTypeEnum(Integer type, String description){
        this.type = type;
        this.description = description;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
