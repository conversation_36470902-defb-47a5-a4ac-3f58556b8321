//package net.summerfarm.mall.enums;
//
///**
// * @Description:  门店注销申请记录状态
// * @Author: lzh
// * @Time: 2023/4/20 0010 09:59
// * @ModifyBy:
// */
//
//@Deprecated
//public enum MerchantIsLockEnum {
//
//    APPROVE(0,"审核通过"),
//    AUDIT(1,"审核中"),
//    FAILED_AUDIT(2,"审核未通过"),
//    BLACK(3,"账号被拉黑"),
//    CANCELLED(4,"已注销")
//    ;
//    private Integer code;
//
//    private String value;
//
//    MerchantIsLockEnum(Integer code, String value){
//        this.code = code;
//        this.value = value;
//    }
//
//
//    public Integer getCode() {
//        return code;
//    }
//
//    public void setCode(Integer code) {
//        this.code = code;
//    }
//
//    public String getValue() {
//        return value;
//    }
//
//    public void setValue(String value) {
//        this.value = value;
//    }
//
//    public static String getValueByKey(Integer key){
//        for (MerchantIsLockEnum c : MerchantIsLockEnum.values()) {
//            if (c.getCode() .equals(key)) {
//                return c.value;
//            }
//        }
//        return null;
//    }
//
//}
