package net.summerfarm.mall.enums;

public enum AfterSaleWorkflowEnum {
    HANDLE(0,"审核"),
    AUDIT(1,"审批");

    private int type;
    private String value;


    AfterSaleWorkflowEnum(int type, String value){
        this.type = type;
        this.value = value;
    }
    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
