package net.summerfarm.mall.enums;

public enum AfterSaleDeliveryedEnum {

    NOT_NEED(0,"未到货"),
    BROKEN(1,"已到货"),

    //发起售后时用于判断，无其他含义
    ALL(2,"全部"),
    CANNOT(3,"任何类型都不能发起，前端进行弹窗提示");
    private Integer type;
    private String value;


    AfterSaleDeliveryedEnum(Integer type, String value){
        this.type = type;
        this.value = value;
    }
    public Integer getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
