package net.summerfarm.mall.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.xianmu.common.enums.base.Enum2Args;

@Getter
@AllArgsConstructor
public enum FulfillmentWayEnum implements Enum2Args {

    /**
     * 配送方式： 0干配 1自提 2干线 3快递
     */
    DELIVERY(0, "干配"),
    SELF_PICKUP(1, "自提"),
    TRUNK(2, "干线"),
    EXPRESS(3, "快递"),
    ;

    private Integer value;
    /**
     * 描述
     */
    private String content;

}
