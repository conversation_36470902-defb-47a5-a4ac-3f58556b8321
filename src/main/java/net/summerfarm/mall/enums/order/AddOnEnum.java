package net.summerfarm.mall.enums.order;

import com.google.common.collect.Lists;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import net.summerfarm.mall.enums.InventoryEnums.SubType;

/**
 * 凑单枚举 凑单用字段（需要用于列表页搜索接口）,0 搜全部商品, 1 只搜代销不入仓商品, 2 搜非代销不入仓的商品
 *
 * @author: <EMAIL>
 * @create: 2023/10/27
 */
@Getter
@AllArgsConstructor
public enum AddOnEnum {

    ALL(0, "全部商品"),

    SALE_NOT_IN_STORE(1, "代销不入仓商品"),

    NO_SALE_NOT_IN_STORE(2, "非代销不入仓商品"),
    ;

    private int code;

    private String desc;

    /**
     * addOn转换成subTypeList
     *
     * @param code
     * @return
     */
    public static List<Integer> getSubTypeList(Integer code) {
        if (code == null) {
            return Lists.newArrayList();
        }
        switch (code) {
            case 0:
                return Lists.newArrayList();
            case 1:
                return Lists.newArrayList(SubType.SELF_NOT_INTO_WAREHOUSE.getSubType());
            case 2:
                return Lists.newArrayList(SubType.SELF_INTO_WAREHOUSE.getSubType(),
                        SubType.SELF_DISTRIBUTION.getSubType(), SubType.PROXY_AGENT.getSubType(), SubType.POP.getSubType());
            default:
                return Lists.newArrayList();
        }
    }
}
