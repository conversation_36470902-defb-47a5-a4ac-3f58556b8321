package net.summerfarm.mall.enums;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2025-01-13
 **/
@Getter
@AllArgsConstructor
public enum MerchantStoreAccountAuthorizationStatusEnum {

    /**
     * 预录入
     */
    PRE_ENTRY(0, "预录入"),

    /**
     * 取消授权
     */
    CANCELED(1, "取消授权"),

    /**
     * 已授权
     */
    AUTHORIZED(2, "已授权"),
    ;

    private final Integer code;
    private final String desc;
}
