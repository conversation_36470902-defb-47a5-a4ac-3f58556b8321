package net.summerfarm.mall.model.bo.order.convert;

import net.summerfarm.mall.model.bo.order.OrderItemWithExtraBO;
import net.summerfarm.mall.model.domain.OrderItem;
import net.summerfarm.mall.model.domain.OrderItemExtra;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: George
 * @date: 2024-08-15
 **/
public class OrderBOConvertor {

    public static List<OrderItemWithExtraBO> convertOrderItemsWithExtraBO(List<OrderItem> orderItems, List<OrderItemExtra> orderItemExtras) {
        if (CollectionUtils.isEmpty(orderItems) || CollectionUtils.isEmpty(orderItemExtras)) {
            return Collections.emptyList();
        }
        Map<Long, OrderItemExtra> orderItemExtraMap = orderItemExtras.stream().collect(Collectors.toMap(OrderItemExtra::getOrderItemId, item -> item));
        return orderItems.stream().map(item -> convertOrderItemWithExtraBO(item, orderItemExtraMap.get(item.getId()))).collect(Collectors.toList());
    }

    public static OrderItemWithExtraBO convertOrderItemWithExtraBO(OrderItem orderItem, OrderItemExtra orderItemExtra) {
        if (orderItemExtra == null) {
            return null;
        }
        OrderItemWithExtraBO orderItemWithExtraBO = new OrderItemWithExtraBO();
        orderItemWithExtraBO.setSku(orderItem.getSku());
        orderItemWithExtraBO.setOrderItemId(orderItem.getId());
        orderItemWithExtraBO.setQuoteType(orderItemExtra.getQuoteType());
        orderItemWithExtraBO.setMinAutoAfterSaleThreshold(orderItemExtra.getMinAutoAfterSaleThreshold());
        orderItemWithExtraBO.setAutoAfterSaleFlag(orderItemExtra.getAutoAfterSaleFlag());
        orderItemWithExtraBO.setAmount(orderItem.getAmount());
        orderItemWithExtraBO.setSpecifications(orderItemExtra.getSpecifications());
        orderItemWithExtraBO.setPrice(orderItem.getPrice());
        orderItemWithExtraBO.setOrderNo(orderItem.getOrderNo());
        orderItemWithExtraBO.setProductType(orderItem.getProductType());
        orderItemWithExtraBO.setWeightNum(orderItem.getWeightNum());
        return orderItemWithExtraBO;
    }
}
