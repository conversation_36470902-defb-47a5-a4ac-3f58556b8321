package net.summerfarm.mall.model.bo.order;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @description:
 * @author: George
 * @date: 2024-08-15
 **/
@Data
public class OrderItemWithExtraBO {

    /**
     * 订单明细id
     */
    private Long orderItemId;

    /**
     * sku
     */
    private String sku;

    /**
     * 数量
     */
    private Integer amount;

    /**
     * 供应商报价类型：0-默认类型，1-按斤报价，2-按件报价
     * @see net.summerfarm.mall.enums.InventoryQuoteTypeEnum
     */
    private Integer quoteType;

    /**
     * 自动补差售后量阈值，自动补差售后量阈值，单位等同于售后单位
     */
    private Integer minAutoAfterSaleThreshold;

    /**
     * 自动售后标识
     * @see net.summerfarm.mall.enums.AutoAfterSaleFlag
     */
    private Integer autoAfterSaleFlag;

    /**
     * 提货毛重（类似于规格）
     */
    private BigDecimal specifications;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 订单编号
     */
    private String orderNo;

    //商品类型：0、普通商品 1、赠品 2、换购商品
    private Integer productType;

    /**
     * 毛重
     */
    private BigDecimal weightNum;
}
