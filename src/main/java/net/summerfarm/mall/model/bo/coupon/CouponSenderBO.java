package net.summerfarm.mall.model.bo.coupon;

import lombok.Data;
import net.summerfarm.enums.coupon.CouponSenderSetupSenderTypeEnum;
import net.summerfarm.mall.enums.market.ReceiveCouponErrorEnum;
import net.summerfarm.mall.model.domain.Coupon;

import java.time.LocalDateTime;
import java.util.List;

import static net.summerfarm.enums.coupon.CouponSenderSetupSenderTypeEnum.RECOMMENDED_ORDER;
import static net.summerfarm.enums.coupon.CouponSenderSetupSenderTypeEnum.REGISTRATION_SEND;
import static net.summerfarm.enums.coupon.CouponSenderSetupSenderTypeEnum.USER_RECEIVE;

/**
 * 卡券发放请求参数
 * <AUTHOR> href="mailto:<EMAIL>>黄棽</a>
 * @since 2021/11/25
 */
@Data
public class CouponSenderBO {

    /**
     * 卡券id-用户领取必传
     */
    private Integer couponId;

    /**
     * 发放设置ID
     */
    private Integer couponSenderId;

    /**
     * 发放卡券的商户id 必传
     */
    private Long mId;

    /**
     * 发放方式 必传
     */
    private CouponSenderSetupSenderTypeEnum senderType;

    /**
     * 优惠券触发发放时间 必传
     */
    private LocalDateTime triggerTime;

    /**
     * 区域号
     */
    private Integer areaNo;

    /**
     * 领取方式-记录来源
     */
    private Integer receiveType;

    /**
     * 卡劵集合
     */
    private List<Coupon> coupons;

    /**
     * 卡劵id集合
     */
    private List<Long> couponIds;

    /**
     * 卡券
     */
    private Coupon coupon;

    /**
     * 领券失败异常原因
     */
    private ReceiveCouponErrorEnum errorEnum;

    /**
     * 优惠活动关联ID（满返等）
     */
    private Long relatedId;

    /**
     * 门店类型
     */
    private String size;

    /**
     * 大客户ID
     */
    private Integer adminId;


    public void setSenderTypeByByValue(Integer senderTypeValue) {
        if(senderType == null){
            return;
        }
        if(senderTypeValue == USER_RECEIVE.getType()){
            this.senderType = USER_RECEIVE;
        }else if(senderTypeValue == REGISTRATION_SEND.getType()){
            this.senderType = REGISTRATION_SEND;
        }else if(senderTypeValue == RECOMMENDED_ORDER.getType()){
            this.senderType = RECOMMENDED_ORDER;
        }
    }
}
