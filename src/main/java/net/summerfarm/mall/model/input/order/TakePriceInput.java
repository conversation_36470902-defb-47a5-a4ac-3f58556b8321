package net.summerfarm.mall.model.input.order;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * @author: <EMAIL>
 * @create: 2023/10/10
 */
@Data
public class TakePriceInput implements Serializable {

    /**
     * 需要计算到手价的sku
     */
    private List<TakePriceItem> itemList;

    class TakePriceItem implements Serializable {

        /**
         * sku code
         */
        private String sku;

        /**
         * 数量
         */
        private Integer quantity;
    }

}
