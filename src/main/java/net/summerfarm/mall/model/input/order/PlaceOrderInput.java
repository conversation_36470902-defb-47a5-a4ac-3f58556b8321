package net.summerfarm.mall.model.input.order;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import lombok.Data;
import net.summerfarm.mall.enums.TimeFrameEnum;

/**
 * @author: <EMAIL>
 * @create: 2023/9/27
 */
@Data
public class PlaceOrderInput implements Serializable {

    /**
     * 订单项信息
     */
    private List<OrderItemInput> orderItemList;

    /**
     * 配送地址
     */
    private Long contactId;

    /**
     * 是否超时加单, 0 否,1 是
     */
    private Integer outTimes;

    /**
     * 精准送,ZERO,FIRST,SECOND,THIRD,FOURTH
     *
     * @see TimeFrameEnum
     */
    private String timeFrameName;

    /**
     * 使用的优惠券id, 0未定义,1普通商品优惠券,2普通运费优惠券,3精准送优惠券,4红包,5商品兑换券
     */
    private Map<Integer, Integer> usedCouponIds;

    /**
     * 使用的优惠券id, 0未定义,1普通商品优惠券,2普通运费优惠券,3精准送优惠券,4红包,5商品兑换券
     */
    private List<UsedCouponInput> usedCoupons;

    /**
     * 拓展购买活动ID
     */
    private Long expandActivityId;

    /**
     * 换购品活动范围id scopeConfigId
     */
    private Long exchangeBizId;

    /**
     * 此标记为true代表获取明细并且不均摊优惠,false则均摊优惠用于预下单和下单
     */
    private Boolean takePriceFlag;

    /**
     * 判断是否为下单计算 0为到手价或者预下单计算卡券，1为下单计算使用具体前端给的
     */
    private Integer isTakePrice;

    /**
     * 省心送获取到手价 0非省心送，1省心送
     */
    private Integer timingSkuPrice;

    /**
     * 省心送id
     */
    private Integer timingRuleId;

    /**
     * 按履约时间拆单的订单项
     */
    private List<SubOrderItem> subOrderItemList;

    /**
     * 线上兼容 -- 是否获取老的配送规则 1表示不获取，0或者null表示获取
     */
    private Integer deliveryRulesType;

    @Data
    public class SubOrderItem implements Serializable {

        /**
         * 履约时间
         */
        private LocalDate deliveryDate;

        /**
         * 子订单订单项
         */
        private List<OrderItemInput> orderItemList;
    }

}
