package net.summerfarm.mall.model.cache;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class MerchantCache {

    /**
     * mId
     */
    private Long mId;

    /**
     * 下单账号
     */
    private Long accountId;

    /**
     * 配送地址
     */
    private Long contactId;

    /**
     * 店铺名
     */
    private String mname;

    /**
     * 下单用户openId
     */
    private String openId;

    /**
     * 是否超时加单
     */
    private Integer outTimes;


    /**
     * 是否代下单
     */
    private Integer helpOrder;

    /**
     * 客户size
     */
    private String size;

    /**
     * 下单流程中使用：是否是大客户
     */
    private Boolean majorMerchant;

    /**
     * 大客户用：合作模式：1、账期 2、现结
     */
    private Integer direct;

    /**
     * 大客户用：是否展示：1、定量 2、全量
     */
    private Integer skuShow;

    /**
     * 大客户用：是否在服务区：1服务区内 2服务区外
     */
    private Integer server;

    /**
     * 大客户用：大客户adminId
     */
    private Integer adminId;

}
