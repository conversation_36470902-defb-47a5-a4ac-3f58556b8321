package net.summerfarm.mall.model.converter;

import net.summerfarm.mall.model.domain.merchant.MerchantB2bHelper;
import net.summerfarm.mall.model.dto.merchant.MerchantB2bDTO;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class MerChantB2bDtoConverter {


    private MerChantB2bDtoConverter() {
        // 无需实现
    }

    public static List<MerchantB2bHelper> toMerchantB2bHelperList(List<MerchantB2bDTO> merchantB2bDTOList) {
        if (merchantB2bDTOList == null) {
            return Collections.emptyList();
        }
        List<MerchantB2bHelper> merchantB2bHelperList = new ArrayList<>();
        for (MerchantB2bDTO merchantB2bDTO : merchantB2bDTOList) {
            merchantB2bHelperList.add(toMerchantB2bHelper(merchantB2bDTO));
        }
        return merchantB2bHelperList;
    }

    public static MerchantB2bHelper toMerchantB2bHelper(MerchantB2bDTO merchantB2bDTO) {
        if (merchantB2bDTO == null) {
            return null;
        }
        MerchantB2bHelper merchantB2bHelper = new MerchantB2bHelper();
        merchantB2bHelper.setMId(merchantB2bDTO.getMId());
        merchantB2bHelper.setAccountId(merchantB2bDTO.getAccountId());
        merchantB2bHelper.setCoverStatus(merchantB2bDTO.getCoverStatus());
// Not mapped TO fields:
// id
// createTime
// updateTime
// b2bStatus
// couponStatus
// popupStatus
// merchantInfo
// mpOpenid
// Not mapped FROM fields:
// openId
// b2bStatus
// couponStatus
// popupStatus
        return merchantB2bHelper;
    }

    public static List<MerchantB2bDTO> toMerchantB2bDTOList(List<MerchantB2bHelper> merchantB2bHelperList) {
        if (merchantB2bHelperList == null) {
            return Collections.emptyList();
        }
        List<MerchantB2bDTO> merchantB2bDTOList = new ArrayList<>();
        for (MerchantB2bHelper merchantB2bHelper : merchantB2bHelperList) {
            merchantB2bDTOList.add(toMerchantB2bDTO(merchantB2bHelper));
        }
        return merchantB2bDTOList;
    }

    public static MerchantB2bDTO toMerchantB2bDTO(MerchantB2bHelper merchantB2bHelper) {
        if (merchantB2bHelper == null) {
            return null;
        }
        MerchantB2bDTO merchantB2bDTO = new MerchantB2bDTO();
        merchantB2bDTO.setMId(merchantB2bHelper.getMId());
        merchantB2bDTO.setAccountId(merchantB2bHelper.getAccountId());
        merchantB2bDTO.setB2bStatus(merchantB2bHelper.getB2bStatus());
        merchantB2bDTO.setCouponStatus(merchantB2bHelper.getCouponStatus());
        merchantB2bDTO.setPopupStatus(merchantB2bHelper.getPopupStatus());
        merchantB2bDTO.setCoverStatus(merchantB2bHelper.getCoverStatus());

        return merchantB2bDTO;
    }
}
