package net.summerfarm.mall.model.converter;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import net.summerfarm.mall.model.vo.MerchantJobVO;
import net.xianmu.jobsdk.model.dto.CrmJobMerchantDetailDto;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @descripton
 * @date 2024/12/20 17:45
 */
public class MerchantJobConverter {


    private MerchantJobConverter() {
        // 无需实现
    }

    public static List<MerchantJobVO> toMerchantJobVoList(List<CrmJobMerchantDetailDto> crmJobMerchantDetailDtoList) {
        if (crmJobMerchantDetailDtoList == null) {
            return Collections.emptyList();
        }
        List<MerchantJobVO> merchantJobVoList = new ArrayList<>();
        for (CrmJobMerchantDetailDto crmJobMerchantDetailDto : crmJobMerchantDetailDtoList) {
            merchantJobVoList.add(convertDtoToVo(crmJobMerchantDetailDto));
        }
        return merchantJobVoList;
    }


    public static MerchantJobVO convertDtoToVo(CrmJobMerchantDetailDto dto) {
        MerchantJobVO vo = new MerchantJobVO();
        vo.setId(dto.getId());
        vo.setJobName(dto.getJobName());
        vo.setDescription(dto.getDescription());
        vo.setType(dto.getType());
        vo.setStartTime(dto.getStartTime());
        vo.setEndTime(dto.getEndTime());
        vo.setJobId(dto.getJobId());
        vo.setMId(dto.getMId());
        vo.setStatus(dto.getStatus());
        vo.setAchievedStatus(dto.getAchievedStatus());
        vo.setClaimingStatus(dto.getClaimingStatus());
        vo.setClaimingTime(dto.getClaimingTime());
        vo.setCompleteTime(dto.getCompleteTime());
        vo.setCategoryIdList(parseCategoryList(dto.getCategoryList()));
        vo.setSkuList(parseSkuList(dto.getMerchantProductList()));
        // 以下字段存在类型或者含义不一致等情况，需要进行额外处理或者无法直接映射
        // 4. vo中的rewardAmount在dto中没有对应字段，无法从dto直接映射，需要根据业务逻辑确定其值，比如可能根据奖励类型和其他相关规则计算得出
        // 5. vo中的coupon在dto中没有对应字段，无法直接映射，需要从其他数据源获取或者根据业务关联获取对应Coupon对象
        vo.setRewardType(dto.getRewardType());
        vo.setRewardValue(dto.getRewardValue());
        vo.setCompletionType(dto.getCompletionType());
        vo.setCompletionValue(dto.getCompletionValue());
        vo.setCreateTime(dto.getCreateTime());
        vo.setUpdateTime(dto.getUpdateTime());
        vo.setOrderTypeList(JSON.parseArray(dto.getOrderTypeList(), Integer.class));
        vo.setOrderNo(parseOrderNo(dto.getOrderNoList(), dto.getRewardOrderNoList()));
        vo.setOrderNoList(parseOrderNoList(dto.getOrderNoList()));
        return vo;
    }

    private static String parseOrderNo(String achievedOrderNoListStr, String rewardOrderNoListStr){
        List<String> rewardOrderNoList = StringUtils.isBlank(rewardOrderNoListStr)? null : JSON.parseArray(rewardOrderNoListStr, String.class);
        List<String> achievedOrderNoList = StringUtils.isBlank(achievedOrderNoListStr) ? null : JSON.parseArray(achievedOrderNoListStr, String.class);


        return CollUtil.isNotEmpty(rewardOrderNoList) ? rewardOrderNoList.get(0) : CollUtil.isNotEmpty(achievedOrderNoList) ? achievedOrderNoList.get(0) : null;
    }


    private static List<String> parseOrderNoList(String achievedOrderNoListStr){
        return StringUtils.isBlank(achievedOrderNoListStr) ? null : JSON.parseArray(achievedOrderNoListStr, String.class);
    }


    private static List<Integer> parseCategoryList(String categoryListStr){
        if(StringUtils.isBlank(categoryListStr)) {
            return Collections.emptyList();
        }
        return JSON.parseObject(categoryListStr, new TypeReference<List<List<Integer>>>() {
                })
                .stream()
                .map(data -> CollectionUtil.get(data, -1)).distinct().collect(Collectors.toList());
    }

    private static List<String> parseSkuList(String merchantProductListStr){
        if(StringUtils.isBlank(merchantProductListStr)) {
            return Collections.emptyList();
        }
        return JSON.parseArray(merchantProductListStr, String.class);
    }
}
