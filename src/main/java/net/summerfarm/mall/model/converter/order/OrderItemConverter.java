package net.summerfarm.mall.model.converter.order;

import java.math.BigDecimal;
import java.util.*;

import net.summerfarm.mall.model.domain.Trolley;
import net.summerfarm.mall.model.dto.order.OrderItemCalcDTO;
import net.summerfarm.mall.model.input.order.OrderItemInput;
import net.summerfarm.mall.model.vo.LadderPriceVO;

/**
 * @author: <EMAIL>
 * @create: 2023/10/11
 */
public class OrderItemConverter {


    private OrderItemConverter() {
        // 无需实现
    }

    public static List<Trolley> toTrolleyList(List<OrderItemInput> orderItemInputList) {
        if (orderItemInputList == null) {
            return Collections.emptyList();
        }
        List<Trolley> trolleyList = new ArrayList<>();
        for (OrderItemInput orderItemInput : orderItemInputList) {
            if (orderItemInput == null) {
                continue;
            }
            trolleyList.add(toTrolley(orderItemInput));
        }
        return trolleyList;
    }

    public static Trolley toTrolley(OrderItemInput orderItemInput) {
        if (orderItemInput == null) {
            return null;
        }
        Trolley trolley = new Trolley();
        trolley.setSku(orderItemInput.getSku());
        trolley.setProductType(orderItemInput.getProductType());
        trolley.setQuantity(orderItemInput.getQuantity());
        //设置默认值，防止后续处理异常
        trolley.setSuitId(0);
        trolley.setParentSku("0");
        return trolley;
    }

    public static OrderItemCalcDTO toItemDTO(OrderItemCalcDTO orderItemCalcDTO, LadderPriceVO ladderPriceVO) {
        //价格信息
        OrderItemCalcDTO dto = new OrderItemCalcDTO();
        dto.setOriginalPrice(orderItemCalcDTO.getOriginalPrice());
        dto.setPrice(orderItemCalcDTO.getPrice());
        dto.setLadderPrice(orderItemCalcDTO.getLadderPrice());
        BigDecimal totalPrice = orderItemCalcDTO.getOriginalPrice().multiply(BigDecimal.valueOf(ladderPriceVO.getUnit()));
        dto.setActualTotalPrice(totalPrice);
        dto.setCalcPartDeliveryFee(totalPrice);
        dto.setActualTotalOriginalPrice(totalPrice);
        dto.setPrepayUsableQuantity(orderItemCalcDTO.getPrepayUsableQuantity());
        dto.setPrepaySetPrice(orderItemCalcDTO.getPrepaySetPrice());
        dto.setActivityPrice(ladderPriceVO.getPrice());
        dto.setFloorPrice(orderItemCalcDTO.getFloorPrice());

        //商品信息
        dto.setPdName(orderItemCalcDTO.getPdName());
        dto.setSkuName(orderItemCalcDTO.getSkuName());
        dto.setWeight(orderItemCalcDTO.getWeight());
        dto.setBaseSaleUnit(orderItemCalcDTO.getBaseSaleUnit());
        dto.setBaseSaleQuantity(orderItemCalcDTO.getBaseSaleQuantity());
        dto.setMaturity(orderItemCalcDTO.getMaturity());
        dto.setSkuType(orderItemCalcDTO.getSkuType());
        dto.setCategoryId(orderItemCalcDTO.getCategoryId());
        dto.setPicturePath(orderItemCalcDTO.getPicturePath());
        dto.setStorageLocation(orderItemCalcDTO.getStorageLocation());
        dto.setVolume(orderItemCalcDTO.getVolume());
        dto.setWeightNum(orderItemCalcDTO.getWeightNum());
        dto.setPdId(orderItemCalcDTO.getPdId());
        dto.setInvId(orderItemCalcDTO.getInvId());

        //类目信息
        dto.setCategoryType(orderItemCalcDTO.getCategoryType());

        //购物车信息
        dto.setSku(orderItemCalcDTO.getSku());
        dto.setParentSku(orderItemCalcDTO.getParentSku());
        dto.setProductType(orderItemCalcDTO.getProductType());
        dto.setAmount(ladderPriceVO.getUnit());
        dto.setAddTime(new Date());

        //大客户相关信息
        dto.setRebateType(orderItemCalcDTO.getRebateType());
        dto.setRebateNumber(orderItemCalcDTO.getRebateNumber());
        dto.setAgentPayMethod(orderItemCalcDTO.getAgentPayMethod());
        return dto;
    }
}
