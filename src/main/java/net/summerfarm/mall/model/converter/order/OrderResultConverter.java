package net.summerfarm.mall.model.converter.order;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import net.summerfarm.mall.enums.CommonStatus;
import net.summerfarm.mall.enums.OrderPreferentialTypeEnum;
import net.summerfarm.mall.model.dto.order.OrderItemCalcDTO;
import net.summerfarm.mall.model.vo.MerchantCouponVO;
import net.summerfarm.mall.model.vo.neworder.OrderNewResultVO;
import net.summerfarm.mall.model.vo.neworder.UsableCouponVO;
import net.summerfarm.mall.model.vo.order.OrderResultVO;

/**
 * @author: <EMAIL>
 * @create: 2023/10/16
 */
public class OrderResultConverter {


    private OrderResultConverter() {
        // 无需实现
    }

    public static List<OrderNewResultVO> toOrderNewResultVOList(
            List<OrderResultVO> orderResultVOList) {
        if (orderResultVOList == null) {
            return Collections.emptyList();
        }
        List<OrderNewResultVO> orderNewResultVOList = new ArrayList<>();
        for (OrderResultVO orderResultVO : orderResultVOList) {
            orderNewResultVOList.add(toOrderNewResultVO(orderResultVO));
        }
        return orderNewResultVOList;
    }

    public static OrderNewResultVO toOrderNewResultVO(OrderResultVO orderResultVO) {
        if (orderResultVO == null) {
            return null;
        }
        OrderNewResultVO orderNewResultVO = new OrderNewResultVO();
        orderNewResultVO.setItemList(orderResultVO.getItemList());
        orderNewResultVO.setSubOrderFailVOS(orderResultVO.getSubOrderFailVOS());
        orderNewResultVO.setFloorPriceFailVOS(orderResultVO.getFloorPriceFailVOS());
        if (CollectionUtil.isNotEmpty(orderResultVO.getPreferentialList())) {
            orderNewResultVO.setPreferentialList(orderResultVO.getPreferentialList());
            //统计价格优惠和活动优惠
            orderNewResultVO.setDiscount(BigDecimal.ZERO);
            orderNewResultVO.setEventDiscount(BigDecimal.ZERO);
            orderResultVO.getPreferentialList().forEach(x -> {
                //价格优惠
                if (OrderPreferentialTypeEnum.isSkuPriceDiscount(x.getType())) {
                    orderNewResultVO.setDiscount(orderNewResultVO.getDiscount().add(x.getAmount()));
                }
                //活动优惠
                if (OrderPreferentialTypeEnum.isOrderPriceDiscount(x.getType())) {
                    orderNewResultVO.setEventDiscount(
                            orderNewResultVO.getEventDiscount().add(x.getAmount()));
                }
            });
        }
        orderNewResultVO.setPrepayUseMap(orderResultVO.getPrepayUseMap());
        orderNewResultVO.setDiscountCardUseRecords(orderResultVO.getDiscountCardUseRecords());
        orderNewResultVO.setMarketRuleHistoryList(orderResultVO.getMarketRuleHistoryList());
        orderNewResultVO.setUsedMerchantCouponId(orderResultVO.getUsedMerchantCouponId());
        orderNewResultVO.setUsedDeliveryCouponId(orderResultVO.getUsedDeliveryCouponId());
        orderNewResultVO.setOutTimesFee(orderResultVO.getOutTimesFee());
        orderNewResultVO.setPrecisionDeliveryFee(orderResultVO.getPrecisionDeliveryFee());
        orderNewResultVO.setRuleDeliveryFee(orderResultVO.getRuleDeliveryFee());
        orderNewResultVO.setOriginalDeliveryFee(orderResultVO.getOriginalDeliveryFee());
        orderNewResultVO.setDeliveryFee(orderResultVO.getDeliveryFee());
        orderNewResultVO.setActualTotalPrice(orderResultVO.getActualTotalPrice());
        orderNewResultVO.setOriginTotalPrice(orderResultVO.getOriginTotalPrice());
        orderNewResultVO.setDiscountTotalPrice(
                orderResultVO.getOriginTotalPrice().subtract(orderResultVO.getActualTotalPrice()));
        orderNewResultVO.setOrderStatus(orderResultVO.getOrderStatus());
        orderNewResultVO.setItemOriginalPrice(orderResultVO.getItemOriginalPrice());
        List<MerchantCouponVO> usableCoupons = Lists.newArrayList();
        //包含普通商品优惠券、运费券、兑换券
        usableCoupons.addAll(Optional.ofNullable(orderResultVO.getUsableNormalCoupon())
                .orElse(Lists.newArrayList()));
        usableCoupons.addAll(Optional.ofNullable(orderResultVO.getUsableRedPackCoupon())
                .orElse(Lists.newArrayList()));
        usableCoupons.addAll(Optional.ofNullable(orderResultVO.getUsableAccurateCoupon())
                .orElse(Lists.newArrayList()));
        if (CollectionUtil.isNotEmpty(usableCoupons)) {
            Map<Integer, List<MerchantCouponVO>> usableCouponMap = usableCoupons.stream().map(x -> {
                        if (CollectionUtil.isNotEmpty(orderResultVO.getUsedMerchantCouponId())
                                && orderResultVO.getUsedMerchantCouponId().contains(x.getId())) {
                            x.setIsSelected(1);
                        }
                        if (x.getId() != null && Objects.equals(x.getId(),
                                orderResultVO.getUsedDeliveryCouponId())) {
                            x.setIsSelected(1);
                        }
                        return x;
                    })
                    .collect(Collectors.groupingBy(MerchantCouponVO::getAgioType));
            List<UsableCouponVO> usableCouponList = usableCouponMap.entrySet().stream().map(x -> {
                UsableCouponVO usableCoupon = new UsableCouponVO();
                usableCoupon.setCouponType(x.getKey());
                List<MerchantCouponVO> couponVOS = x.getValue();
                couponVOS.sort(Comparator.comparing(MerchantCouponVO::getMoney).reversed());
                usableCoupon.setCoupons(couponVOS);
                return usableCoupon;
            }).collect(Collectors.toList());
            orderNewResultVO.setUsableCouponList(usableCouponList);
        }

// Not mapped TO fields:
// discount
// eventDiscount
// discountCardList
// orderItemErrorList
// canChangeAddress
// subOrderList
// Not mapped FROM fields:
// orderDeliveryType
// outTimes
// usedTimeFrame
// timeFrameItem
// areaDeliveryFee
// deliveryDate
// scheduledDeliveryTime
// satisfyFreeDelivery
// freeDeliveryRule
        return orderNewResultVO;
    }

    public static List<OrderResultVO> toOrderResultVOList(
            List<OrderNewResultVO> orderNewResultVOList) {
        if (orderNewResultVOList == null) {
            return Collections.emptyList();
        }
        List<OrderResultVO> orderResultVOList = new ArrayList<>();
        for (OrderNewResultVO orderNewResultVO : orderNewResultVOList) {
            orderResultVOList.add(toOrderResultVO(orderNewResultVO));
        }
        return orderResultVOList;
    }

    public static OrderResultVO toOrderResultVO(OrderNewResultVO orderNewResultVO) {
        if (orderNewResultVO == null) {
            return null;
        }
        OrderResultVO orderResultVO = new OrderResultVO();
        orderResultVO.setItemList(orderNewResultVO.getItemList());
        orderResultVO.setPreferentialList(orderNewResultVO.getPreferentialList());
        orderResultVO.setPrepayUseMap(orderNewResultVO.getPrepayUseMap());
        orderResultVO.setDiscountCardUseRecords(orderNewResultVO.getDiscountCardUseRecords());
        orderResultVO.setMarketRuleHistoryList(orderNewResultVO.getMarketRuleHistoryList());
        orderResultVO.setUsedMerchantCouponId(orderNewResultVO.getUsedMerchantCouponId());
        orderResultVO.setUsedDeliveryCouponId(orderNewResultVO.getUsedDeliveryCouponId());
        orderResultVO.setOutTimesFee(orderNewResultVO.getOutTimesFee());
        orderResultVO.setOriginalDeliveryFee(orderNewResultVO.getOriginalDeliveryFee());
        orderResultVO.setDeliveryFee(orderNewResultVO.getDeliveryFee());
        orderResultVO.setOrderStatus(orderNewResultVO.getOrderStatus());
        orderResultVO.setActualTotalPrice(orderNewResultVO.getActualTotalPrice());
        orderResultVO.setOriginTotalPrice(orderNewResultVO.getOriginTotalPrice());
        orderResultVO.setPrecisionDeliveryFee(orderNewResultVO.getPrecisionDeliveryFee());
// Not mapped TO fields:
// orderDeliveryType
// usableNormalCoupon
// usableRedPackCoupon
// usableAccurateCoupon
// outTimes
// usedTimeFrame
// timeFrameItem
// areaDeliveryFee
// deliveryDate
// scheduledDeliveryTime
// satisfyFreeDelivery
// freeDeliveryRule
// Not mapped FROM fields:
// usableCouponMap
// discount
// eventDiscount
// discountCardList
// orderItemErrorList
// canChangeAddress
// subOrderList
        return orderResultVO;
    }
}
