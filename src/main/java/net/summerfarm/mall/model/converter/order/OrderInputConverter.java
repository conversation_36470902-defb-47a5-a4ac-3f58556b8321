package net.summerfarm.mall.model.converter.order;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import java.time.LocalDate;
import java.util.List;
import net.summerfarm.mall.model.dto.order.OrderItemCalcDTO;
import net.summerfarm.mall.model.input.order.PlaceOrderInput;
import net.summerfarm.mall.model.input.order.PlaceOrderInput.SubOrderItem;
import net.summerfarm.mall.model.input.order.PreOrderInput;
import net.summerfarm.mall.model.vo.order.OrderResultVO;
import net.summerfarm.mall.model.vo.order.PlaceOrderVO;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: <EMAIL>
 * @create: 2023/10/11
 */
public class OrderInputConverter {


    private OrderInputConverter() {
        // 无需实现
    }

    public static PlaceOrderVO toPlaceOrderVO(PreOrderInput preOrderInput) {
        if (preOrderInput == null) {
            return null;
        }
        PlaceOrderVO placeOrderVO = new PlaceOrderVO();
        placeOrderVO.setOrderItemList(preOrderInput.getOrderItemList());
        placeOrderVO.setContactId(preOrderInput.getContactId());
        placeOrderVO.setOutTimes(preOrderInput.getOutTimes());
        placeOrderVO.setTimeFrameName(preOrderInput.getTimeFrameName());
        if (CollectionUtil.isNotEmpty(preOrderInput.getUsedCoupons())) {
            Map<Integer, Integer> usedCouponIds = preOrderInput.getUsedCoupons().stream().collect(
                    Collectors.toMap(x -> x.getCouponType(), t -> t.getUsedCouponId(),
                            (a, b) -> a));
            placeOrderVO.setUsedCouponIds(usedCouponIds);
            placeOrderVO.setMerchantCouponId(Sets.newHashSet(usedCouponIds.values()));
        }
        placeOrderVO.setExpandActivityId(preOrderInput.getExpandActivityId());
        placeOrderVO.setIsTakePrice(preOrderInput.getIsTakePrice());
        placeOrderVO.setTimingSkuPrice(preOrderInput.getTimingSkuPrice());
        placeOrderVO.setTimingRuleId(preOrderInput.getTimingRuleId());
        placeOrderVO.setBizId(preOrderInput.getExchangeBizId());
        placeOrderVO.setDeliveryRulesType(preOrderInput.getDeliveryRulesType());
        return placeOrderVO;
    }

    public static PlaceOrderVO toPlaceOrderVO(PlaceOrderInput placeOrderInput) {
        if (placeOrderInput == null) {
            return null;
        }
        PlaceOrderVO placeOrderVO = new PlaceOrderVO();
        placeOrderVO.setOrderItemList(placeOrderInput.getOrderItemList());
        placeOrderVO.setContactId(placeOrderInput.getContactId());
        placeOrderVO.setOutTimes(placeOrderInput.getOutTimes());
        placeOrderVO.setTimeFrameName(placeOrderInput.getTimeFrameName());
        placeOrderVO.setUsedCouponIds(placeOrderInput.getUsedCouponIds());
        placeOrderVO.setBizId(placeOrderInput.getExchangeBizId());
        if (CollectionUtil.isNotEmpty(placeOrderInput.getUsedCoupons())) {
            Map<Integer, Integer> usedCouponIds = placeOrderInput.getUsedCoupons().stream().collect(
                    Collectors.toMap(x -> x.getCouponType(), t -> t.getUsedCouponId(),
                            (a, b) -> a));
            placeOrderVO.setUsedCouponIds(usedCouponIds);
            placeOrderVO.setMerchantCouponId(Sets.newHashSet(usedCouponIds.values()));
        }
        placeOrderVO.setExpandActivityId(placeOrderInput.getExpandActivityId());
        placeOrderVO.setIsTakePrice(placeOrderInput.getIsTakePrice());
        placeOrderVO.setTimingSkuPrice(placeOrderInput.getTimingSkuPrice());
        placeOrderVO.setTimingRuleId(placeOrderInput.getTimingRuleId());
        placeOrderVO.setDeliveryRulesType(placeOrderInput.getDeliveryRulesType());
        List<SubOrderItem> subOrderItemList = placeOrderInput.getSubOrderItemList();
        if (CollectionUtil.isNotEmpty(subOrderItemList)) {
            Map<LocalDate, OrderResultVO> subPlaceOrderMap = Maps.newHashMap();
            subOrderItemList.forEach(x -> {
                OrderResultVO resultVO = new OrderResultVO();
                List<OrderItemCalcDTO> itemList = Lists.newArrayList();
                x.getOrderItemList().forEach(t -> {
                    OrderItemCalcDTO calcDTO = new OrderItemCalcDTO();
                    calcDTO.setSku(t.getSku());
                    calcDTO.setProductType(t.getProductType());
                    calcDTO.setAmount(t.getQuantity());
                    itemList.add(calcDTO);
                });
                resultVO.setItemList(itemList);
                subPlaceOrderMap.put(x.getDeliveryDate(), resultVO);
            });
            placeOrderVO.setSubPlaceOrderMap(subPlaceOrderMap);
        }
        return placeOrderVO;
    }

}
