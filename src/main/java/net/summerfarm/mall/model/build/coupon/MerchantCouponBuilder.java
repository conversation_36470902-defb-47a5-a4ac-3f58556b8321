package net.summerfarm.mall.model.build.coupon;

import net.summerfarm.mall.model.domain.Coupon;
import net.summerfarm.mall.model.domain.MerchantCoupon;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

import static net.summerfarm.enums.coupon.MerchantCouponReceiveTypeEnum.RECEIVE;

/**
 *
 * <AUTHOR> href="mailto:<EMAIL>>黄棽</a>
 * @since 2021/11/25
 */
public class MerchantCouponBuilder {

    public static MerchantCoupon build(Coupon coupon,
                                       Long mId,
                                       String couponSenderSetupName, LocalDateTime nowTime){
        int couponType = coupon.getType();
        LocalDateTime validDate = null;
        LocalDateTime startTime = null;

        if (couponType == 0) {
            // 0指固定时间间隔到期
            int validTime = coupon.getVaildTime();
            validDate = LocalDateTime.of(LocalDate.now().plusDays(validTime), LocalTime.of(23, 59, 59));
            startTime = coupon.getStartTime() == null ? LocalDateTime.now() : LocalDateTime.now().plusDays(coupon.getStartTime());
        }
        else if (couponType == 1) {
            // 1固定时间点到期
            validDate = coupon.getVaildDate();
            startTime = coupon.getStartDate() == null ? LocalDateTime.now() : coupon.getStartDate();
        }

        Integer couponId = coupon.getId();
        //查看该优惠劵在对应的时间属于哪个发放设置
        MerchantCoupon merchantCoupon = new MerchantCoupon();
        merchantCoupon.setmId(mId);
        merchantCoupon.setCouponId(couponId);
        merchantCoupon.setVaildDate(validDate);
        merchantCoupon.setAddTime(nowTime);
        merchantCoupon.setStartTime(startTime);
        merchantCoupon.setReceiveType(RECEIVE.getReceiveType());
        return merchantCoupon;
    }

}
