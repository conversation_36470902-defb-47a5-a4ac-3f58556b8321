package net.summerfarm.mall.model.build.coupon;

import lombok.Data;
import net.summerfarm.mall.model.domain.CouponReceiveLog;

import java.time.LocalDateTime;

/**
 *
 * <AUTHOR> href="mailto:<EMAIL>>黄棽</a>
 * @since 2021/11/25
 */
@Data
public class CouponReceiveLogBuild {

    public static CouponReceiveLog build(Long mId, Integer couponId,
                                         Integer couponSenderSetupId,
                                         LocalDateTime nowTime){
        CouponReceiveLog couponReceiveLog = new CouponReceiveLog();
        couponReceiveLog.setCreateTime(nowTime);
        couponReceiveLog.setCreator(mId.intValue());
        couponReceiveLog.setCouponId(couponId);
        couponReceiveLog.setCouponSenderId(couponSenderSetupId);
        couponReceiveLog.setMId(mId);
        return couponReceiveLog;
    }

}
