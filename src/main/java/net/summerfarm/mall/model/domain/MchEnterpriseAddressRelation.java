package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * mch_enterprise_address_relation
 * <AUTHOR>
@Data
public class MchEnterpriseAddressRelation implements Serializable {
    /**
     * 自增长主键
     */
    private Long id;

    /**
     * 取自mch_enterprise_information_management表中id
     */
    private Long enterpriseInformationId;

    /**
     * 取自contact表中contact_id
     */
    private Long contactId;

    /**
     * 0:生效中（默认), 1:(失效)
     */
    private Integer validStatus;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    private static final long serialVersionUID = 1L;
}