package net.summerfarm.mall.model.domain;

import lombok.Data;
import net.summerfarm.mall.enums.PayTypeEnum;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class Orders {
    private Long orderId;

    private String orderNo;

    private Long mId;

    private Long accountId;

    private Date orderTime;

    private Integer type;

    private Short status;

    private BigDecimal deliveryFee;

    private BigDecimal totalPrice;

    private String remark;

    private Date confirmTime;

    private Integer outTimes;

    private BigDecimal outTimesFee;

    private Integer areaNo;

    private String mSize;

    private Integer direct;

    private Integer skuShow;

    private Integer cardRuleId;

    private BigDecimal redPackAmount;
    /**
    * 原价(应付价)
    */
    private BigDecimal originPrice;

    /**
     * 优惠卡id
     */
    private Integer discountCardId;

    /**
    * 类型 0 普通 1 预售
     * */
    private Integer orderSaleType;

    /**
     * 下单门店为大客户时所属的客户的id
     */
    private Integer adminId;

    private Integer operateId;

    /**
     * 自提标识 0 非自提 没有出库任务 1 自提 生成出库任务
     */
    private Integer outStock;

    /**
     * 0:未开票,1:部分开票,2:已开票
     */
    private Integer invoiceStatus;

    /**
     * 代订单类型
     *
     * @see PayTypeEnum
     */
    private Integer orderPayType;

    /**
     * 销售主体名称
     */
    private String sellingEntityName;

    public Orders() {
    }

    public Orders(String orderNo, Long mId, Long accountId, Date orderTime, Integer type, String timeFrame) {
        this.orderNo = orderNo;
        this.mId = mId;
        this.accountId = accountId;
        this.orderTime = orderTime;
        this.type = type;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo == null ? null : orderNo.trim();
    }

    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public Date getOrderTime() {
        return orderTime;
    }

    public void setOrderTime(Date orderTime) {
        this.orderTime = orderTime;
    }

    public Short getStatus() {
        return status;
    }

    public void setStatus(Short status) {
        this.status = status;
    }

    public BigDecimal getDeliveryFee() {
        return deliveryFee;
    }

    public void setDeliveryFee(BigDecimal deliveryFee) {
        this.deliveryFee = deliveryFee;
    }


    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public Date getConfirmTime() {
        return confirmTime;
    }

    public void setConfirmTime(Date confirmTime) {
        this.confirmTime = confirmTime;
    }

    public Integer getOutTimes() {
        return outTimes;
    }

    public void setOutTimes(Integer outTimes) {
        this.outTimes = outTimes;
    }

    public BigDecimal getOutTimesFee() {
        return outTimesFee;
    }

    public void setOutTimesFee(BigDecimal outTimesFee) {
        this.outTimesFee = outTimesFee;
    }

    public Integer getAreaNo() {
        return areaNo;
    }

    public void setAreaNo(Integer areaNo) {
        this.areaNo = areaNo;
    }

    public String getmSize() {
        return mSize;
    }

    public void setmSize(String mSize) {
        this.mSize = mSize;
    }

    public Integer getDirect() {
        return direct;
    }

    public void setDirect(Integer direct) {
        this.direct = direct;
    }

    public Integer getSkuShow() {
        return skuShow;
    }

    public void setSkuShow(Integer skuShow) {
        this.skuShow = skuShow;
    }

    public Integer getCardRuleId() {
        return cardRuleId;
    }

    public void setCardRuleId(Integer cardRuleId) {
        this.cardRuleId = cardRuleId;
    }

    public BigDecimal getOriginPrice() {
        return originPrice;
    }

    public void setOriginPrice(BigDecimal originPrice) {
        this.originPrice = originPrice;
    }

    public Integer getDiscountCardId() {
        return discountCardId;
    }

    public void setDiscountCardId(Integer discountCardId) {
        this.discountCardId = discountCardId;
    }

    public Integer getOrderSaleType() {
        return orderSaleType;
    }

    public void setOrderSaleType(Integer orderSaleType) {
        this.orderSaleType = orderSaleType;
    }

    public Integer getAdminId() {
        return adminId;
    }

    public void setAdminId(Integer adminId) {
        this.adminId = adminId;
    }

    public Integer getOperateId() {
        return operateId;
    }

    public void setOperateId(Integer operateId) {
        this.operateId = operateId;
    }

    public Integer getOutStock() {
        return outStock;
    }

    public void setOutStock(Integer outStock) {
        this.outStock = outStock;
    }

    public Integer getInvoiceStatus() {
        return invoiceStatus;
    }

    public void setInvoiceStatus(Integer invoiceStatus) {
        this.invoiceStatus = invoiceStatus;
    }
}
