package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * discount_card
 * <AUTHOR>
@Data
public class DiscountCard implements Serializable {
    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * 优惠卡名称
     */
    private String name;

    /**
     * 优惠卡价格
     */
    private BigDecimal price;

    /**
     * 折扣金额
     */
    private BigDecimal discount;

    /**
     * 有效天数
     */
    private Integer vaildDays;

    /**
     * 可用次数
     */
    private Integer vaildTimes;

    /**
     * 更新人员
     */
    private String updator;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 到期类型 0 指定时间间隔 1 指定时间到期(到期后不能再次购买)
     */
    private Integer cardType;

    /**
     * 到期时间
     */
    private LocalDate appointTime;

    /**
     * 适用城市 ,分割
     */
    private String areaNos;

    private static final long serialVersionUID = 1L;
}