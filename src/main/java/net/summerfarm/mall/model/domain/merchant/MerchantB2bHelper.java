package net.summerfarm.mall.model.domain.merchant;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * merchant_b2b_helper
 * <AUTHOR>
@Data
public class MerchantB2bHelper implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * 门店id
     */
    private Long mId;

    /**
     * 登陆自账号人id
     */
    private Long accountId;

    /**
     * 录入状态 0预录入 1 取消授权  2录入完成
     */
    private Integer b2bStatus;

    /**
     * 优惠卷状态 0未发放 1发放
     */
    private Integer couponStatus;

    /**
     * 0未弹出 1已弹出
     */
    private Integer popupStatus;

    /**
     * 0未弹出 1已弹出
     */
    private Integer coverStatus;
    /**
     * 录入的门店信息
     */
    private String merchantInfo;

    /**
     * 小程序openID
     */
    private String mpOpenid;

    private static final long serialVersionUID = 1L;
}