package net.summerfarm.mall.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * merchant_free_day_log
 * <AUTHOR>
@Data
public class MerchantFreeDayLog implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 客户ID
     */
    private Long mId;

    /**
     * 点击是否 0否 1是
     */
    private Integer clickFlag;

    /**
     * 免邮日
     */
    private String freeDay;

    /**
     * 标识是否执行 0否 1是
     */
    private Integer effectFlag;

    /**
     * 是否删除 0否 1是
     */
    private Integer deleteFlag;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    private static final long serialVersionUID = 1L;
}