package net.summerfarm.mall.model.domain;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import net.summerfarm.mall.common.util.DateUtils;
import net.summerfarm.mall.enums.CouponReceiveStatusEnum;
import net.summerfarm.mall.enums.CouponSkuScopeEnum;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * 优惠券
 * <AUTHOR> href="mailto:<EMAIL>>黄棽</a>
 * @since 2021/11/25
 */
@Data
public class Coupon {

    /**
     * id
     */
    private Integer id;

    /**
     * 优惠券名称
     */
    private String name;

    /**
     * 优惠券编码
     */
    private String code;

    private BigDecimal money;

    private BigDecimal threshold;

    private Byte type;

    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime vaildDate;

    private Integer vaildTime;

    private Boolean newHand;

    private String reamrk;

    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime addTime;

    /**
     * @see net.summerfarm.mall.enums.CouponEnum.CouponTypeEnum
     */
    private Integer agioType;

    private Integer status;

    private Integer grouping;

    private String sku;

    private String categoryId;

    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime startDate;

    private Integer startTime;

    private Integer couponSenderId;

    /**
     * 可领取数量，目前都是1
     */
    private Integer num;

    private Integer effectiveNum;

    /**
     * 在有效期内是否存在未使用卡劵 0-否 1-是
     */
    private Integer useStatus;

    @Getter
    @Setter
    private Integer activityScope;

    /**
     * 是否系统创建
     */
    @Setter
    @Getter
    private Integer autoCreated;

    /**
     * 适用sku
     */
    private Set<String> skus;

    /**
     * 适用类目id
     */
    private Set<Integer> categoryIds;

    /**
     * 适用全部商品，true 是，false 否
     */
    private Boolean all;

    /**
     * 商家优惠券id
     */
    private Integer merchantCouponId;

    /**
     * 领取次数  等于0不限  大于0就是实际限制领取次数
     */
    private Integer quantityClaimed;

    /**
     * 领取总量
     */
    private Integer grantAmount;

    /**
     * 领取限制 0-不限  1-限制多少张
     */
    private Integer grantLimit;

    /**
     * 可领取状态 1-立即领取 2-去使用 3-已抢光 4-已领取（领取到达上限）
     * @see CouponReceiveStatusEnum
     */
    private Integer receiveStatus;

    /**
     * 商品使用范围
     * @see CouponSkuScopeEnum
     */
    private Integer skuScope;

    /**
     * 前端展示需要
     */
    private Integer couponId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 领取类型 0-发放（人工）1-手动领取  2-抽奖活动 （被动）3-自动领取（新人注册/推荐好友下单）4-满返活动（被动） 5-其他
     */
    private Integer receiveType;

    public Coupon(Byte type,LocalDateTime vaildDate,Integer vaildTime,Boolean newHand,Integer agioType,Integer grouping){
        this.type = type;
        this.vaildDate = vaildDate;
        this.vaildTime = vaildTime;
        this.newHand = newHand;
        this.agioType = agioType;
        this.grouping = grouping;
        this.addTime = LocalDateTime.now();
    }

    public Coupon(){

    }

    public Coupon(String name,BigDecimal threshold,Integer agioType,Byte type,Integer grouping,Boolean newHand){
        this.name= name==null?null:name.trim();
        this.threshold=threshold;
        this.agioType=agioType;
        this.type=type;
        this.grouping=grouping;
        this.newHand=newHand;
        this.addTime= LocalDateTime.now();
    }

    public Integer getUseStatus() {
        return useStatus;
    }

    public void setUseStatus(Integer useStatus) {
        this.useStatus = useStatus;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public Integer getEffectiveNum() {
        return effectiveNum;
    }

    public void setEffectiveNum(Integer effectiveNum) {
        this.effectiveNum = effectiveNum;
    }

    public Integer getCouponSenderId() {
        return couponSenderId;
    }

    public void setCouponSenderId(Integer couponSenderId) {
        this.couponSenderId = couponSenderId;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public BigDecimal getMoney() {
        return money;
    }

    public void setMoney(BigDecimal money) {
        this.money = money;
    }

    public BigDecimal getThreshold() {
        return threshold;
    }

    public void setThreshold(BigDecimal threshold) {
        this.threshold = threshold;
    }

    public Byte getType() {
        return type;
    }

    public void setType(Byte type) {
        this.type = type;
    }

    public LocalDateTime getVaildDate() {
        return vaildDate;
    }

    public void setVaildDate(LocalDateTime vaildDate) {
        this.vaildDate = vaildDate;
    }

    public Integer getVaildTime() {
        return vaildTime;
    }

    public void setVaildTime(Integer vaildTime) {
        this.vaildTime = vaildTime;
    }

    public Boolean getNewHand() {
        return newHand;
    }

    public void setNewHand(Boolean newHand) {
        this.newHand = newHand;
    }

    public String getReamrk() {
        return reamrk;
    }

    public void setReamrk(String reamrk) {
        this.reamrk = reamrk;
    }

    public LocalDateTime getAddTime() {
        return addTime;
    }

    public void setAddTime(LocalDateTime addTime) {
        this.addTime = addTime;
    }

    public Integer getAgioType() {
        return agioType;
    }

    public void setAgioType(Integer agioType) {
        this.agioType = agioType;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getGrouping() {
        return grouping;
    }

    public void setGrouping(Integer grouping) {
        this.grouping = grouping;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public LocalDateTime getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDateTime startDate) {
        this.startDate = startDate;
    }

    public Integer getStartTime() {
        return startTime;
    }

    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    @Override
    public String toString() {
        return "Coupon{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", code='" + code + '\'' +
                ", money=" + money +
                ", threshold=" + threshold +
                ", type=" + type +
                ", vaildDate=" + vaildDate +
                ", vaildTime=" + vaildTime +
                ", newHand=" + newHand +
                ", reamrk='" + reamrk + '\'' +
                ", addTime=" + addTime +
                ", agioType=" + agioType +
                ", status=" + status +
                ", grouping=" + grouping +
                ", sku='" + sku + '\'' +
                ", categoryId='" + categoryId + '\'' +
                '}';
    }


}