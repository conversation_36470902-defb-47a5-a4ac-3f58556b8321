package net.summerfarm.mall.model.domain;

import io.swagger.annotations.ApiModelProperty;
import net.summerfarm.common.util.validation.groups.Update;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

public class DeliveryPath {
    @NotNull(groups = {Update.class},message = "id.not.null")
    private Integer id;

    private Integer storeNo;

    private LocalDate deliveryTime;

    private Long contactId;

    private String timeFrame;

    private String remark;

    private BigDecimal totalVolume;

    @NotNull(groups = {Update.class},message = "path.not.null")
    private String path;

    private Integer sort;

    private LocalDateTime addtime;

    private BigDecimal totalPrice;

    /**
    *  出样标签
    */
    private Integer type;

    private Integer pathStatus;

    private String finishPoi;

    private String finishPoiName;

    private String deliveryPic;

    private Integer finishDistance;

    @ApiModelProperty("是否是正常签收 0 正常 1 不正常")
    private Integer signForStatus;

    @ApiModelProperty("签收备注")
    private String signForRemarks;

    @ApiModelProperty("完成配送时间")
    private LocalDateTime finishTime;

    @ApiModelProperty("配送类型 配送 回收 配送/回收")
    private  Integer deliveryType;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getStoreNo() {
        return storeNo;
    }

    public void setStoreNo(Integer storeNo) {
        this.storeNo = storeNo;
    }

    public LocalDate getDeliveryTime() {
        return deliveryTime;
    }

    public void setDeliveryTime(LocalDate deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    public Long getContactId() {
        return contactId;
    }

    public void setContactId(Long contactId) {
        this.contactId = contactId;
    }

    public String getTimeFrame() {
        return timeFrame;
    }

    public void setTimeFrame(String timeFrame) {
        this.timeFrame = timeFrame;
    }

    public BigDecimal getTotalVolume() {
        return totalVolume;
    }

    public void setTotalVolume(BigDecimal totalVolume) {
        this.totalVolume = totalVolume;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public LocalDateTime getAddtime() {
        return addtime;
    }

    public void setAddtime(LocalDateTime addtime) {
        this.addtime = addtime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }


    public Integer getPathStatus() {
        return pathStatus;
    }

    public void setPathStatus(Integer pathStatus) {
        this.pathStatus = pathStatus;
    }

    public String getFinishPoi() {
        return finishPoi;
    }

    public void setFinishPoi(String finishPoi) {
        this.finishPoi = finishPoi;
    }

    public String getFinishPoiName() {
        return finishPoiName;
    }

    public void setFinishPoiName(String finishPoiName) {
        this.finishPoiName = finishPoiName;
    }

    public String getDeliveryPic() {
        return deliveryPic;
    }

    public void setDeliveryPic(String deliveryPic) {
        this.deliveryPic = deliveryPic;
    }

    public Integer getFinishDistance() {
        return finishDistance;
    }

    public void setFinishDistance(Integer finishDistance) {
        this.finishDistance = finishDistance;
    }

    public Integer getSignForStatus() {
        return signForStatus;
    }

    public void setSignForStatus(Integer signForStatus) {
        this.signForStatus = signForStatus;
    }

    public String getSignForRemarks() {
        return signForRemarks;
    }

    public void setSignForRemarks(String signForRemarks) {
        this.signForRemarks = signForRemarks;
    }

    public LocalDateTime getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(LocalDateTime finishTime) {
        this.finishTime = finishTime;
    }

    public Integer getDeliveryType() {
        return deliveryType;
    }

    public void setDeliveryType(Integer deliveryType) {
        this.deliveryType = deliveryType;
    }
}
