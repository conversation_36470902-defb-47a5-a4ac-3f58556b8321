package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * fence_delivery
 * <AUTHOR>
@Data
public class FenceDelivery implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 围栏id
     */
    private Integer fenceId;

    /**
     * 首配日
     */
    private LocalDate nextDeliveryDate;

    /**
     * 逻辑删除 0否 1是
     */
    private Byte deleteFlag;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private Integer updater;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}