package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * active_sku_purchase_quantity
 * <AUTHOR>
@Data
public class ActivitySkuPurchaseQuantity implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 商户id
     */
    private Long mId;

    /**
     * sku
     */
    private String sku;

    /**
     * 活动id
     */
    private Integer activityId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单项id
     */
    private Long orderItemId;

    /**
     * 用户购买数量
     */
    private Integer purchaseQuantity;

    /**
     * 用户剩余数量
     */
    private Integer remainQuantity;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 新营销活动id
     */
    private Long newActivityId;

    private static final long serialVersionUID = 1L;

    public ActivitySkuPurchaseQuantity(Long mId,String sku,Long newActivityId,Integer purchaseQuantity,Integer remainQuantity){
        this.mId = mId;
        this.sku = sku;
        this.newActivityId = newActivityId;
        this.purchaseQuantity = purchaseQuantity;
        this.remainQuantity = remainQuantity;
    }

    public ActivitySkuPurchaseQuantity(){

    }
}