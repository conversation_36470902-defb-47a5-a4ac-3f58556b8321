package net.summerfarm.mall.model.domain;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> ct
 * create at:  2020/11/13  16:13
 */
@Data
public class StoreAllocation {

    public static final Integer ALLOCATION_TYPE_WEEK = 1;
    public static final Integer ALLOCATION_TYPE_MONTH = 2;

    private Integer id;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime  gmtModified;

    /**
     * 状态 0 生效 1失效
     */
    private Integer status;

    /**
     * 类型 0 周 1 月
     */
    private Integer type;

    /**
     * 归属仓
     */
    private Integer storeNo;

    /**
     * 调拨出库仓
     */
    private Integer outStoreNo;

    /**
     * 调拨时间
     */
    private String allocationTime;

    /**
     * 是否是次日达 0 是 1 否
     */
    private Integer nextDayReach;

}
