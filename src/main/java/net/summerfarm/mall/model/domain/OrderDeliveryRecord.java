package net.summerfarm.mall.model.domain;

import lombok.Data;


import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> ct
 * create at:  2019/11/20  2:56 PM
 */
@Data
public class OrderDeliveryRecord implements Serializable {

    private Integer id;

    /**
    * 订单号
    */
    private String orderNo;

    /**
    * 下单时间
    */
    private Date addTime;

    /**
    * 应付配送费金额
    */
    private BigDecimal deliveryFee;

    /**
     * 实付配送费金额
     */
    private BigDecimal payDeliveryFee;

    /**
    * 免配送费规则
    */
    private String deliveryRule;

    /**
    * 免配送费日
    */
    private String deliveryFreeDay;

    /**
    * 大客户专享免邮规则
    */
    private String bigMerchantDeliveryRule;

    /**
    * 大客户配送费金额
    */
    private BigDecimal bigMerchantDeliveryFee;

    /**
    * 订单使用的免邮卡ID
    */
    private Integer deliveryCardID;

    /**
    * 订单使用运费券ID
    */
    private Integer deliveryCouponID;

}
