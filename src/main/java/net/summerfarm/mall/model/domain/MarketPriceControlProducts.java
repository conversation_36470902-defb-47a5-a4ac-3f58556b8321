package net.summerfarm.mall.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * market_price_control_products
 * <AUTHOR>
@Data
public class MarketPriceControlProducts implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 商品表-id
     */
    private Long pdId;

    /**
     * 商品表-spu编号
     */
    private String pdNo;

    /**
     * 商品表-商品名称
     */
    private String pdName;

    /**
     * 控价品是否隐藏实付价 2-隐藏  1-或者空不隐藏
     */
    private Integer priceHide;

    /**
     * 隐藏面价  1-不隐藏  2-隐藏（默认）
     */
    private Integer facePriceHide;

    /**
     * 隐藏控价线（和隐藏面价搭配使用）
     */
    private BigDecimal priceControlLine;

    /**
     * sku
     */
    private String sku;

    private static final long serialVersionUID = 1L;
}