package net.summerfarm.mall.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class OrderExportRecord implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 门店ID
     */
    private Long mId;

    /**
     * 门店邮箱
     */
    private String email;

    /**
     * 0-待发送  1-发送成功  2-发送失败
     */
    private Integer status;

    /**
     * 入参信息
     */
    private String param;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}