package net.summerfarm.mall.model.domain;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> ct
 * create at:  2021/1/28  15:07
 * 设置大客户类目展示
 */
@Data
public class MajorCategory {

    private Integer id ;

    /**
    * 添加时间
    */
    private LocalDateTime addTime;

    /**
    * 修改时间
    */
    private LocalDateTime updateTime;

    /**
    * 是否展示 0 展示 1 不展示
    */
    private Integer type ;

    /**
    * 是否删除 0 删除 1 未删除
    */
    private Integer status;

    /**
    * 类目id
    */
    private Integer categoryId;

    /**
    * 1 先结 2 账期
    */
    private Integer direct;

    /**
    * 城市编号
    */
    private Integer areaNo;

    /**
    * '大客户id'
    */
    private Integer adminId;


}
