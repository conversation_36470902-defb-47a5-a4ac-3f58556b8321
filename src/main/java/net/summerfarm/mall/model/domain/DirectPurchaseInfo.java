package net.summerfarm.mall.model.domain;

import lombok.Data;
import net.summerfarm.mall.common.util.DateUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Date: 2020/11/17 10:24
 * @Author: <EMAIL>
 */
@Data
public class DirectPurchaseInfo implements Serializable {

    private static final long serialVersionUID = -7843880994894026175L;

    /**
     * 自增长主键
     */
    private Integer id;

    /**
     * 用户id
     */
    private Long mId;

    /**
     * 采购类型为直发采购批次
     */
    private String purchaseNo;

    /**
     * 采购类型为直发采购对应的订单
     */
    private String orderNo;

    /**
     * 供应商发货时间
     */
    @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT)
    private LocalDate sendTime;

    /**
     * 采购用户收货地址
     */
    private String receivePlace;

    /**
     * 供应商给用户发货对应的一些物流信息
     */
    private String logisticsInfo;

    /**
     * 添加信息时间
     */
    private LocalDateTime addtime;

    /**
     * mId对应的contact表内的contact_id
     */
    private Long contactId;

    /**
     *mId选择对应的联系人
     */
    private String contactPhone;

    /**
     * 添加信息时间
     */
    private LocalDateTime oneClickShipTime;

    /**
     * 添加信息时间
     */
    private LocalDate deliveryDate;
}