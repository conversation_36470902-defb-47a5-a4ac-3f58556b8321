package net.summerfarm.mall.model.domain;

import java.time.LocalDateTime;

public class LuckyDrawWin {
    private Integer id;

    private Integer prizeId;

    private Integer luckyDrawId;

    private String prizeName;

    private Long mId;

    private LocalDateTime addtime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getPrizeId() {
        return prizeId;
    }

    public void setPrizeId(Integer prizeId) {
        this.prizeId = prizeId;
    }

    public String getPrizeName() {
        return prizeName;
    }

    public void setPrizeName(String prizeName) {
        this.prizeName = prizeName == null ? null : prizeName.trim();
    }

    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }

    public LocalDateTime getAddtime() {
        return addtime;
    }

    public void setAddtime(LocalDateTime addtime) {
        this.addtime = addtime;
    }

    public Integer getLuckyDrawId() {
        return luckyDrawId;
    }

    public void setLuckyDrawId(Integer luckyDrawId) {
        this.luckyDrawId = luckyDrawId;
    }
}