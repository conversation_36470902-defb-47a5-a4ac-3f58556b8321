package net.summerfarm.mall.model.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.common.util.validation.groups.Add;

import javax.persistence.Table;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import java.io.Serializable;
import java.util.Date;

/**
 * @Date 2022.04.21
 * <AUTHOR>
 */
@Table(name="focus_on")
@Data
@ApiModel(description = "关注商品表")
public class FocusOn implements Serializable {

    @ApiModelProperty(value = "id")
    @Null(message = "invertory.invId",groups = {Add.class})
    private Long id;

    @ApiModelProperty(value = "商户ID")
    private Long mId;

    @ApiModelProperty(value = "sku")
    @NotNull(message = "sku.null", groups = {Add.class})
    private String sku;

    @ApiModelProperty(value = "关注时间")
    private Date addTime;

    @ApiModelProperty(value = "城市编号")
    private Integer areaNo;

    @ApiModelProperty(value = "逻辑删除：0：未删除，1已删除")
    private Byte isDeleted;

    @ApiModelProperty(value = "是否到货提醒：0:不提醒，1:提醒")
    private Byte isRemind;
}
