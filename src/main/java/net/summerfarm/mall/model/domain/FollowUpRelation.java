package net.summerfarm.mall.model.domain;

import java.util.Date;

public class FollowUpRelation {
    private Integer id;

    private Long mId;

    private Integer adminId;

    private String adminName;

    private Date addTime;

    private Boolean reassign;

    private Date lastFollowUpTime;

    private Date reassignTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }

    public Integer getAdminId() {
        return adminId;
    }

    public void setAdminId(Integer adminId) {
        this.adminId = adminId;
    }

    public String getAdminName() {
        return adminName;
    }

    public void setAdminName(String adminName) {
        this.adminName = adminName == null ? null : adminName.trim();
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Boolean getReassign() {
        return reassign;
    }

    public void setReassign(Boolean reassign) {
        this.reassign = reassign;
    }

    public Date getLastFollowUpTime() {
        return lastFollowUpTime;
    }

    public void setLastFollowUpTime(Date lastFollowUpTime) {
        this.lastFollowUpTime = lastFollowUpTime;
    }

    public Date getReassignTime() {
        return reassignTime;
    }

    public void setReassignTime(Date reassignTime) {
        this.reassignTime = reassignTime;
    }
}