package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 主订单优惠明细表
 * @TableName master_order_preferential
 */
@Data
public class MasterOrderPreferential implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 主订单号
     */
    private String masterOrderNo;

    /**
     * 子订单号
     */
    private String orderNo;

    /**
     * 优惠金额
     */
    private BigDecimal amount;

    /**
     * 优惠类型
     */
    private Integer type;

    /**
     * type关联优惠id
     */
    private Long relatedId;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}