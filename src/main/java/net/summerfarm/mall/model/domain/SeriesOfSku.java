package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * series_of_sku
 * <AUTHOR>
@Data
public class SeriesOfSku implements Serializable {
    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * 聚合类型：0、鲜沐严选 1、商品排序
     */
    private Integer seriesType;

    /**
     * 聚合id
     */
    private Integer seriesId;

    /**
     * sku
     */
    private String sku;

    /**
     * 排序值，从小到大
     */
    private Integer sort;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    private static final long serialVersionUID = 1L;
}