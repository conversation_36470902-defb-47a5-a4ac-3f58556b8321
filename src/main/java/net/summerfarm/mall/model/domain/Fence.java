package net.summerfarm.mall.model.domain;

import lombok.Data;
import net.summerfarm.common.util.validation.groups.Add;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR> ct
 * create at:  2021/8/9  16:55 围栏实体
 *
 */
@Data
public class Fence implements Serializable {

    private Integer id ;

    /**
    * 围栏名称
    */
    @NotNull(groups = {Add.class},message = "围栏名称不能为空")
    private String fenceName;

    /**
    * 添加时间
    */
    private LocalDateTime addTime;

    /**
    * 修改时间
    */
    private LocalDateTime updateTime;

    /**
     * 运营区域编号
     */
    private Integer areaNo;

    /**
     * 配送仓编号
     */
    @NotNull(groups = {Add.class},message = "城配仓不能为空")
    private Integer storeNo;

    /**
     * 状态
     */
    private Integer status;


    /**
    * admin msg
    */
    private Integer adminId;


    /**
    * 打包编号 省的递归
    */
    private Integer packId;
}
