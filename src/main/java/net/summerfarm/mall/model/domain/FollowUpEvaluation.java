package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * follow_up_evaluation
 * <AUTHOR>
@Data
public class FollowUpEvaluation implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 拜访记录ID
     */
    private Integer followRecordId;

    /**
     * 满意级别
     */
    private Byte satisfactionLevel;

    /**
     * 评价标签
     */
    private String tag;

    /**
     * 评价描述
     */
    private String remark;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}