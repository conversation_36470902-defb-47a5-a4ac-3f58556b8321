package net.summerfarm.mall.model.domain;

import io.swagger.annotations.ApiModelProperty;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.common.util.validation.groups.Update;
import net.summerfarm.mall.common.util.DateUtils;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Created by wjd on 2018/7/13.
 */
public class MajorPrice {

    private Integer id;

    private String weight;

    private String sku;

    private String pdName;

    private BigDecimal price;

    private  Integer direct;

    private Integer areaNo;

    private Integer adminId;

    /**
     * 当为代仓商品时的支付方式：0：无需支付 1：下单时支付, 非代仓商品为NULL默认
     */
    private Integer payMethod;
    /**
     * 报价单的生效时间
     */
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    @NotNull(groups = {Add.class, Update.class} , message = "validTime is not null")
    private LocalDateTime validTime;

    /**
     * 报价单的失效时间
     */
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    @NotNull(groups = {Add.class, Update.class} , message = "invalidTime is not null")
    private LocalDateTime  invalidTime;

    //价格类型：0代表商城价 1合同价（指定价）2 合同价（毛利率）
    private Integer priceType;

    private Integer mallShow;
    /**
     * 运营大区
     */
    private Integer largeAreaNo;

    /**
     * 商城价浮动/加减值
     */
    private BigDecimal priceAdjustmentValue;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态：0=保存, 1=提交
     */
    private Integer status;
    public Integer getLargeAreaNo() {
        return largeAreaNo;
    }

    public void setLargeAreaNo(Integer largeAreaNo) {
        this.largeAreaNo = largeAreaNo;
    }

    public BigDecimal getPriceAdjustmentValue() {
        return priceAdjustmentValue;
    }

    public void setPriceAdjustmentValue(BigDecimal priceAdjustmentValue) {
        this.priceAdjustmentValue = priceAdjustmentValue;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
    public MajorPrice() {
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getPdName() {
        return pdName;
    }

    public void setPdName(String pdName) {
        this.pdName = pdName;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Integer getAreaNo() {
        return areaNo;
    }

    public void setAreaNo(Integer areaNo) {
        this.areaNo = areaNo;
    }

    public Integer getAdminId() {
        return adminId;
    }

    public void setAdminId(Integer adminId) {
        this.adminId = adminId;
    }

    public Integer getDirect() {
        return direct;
    }

    public void setDirect(Integer direct) {
        this.direct = direct;
    }

    public Integer getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(Integer payMethod) {
        this.payMethod = payMethod;
    }

    public LocalDateTime getValidTime() {
        return validTime;
    }

    public void setValidTime(LocalDateTime validTime) {
        this.validTime = validTime;
    }

    public LocalDateTime getInvalidTime() {
        return invalidTime;
    }

    public void setInvalidTime(LocalDateTime invalidTime) {
        this.invalidTime = invalidTime;
    }

    public Integer getPriceType() {
        return priceType;
    }

    public void setPriceType(Integer priceType) {
        this.priceType = priceType;
    }

    public Integer getMallShow() {
        return mallShow;
    }

    public void setMallShow(Integer mallShow) {
        this.mallShow = mallShow;
    }
}

