package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * prepay_inventory_record
 * <AUTHOR>
@Data
public class PrepayInventoryRecord implements Serializable {
    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * 大客户id
     */
    private Integer adminId;

    /**
     * 预付商品id
     */
    private Integer prepayInventoryId;

    /**
     * 使用数量
     */
    private Integer amount;

    /**
     * 是否有效：f、无效 t、有效（默认）
     */
    private Boolean valid;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * sku
     */
    private String sku;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    private static final long serialVersionUID = 1L;
}