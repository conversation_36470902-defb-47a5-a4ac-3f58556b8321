package net.summerfarm.mall.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class LuckyDrawPrizeRecord implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 抽奖活动ID
     */
    private Long activityId;

    /**
     * 卡劵ID
     */
    private Long couponId;

    /**
     * 客户卡劵ID
     */
    private Long merchantCouponId;

    /**
     * 客户ID
     */
    private Long mId;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}