package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * discount_card_use_record
 * <AUTHOR>
@Data
public class DiscountCardUseRecord implements Serializable {
    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * sku
     */
    private String  sku;

    /**
    * 组合包ID
    */
    private Integer suitId;
    /**
     * 优惠卡id
     */
    private Integer discountCardMerchantId;

    /**
     * 权益卡名称
     */
    private String cardName;

    /**
     * 生效次数
     */
    private Integer useTimes;

    /**
     * 折扣总金额
     */
    private BigDecimal totalDiscount;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    private static final long serialVersionUID = 1L;
}