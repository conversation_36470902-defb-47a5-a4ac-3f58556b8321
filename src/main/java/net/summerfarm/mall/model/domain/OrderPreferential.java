package net.summerfarm.mall.model.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.mall.enums.OrderPreferentialTypeEnum;

import java.math.BigDecimal;

/**
 * <AUTHOR> ct
 * create at:  2019/12/26  10:42
 */
@Data
@NoArgsConstructor
public class OrderPreferential {

    public static final Integer ACTIVITY = 0;

    public static final Integer SUIT = 1;

    public static final Integer MARKET_RULE =2;

    public static final  Integer LADDER = 3;

    public static final Integer PRE_SALE= 5;

    public static final Integer PANIC_BUY= 6;

    public static final  Integer COLLOCATION = 7;

    private Integer id;

    /**
     * 优惠类型 0 活动，1组合包,2 满减 ,3 阶梯价,4 黄金卡优惠
     * @see OrderPreferentialTypeEnum
     */
    private int type;

    /**
     * 活动名称
     */
    private String activityName;
    /**
    * 订单号
    */
    private String orderNo;

    /**
    * 优惠金额
    */
    private BigDecimal amount;

    /**
     * 关联Id，优惠券时为couponId
     */
    private Long relatedId;

    public OrderPreferential(OrderPreferentialTypeEnum typeEnum, BigDecimal amount){
        this.type = typeEnum.ordinal();
        this.amount = amount;
    }
}
