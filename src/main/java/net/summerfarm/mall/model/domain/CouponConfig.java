package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * coupon_config
 * <AUTHOR>
@Data
public class CouponConfig implements Serializable {
    private Integer id;

    /**
     * 购卡金额级别0 1 2 3 4 5，0为不满足充送的情况
     */
    private Integer level;

    /**
     * 购卡金额
     */
    private BigDecimal buyMoney;

    /**
     * 添加时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 0 存在 1 失效
     */
    private Integer status;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    private String updater;

    private static final long serialVersionUID = 1L;
}