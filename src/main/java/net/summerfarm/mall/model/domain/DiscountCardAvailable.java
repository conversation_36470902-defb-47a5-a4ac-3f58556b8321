package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * discount_card_available
 * <AUTHOR>
@Data
public class DiscountCardAvailable implements Serializable {
    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * 优惠卡id
     */
    private Integer discountCardId;

    /**
     * 可用sku
     */
    private String sku;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    private static final long serialVersionUID = 1L;
}