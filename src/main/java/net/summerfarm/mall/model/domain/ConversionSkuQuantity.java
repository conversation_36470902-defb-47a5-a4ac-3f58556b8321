package net.summerfarm.mall.model.domain;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR> ct
 * create at:  2021/10/22  14:24
 */
@Data
public class ConversionSkuQuantity {

    private Integer id;
    /**
     * 库存仓编号
     **/
    private Integer warehouseNo;

    /**
    * sku
    */
    private String sku;

    /**
    * 订单最小销售量
    */
    private Integer minSaleCnt;

    /**
    * 15天平均销量
    */
    private BigDecimal saleCntFifteen;

    /**
    * 7天平均销量
    */
    private BigDecimal saleCntSeven;

    /**
    * 计算时间
    */
    private LocalDate date;

    /**
    * 添加时间
    */
    private LocalDateTime addTime;

    /**
    * 修改时间
    */
    private LocalDateTime updateTime;


}
