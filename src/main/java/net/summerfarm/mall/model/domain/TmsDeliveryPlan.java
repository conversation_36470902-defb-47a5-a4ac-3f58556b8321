package net.summerfarm.mall.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class TmsDeliveryPlan implements Serializable {


    private Integer id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 配送时间
     */
    private LocalDate deliveryTime;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 外部门店id
     */
    private Long storeId;

    /**
     * 类型  1销售单 2售后单
     */
    private Integer type;

    /**
     * 状态 配送单状态 0 确认中 1配送 2取消
     */
    private Integer status;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 外部地址id
     */
    private Integer contactId;

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /**
     * 配送类型 配送 回收 配送/回收 自提
     */
    private Integer deliveryType;


}
