package net.summerfarm.mall.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

@Data
public class LuckyDrawActivityRecord implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 客户ID
     */
    private Long mId;

    /**
     * 抽奖活动ID
     */
    private Long activityId;

    /**
     * 参与时间-针对每日参与一次
     */
    private LocalDate participationTime;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}