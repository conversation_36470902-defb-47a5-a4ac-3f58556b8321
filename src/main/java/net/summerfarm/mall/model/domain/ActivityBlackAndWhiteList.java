package net.summerfarm.mall.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class ActivityBlackAndWhiteList implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * sku
     */
    private String sku;

    /**
     * 库存仓编号
     */
    private Integer warehouseNo;

    /**
     * 库存仓名称
     */
    private String warehouseName;

    /**
     * 城市名称
     */
    private String areaName;

    /**
     * 城市编号
     */
    private Integer areaNo;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 类型 1-黑名单  2-白名单
     */
    private Integer type;

    /**
     * 创建人
     */
    private String creator;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}