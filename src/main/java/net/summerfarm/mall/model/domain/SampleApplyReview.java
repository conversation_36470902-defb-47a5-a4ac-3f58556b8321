package net.summerfarm.mall.model.domain;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.Date;

/**
* 样品申请审核信息表
* <AUTHOR>
*/
@Data
public class SampleApplyReview {

    /**
    * 样品申请审核ID
    */
    private Integer id;
    /**
    * 样品申请ID
    */
    private Integer sampleId;
    /**
    * 创建人id
    */
    private Integer reviewId;
    /**
    * 创建人名称
    */
    @Length(max= 50,message="编码长度不能超过50")
    private String reviewName;
    /**
    * 审核时间
    */
    private Date auditTime;
    /**
    * 审核状态 0 通过 1 不通过
    */
    private Integer status;
    /**
    * 审核备注
    */
    @Length(max= 255,message="编码长度不能超过255")
    private String reviewRemark;
}
