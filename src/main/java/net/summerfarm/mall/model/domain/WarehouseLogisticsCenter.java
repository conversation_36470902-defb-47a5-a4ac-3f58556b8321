package net.summerfarm.mall.model.domain;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * 仓储物流中心
 */
@Data
public class WarehouseLogisticsCenter {
    private Integer id;

    private Integer storeNo;

    private String storeName;

    private Integer status;

    private Integer manageAdminId;

    private String poiNote;

    private String address;

    private Integer closeOrderType;

    private Integer originStoreNo;

    private Date sotFinishTime;

    private Integer creator;

    private Integer updater;

    private Date updateTime;

    private Date createTime;

    private String closeTime;

    private String updateCloseTime;

    private String personContact;

    private String phone;


}