package net.summerfarm.mall.model.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * contact_operate_log
 * <AUTHOR>
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ContactOperateLog implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 联系人id
     */
    private Long contactId;

    /**
     * 店铺id
     */
    private Long mId;

    /**
     * 操作人名称
     */
    private String operateName;

    /**
     * 0鲜沐 1商城
     */
    private Integer operateSource;

    /**
     * 操作类型 0新增 1修改 2删除 3自动审批
     */
    private Integer operateType;

    /**
     * 操作内容
     */
    private String context;

    private static final long serialVersionUID = 1L;

}