package net.summerfarm.mall.model.domain;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 外部平台信息
 * @createTime 2021年10月18日 17:27:00
 */
@Data
public class OuterPlatform {

    private Long id;

    /**
     * 外部平台 1：AOL；2：悸动烧仙草
     */
    private Integer outerPlatformId;

    /**
     * 外部平台名称
     */
    private String outerPlatformName;

    /**
     * 外部平台调用地址
     */
    private String callUrl;

    /**
     * 外部平台调用token
     */
    private String token;

    /**
     * 推送商品基础信息开关 0：关；1：开
     */
    private Integer pushGoodsSwitch;

    /**
     * 推送订单信息开关 0：关；1：开
     */
    private Integer pushOrderSwitch;

    /**
     * 订单回调开关 0：关；1：开
     */
    private Integer orderCallBackSwitch;

    /**
     * 推送门店开关 0：关；1：开
     */
    private Integer pushStoreSwitch;

    /**
     * 订单汇报开关 0：关；1：日汇报
     */
    private Integer orderReportSwitch;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

}