package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * expand_activity_quantity
 * <AUTHOR>
@Data
public class ExpandActivityQuantity implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 活动id
     */
    private Long expandActivityId;

    /**
     * sku
     */
    private String sku;

    /**
     * 商户id
     */
    private Long mId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单项id
     */
    private Long orderItemId;

    /**
     * 用户购买数量
     */
    private Integer purchaseQuantity;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;

    public ExpandActivityQuantity(Long mId,String sku,Long activityId,Integer purchaseQuantity){
        this.mId = mId;
        this.sku = sku;
        this.expandActivityId = activityId;
        this.purchaseQuantity = purchaseQuantity;
    }

    public ExpandActivityQuantity(){

    }
}