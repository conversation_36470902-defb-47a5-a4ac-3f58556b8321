package net.summerfarm.mall.model.domain.market;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ActivityScopeConfig implements Serializable {

    private Long id;

    /**
     * 基础信息id
     */
    private Long basicInfoId;

    /**
     * 范围id，全部的时候值为0
     */
    private Long scopeId;

    /**
     * 活动范围类型，0 全部，1 人群包，2 运营城市，3 运营大区
     */
    private Integer scopeType;

    /**
     * 最后一次修改人id
     */
    private Integer updaterId;

    /**
     * 是否已被删除，0 否，1 是
     */
    private Integer delFlag;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}
