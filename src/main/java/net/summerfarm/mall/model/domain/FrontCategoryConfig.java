package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * front_category_config
 * <AUTHOR>
@Data
public class FrontCategoryConfig implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * sku
     */
    private String sku;

    /**
     * 0不置顶，1置顶
     */
    private Integer sortTop;

    /**
     * 运营大区编号
     */
    private Integer largeAreaNo;

    /**
     * 前台类目id
     */
    private Integer frontCategoryId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}