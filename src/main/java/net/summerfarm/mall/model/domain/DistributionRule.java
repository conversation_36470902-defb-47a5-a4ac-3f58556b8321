package net.summerfarm.mall.model.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR> ct
 * create at:  2019/7/8  2:57 PM
 */
@ApiModel(value = "配送费规则")
@Data
public class DistributionRule implements Serializable {

    @ApiModelProperty("id")
    private Integer id ;

    @ApiModelProperty("城市")
    private Integer areaNo;

    @ApiModelProperty("配送费金额 单位 元")
    private BigDecimal deliveryFee ;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("所属大客户id")
    private Integer adminId;
}