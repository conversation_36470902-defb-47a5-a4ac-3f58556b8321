package net.summerfarm.mall.model.domain;

import net.summerfarm.enums.TrolleyProductTypeEnum;

import java.io.Serializable;
import java.util.Date;

public class Trolley implements Serializable {

    private Long mId;

    private Long accountId;

    private String sku;

    private String parentSku;

    //商品类型：0、普通商品 1、赠品
    private Integer productType;

    //默认为0
    private int suitId;

    private Integer quantity;

    private Byte check;

    private Date updateTime;

    private Boolean delFlag;

    private Long bizId;

    public Trolley() {
        //默认普通商品
        this.productType = TrolleyProductTypeEnum.NORMAL.ordinal();
    }

    public Trolley(Long merchantId,Long accountId ,String sku,String parentSku, Integer suitId, Integer quantity) {
        this(merchantId, accountId, sku,parentSku, suitId, TrolleyProductTypeEnum.NORMAL.ordinal(), quantity);
    }

    public Trolley(Long merchantId,Long accountId ,String sku, String parentSku, Integer suitId, Integer productType, Integer quantity) {
        this.mId = merchantId;
        this.accountId = accountId;
        this.sku = sku;
        this.parentSku = parentSku;
        this.suitId = suitId;
        this.productType = productType;
        this.quantity = quantity;
        this.updateTime = new Date();
    }

    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku == null ? null : sku.trim();
    }

    public String getParentSku() {
        return parentSku;
    }

    public void setParentSku(String parentSku) {
        this.parentSku = parentSku;
    }

    public int getSuitId() {
        return suitId;
    }

    public void setSuitId(int suitId) {
        this.suitId = suitId;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public Byte getCheck() {
        return check;
    }

    public void setCheck(Byte check) {
        this.check = check;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Boolean delFlag) {
        this.delFlag = delFlag;
    }

    public Integer getProductType() {
        return productType;
    }

    public void setProductType(Integer productType) {
        this.productType = productType;
    }

    @Override
    public String toString() {
        return "Trolley{" +
                "mId=" + mId +
                ", sku='" + sku + '\'' +
                ", suitId=" + suitId +
                ", productType=" + productType +
                ", quantity=" + quantity +
                ", check=" + check +
                ", updateTime=" + updateTime +
                ", delFlag=" + delFlag +
                '}';
    }

    public Long getBizId() {
        return bizId;
    }

    public void setBizId(Long bizId) {
        this.bizId = bizId;
    }
}