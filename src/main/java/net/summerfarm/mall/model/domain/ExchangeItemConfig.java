package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ExchangeItemConfig implements Serializable {

    private Long id;

    private Long scopeConfigId;

    private String sku;

    private Integer adjustType;

    private BigDecimal amount;

    private Integer priority;

    private String creator;

    private Date updateTime;

    private Date createTime;
}