package net.summerfarm.mall.model.domain;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2019-09-06
 * @description
 */
@Data
public class StoreRecord {
    private Integer id;

    private String batch;

    private String sku;

    private Integer type;

    private Integer quantity;

    private String unit;

    private String recorder;

    private String remark;

    private Date updateTime;

    private Integer areaNo;

    private LocalDate qualityDate;

    private LocalDate productionDate;

    private Integer storeQuantity;

    private BigDecimal cost;
}
