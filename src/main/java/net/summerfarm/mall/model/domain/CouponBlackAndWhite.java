package net.summerfarm.mall.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class CouponBlackAndWhite implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 优惠劵ID
     */
    private Long couponId;

    /**
     * sku
     */
    private String sku;

    /**
     * 名单类型 1-黑名单 2-白名单
     */
    private Integer type;

    /**
     * 优惠劵ID集合
     */
    private List<Long> couponIds;

    /**
     * 创建人
     */
    private String creator;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}