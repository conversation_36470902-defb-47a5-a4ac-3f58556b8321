package net.summerfarm.mall.model.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR> ct
 * create at:  2020/7/14  10:32
 */
@Data
public class TmsTask {

    @ApiModelProperty("id 自增")
    private Integer id;

    @ApiModelProperty("配送时间")
    private LocalDate DeliveryTime;

    @ApiModelProperty("路线")
    private String path;

    @ApiModelProperty("捡货商品数量")
    private Integer skuCnt;

    @ApiModelProperty("配送商家数量")
    private Integer concatCnt;

    @ApiModelProperty("任务状态")
    private Integer taskStatus;

    @ApiModelProperty("添加时间")
    private LocalDateTime addTime;

    @ApiModelProperty("完成捡货时间")
    private LocalDateTime pickUpTime;

    @ApiModelProperty("仓库编号")
    private Integer storeNo;

    @ApiModelProperty("出仓时间")
    private LocalDateTime outTime;

    @ApiModelProperty("装车照片")
    private String loadingPhotos;

    @ApiModelProperty("出仓温度")
    private BigDecimal outWarehouseTemperature;

    @ApiModelProperty("出仓晚点原因")
    private String overOutTimeReason;

    @ApiModelProperty("打卡时间")
    private LocalDateTime punchTime;
}
