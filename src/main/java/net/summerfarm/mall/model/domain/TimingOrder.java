package net.summerfarm.mall.model.domain;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

public class TimingOrder {
    private Integer id;

    private String orderNo;

    private Date deliveryStartTime;

    private Date deliveryEndTime;

    private Integer priceLadder;

    private Integer discountDeliveryTimes;

    private Integer deliveryTimes;

    private Date addTime;

    private Integer deliveryUnit;

    private Integer deliveryUpperLimit;

    private Integer type;

    public TimingOrder() {
    }

    public TimingOrder(String orderNo, Date deliveryStartTime, Date deliveryEndTime, Integer priceLadder, Integer discountDeliveryTimes, Integer deliveryTimes, Integer deliveryUnit, Integer deliveryUpperLimit) {
        this.orderNo = orderNo;
        this.deliveryStartTime = deliveryStartTime;
        this.deliveryEndTime = deliveryEndTime;
        this.priceLadder = priceLadder;
        this.discountDeliveryTimes = discountDeliveryTimes;
        this.deliveryTimes = deliveryTimes;
        this.deliveryUnit = deliveryUnit;
        this.deliveryUpperLimit = deliveryUpperLimit;
        this.addTime = new Date();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo == null ? null : orderNo.trim();
    }

    public Date getDeliveryStartTime() {
        return deliveryStartTime;
    }

    public void setDeliveryStartTime(Date deliveryStartTime) {
        this.deliveryStartTime = deliveryStartTime;
    }

    public Date getDeliveryEndTime() {
        return deliveryEndTime;
    }

    public void setDeliveryEndTime(Date deliveryEndTime) {
        this.deliveryEndTime = deliveryEndTime;
    }

    public Integer getPriceLadder() {
        return priceLadder;
    }

    public void setPriceLadder(Integer priceLadder) {
        this.priceLadder = priceLadder;
    }

    public Integer getDiscountDeliveryTimes() {
        return discountDeliveryTimes;
    }

    public void setDiscountDeliveryTimes(Integer discountDeliveryTimes) {
        this.discountDeliveryTimes = discountDeliveryTimes;
    }

    public Integer getDeliveryTimes() {
        return deliveryTimes;
    }

    public void setDeliveryTimes(Integer deliveryTimes) {
        this.deliveryTimes = deliveryTimes;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Integer getDeliveryUnit() {
        return deliveryUnit;
    }

    public void setDeliveryUnit(Integer deliveryUnit) {
        this.deliveryUnit = deliveryUnit;
    }

    public Integer getDeliveryUpperLimit() {
        return deliveryUpperLimit;
    }

    public void setDeliveryUpperLimit(Integer deliveryUpperLimit) {
        this.deliveryUpperLimit = deliveryUpperLimit;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

}