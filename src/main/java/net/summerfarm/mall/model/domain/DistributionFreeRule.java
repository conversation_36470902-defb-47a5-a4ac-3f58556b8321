package net.summerfarm.mall.model.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> ct
 * create at:  2019/7/8  2:25 PM
 */
@Data
@ApiModel("免配送费规则")
public class DistributionFreeRule implements Serializable {

    @ApiModelProperty("id")
    private Integer id;

    @ApiModelProperty("状态 0 生效  1 失效")
    private Integer status;

    @ApiModelProperty("免配送费条件")
    private String rule;

    @ApiModelProperty("配送费规则ID")
    private Integer distributionId;

}

