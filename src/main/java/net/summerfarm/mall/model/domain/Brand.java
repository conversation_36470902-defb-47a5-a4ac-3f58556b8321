package net.summerfarm.mall.model.domain;

public class Brand {
    private Integer brandId;

    private String name;

    private String alias;

    private String logoPath;

    private Integer priority;

    private String firstcharacter;

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias == null ? null : alias.trim();
    }

    public String getLogoPath() {
        return logoPath;
    }

    public void setLogoPath(String logoPath) {
        this.logoPath = logoPath == null ? null : logoPath.trim();
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public String getFirstcharacter() {
        return firstcharacter;
    }

    public void setFirstcharacter(String firstcharacter) {
        this.firstcharacter = firstcharacter == null ? null : firstcharacter.trim();
    }
}