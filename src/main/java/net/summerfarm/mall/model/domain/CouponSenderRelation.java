package net.summerfarm.mall.model.domain;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * coupon_sender_relation
 * <AUTHOR>
@Data
public class CouponSenderRelation implements Serializable {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 优惠劵id
     */
    @NotNull(message = "优惠卡ID不能为空！")
    private Integer couponId;

    /**
     * 发放设置id
     */
    private Integer couponSenderId;

    /**
     * 领取数量
     */
    private Integer number;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 是否从发放设置领取,true 是,false 否
     */
    private Boolean isFromSender;

    private static final long serialVersionUID = 1L;
}