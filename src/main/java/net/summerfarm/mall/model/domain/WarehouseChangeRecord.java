package net.summerfarm.mall.model.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@ApiModel(description = "配送仓冻结库存变动记录")
@Data
public class WarehouseChangeRecord implements Serializable {

    private Long id;

    @ApiModelProperty(value = "仓库编号")
    private Integer warehouseNo;

    @ApiModelProperty(value = "配送仓编号")
    private Integer storeNo;

    @ApiModelProperty(value = "id")
    private String sku;

    @ApiModelProperty(value = "记录人")
    private String recorder;

    @ApiModelProperty(value = "新销售冻结库存")
    private Integer newSaleLockQuantity;

    @ApiModelProperty(value = "原销售冻结库存")
    private Integer oldSaleLockQuantity;

    @ApiModelProperty(value = "库存类型")
    private String typeName;

    private String recordNo;

    private LocalDateTime addtime;
}