package net.summerfarm.mall.model.domain;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> ct
 * create at:  2021/2/24  15:47
 */
@Data
public class TemporaryActivityRuler {

    private Integer id;
    /** 规则id */
    private Integer rulerId;

    /** 二级类目 */
    private Integer categoryId;

    /** 品牌名 */
    private String brandName;

    /**与二级类目对应的时间点 当前没用 刚加上sku添加时间 现在根据inv_id 筛选*/
    private LocalDateTime categoryTime;
}
