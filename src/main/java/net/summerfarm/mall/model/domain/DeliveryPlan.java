package net.summerfarm.mall.model.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.mall.common.util.DateUtils;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.common.util.validation.groups.Update;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;

@ApiModel(description = "配送计划实体类")
@Data
public class DeliveryPlan {
    @ApiModelProperty(value = "配送计划id",hidden = true)
    private Integer id;

    @ApiModelProperty(value = "订单编号")
    @NotNull(message = "orderNo.null",groups = {Add.class})
    private String orderNo;

    /**
     *     拦截状态 0 正常 1被拦截
     */
    private Integer interceptFlag;

    @ApiModelProperty(value = "配送状态")
    private Integer status;

    @ApiModelProperty(value = "配送时间")
    @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT)
    private LocalDate deliveryTime;

    @ApiModelProperty(value = "旧配送时间")
    @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT)
    private LocalDate oldDeliveryTime;

    @ApiModelProperty(value = "配送数量")
    @Min(value = 0,groups = {Add.class, Update.class})
    private Integer quantity;

    @ApiModelProperty(value = "随单配送时对应的普通订单订单号")
    private String masterOrderNo;

    @ApiModelProperty(value = "更新时间",hidden = true)
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "联系人id")
    @NotNull(message = "联系人id不能为空！")
    private Long contactId;

    @ApiModelProperty(value = "配送方式,0默认普通，1自提")
    private Boolean deliverytype;

    @ApiModelProperty(value = "配送时间区间")
    private String timeFrame;

    private Long accountId;

    @ApiModelProperty(value = "下单仓编号")
    private Integer orderStoreNo;

    /**
     * 修改地址才会传 true 单纯修改数量不传
     */
    private Boolean updateMark;

    private Boolean updateAddressMark;

    /**
     * 配送评价状态0 -未评价 1-已评价
     */
    private Integer deliveryEvaluationStatus;

    /**
     * 管理员id
     */
    private Integer adminId;

    /**
     * 创建时间
     */
    private LocalDateTime addTime;

    public DeliveryPlan() {
    }

    public DeliveryPlan(String orderNo, Integer status, LocalDate deliveryTime, Integer quantity, String masterOrderNo,Long contactId,String timeFrame, Long accountId) {
        this.orderNo = orderNo;
        this.status = status;
        this.deliveryTime = deliveryTime;
        this.quantity = quantity;
        this.masterOrderNo = masterOrderNo;
        this.deliverytype=false;
        this.contactId=contactId;
        this.timeFrame=timeFrame;
        this.accountId = accountId;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo == null ? null : orderNo.trim();
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public LocalDate getDeliveryTime() {
        return deliveryTime;
    }

    public void setDeliveryTime(LocalDate deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    public Boolean getDeliverytype() {
        return deliverytype;
    }

    public void setDeliverytype(Boolean deliverytype) {
        this.deliverytype = deliverytype;
    }

    public String getTimeFrame() {
        return timeFrame;
    }

    public void setTimeFrame(String timeFrame) {
        this.timeFrame = timeFrame;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getMasterOrderNo() {
        return masterOrderNo;
    }

    public void setMasterOrderNo(String masterOrderNo) {
        this.masterOrderNo = masterOrderNo;
    }

    public Long getContactId() {
        return contactId;
    }

    public void setContactId(Long contactId) {
        this.contactId = contactId;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public Integer getOrderStoreNo() {
        return orderStoreNo;
    }

    public void setOrderStoreNo(Integer orderStoreNo) {
        this.orderStoreNo = orderStoreNo;
    }

    public LocalDate getOldDeliveryTime() {
        return oldDeliveryTime;
    }

    public void setOldDeliveryTime(LocalDate oldDeliveryTime) {
        this.oldDeliveryTime = oldDeliveryTime;
    }

    public Boolean getUpdateMark() {
        return updateMark;
    }

    public void setUpdateMark(Boolean updateMark) {
        this.updateMark = updateMark;
    }

    public Boolean getUpdateAddressMark() {
        return updateAddressMark;
    }

    public void setUpdateAddressMark(Boolean updateAddressMark) {
        this.updateAddressMark = updateAddressMark;
    }

    public Integer getDeliveryEvaluationStatus() {
        return deliveryEvaluationStatus;
    }

    public void setDeliveryEvaluationStatus(Integer deliveryEvaluationStatus) {
        this.deliveryEvaluationStatus = deliveryEvaluationStatus;
    }

    public Integer getInterceptFlag() {
        return interceptFlag;
    }

    public void setInterceptFlag(Integer interceptFlag) {
        this.interceptFlag = interceptFlag;
    }

    public Integer getAdminId() {
        return adminId;
    }

    public void setAdminId(Integer adminId) {
        this.adminId = adminId;
    }

    public LocalDateTime getAddTime() {
        return addTime;
    }

    public void setAddTime(LocalDateTime addTime) {
        this.addTime = addTime;
    }
}