package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * circle_people_relation
 * <AUTHOR>
@Data
public class CirclePeopleRelation implements Serializable {
    /**
     * 主键id
     */
    private Integer id;

    /**
     * 0秒杀、1严选
     */
    private Boolean type;

    /**
     * 具体规则id
     */
    private Integer ruleId;

    /**
     * 营销活动id
     */
    private Integer typeId;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    private static final long serialVersionUID = 1L;
}