package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * sort_combination
 * <AUTHOR>
@Data
public class SortCombination implements Serializable {
    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * 组合名称
     */
    private String name;

    /**
     * 更新人
     */
    private Integer updater;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    private static final long serialVersionUID = 1L;
}