package net.summerfarm.mall.model.domain;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.summerfarm.common.util.BaseDateUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * financial_invoice
 * <AUTHOR>
@ApiModel(description = "财务票据实体类")
@Data
@EqualsAndHashCode
public class FinancialInvoice implements Serializable {

    private static final long serialVersionUID = -6563578494228260549L;

    /**
     * 自增长主键
     */
    private Long id;

    /**
     * 取自invoice_config表内
     */

    private Long invoiceId;

    /**
     * 0: 增值税电子发票, 1:增值税专用发票
     */
    @ApiModelProperty(value = "0: 增值税电子发票, 1:增值税专用发票")
    private Integer invoiceType;

    /**
     * 开票时总金额,大客户下的则填入了则为填写的，若是未填写则为退款后订单总金额，普通门店则为退款后订单总金额
     */
    @ApiModelProperty(value = "开票时总金额,大客户下的则填入了则为填写的，若是未填写则为退款后订单总金额，普通门店则为退款后订单总金额")
    private BigDecimal amountMoney;

    /**
     * 开票进度:0:开票中,1:已开票, 2:开票失败
     */
    private Integer invoiceResult;

    /**
     * 取自admin表，发起人
     */
    @ApiModelProperty(value = "取自admin表，发起人")
    private Integer creatorId;

    /**
     * 取自admin表，发票处理人
     */
    @ApiModelProperty(value = "取自admin表，发票处理人")
    private Integer handlerId;

    /**
     * 发起人发起时填写的备注
     */
    @ApiModelProperty(value = "发起人发起时填写的备注")
    private String creatorRemark;

    /**
     * 处理人处理时填入的备注
     */
    @ApiModelProperty(value = "处理人处理时填入的备注")
    private String handlerRemark;

    /**
     * 处理人处理的时间
     */
    @ApiModelProperty(value = "处理人处理的时间")
    @DateTimeFormat(pattern = BaseDateUtils.LONG_DATE_FORMAT)
    private LocalDateTime handleTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 创建该发票时提交时间
     */
    @ApiModelProperty(value = "创建该发票时提交时间")
    @JSONField(format = BaseDateUtils.LONG_DATE_FORMAT)
    private LocalDateTime createTime;

    /**
     * 快递单号
     */
    @ApiModelProperty(value = "快递单号")
    private String express;

    @ApiModelProperty(value = "账期订单表id 来源于finance_accounting_period_order")
    private Long financeOrderId;

    /**
     * 票据状态:0:正常,1:部分红冲, 2:全额红冲,3:作废
     */
    private Integer invoiceStatus;
    /**
     * 发票流水号
     */
    private String serialNumber;
    /**
     * 发票pdf储存路劲
     */
    private String pdfUrl;
    /**
     * 发票合计税额
     */
    private BigDecimal taxAmount;
    /**
     * 免税品票:0 否,1 是
     */
    private Integer dutyFreeGood;
    /**
     * 发票代码
     */
    private String invoiceCode;
    /**
     * 发票号码
     */
    private String invoiceNumber;


    /**
     * 开票类型 0企业 1个人
     */
    private Integer belongType;

    /**
     * 邮箱地址
     */
    private String mailAddress;

    /**
     * 抬头
     */
    private String title;
}