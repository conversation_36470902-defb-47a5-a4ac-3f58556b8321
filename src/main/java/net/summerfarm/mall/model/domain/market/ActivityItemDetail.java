package net.summerfarm.mall.model.domain.market;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ActivityItemDetail implements Serializable {
    
    private Long id;

    /**
     * 商品配置id
     */
    private Long itemConfigId;

    /**
     * 叶子节点类目id
     */
    private Integer categoryId;

    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 是否已被删除，0 否，1 是
     */
    private Integer delFlag;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}