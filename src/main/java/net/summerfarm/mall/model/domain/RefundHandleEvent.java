package net.summerfarm.mall.model.domain;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
    * 退款事件表
    */
@Data
@NoArgsConstructor
public class RefundHandleEvent {
    /**
    * 主键
    */
    private Integer id;

    /**
    * 退款号
    */
    private String refundNo;

    /**
    * 状态0-NEW 1-IN_HANDLE 2-SUCCESS 3-FAIL
    */
    private Integer status;

    /**
    * 重试次数
    */
    private Integer retryCount;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 更新时间
    */
    private Date updateTime;
}