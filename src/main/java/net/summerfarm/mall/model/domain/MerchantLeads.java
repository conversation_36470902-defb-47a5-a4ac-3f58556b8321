package net.summerfarm.mall.model.domain;

/**
 * @Package: net.summerfarm.mall.model.domain
 * @Description:
 * @author: <EMAIL>
 * @Date: 2019-12-10
 */

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

public class MerchantLeads {
    private Long id;

    @NotNull(message = "name.null")
    private String mname;

    private String phone;
    @NotNull
    private String province;
    @NotNull
    private String city;
    @NotNull
    private String area;

    @NotNull(message = "address.null")
    private String address;

    @NotNull(message = "poi.null")
    private String poiNote;

    @NotNull
    private Integer areaNo;

    private String areaName;

    private String size;

    private String author;

    private Integer adminId;

    private String adminName;

    private String source;

    private String mcontact;

    private Integer status;

    private String remark;


    /**
     * 门牌号
     */
    private String houseNumber;

    /**
     * 企业规模
     */
    private String enterpriseScale;

    /**
     * 公司品牌
     */
    private String companyBrand;


    private String type;

    private Long mId;

    /**
     * 0-鲜沐客户，1-pop客户
     */
    @Getter
    @Setter
    private Integer merchantType;

    /**
     * 门头照
     */
    @Getter
    @Setter
    private String doorPic;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMname() {
        return mname;
    }

    public void setMname(String mname) {
        this.mname = mname == null ? null : mname.trim();
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone == null ? null : phone.trim();
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province == null ? null : province.trim();
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city == null ? null : city.trim();
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area == null ? null : area.trim();
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address == null ? null : address.trim();
    }

    public String getPoiNote() {
        return poiNote;
    }

    public void setPoiNote(String poiNote) {
        this.poiNote = poiNote == null ? null : poiNote.trim();
    }

    public Integer getAreaNo() {
        return areaNo;
    }

    public void setAreaNo(Integer areaNo) {
        this.areaNo = areaNo;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName == null ? null : areaName.trim();
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size == null ? null : size.trim();
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author == null ? null : author.trim();
    }

    public Integer getAdminId() {
        return adminId;
    }

    public void setAdminId(Integer adminId) {
        this.adminId = adminId;
    }

    public String getAdminName() {
        return adminName;
    }

    public void setAdminName(String adminName) {
        this.adminName = adminName == null ? null : adminName.trim();
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source == null ? null : source.trim();
    }

    public String getMcontact() {
        return mcontact;
    }

    public void setMcontact(String mcontact) {
        this.mcontact = mcontact == null ? null : mcontact.trim();
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getHouseNumber() {
        return houseNumber;
    }

    public void setHouseNumber(String houseNumber) {
        this.houseNumber = houseNumber;
    }

    public String getEnterpriseScale() {
        return enterpriseScale;
    }

    public void setEnterpriseScale(String enterpriseScale) {
        this.enterpriseScale = enterpriseScale;
    }

    public String getCompanyBrand() {
        return companyBrand;
    }

    public void setCompanyBrand(String companyBrand) {
        this.companyBrand = companyBrand;
    }


    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }
}