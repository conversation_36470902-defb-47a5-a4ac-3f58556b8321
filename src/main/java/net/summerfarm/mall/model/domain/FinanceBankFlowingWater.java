package net.summerfarm.mall.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * finance_bank_flowing_water
 * <AUTHOR>
@Data
public class FinanceBankFlowingWater implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 交易日
     */
    private String tradingDay;

    /**
     * 交易时间
     */
    private String tradingTime;

    /**
     * 起息日
     */
    private String valueDate;

    /**
     * 交易类型
     */
    private String tradingType;

    /**
     * 摘要
     */
    private String abstractText;

    /**
     * 交易金额
     */
    private BigDecimal transactionAmount;

    /**
     * 借贷标记 C:贷；D:借
     */
    private String mark;

    /**
     * 流水号 银行会计系统交易流水号,可以和回单命名中的流水号关联
     */
    private String serialNumber;

    /**
     * 流程实例号 企业银行交易序号，唯一标示企业银行客户端发起的一笔交易
     */
    private String processInstanceNumber;

    /**
     * 业务名称
     */
    private String businessName;

    /**
     * 用途
     */
    private String purpose;

    /**
     * 业务参考号
     */
    private String businessReferenceNumber;

    /**
     * 业务摘要
     */
    private String businessSummary;

    /**
     * 其他摘要
     */
    private String otherSummaries;

    /**
     * 收/付方开户地区分行号
     */
    private String bankAreaNo;

    /**
     * 收/付方名称
     */
    private String userName;

    /**
     * 收/付方帐号
     */
    private String accountNumber;

    /**
     * 收/付方开户行行号
     */
    private String bankNo;

    /**
     * 收/付方开户行名
     */
    private String bankName;

    /**
     * 收/付方开户行地址
     */
    private String bankAddress;

    /**
     * 母/子公司所在地区分行
     */
    private String companyDivision;

    /**
     * 母/子公司帐号
     */
    private String companyAccount;

    /**
     * 母/子公司名称
     */
    private String companyName;

    /**
     * 信息标志
     */
    private String informationSigns;

    /**
     * 有否附件信息标志 Y：是 N：否
     */
    private String informationExistence;

    /**
     * 票据号
     */
    private String billNo;

    /**
     * 冲帐标志 N	*为冲帐，X为补帐（冲账交易与原交易借贷相反）
     */
    private String reversalFlag;

    /**
     * 扩展摘要 有效位数为16
     */
    private String extendedSummary;

    /**
     * 交易分析码 1-2位取值含义见附录A.8交易分析码，3-6位取值含义见trscod字段说明。
     */
    private String transactionAnalysisCode;

    /**
     * 商务支付订单号 由商务支付订单产生
     */
    private String paymentOrderNo;

    /**
     * 企业识别码 开通收方识别功能的账户可以通过此码识别付款方
     */
    private String enterpriseIdentificationCode;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 认领状态；0-待认领；1-部分认领；2-全部认领 3-已撤销
     */
    private Integer claimStatus;

    /**
     * 收款类型 0 账期账单 1 鲜沐卡
     */
    private Integer payType;

    private static final long serialVersionUID = 1L;
}