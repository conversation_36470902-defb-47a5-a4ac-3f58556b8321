package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 主订单表
 * @TableName master_order
 */
@Data
public class MasterOrder implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 主订单编号
     */
    private String masterOrderNo;

    /**
     * 商户编号
     */
    private Long mId;

    /**
     * 主订单生成时间
     */
    private LocalDateTime orderTime;

    /**
     * 订单类型
     */
    private Integer type;

    /**
     * 主订单状态
     */
    private Integer status;

    /**
     * 总金额
     */
    private BigDecimal totalPrice;

    /**
     * 应付金额
     */
    private BigDecimal originPrice;

    /**
     * 运营区域编号
     */
    private Integer areaNo;

    /**
     * 客户类型
     */
    private String mSize;

    /**
     * 子账号id
     */
    private Long accountId;

    /**
     * 大客户的门店下单时，该门店下单时所属大客户
     */
    private Integer adminId;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}