package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 主子单支付关联关系表
 * @TableName payment_relation
 */
@Data
public class PaymentRelation implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 主支付单id
     */
    private Long masterPaymentId;

    /**
     * 子支付单id
     */
    private Long paymentId;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}