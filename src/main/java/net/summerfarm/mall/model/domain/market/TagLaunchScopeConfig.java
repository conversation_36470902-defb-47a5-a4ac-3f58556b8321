package net.summerfarm.mall.model.domain.market;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class TagLaunchScopeConfig implements Serializable {
    
    private Long id;

    
    private Long infoId;

    
    private Long scopeId;

    
    private Integer updaterId;

    
    private LocalDateTime createTime;

    
    private LocalDateTime updateTime;
    
}