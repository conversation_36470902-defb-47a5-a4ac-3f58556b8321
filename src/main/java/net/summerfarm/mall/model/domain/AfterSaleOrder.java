package net.summerfarm.mall.model.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.common.util.validation.groups.Update;
import net.summerfarm.enums.AfterSaleOrderStatus;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import java.io.Serializable;
import java.time.LocalDateTime;

@ApiModel(description = "售后实体类")
@Data
public class AfterSaleOrder implements Serializable {

    /** 未到货售后 */
    public final static int DELIVERY_NOT_RECEIVED = 0;

    /** 已到货售后 */
    public final static int DELIVERY_RECEIVED = 1;

    @ApiModelProperty(value = "售后id", hidden = true)
    @Null(message = "param.illegal", groups = Add.class)
    private Integer id;

    /**
     * 售后单类型：0 普通售后单，1 拦截售后单'
     */
    private Integer afterSaleOrderStatus;

    @ApiModelProperty(value = "售后编号")
    @Null(message = "param.illegal", groups = Add.class)
    private String afterSaleOrderNo;

    @ApiModelProperty(value = "商户id")
    @Null(message = "param.illegal", groups = {Add.class, Update.class})
    private Long mId;

    @ApiModelProperty(value = "子账号id")
    @Null(message = "param.illegal", groups = {Add.class, Update.class})
    private Long accountId;

    @ApiModelProperty(value = "订单编号")
    @NotNull(message = "order.no.null ", groups = Add.class)
    private String orderNo;

    @ApiModelProperty(value = "sku编号")
    @NotNull(message = "sku.null", groups = Add.class)
    private String sku;

    /**
     * {@link AfterSaleOrderStatus}
     */
    @ApiModelProperty(value = "售后状态", hidden = true)
    private Integer status;

    /**
     * 0为没退运费，1为退了运费
     */
    private Integer refundFreight;

    @ApiModelProperty(value = "售后单位", hidden = true)
    private String afterSaleUnit;

    @ApiModelProperty(value = "添加时间", hidden = true)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime addTime;

    @ApiModelProperty(value = "售后订单类型：0普通售后，1极速退款,2全部售后", required = false)
    private Integer type;

    @ApiModelProperty(value = "记录等级", hidden = true)
    private Integer grade;

    /** 0 未到货售后 1 已到货售后 */
    private Integer deliveryed;

    private Integer suitId;

    /**
     * 售后次数 默认1
     */
    private Integer times;

    private Integer view;

    /**
     * 是否全额退款 默认0
     */
    private Boolean isFull;

    /**
     * 商品类型：0、普通商品 1、赠品 2、换购
     */
    private Integer productType;

    @Setter
    @Getter
    private Boolean isManage;

    /**
     * 售后类型
     */
    @Getter
    @Setter
    private Integer afterSaleRemarkType;
    /**
     * 售后类型备注
     */
    @Setter
    @Getter
    private String afterSaleRemark;

    /**
     * 配送计划ID
     */
    private Integer deliveryId;

    /**
    * 是否需要回收费 0 不需要 1 需要
    */
    private Integer recoveryType;

    /**
     * 补发是否带货回来：0没带，1带了
     */
    private Integer carryingGoods;

    /**
     * 关单人
     */
    private String closer;

    /**
     * 关单时间
     */
    private LocalDateTime closeTime;

    /**
     * 售后快照信息（平台、供应商定责比例）
     */
    private String snapshot;

    @Getter
    @Setter
    private Integer autoAfterSaleFlag;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getAfterSaleOrderNo() {
        return afterSaleOrderNo;
    }

    public void setAfterSaleOrderNo(String afterSaleOrderNo) {
        this.afterSaleOrderNo = afterSaleOrderNo;
    }

    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }


    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getAfterSaleUnit() {
        return afterSaleUnit;
    }

    public void setAfterSaleUnit(String afterSaleUnit) {
        this.afterSaleUnit = afterSaleUnit;
    }

    public LocalDateTime getAddTime() {
        return addTime;
    }

    public void setAddTime(LocalDateTime addTime) {
        this.addTime = addTime;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getGrade() {
        return grade;
    }

    public void setGrade(Integer grade) {
        this.grade = grade;
    }

    public Integer getDeliveryed() {
        return deliveryed;
    }

    public void setDeliveryed(Integer deliveryed) {
        this.deliveryed = deliveryed;
    }

    public Integer getSuitId() {
        return suitId;
    }

    public void setSuitId(Integer suitId) {
        this.suitId = suitId;
    }

    public Integer getTimes() {
        return times;
    }

    public void setTimes(Integer times) {
        this.times = times;
    }

    public Integer getView() {
        return view;
    }

    public void setView(Integer view) {
        this.view = view;
    }

    public Boolean getIsFull() {
        return isFull;
    }

    public void setIsFull(Boolean isFull) {
        isFull = isFull;
    }

    public Integer getDeliveryId() {
        return deliveryId;
    }

    public void setDeliveryId(Integer deliveryId) {
        this.deliveryId = deliveryId;
    }

    public Integer getRecoveryType() {
        return recoveryType;
    }

    public void setRecoveryType(Integer recoveryType) {
        this.recoveryType = recoveryType;
    }

    public String getCloser() {
        return closer;
    }

    public void setCloser(String closer) {
        this.closer = closer;
    }

    public LocalDateTime getCloseTime() {
        return closeTime;
    }

    public void setCloseTime(LocalDateTime closeTime) {
        this.closeTime = closeTime;
    }
}