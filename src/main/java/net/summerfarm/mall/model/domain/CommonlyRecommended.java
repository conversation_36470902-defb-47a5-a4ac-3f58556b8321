package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * commonly_recommended
 * <AUTHOR>
@Data
public class CommonlyRecommended implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * sku
     */
    private String sku;

    /**
     * 商户id
     */
    private Long mId;

    /**
     * 商品排序分值
     */
    private BigDecimal score;

    /**
     * 一年内购买次数
     */
    private Integer purchases;

    /**
     * 同步时间标记(yyyyMMdd/yyyyMM)
     */
    private Integer dateFlag;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}