package net.summerfarm.mall.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR> ct
 * create at:  2021/8/10  14:34
 */
@Data
public class AdCodeMsg implements Serializable {

    private Integer id;

    private LocalDateTime addTime;

    private LocalDateTime updateTime;

    /**
    * 状态
    */
    private Integer status;
    /**
     * 区域编码
     */
    private String adCode;

    /**
    * 省份
    */
    private String province;

    /**
    * 城市
    */
    private String city;

    /**
     * 区域
     */
    private String area;

    /**
     * 等级
     */
    private String level;

    /**
    * 高德
    */
    private String gdId;

    /**
    * 围栏id
    */
    private Integer fenceId;

    public AdCodeMsg(){}

    public AdCodeMsg(Contact contact){
        this.city = contact.getCity();
    }

}
