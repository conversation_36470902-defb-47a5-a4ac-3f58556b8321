package net.summerfarm.mall.model.domain;

import java.util.List;

public class MajorRebate {

    private Integer id;

    private String sku;

    private String name;

    private String weight;

    private Integer type;

    private Double number;

    private Integer adminId;

    private Integer areaNo;

    private String areaName;

    private Integer status;

    private Integer cate;

    private List<MajorRebate> majorRebates;

    public MajorRebate() {
    }

    public MajorRebate(Integer id, Integer areaNo, String areaName) {
        this.id = id;
        this.areaNo = areaNo;
        this.areaName = areaName;
    }

    public MajorRebate(Integer adminId, Integer areaNo){
        this.adminId = adminId;
        this.areaNo = areaNo;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Double getNumber() {
        return number;
    }

    public void setNumber(Double number) {
        this.number = number;
    }

    public Integer getAdminId() {
        return adminId;
    }

    public void setAdminId(Integer adminId) {
        this.adminId = adminId;
    }

    public Integer getAreaNo() {
        return areaNo;
    }

    public void setAreaNo(Integer areaNo) {
        this.areaNo = areaNo;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public List<MajorRebate> getMajorRebates() {
        return majorRebates;
    }

    public void setMajorRebates(List<MajorRebate> majorRebates) {
        this.majorRebates = majorRebates;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getCate() {
        return cate;
    }

    public void setCate(Integer cate) {
        this.cate = cate;
    }
}
