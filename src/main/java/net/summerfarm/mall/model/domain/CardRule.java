package net.summerfarm.mall.model.domain;

import lombok.Data;
import net.summerfarm.mall.common.util.DateUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

@Data
public class CardRule {

    private Integer id;

    private Integer cardId;

    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime startTime;

    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime endTime;

    private Integer areaNo;

    private Integer merchantType;

    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime addTime;
}
