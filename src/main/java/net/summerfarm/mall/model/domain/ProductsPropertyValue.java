package net.summerfarm.mall.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * products_property_value
 * <AUTHOR>
@Data
public class ProductsPropertyValue implements Serializable {
    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * pd_id
     */
    private Long pdId;

    /**
     * sku
     */
    private String sku;

    /**
     * 属性id
     */
    private Integer productsPropertyId;

    /**
     * 属性值
     */
    private String productsPropertyValue;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    private static final long serialVersionUID = 1L;
}