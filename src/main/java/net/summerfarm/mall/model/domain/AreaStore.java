package net.summerfarm.mall.model.domain;

import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ToString
public class AreaStore {
    private Integer id;

    private Integer areaNo;

    private String sku;

    /** 可用库存 */
    private Integer quantity;

    private Date updateTime;

    private Integer adminId;

    private Integer leadTime;

    private int count;

    private BigDecimal costPrice;

    private BigDecimal marketPrice;

    private Integer priceStatus;

    /** 虚拟库存 */
    private Integer onlineQuantity;

    private Integer lockQuantity;

    private Integer saleLockQuantity;

    private Integer roadQuantity;

    private Integer change;

    private int sync = 0;


    private Boolean autoTransfer;

    private Integer safeQuantity;

    /**
    * 是否支持预留库存
    */
    private Integer supportReserved;

    /**
    * 预留库存最大值
    */
    private Integer reserveMaxQuantity;
    /**
     * 预留库存最小值
     */
    private Integer reserveMinQuantity;

    /**
     * 预留库存剩余数量(可以为负值) 展示 大于最小值 取剩余数量 小于最小值 取最小值
     */
    private Integer reserveUseQuantity;

    /**
     * 预警库存
     */
    private Integer warningQuantity;

    /**
     * 是否预警消息提醒过 0 未提醒 1 已提醒
     */
    private Integer sendWarningFlag;

    /**
     * 预售库存
     */
    private Integer advanceQuantity;

    private Long tenantId;

    private Long warehouseTenantId;

    public AreaStore() {

    }

    public AreaStore(Integer areaNo, String sku) {
        this.areaNo = areaNo;
        this.sku = sku;
    }


}
