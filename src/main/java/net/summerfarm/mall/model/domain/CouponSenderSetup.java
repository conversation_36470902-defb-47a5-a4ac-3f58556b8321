package net.summerfarm.mall.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * coupon_sender_setup
 * <AUTHOR>
@Data
public class CouponSenderSetup implements Serializable {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 设置名称
     */
    private String name;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 配置规则类型 0-全部用户(废弃) 1-圈人 2-城市规则
     * @see net.summerfarm.enums.coupon.CouponSenderSetupTypeEnum
     */
    private Integer type;

    /**
     * 发放方式 1-需用户领取 2-新人注册后立即发放 3-推荐好友下单，确定收货后立即发放
     * @see net.summerfarm.enums.coupon.CouponSenderSetupSenderTypeEnum
     */
    private Integer senderType;

    /**
     * 有效状态 0-未生效 1-有效 2-无效(过期失效) 3-无效(人工关闭)
     * @see net.summerfarm.enums.coupon.CouponSenderSetupStatusEnum
     */
    private Integer status;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private Integer updater;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}