package net.summerfarm.mall.model.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.mall.common.util.DateUtils;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * @date 2022.04.21
 * <AUTHOR>
 */
@Data
public class SkuPriceFluctuation implements Serializable {

    private Long id;

    /**
     * 销售价格
     * */
    private BigDecimal price;

    private String sku;

    /**
     * 记录时间
     * */
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private Date recodeTime;

    /**
     * 添加时间
     * */
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime addTime;

    /**
     * 活动价格
     * */
    private BigDecimal activityPrice;


    private Integer areaNo;


}
