package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * merchant_business_switch
 * <AUTHOR>
@Data
public class MerchantBusinessSwitch implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 用户标记业务类型-0常用推荐new开关
     */
    private Integer type;

    /**
     * 商户id
     */
    private Long mId;

    /**
     * 1开启，0不开启
     */
    private Byte enabled;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}