package net.summerfarm.mall.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class LuckyDrawActivityEquityPackage implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 抽奖活动ID
     */
    private Long activityId;

    /**
     * 奖项名称
     */
    private String name;

    /**
     * 中奖概率
     */
    private BigDecimal probability;

    /**
     * 奖项数量
     */
    private Integer quantity;

    /**
     * 剩余数量
     */
    private Integer surplusQuantity;

    /**
     * 发放数量
     */
    private Integer sendQuantity;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}