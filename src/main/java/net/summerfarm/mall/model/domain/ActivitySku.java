package net.summerfarm.mall.model.domain;

import java.math.BigDecimal;

/**
 * Created by wjd on 2017/9/25.
 */
public class ActivitySku {

    private Integer id;

    private Integer activityId;

    private String sku;

    private String skuName;

    private String weight;

    private String  unit;

    private String  logo;

    private BigDecimal activityPrice;

    private BigDecimal salePrice;

    /**
     * 活动库存
     */
    private Integer activityStock;
    /**
     * 限购数量
     */
    private Integer limitedQuantity;

    public ActivitySku() {
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getActivityId() {
        return activityId;
    }

    public void setActivityId(Integer activityId) {
        this.activityId = activityId;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    public BigDecimal getActivityPrice() {
        return activityPrice;
    }

    public void setActivityPrice(BigDecimal activityPrice) {
        this.activityPrice = activityPrice;
    }

    public BigDecimal getSalePrice() {
        return salePrice;
    }

    public void setSalePrice(BigDecimal salePrice) {
        this.salePrice = salePrice;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public Integer getActivityStock() {
        return activityStock;
    }

    public void setActivityStock(Integer activityStock) {
        this.activityStock = activityStock;
    }

    public Integer getLimitedQuantity() {
        return limitedQuantity;
    }

    public void setLimitedQuantity(Integer limitedQuantity) {
        this.limitedQuantity = limitedQuantity;
    }
}
