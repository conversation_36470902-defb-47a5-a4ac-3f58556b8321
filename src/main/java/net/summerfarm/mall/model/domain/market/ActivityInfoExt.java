package net.summerfarm.mall.model.domain.market;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ActivityInfoExt implements Serializable {
    
    private Long id;

    /**
     * 基本信息id
     */
    private Long basicInfoId;

    /**
     * 活动限购类型，0 不限购，1 每人，2 每人每天
     */
    private Integer limitType;

    /**
     * 活动限购数量
     */
    private Integer limitNum;

    /**
     * 活动特殊规则json
     */
    private String ruleDetail;

    /**
     * 最后一次修改人id
     */
    private Integer updaterId;

    /**
     * 是否已被删除，0 否，1 是
     */
    private Integer delFlag;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}