package net.summerfarm.mall.model.domain;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.concurrent.atomic.AtomicInteger;

@Data
public class CompanyAccount {
    private Integer id;

    private String companyName;

    private String wxAccountInfo;

    private Integer adminId;

    private LocalDateTime addtime;

    private Integer channel;

    private String mchAppId;

    private String mchxAppId;


    private AtomicInteger payDayCount = new AtomicInteger(0);
}
