package net.summerfarm.mall.model.domain;

import lombok.Data;
import net.summerfarm.mall.enums.CommonStatus;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class MerchantCancel implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 门店ID
     */
    private Long mId;

    /**
     * 注册手机号
     */
    private String phone;

    /**
     * 门店名称
     */
    private String mName;

    /**
     * 注销状态 默认-1（待注销） MerchantCancelEnum
     */
    private Integer status;

    /**
     * 申请原因
     */
    private String remake;

    /**
     * 注销来源 默认商城-0
     * @see CommonStatus
     */
    private Integer resource;

    /**
     * 申请凭证-后台申请必填
     */
    private String certificate;

    /**
     * 申请人
     */
    private Long creator;

    /**
     * 更新人
     */
    private Long updater;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 运营区域
     */
    private Integer areaNo;

    private static final long serialVersionUID = 1L;
}