package net.summerfarm.mall.model.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.mall.enums.OrderPreferentialTypeEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * order_item_preferential
 * <AUTHOR>
@Data
@NoArgsConstructor
public class OrderItemPreferential {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 优惠金额
     */
    private BigDecimal amount;

    /**
     * 订单项id
     */
    private Long orderItemId;

    /**
     * 优惠类型 0 活动 1组合包 2满减 3阶梯价 4黄金卡优惠 5预售尾款立减优惠 6秒杀 7搭配购 8多人拼团 9优惠券
     */
    private Integer type;

    /**
     * type关联优惠id
     */
    private Long relatedId;

    /**
     * 优惠名称
     */
    private String activityName;

    /**
     * 优惠明细快照-根据相应的优惠类型
     */
    private String discountsDetailSnapshot;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;

    public OrderItemPreferential(OrderPreferentialTypeEnum typeEnum, BigDecimal amount){
        this.type = typeEnum.ordinal();
        this.amount = amount;
    }
}