package net.summerfarm.mall.model.domain;

import java.math.BigDecimal;
import java.util.Date;

public class LadderPrice {
    private Integer id;

    private Integer timingRuleId;

    private Integer ladder;

    private BigDecimal price;

    private Date updateTime;

    private Integer discountDeliveryTimes;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getTimingRuleId() {
        return timingRuleId;
    }

    public void setTimingRuleId(Integer timingRuleId) {
        this.timingRuleId = timingRuleId;
    }

    public Integer getLadder() {
        return ladder;
    }

    public void setLadder(Integer ladder) {
        this.ladder = ladder;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getDiscountDeliveryTimes() {
        return discountDeliveryTimes;
    }

    public void setDiscountDeliveryTimes(Integer discountDeliveryTimes) {
        this.discountDeliveryTimes = discountDeliveryTimes;
    }
}