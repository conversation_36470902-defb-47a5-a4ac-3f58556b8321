package net.summerfarm.mall.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class LuckyDrawActivityPrize implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 抽奖活动ID
     */
    private Long activityId;

    /**
     * 抽奖活动权益包ID
     */
    private Long equityPackageId;

    /**
     * 卡劵ID-奖品
     */
    private Long couponId;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}