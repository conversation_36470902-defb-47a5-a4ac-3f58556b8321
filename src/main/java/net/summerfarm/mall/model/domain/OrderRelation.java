package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 主子单关联关系表
 * @TableName order_relation
 */
@Data
public class OrderRelation implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 主订单号
     */
    private String masterOrderNo;

    /**
     * 子订单号
     */
    private String orderNo;

    /**
     * 精准送费用
     */
    private BigDecimal precisionDeliveryFee;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}