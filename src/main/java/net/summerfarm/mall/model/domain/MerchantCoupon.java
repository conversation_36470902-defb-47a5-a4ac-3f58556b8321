package net.summerfarm.mall.model.domain;

import lombok.Data;
import net.summerfarm.mall.common.util.DateUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class MerchantCoupon implements Serializable{

    /**
     * id
     */
    private Integer id;

    /**
     * 商户id
     */
    private Long mId;

    /**
     * 优惠券id
     */
    private Integer couponId;

    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime vaildDate;

    /**
     * 红包发送者
     */
    private String sender;

    /**
     * 是否使用，0未使用，1已使用
     */
    private Byte used;

    /**
     * 领取类型 0发放 1领取
     */
    private Integer receiveType;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime addTime;

    private Integer view;

    private String orderNo;

    /**
     * 开始生效时间
     */
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime startTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 优惠活动关联ID（满返等）
     */
    private Long relatedId;


    public MerchantCoupon() {
    }

    public MerchantCoupon(Long mId, Integer couponId, LocalDateTime vaildDate, LocalDateTime startTime) {
        this.mId = mId;
        this.couponId = couponId;
        this.vaildDate = vaildDate;
        this.addTime = LocalDateTime.now();
        this.startTime = startTime;
    }

    public MerchantCoupon(Long mId, Integer couponId, LocalDateTime vaildDate, String sender, LocalDateTime startTime) {
        this.mId = mId;
        this.couponId = couponId;
        this.vaildDate = vaildDate;
        this.sender = sender;
        this.addTime = LocalDateTime.now();
        this.startTime = startTime;
    }

    public Integer getReceiveType() {
        return receiveType;
    }

    public void setReceiveType(Integer receiveType) {
        this.receiveType = receiveType;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }

    public Integer getCouponId() {
        return couponId;
    }

    public void setCouponId(Integer couponId) {
        this.couponId = couponId;
    }

    public LocalDateTime getVaildDate() {
        return vaildDate;
    }

    public void setVaildDate(LocalDateTime vaildDate) {
        this.vaildDate = vaildDate;
    }

    public Byte getUsed() {
        return used;
    }

    public void setUsed(Byte used) {
        this.used = used;
    }

    public LocalDateTime getAddTime() {
        return addTime;
    }

    public void setAddTime(LocalDateTime addTime) {
        this.addTime = addTime;
    }

    public Integer getView() {
        return view;
    }

    public void setView(Integer view) {
        this.view = view;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
}