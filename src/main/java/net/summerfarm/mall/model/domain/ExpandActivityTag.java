package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * expand_activity_tag
 * <AUTHOR>
@Data
public class ExpandActivityTag implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 活动id
     */
    private Long expandActivityId;

    /**
     * 用户id
     */
    private Long mId;

    /**
     * 生效活动关联订单号
     */
    private String orderNo;

    /**
     * 生效时间
     */
    private LocalDateTime effectiveTime;

    /**
     * 特惠有效期-单位：分钟
     */
    private Integer validityPeriod;

    /**
     * 当次触发省心送配送计划时间
     */
    private LocalDate deliveryTime;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}