package net.summerfarm.mall.model.domain.market;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ActivitySceneConfig implements Serializable {
    
    private Long id;

    /**
     * 基本信息id
     */
    private Long basicInfoId;

    /**
     * 生效平台，0 商城，1 直播，2 其他
     */
    private Integer platform;

    /**
     * 投放区域，枚举值待补充
     */
    private Integer place;

    /**
     * 是否已被删除，0 否，1 是
     */
    private Integer delFlag;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}