package net.summerfarm.mall.model.domain;

import lombok.Data;
import net.summerfarm.mall.common.util.DateUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class Cash {
    private Integer cashId;

    private Long mId;

    private Long accountId;

    private BigDecimal amount;

    private Integer status;

    private Integer auditAdmin;

    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime auditTime;

    private String auditRemark;

    private String response;

    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime createTime;

    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime updateTime;
}
