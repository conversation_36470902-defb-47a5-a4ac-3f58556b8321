package net.summerfarm.mall.model.domain;

import java.util.Date;
import lombok.Getter;
import lombok.Setter;

public class TimingRule {

    private Integer id;

    private String name;

    private String timingSku;

    private Integer areaNo;

    private Date startTime;

    private Date endTime;

    private Date deliveryStart;

    private Date deliveryEnd;

    private String ruleInformation;

    private Integer deliveryUnit;

    private Integer deliveryUpperLimit;

    private Date updateTime;

    private Integer type;

    private Integer priority;

    private Boolean autoCalculate;

    private Integer deliveryPeriod;

    private Integer deliveryStartType;

    private Integer supportType;
    /**
     * 配送门槛
     */
    private Integer threshold;

    /**
     * 下单日期+N,N值
     */
    @Getter
    @Setter
    private Integer plusDay;

    /**
     * 展示标记：0不展示，1展示
     */
    @Getter
    @Setter
    private Integer display;

    public Integer getThreshold() {
        return threshold;
    }

    public void setThreshold(Integer threshold) {
        this.threshold = threshold;
    }

    public Integer getAreaNo() {
        return areaNo;
    }

    public void setAreaNo(Integer areaNo) {
        this.areaNo = areaNo;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getTimingSku() {
        return timingSku;
    }

    public void setTimingSku(String timingSku) {
        this.timingSku = timingSku == null ? null : timingSku.trim();
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Date getDeliveryStart() {
        return deliveryStart;
    }

    public void setDeliveryStart(Date deliveryStart) {
        this.deliveryStart = deliveryStart;
    }

    public Date getDeliveryEnd() {
        return deliveryEnd;
    }

    public void setDeliveryEnd(Date deliveryEnd) {
        this.deliveryEnd = deliveryEnd;
    }

    public String getRuleInformation() {
        return ruleInformation;
    }

    public void setRuleInformation(String ruleInformation) {
        this.ruleInformation = ruleInformation == null ? null : ruleInformation.trim();
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getDeliveryUnit() {
        return deliveryUnit;
    }

    public void setDeliveryUnit(Integer deliveryUnit) {
        this.deliveryUnit = deliveryUnit;
    }

    public Integer getDeliveryUpperLimit() {
        return deliveryUpperLimit;
    }

    public void setDeliveryUpperLimit(Integer deliveryUpperLimit) {
        this.deliveryUpperLimit = deliveryUpperLimit;
    }


    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public Boolean getAutoCalculate() {
        return autoCalculate;
    }

    public void setAutoCalculate(Boolean autoCalculate) {
        this.autoCalculate = autoCalculate;
    }

    public Integer getDeliveryPeriod() {
        return deliveryPeriod;
    }

    public void setDeliveryPeriod(Integer deliveryPeriod) {
        this.deliveryPeriod = deliveryPeriod;
    }

    public Integer getDeliveryStartType() {
        return deliveryStartType;
    }

    public void setDeliveryStartType(Integer deliveryStartType) {
        this.deliveryStartType = deliveryStartType;
    }

    public Integer getSupportType() {
        return supportType;
    }

    public void setSupportType(Integer supportType) {
        this.supportType = supportType;
    }
}