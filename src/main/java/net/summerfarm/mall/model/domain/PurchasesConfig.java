package net.summerfarm.mall.model.domain;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

public class PurchasesConfig {
    private Integer id;

    @ApiModelProperty(value = "仓库类型:0本部仓、1外部仓、2合伙人仓")
    private Integer type;

    private Integer areaNo;

    @ApiModelProperty(value = "仓库所属管理员")
    private Integer areaManageId;

    private String sku;

    private List<String> skus;

    @ApiModelProperty(value = "当前sku状态：0正常、1采购预警")
    private Integer status;

    @ApiModelProperty(value = "安全水位")
    private Integer safeLevel;

    @ApiModelProperty(value = "采购提前期")
    private BigDecimal leadTime;

    @ApiModelProperty(value = "常用")
    private Integer supplierId;

    @ApiModelProperty(value = "有货率")
    private BigDecimal stockRate;

    @ApiModelProperty(value = "消息关注人")
    private String ccAdminId;

    private LocalDateTime addtime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getAreaNo() {
        return areaNo;
    }

    public void setAreaNo(Integer areaNo) {
        this.areaNo = areaNo;
    }

    public Integer getAreaManageId() {
        return areaManageId;
    }

    public void setAreaManageId(Integer areaManageId) {
        this.areaManageId = areaManageId;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public List<String> getSkus() {
        return skus;
    }

    public void setSkus(List<String> skus) {
        this.skus = skus;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getSafeLevel() {
        return safeLevel;
    }

    public void setSafeLevel(Integer safeLevel) {
        this.safeLevel = safeLevel;
    }

    public BigDecimal getLeadTime() {
        return leadTime;
    }

    public void setLeadTime(BigDecimal leadTime) {
        this.leadTime = leadTime;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public BigDecimal getStockRate() {
        return stockRate;
    }

    public void setStockRate(BigDecimal stockRate) {
        this.stockRate = stockRate;
    }

    public String getCcAdminId() {
        return ccAdminId;
    }

    public void setCcAdminId(String ccAdminId) {
        this.ccAdminId = ccAdminId;
    }

    public LocalDateTime getAddtime() {
        return addtime;
    }

    public void setAddtime(LocalDateTime addtime) {
        this.addtime = addtime;
    }
}
