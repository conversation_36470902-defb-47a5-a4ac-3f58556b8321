package net.summerfarm.mall.model.domain.merchant;

import com.alibaba.schedulerx.shade.org.apache.commons.lang.StringUtils;
import lombok.Data;
import net.summerfarm.mall.model.dto.MerchantRetailInfo;
import net.summerfarm.mall.model.dto.merchant.MerchantAndAccountDTO;
import net.summerfarm.mall.model.dto.merchant.TXAddressResp;

import java.time.LocalDateTime;

/**
* @description: ${description}
* @author: <PERSON>
* @date: 2025-01-13
**/
/**
 * 门店账户授权记录表
 * <AUTHOR>
 */
@Data
public class MerchantStoreAccountAuthorizationRecord {
    /**
    * 主键ID
    */
    private Long id;

    /**
    * 商户ID
    */
    private Long mId;

    /**
    * 账户ID
    */
    private Long accountId;

    /**
    * 用户的OpenID，唯一键
    */
    private String openId;

    /**
    * 手机号
    */
    private String mobilePhone;

    /**
    * 门店名称
    */
    private String storeName;

    /**
    * 一级门店类型
    */
    private String storeType;

    /**
    * 二级门店类型
    */
    private String subStoreType;

    /**
    * 门店地址 - 省
    */
    private String addressProvince;

    /**
    * 门店地址 - 市
    */
    private String addressCity;

    /**
    * 门店地址 - 区
    */
    private String addressRegion;

    /**
    * 门店地址 - 街道
    */
    private String addressStreet;

    /**
    * 营业执照注册号
    */
    private String registrationNumber;

    /**
    * 企业名称
    */
    private String companyName;

    /**
    * 法人名称
    */
    private String corporationName;

    /**
    * 授权状态: 0-预录入, 1-取消授权, 2-录入完成
    */
    private Integer status;

    /**
    * 记录创建时间
    */
    private LocalDateTime createTime;

    /**
    * 记录更新时间
    */
    private LocalDateTime updateTime;


    public static MerchantStoreAccountAuthorizationRecord buildRecord(MerchantAndAccountDTO accountDTO, MerchantRetailInfo merchantRetailInfo){
        MerchantStoreAccountAuthorizationRecord retailInfo = new MerchantStoreAccountAuthorizationRecord();
        retailInfo.setMId(accountDTO.getmId());
        retailInfo.setAccountId(accountDTO.getAccountId());
        retailInfo.setStoreName(accountDTO.getMname());
        retailInfo.setStoreType("餐饮店");
        retailInfo.setStatus(0);
        retailInfo.setMobilePhone(accountDTO.getPhone());
        retailInfo.setOpenId(accountDTO.getMpOpenid());
        retailInfo.setAddressProvince(merchantRetailInfo.getAddressProvince());
        retailInfo.setAddressCity(merchantRetailInfo.getAddressCity());
        retailInfo.setAddressRegion(merchantRetailInfo.getAddressRegion());
        retailInfo.setAddressStreet(merchantRetailInfo.getAddressStreet());
        return retailInfo;
    }
}