package net.summerfarm.mall.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * front_category_to_category
 * <AUTHOR>
@Data
public class FrontCategoryToCategory implements Serializable {
    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * 前台类目id
     */
    private Integer frontCategoryId;

    /**
     * 后台类目id
     */
    private Integer categoryId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    private static final long serialVersionUID = 1L;
}