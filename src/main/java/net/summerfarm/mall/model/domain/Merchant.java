package net.summerfarm.mall.model.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.common.util.validation.groups.Update;
import net.summerfarm.mall.constant.validation.group.MerchantInfoComplete;
import net.summerfarm.mall.enums.MerchantLeadsEnum;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@ApiModel(description = "商户实体类")
public class Merchant implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "商户id")
    @Null(groups = Add.class)
    private Long mId;

    private Integer roleId;

    @ApiModelProperty(value = "店铺名称")
    @NotBlank(groups = MerchantInfoComplete.class, message = "店铺名称不可为空！")
    private String mname;

    @ApiModelProperty(value = "联系人")
    private String mcontact;

    @ApiModelProperty(value = "微信openid")
    private String openid;

    @ApiModelProperty(value = "手机号")
    @NotNull(groups = {Add.class})
    private String phone;

    @ApiModelProperty(value = "审核状态")
    private Integer islock;

    @ApiModelProperty(value = "等级",hidden = true)
    private Integer rankId;

    @ApiModelProperty(value = "注册时间",hidden = true)
    private Date registerTime;

    @ApiModelProperty(value = "登录时间",hidden = true)
    private Date loginTime;

    @ApiModelProperty(value = "6位邀请码")
    private String invitecode;

    @ApiModelProperty(value = "用户分享码")
    private String channelCode;

    @ApiModelProperty(value = "邀请人分享码")
    private String inviterChannelCode;

    @ApiModelProperty(value = "审核时间",hidden = true)
    private Date auditTime;

    @ApiModelProperty(value = "审核人",hidden = true)
    private Integer auditUser;

    @ApiModelProperty(value = "营业执照路径")
    private String businessLicense;

    @ApiModelProperty(value = "省")
    private String province;

    @ApiModelProperty(value = "市")
    private String city;

    @ApiModelProperty(value = "地区")
    private String area;

    @ApiModelProperty(value = "详细地址")
    private String address;

    @ApiModelProperty(value = "商家腾讯地图坐标",required = false)
    private String poiNote;

    @ApiModelProperty(value = "审核备注",hidden = true)
    private String remark;

    @ApiModelProperty(value = "店铺招牌")
    private String shopSign;

    @ApiModelProperty(value = "其他证明照片")
    private String otherProof;

    @ApiModelProperty(value = "上次下单时间",hidden = true)
    private Date lastOrderTime;

    @ApiModelProperty(value = "城市编号")
    private Integer areaNo;

    @ApiModelProperty(value = "微信联合码")
    private String unionid;

    @ApiModelProperty(value = "微信小程序码")
    private String mpOpenid;

    @ApiModelProperty(value = "大客户账号id",hidden = true)
    private Integer adminId;

    @ApiModelProperty(value = "1是直营 2是加盟",hidden = true)
    private Integer direct;

    @ApiModelProperty(value = "1是定量 2是全量",hidden = true)
    private Integer skuShow;

    @ApiModelProperty(value = "客户类型",hidden = true)
    private String size;

    @ApiModelProperty(value = "1服务区内 2服务区外",hidden = true)
    private Integer server;

    @ApiModelProperty(value = "会员当月积分",hidden = true)
    private BigDecimal memberIntegral;

    @ApiModelProperty(value = "会员等级")
    private Integer grade;

    @ApiModelProperty(value = "弹窗是否已读")
    private Integer popView;

    @ApiModelProperty(value = "余额")
    private BigDecimal rechargeAmount;

    @ApiModelProperty(value = "可提现金额")
    private BigDecimal cashAmount;

    @ApiModelProperty(value = "红包最新入账时间")
    private LocalDateTime cashUpdateTime;

    /**
     * 首次登录弹窗标识：0、弹窗
     */
    private Integer firstLoginPop;

    /**
     * 更换账号绑定弹窗标识：0、弹窗
     */
    private Integer changePop;

    /**
    * 门牌号
    */
    private String houseNumber;

    /**
    * 线索池
    */
    private Integer cluePool;


    /**
     * 企业规模
     */
    private String enterpriseScale;

    /**
     * 公司品牌
     */
    private String companyBrand;

    /**
    * 客户类型
    */
    @NotBlank(groups = MerchantInfoComplete.class, message = "主营类型不可为空！")
    private String type;

    private String merchantType;

    /**
     * 开关状态 0 开（展示） 1 关（不展示）
     */
    private Integer displayButton;

    /**
     * 门头图片
     */
    @Getter
    @Setter
    @NotBlank(groups = MerchantInfoComplete.class, message = "门头照不可为空！")
    private String doorPic;

    /**
     * 门头照OCR 结果
     */
    @Getter
    @Setter
    private String doorPicOcr;

    /**
     * 预注册标记,1- 预注册，0- 非预注册
     */
    @Getter
    @Setter
    private Integer preRegisterFlag;

    /**
     * crm扫码注册标记
     */
    @Getter
    @Setter
    private boolean crmRegisterFlag;

    /**
     * 自动审核标记（无需复核）
     */
    @Getter
    @Setter
    private boolean autoAuditFlag;

    /**
     * 是否pop客户
     */
    @Getter
    @Setter
    private boolean popMerchant;

    /**
     * 运营状态:正常(0),倒闭(1)，待提交核验（2），待核验（3），核验拒绝（4）
     */
    @Getter
    @Setter
    private Integer operateStatus;

    /**
     * 地址完善标记。0-未完善，1-已完善（目前仅用于下单环节）
     */
    @Getter
    @Setter
    private Integer addressCompletionFlag;


    /**
     * 业务线:0=鲜沐;1=pop
     */
    @Getter
    @Setter
    private Integer businessLine;

    /**
     * 用户提交审核时间
     */
    @Getter
    @Setter
    private Date submitReviewTime;

    /**
     * 用户下个月等级
     */
    @Getter
    @Setter
    private Integer nextGrade;



    public Merchant() {

    }

    public String getMerchantType() {
        return merchantType;
    }

    public void setMerchantType(String merchantType) {
        this.merchantType = merchantType;
    }

    public Integer getDisplayButton() {
        return displayButton;
    }

    public void setDisplayButton(Integer displayButton) {
        this.displayButton = displayButton;
    }

    /**
     * 例子转商户
     * @param merchantLeads
     */
    public Merchant(MerchantLeads merchantLeads) {
        this.mname = merchantLeads.getMname();
        this.phone = merchantLeads.getPhone();
        this.mcontact = merchantLeads.getMcontact() == null?merchantLeads.getMname():merchantLeads.getMcontact();
        this.province = merchantLeads.getProvince();
        this.city = merchantLeads.getCity();
        this.area = merchantLeads.getArea();
        this.areaNo = merchantLeads.getAreaNo();
        this.address = merchantLeads.getAddress();
        this.size = merchantLeads.getSize();
        this.poiNote = merchantLeads.getPoiNote();
        this.houseNumber = merchantLeads.getHouseNumber();
        this.enterpriseScale = merchantLeads.getEnterpriseScale();
        this.companyBrand = merchantLeads.getCompanyBrand();
        this.type = merchantLeads.getType();
        this.doorPic=merchantLeads.getDoorPic();
        this.popMerchant = MerchantLeadsEnum.MerchantTypeEnum.POP.getCode().equals(merchantLeads.getMerchantType());
    }

    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }

    public Integer getRoleId() {
        return roleId;
    }

    public void setRoleId(Integer roleId) {
        this.roleId = roleId;
    }

    public String getMname() {
        return mname;
    }

    public void setMname(String mname) {
        this.mname = mname;
    }

    public String getMcontact() {
        return mcontact;
    }

    public void setMcontact(String mcontact) {
        this.mcontact = mcontact;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Integer getIslock() {
        return islock;
    }

    public void setIslock(Integer islock) {
        this.islock = islock;
    }

    public Integer getRankId() {
        return rankId;
    }

    public void setRankId(Integer rankId) {
        this.rankId = rankId;
    }

    public Date getRegisterTime() {
        return registerTime;
    }

    public void setRegisterTime(Date registerTime) {
        this.registerTime = registerTime;
    }

    public Date getLoginTime() {
        return loginTime;
    }

    public void setLoginTime(Date loginTime) {
        this.loginTime = loginTime;
    }

    public String getInvitecode() {
        return invitecode;
    }

    public void setInvitecode(String invitecode) {
        this.invitecode = invitecode;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getInviterChannelCode() {
        return inviterChannelCode;
    }

    public void setInviterChannelCode(String inviterChannelCode) {
        this.inviterChannelCode = inviterChannelCode;
    }

    public Date getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }

    public Integer getAuditUser() {
        return auditUser;
    }

    public void setAuditUser(Integer auditUser) {
        this.auditUser = auditUser;
    }

    public String getBusinessLicense() {
        return businessLicense;
    }

    public void setBusinessLicense(String businessLicense) {
        this.businessLicense = businessLicense;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getPoiNote() {
        return poiNote;
    }

    public void setPoiNote(String poiNote) {
        this.poiNote = poiNote;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getShopSign() {
        return shopSign;
    }

    public void setShopSign(String shopSign) {
        this.shopSign = shopSign;
    }

    public String getOtherProof() {
        return otherProof;
    }

    public void setOtherProof(String otherProof) {
        this.otherProof = otherProof;
    }

    public Date getLastOrderTime() {
        return lastOrderTime;
    }

    public void setLastOrderTime(Date lastOrderTime) {
        this.lastOrderTime = lastOrderTime;
    }

    public Integer getAreaNo() {
        return areaNo;
    }

    public void setAreaNo(Integer areaNo) {
        this.areaNo = areaNo;
    }

    public String getUnionid() {
        return unionid;
    }

    public void setUnionid(String unionid) {
        this.unionid = unionid;
    }

    public String getMpOpenid() {
        return mpOpenid;
    }

    public void setMpOpenid(String mpOpenid) {
        this.mpOpenid = mpOpenid;
    }

    public Integer getAdminId() {
        return adminId;
    }

    public void setAdminId(Integer adminId) {
        this.adminId = adminId;
    }

    public Integer getDirect() {
        return direct;
    }

    public void setDirect(Integer direct) {
        this.direct = direct;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public Integer getServer() {
        return server;
    }

    public void setServer(Integer server) {
        this.server = server;
    }

    public BigDecimal getMemberIntegral() {
        return memberIntegral;
    }

    public void setMemberIntegral(BigDecimal memberIntegral) {
        this.memberIntegral = memberIntegral;
    }

    public Integer getGrade() {
        return grade;
    }

    public void setGrade(Integer grade) {
        this.grade = grade;
    }

    public Integer getPopView() {
        return popView;
    }

    public void setPopView(Integer popView) {
        this.popView = popView;
    }

    public Integer getSkuShow() {
        return skuShow;
    }

    public void setSkuShow(Integer skuShow) {
        this.skuShow = skuShow;
    }

    public BigDecimal getRechargeAmount() {
        return rechargeAmount;
    }

    public void setRechargeAmount(BigDecimal rechargeAmount) {
        this.rechargeAmount = rechargeAmount;
    }

    public BigDecimal getCashAmount() {
        return cashAmount;
    }

    public void setCashAmount(BigDecimal cashAmount) {
        this.cashAmount = cashAmount;
    }

    public LocalDateTime getCashUpdateTime() {
        return cashUpdateTime;
    }

    public void setCashUpdateTime(LocalDateTime cashUpdateTime) {
        this.cashUpdateTime = cashUpdateTime;
    }

    public Integer getFirstLoginPop() {
        return firstLoginPop;
    }

    public void setFirstLoginPop(Integer firstLoginPop) {
        this.firstLoginPop = firstLoginPop;
    }

    public Integer getChangePop() {
        return changePop;
    }

    public void setChangePop(Integer changePop) {
        this.changePop = changePop;
    }

    public String getHouseNumber() {
        return houseNumber;
    }

    public void setHouseNumber(String houseNumber) {
        this.houseNumber = houseNumber;
    }


    public Integer getCluePool() {
        return cluePool;
    }

    public void setCluePool(Integer cluePool) {
        this.cluePool = cluePool;
    }


    public String getEnterpriseScale() {
        return enterpriseScale;
    }

    public void setEnterpriseScale(String enterpriseScale) {
        this.enterpriseScale = enterpriseScale;
    }

    public String getCompanyBrand() {
        return companyBrand;
    }

    public void setCompanyBrand(String companyBrand) {
        this.companyBrand = companyBrand;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return "Merchant{" +
                "mId=" + mId +
                ", mname='" + mname + '\'' +
                ", mcontact='" + mcontact + '\'' +
                ", openid='" + openid + '\'' +
                ", phone='" + phone + '\'' +
                ", islock=" + islock +
                ", rankId=" + rankId +
                ", registerTime=" + registerTime +
                ", loginTime=" + loginTime +
                ", invitecode='" + invitecode + '\'' +
                ", channelCode='" + channelCode + '\'' +
                ", inviterChannelCode='" + inviterChannelCode + '\'' +
                ", auditTime=" + auditTime +
                ", auditUser=" + auditUser +
                ", businessLicense='" + businessLicense + '\'' +
                ", province='" + province + '\'' +
                ", city='" + city + '\'' +
                ", area='" + area + '\'' +
                ", address='" + address + '\'' +
                ", poiNote='" + poiNote + '\'' +
                ", remark='" + remark + '\'' +
                ", shopSign='" + shopSign + '\'' +
                ", otherProof='" + otherProof + '\'' +
                ", lastOrderTime=" + lastOrderTime +
                ", areaNo=" + areaNo +
                ", unionid='" + unionid + '\'' +
                ", mpOpenid='" + mpOpenid + '\'' +
                ", adminId=" + adminId +
                ", direct=" + direct +
                ", skuShow=" + skuShow +
                ", size='" + size + '\'' +
                ", server=" + server +
                ", memberIntegral=" + memberIntegral +
                ", grade=" + grade +
                ", popView=" + popView +
                ", rechargeAmount=" + rechargeAmount +
                ", cashAmount=" + cashAmount +
                ", cashUpdateTime=" + cashUpdateTime +
                ", firstLoginPop=" + firstLoginPop +
                ", changePop=" + changePop +
                ", houseNumber='" + houseNumber + '\'' +
                ", cluePool=" + cluePool +
                ", enterpriseScale='" + enterpriseScale + '\'' +
                ", companyBrand='" + companyBrand + '\'' +
                ", type='" + type + '\'' +
                ", merchantType='" + merchantType + '\'' +
                '}';
    }
}