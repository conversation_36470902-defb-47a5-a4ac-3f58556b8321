package net.summerfarm.mall.model.domain;

import lombok.Data;
import net.summerfarm.common.util.validation.annotation.InRange;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.common.util.validation.groups.Update;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class FollowUpRecord implements Serializable {
    /**
     * 记录id
     */
    @Null(message = "记录还没生成,为啥传了个id" ,groups = {Add.class})
    private Integer id;
    /**
     * 商户id
     */
    @Min(message = "商户id不能为空",value = 1,groups = {Add.class, Update.class})
    private Long mId;
    /**
     * 跟进人
     */
    private Integer adminId;
    /**
     * 跟进人名称
     */
    private String adminName;
    /**
     * 工单创建人
     */
    private String creator;
    /**
     * 用户生命周期
     */
    private Integer mLifecycle;
    /**
     * 用户标签
     */
    private String mTag;
    /**
     * 工单生成前最近一次下单时间
     */
    private Date mLastOrderTime;
    /**
     * 跟进方式
     */
    private String followUpWay;
    /**
     * 跟进图片
     */
    private String followUpPic;
    /**
     * 跟进记录状态:0未跟进，1已跟进，2已跟进且下单，3联系不上，4放弃跟进,9重置
     */
    @InRange(rangeNums = {0,1,2,3,4},message = "跟进状态传值异常" ,groups = {Add.class})
    private Integer status;
    /**
     * 优先级
     */
    private Integer priority;
    /**
     * 跟进情况描述
     */
    @NotNull(message = "跟进清空描述不能为空", groups = {Update.class})
    private String condition;
    /**
     * 添加时间
     */
    private Date addTime;
    /**
     *  contactId
     */
    private Integer contactId;
    /**
     * 下次拜访时间
     */
    private LocalDate nextFollowTime;
    /**
     * 期望内容
     */
    private String expectedContent;
    /**
     * 拜访目的:0拉新,1催月活,2客户维护,3拓品,4售后处理,5催省心送
     */
    private Integer visitObjective;
    /**
     * 拜访类型:0普通拜访,1陪访
     */
    private Integer visitType;
    /**
     * 陪访人id
     */
    private Integer escortAdminId;

    /**
     * 经营状态:正常经营(0),已确认倒闭(1),倒闭确认中(2)
     */
    private Integer operateStatus;
    /**
     * 备注任务id
     */
    private Integer whetherRemark;
    /**
     * 拜访定位
     */
    private String location;
    /**
     * 拜访的kpId
     */
    private Long kpId;
    /**
     * 商户经纬度信息
     */
    private String poiNote;
    /**
     * 拜访计划id
     */
    private Long visitPlanId;
    /**
     * 陪访计划id
     */
    private Long escortVisitPlanId;
    /**
     * 自账号id
     */
    private Long accountId;

    /**
     * 反馈
     */
    private String feedback;

    /**
     * 添加时间
     */
    private Date feedbackTime;

    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }

    public Integer getmLifecycle() {
        return mLifecycle;
    }

    public void setmLifecycle(Integer mLifecycle) {
        this.mLifecycle = mLifecycle;
    }

    public String getmTag() {
        return mTag;
    }

    public void setmTag(String mTag) {
        this.mTag = mTag;
    }

    public Date getmLastOrderTime() {
        return mLastOrderTime;
    }

    public void setmLastOrderTime(Date mLastOrderTime) {
        this.mLastOrderTime = mLastOrderTime;
    }
}