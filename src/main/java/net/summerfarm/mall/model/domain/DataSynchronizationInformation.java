package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * data_synchronization_information
 * <AUTHOR>
@Data
public class DataSynchronizationInformation implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 同步时间标记(yyyyMMdd/yyyyMM)
     */
    private Integer dateFlag;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * create time
     */
    private LocalDateTime createTime;

    private static final long serialVersionUID = 1L;
}