package net.summerfarm.mall.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * order_defect_info
 * <AUTHOR>
@Data
public class OrderDefectInfo implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * sku
     */
    private String sku;

    /**
     * mId
     */
    private Long mId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 配送计划ID
     */
    private Integer deliveryPlanId;

    /**
     * sku规格
     */
    private String weight;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * sku图
     */
    private String skuPic;

    /**
     * 缺损数量
     */
    private Integer defectAmount;

    /**
     * 应送数量
     */
    private Integer amount;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}