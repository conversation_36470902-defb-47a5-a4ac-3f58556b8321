package net.summerfarm.mall.model.domain.offline;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class TimingSkuTop implements Serializable {

    private Long id;

    private String sku;

    private Integer areaNo;

    private Integer priority;

    private Integer dateFlag;

    private LocalDateTime updateTime;

    private LocalDateTime createTime;
}