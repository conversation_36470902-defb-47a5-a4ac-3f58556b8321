package net.summerfarm.mall.model.domain;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 外部对接，处理返回信息
 * @createTime 2021年10月22日 17:44:00
 */
@Data
public class OuterPlatformRes {

    /**
     * 应答码 1000：成功 2000：失败
     */
    private String recode;

    /**
     * 返回描述信息
     */
    private String remsg;

    /**
     * 推送状态 1：成功; 2:失败
     */
    private Integer pushStatus;

    /**
     * 请求内容
     */
    private String reqContent;

    /**
     * 返回内容
     */
    private String resContent;

}
