package net.summerfarm.mall.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * front_category_to_area
 * <AUTHOR>
@Data
public class FrontCategoryToArea implements Serializable {
    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * 前台类目id
     */
    private Integer frontCategoryId;

    /**
     * 城市编号
     */
    private Integer areaNo;

    /**
     * 展示状态：0、不展示 1、展示
     */
    private Integer display;

    /**
     * 排序值，从小到大
     */
    private Integer priority;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    private static final long serialVersionUID = 1L;
}