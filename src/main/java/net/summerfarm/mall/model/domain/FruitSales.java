package net.summerfarm.mall.model.domain;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2021/9/7
 * @Description:
 */
public class FruitSales {
    private Long id;
    private Integer areaNo;
    private Long orderItemId;
    private String sku;
    private Integer sales;
    private LocalDateTime time;
    private Integer status;

    public FruitSales() {
    }

    public FruitSales(Long id, Integer areaNo, Long orderItemId, String sku, Integer sales, LocalDateTime time, Integer status) {
        this.id = id;
        this.areaNo = areaNo;
        this.orderItemId = orderItemId;
        this.sku = sku;
        this.sales = sales;
        this.time = time;
        this.status = status;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getSales() {
        return sales;
    }

    public void setSales(Integer sales) {
        this.sales = sales;
    }

    public LocalDateTime getTime() {
        return time;
    }

    public void setTime(LocalDateTime time) {
        this.time = time;
    }

    public Long getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(Long orderItemId) {
        this.orderItemId = orderItemId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getAreaNo() {
        return areaNo;
    }

    public void setAreaNo(Integer areaNo) {
        this.areaNo = areaNo;
    }
}
