package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * sort_combination_tab
 * <AUTHOR>
@Data
public class SortCombinationTab implements Serializable {
    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * 组合id
     */
    private Integer combinationId;

    /**
     * 类型：0、商品推荐前四个 1、tab
     */
    private Integer type;

    /**
     * tab名称
     */
    private String name;

    /**
     * 副标题
     */
    private String subtitle;

    /**
     * 更新人
     */
    private Integer updater;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    private static final long serialVersionUID = 1L;
}