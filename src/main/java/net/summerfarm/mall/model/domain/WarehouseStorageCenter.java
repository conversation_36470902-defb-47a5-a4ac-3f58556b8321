package net.summerfarm.mall.model.domain;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 *
 */
@Data
public class WarehouseStorageCenter {
    private Integer id;

    private Integer warehouseNo;

    private String warehouseName;

    private Integer manageAdminId;

    private Integer type;

    private Integer areaManageId;

    private Integer status;

    private String address;

    private String poiNote;

    private String mailToAddress;

    private Integer updater;

    private Date updateTime;

    private Integer creator;

    private Date createTime;

    private String personContact;

    private String phone;

}