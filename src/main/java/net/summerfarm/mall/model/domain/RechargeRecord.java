package net.summerfarm.mall.model.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@ApiModel(description = "余额变动记录model")
public class RechargeRecord implements Serializable {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "余额变动编号")
    private String rechargeRecordNo;

    @ApiModelProperty(value = "客户id")
    private Long mId;

    @ApiModelProperty(value = "子账号id")
    private Long accountId;

    @ApiModelProperty(value = "类型：0消费,1退款,2充值")
    private Integer type;

    @ApiModelProperty(value = "编号：订单号、售后单号、充值单号")
    private String recordNo;

    @ApiModelProperty(value = "变动前金额")
    private BigDecimal oldAmount;

    @ApiModelProperty(value = "变动后金额")
    private BigDecimal newAmount;

    @ApiModelProperty(value = "添加时间")
    private LocalDateTime addtime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getRechargeRecordNo() {
        return rechargeRecordNo;
    }

    public void setRechargeRecordNo(String rechargeRecordNo) {
        this.rechargeRecordNo = rechargeRecordNo;
    }

    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getRecordNo() {
        return recordNo;
    }

    public void setRecordNo(String recordNo) {
        this.recordNo = recordNo;
    }

    public BigDecimal getOldAmount() {
        return oldAmount;
    }

    public void setOldAmount(BigDecimal oldAmount) {
        this.oldAmount = oldAmount;
    }

    public BigDecimal getNewAmount() {
        return newAmount;
    }

    public void setNewAmount(BigDecimal newAmount) {
        this.newAmount = newAmount;
    }

    public LocalDateTime getAddtime() {
        return addtime;
    }

    public void setAddtime(LocalDateTime addtime) {
        this.addtime = addtime;
    }
}
