package net.summerfarm.mall.model.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.common.util.UnsafeUtil;

import java.time.LocalDateTime;

@Data
public class QuantityChangeRecord {
    private Long id;

    private Integer areaNo;

    private String sku;

    private String recorder;

    private Integer newQuantity;

    private Integer oldQuantity;

    private Integer newOnlineQuantity;

    private Integer oldOnlineQuantity;

    private Integer newLockQuantity;

    private Integer oldLockQuantity;

    private Integer newSaleLockQuantity;

    private Integer oldSaleLockQuantity;

    private Integer newRoadQuantity;

    private Integer oldRoadQuantity;

    private Integer newSafeQuantity;

    private Integer oldSafeQuantity;

    private Integer newChange;

    private Integer oldChange;

    private String typeName;

    private String recordNo;

    private LocalDateTime addtime;

    @ApiModelProperty(value = "配送仓编号")
    private Integer storeNo;

    @ApiModelProperty(value = "数量")
    private Integer quantity;

    private Integer newReserveUseQuantity;

    private Integer oldReserveUseQuantity;

    private String remark;

    private Long tenantId;

    private Long warehouseTenantId;

    public static final long oldQuantityOffset;

    public static final long oldOnlineQuantityOffset;

    public static final long oldLockQuantityOffset;

    public static final long oldSaleLockQuantityOffset;

    public static final long oldRoadQuantityOffset;

    public static final long oldSafeQuantityOffset;

    public static final long oldChangeOffset;

    public static final long oldReserveUseQuantityOffset;

    static {
        try {
            oldQuantityOffset = UnsafeUtil.unsafe.objectFieldOffset(QuantityChangeRecord.class.getDeclaredField("oldQuantity"));
            oldOnlineQuantityOffset = UnsafeUtil.unsafe.objectFieldOffset(QuantityChangeRecord.class.getDeclaredField("oldOnlineQuantity"));
            oldLockQuantityOffset = UnsafeUtil.unsafe.objectFieldOffset(QuantityChangeRecord.class.getDeclaredField("oldLockQuantity"));
            oldSaleLockQuantityOffset = UnsafeUtil.unsafe.objectFieldOffset(QuantityChangeRecord.class.getDeclaredField("oldSaleLockQuantity"));
            oldRoadQuantityOffset = UnsafeUtil.unsafe.objectFieldOffset(QuantityChangeRecord.class.getDeclaredField("oldRoadQuantity"));
            oldSafeQuantityOffset = UnsafeUtil.unsafe.objectFieldOffset(QuantityChangeRecord.class.getDeclaredField("oldSafeQuantity"));
            oldChangeOffset = UnsafeUtil.unsafe.objectFieldOffset(QuantityChangeRecord.class.getDeclaredField("oldChange"));
            oldReserveUseQuantityOffset = UnsafeUtil.unsafe.objectFieldOffset(QuantityChangeRecord.class.getDeclaredField("oldReserveUseQuantity"));

        } catch (NoSuchFieldException e) {
            throw new Error(e);
        }
    }

    public QuantityChangeRecord() {

    }

    public QuantityChangeRecord(Integer storeNo,Integer areaNo, String sku, String recorder, String typeName, String recordNo,Integer quantity) {
        this.storeNo =storeNo;
        this.areaNo = areaNo;
        this.sku = sku;
        this.recorder = recorder;
        this.typeName = typeName;
        this.recordNo = recordNo;
        this.quantity=quantity;
    }

    public QuantityChangeRecord(Integer areaNo, String sku, String recorder, String typeName, String recordNo, Long tenantId, Long warehouseTenantId) {
        this.areaNo = areaNo;
        this.sku = sku;
        this.recorder = recorder;
        this.typeName = typeName;
        this.recordNo = recordNo;
        this.tenantId = tenantId;
        this.warehouseTenantId = warehouseTenantId;
    }


    public QuantityChangeRecord(Integer areaNo, String sku, String recorder, String typeName, String recordNo) {
        this.areaNo = areaNo;
        this.sku = sku;
        this.recorder = recorder;
        this.typeName = typeName;
        this.recordNo = recordNo;
    }

}
