package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * circle_people_rule
 * <AUTHOR>
@Data
public class CirclePeopleRule implements Serializable {
    /**
     * 主键id
     */
    private Integer id;

    /**
     * 名称
     */
    private String name;

    /**
     * 0上传execl
     */
    private Boolean type;

    private Integer typeId;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    /**
     * 文件id
     */
    private String fileId;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private Integer updater;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}