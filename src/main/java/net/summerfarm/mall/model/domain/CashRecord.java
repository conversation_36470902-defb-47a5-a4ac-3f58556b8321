package net.summerfarm.mall.model.domain;

import lombok.Data;
import net.summerfarm.mall.common.util.DateUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class CashRecord {

    private Integer crId;

    private Long mId;

    private Long accountId;

    private BigDecimal amount;

    private Integer businessId;

    private Integer type;

    private String remark;

    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime createTime;

}
