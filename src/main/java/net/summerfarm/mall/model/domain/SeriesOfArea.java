package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * series_of_area
 * <AUTHOR>
@Data
public class SeriesOfArea implements Serializable {
    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * 聚合类型：0、鲜沐严选 1、商品排序
     */
    private Integer seriesType;

    /**
     * 聚合id
     */
    private Integer seriesId;

    /**
     * 城市编号
     */
    private Integer areaNo;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    private static final long serialVersionUID = 1L;
}