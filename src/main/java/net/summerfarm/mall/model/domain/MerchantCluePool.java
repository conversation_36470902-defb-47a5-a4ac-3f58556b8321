package net.summerfarm.mall.model.domain;

import lombok.Data;
import org.joda.time.LocalDateTime;

/**
 * <AUTHOR> ct
 * create at:  2020/10/20  15:14
 */
@Data
public class MerchantCluePool {

    //生效
    public static final  Integer CLUE_POOL_EFFICACY = 0;

    //失效
    public static final  Integer CLUE_POOL_NOT_EFFICACY = 1;

    private Integer id;

    /**
     * mid
     */
    private Long mId;

    /**
     * es线索池Id
     */
    private String esId;

    /**
     * merchantLeads id 例子池id
     */
    private Long mlId;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;

    /**
     * 地址
     */
    private String address;

    /**
     * 门店名称
     */
    private String mName;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 状态 0 生效 1 失效
     */
    private Integer status;


}
