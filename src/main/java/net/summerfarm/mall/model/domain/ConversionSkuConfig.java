package net.summerfarm.mall.model.domain;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> ct
 * create at:  2021/10/25  14:45
 */
@Data
public class ConversionSkuConfig {

    private Integer id;

    /**
     * 添加时间
     */
    private LocalDateTime addTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 库存仓编号
     */
    private Integer warehouseNo;

    /**
     * 商品pdid
     */
    private Long pdId;

    /**
     * 转入sku
     */
    private String  inSku;

    /**
     * 转出sku
     */
    private String outSku;

    /**
     * 操作人id
     */
    private Integer adminId;


    /**
     * 状态 0 生效 1 失效
     */
    private Integer status;

    /**
     * 比例  1:2
     */
    private String rates;
}
