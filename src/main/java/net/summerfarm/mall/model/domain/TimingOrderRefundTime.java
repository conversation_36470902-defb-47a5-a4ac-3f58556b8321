package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * timing_order_refund_time
 * <AUTHOR>
@Data
public class TimingOrderRefundTime implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 省心送订单号
     */
    private String orderNo;

    /**
     * 订单退款时间
     */
    private LocalDate refundTime;

    /**
     * 商户id
     */
    private Long mId;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}