package net.summerfarm.mall.model.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.mall.model.vo.ProductInfoVO;
import org.apache.commons.lang3.StringUtils;

import java.util.List;


/**
 * ES商品数据模型
 * <AUTHOR> href="mailto:<EMAIL>>黄棽</a>
 * @since 2022-07-27
 */
@Data
public class EsAreaSkuInfo {

    private Integer limitedQuantity;

    private String fixFlag;

    private String pdPriority;

    private String show;

    private Integer salesMode;

    private Integer qualityTime;

    private Integer coreSaleOut;

    private String advance;

    private String qualityTimeUnit;

    private Integer outdated;

    private Integer saleOut;

    private String areaNo;

    private String pddetail;

    private String onSale;

    private List<String> propertyValues;

    private Long pdId;

    private String pdName;

    private String sku;

    private String skuName;

    private Integer extType;

    private String info;

    private String picturePath;

    private Integer baseSaleQuantity;

    private String weight;

    private String otherSlogan;

    private Integer coreStoreQuantity;

    private String mType;

    private Integer warehouseNo;

    private Integer productsOutdated;

    private String unit;

    private Integer showAdvance;

    private Integer storeQuantity;

    private String skuPic;

    private Integer baseSaleUnit;

    private Integer fixNum;

    private String slogan;

    private Integer categoryId;

    public static ProductInfoVO convertToProductInfoVO(EsAreaSkuInfo esAreaSkuInfo, boolean isCoreCustomer) {
        ProductInfoVO productInfoVO = new ProductInfoVO();
        productInfoVO.setBaseSaleUnit(esAreaSkuInfo.getBaseSaleUnit());
        productInfoVO.setBaseSaleQuantity(esAreaSkuInfo.getBaseSaleQuantity());
        productInfoVO.setSku(esAreaSkuInfo.getSku());
        productInfoVO.setSkuName(esAreaSkuInfo.getSkuName());
        productInfoVO.setSalesMode(esAreaSkuInfo.getSalesMode());
        if(esAreaSkuInfo.getLimitedQuantity() != null){
            productInfoVO.setLimitedQuantity(esAreaSkuInfo.getLimitedQuantity());
        }
        productInfoVO.setPdName(esAreaSkuInfo.getPdName());
        productInfoVO.setCategoryId(esAreaSkuInfo.getCategoryId());
        productInfoVO.setPddetail(esAreaSkuInfo.getPddetail());
        productInfoVO.setUnit(esAreaSkuInfo.getUnit());
        productInfoVO.setWeight(esAreaSkuInfo.getWeight());
        productInfoVO.setPicturePath(esAreaSkuInfo.getPicturePath());
        productInfoVO.setPdId(esAreaSkuInfo.getPdId());
        productInfoVO.setInfo(esAreaSkuInfo.getInfo());
        productInfoVO.setSlogan(esAreaSkuInfo.getSlogan());
        productInfoVO.setOtherSlogan(esAreaSkuInfo.getOtherSlogan());
        productInfoVO.setShowAdvance(esAreaSkuInfo.getShowAdvance() == 1);
        productInfoVO.setAdvance(esAreaSkuInfo.getAdvance());
        productInfoVO.setSkuPic(esAreaSkuInfo.getSkuPic());
        productInfoVO.setFixNum(esAreaSkuInfo.getFixNum());
        productInfoVO.setQualityTime(esAreaSkuInfo.getQualityTime());
        productInfoVO.setQualityTimeUnit(esAreaSkuInfo.getQualityTimeUnit());
        productInfoVO.setExtType(esAreaSkuInfo.getExtType());
        if(isCoreCustomer){
            if(esAreaSkuInfo.getCoreStoreQuantity() == null){
                productInfoVO.setQuantity(0);
            }else{
                productInfoVO.setQuantity(esAreaSkuInfo.getCoreStoreQuantity());
            }
        }else{
            if(esAreaSkuInfo.getStoreQuantity() == null){
                productInfoVO.setQuantity(0);
            }else{
                productInfoVO.setQuantity(esAreaSkuInfo.getStoreQuantity());
            }

        }
        return productInfoVO;
    }
}