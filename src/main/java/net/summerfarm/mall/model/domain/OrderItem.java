package net.summerfarm.mall.model.domain;

import net.summerfarm.common.util.validation.annotation.InRange;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class OrderItem implements Serializable {

    //未使用商品优惠券
    public static final Integer NOT_USE_COUPON = 0;
    //使用商品优惠券
    public static final Integer USE_COUPON = 1 ;

    private Long id;

    private String sku;

    private String skuName;

    private String skuPic;

    private String orderNo;

    private Integer amount;

    private BigDecimal price;

    /**
     * 享受首件优惠的售价
     */
    private BigDecimal collocationPrice;

    private String pdName;

    private String maturity;

    private String weight;

    private Integer categoryId;

    private String picturePath;

    private Date addTime;

    @InRange(rangeNums = {0,1,2,3,4})  //0，未分类，1：冷冻，2：冷藏，3：恒温，4：顶汇大流通
    private Integer storageLocation;

    private Boolean _share;

    private Integer suitId=0;

    /**
     * inventory表主键
     */
    private Long invId;

    private String parentSku = "0";

    private String suitName;

    private Integer suitAmount;

    private BigDecimal originalPrice;

    private Integer status;

    private Integer rebateType;

    private Double rebateNumber;

    //普通人买的价格
    private Double mPrice;

    private String volume;

    private BigDecimal weightNum;

    private Integer useCoupon;

    private Integer maxThreshold;

    //商品类型：0、普通商品 1、赠品 2、换购商品
    private Integer productType;

    private Integer overlap;

    private BigDecimal actualTotalPrice;

    //商品快照信息
    private String info;

    public Integer getOverlap() {
        return overlap;
    }

    public void setOverlap(Integer overlap) {
        this.overlap = overlap;
    }

    public BigDecimal getCollocationPrice() {
        return collocationPrice;
    }

    public void setCollocationPrice(BigDecimal collocationPrice) {
        this.collocationPrice = collocationPrice;
    }

    public OrderItem() {
    }

    public OrderItem(Integer amount) {
        this.amount = amount;
    }

    public OrderItem(String sku, String pdName, String orderNo, Integer amount, BigDecimal price, BigDecimal originalPrice, Integer categoryId, String maturity, String weight, String picturePath, Integer storageLocation, Boolean _share) {
        this.sku = sku;
        this.pdName = pdName;
        this.categoryId = categoryId;
        this.orderNo = orderNo;
        this.amount = amount;
        this.price = price;
        this.originalPrice = originalPrice;
        this.maturity = maturity;
        this.weight = weight;
        this.addTime = new Date();
        this.picturePath = picturePath;
        this.storageLocation = storageLocation;
        this._share = _share;
    }

    public OrderItem(String sku, String pdName, String orderNo, Integer amount, BigDecimal price, BigDecimal originalPrice, Integer categoryId, String maturity, String weight, String picturePath, Integer storageLocation, Boolean _share,
                     String volume,BigDecimal weightNum) {
        this.sku = sku;
        this.pdName = pdName;
        this.categoryId = categoryId;
        this.orderNo = orderNo;
        this.amount = amount;
        this.price = price;
        this.originalPrice = originalPrice;
        this.maturity = maturity;
        this.weight = weight;
        this.addTime = new Date();
        this.picturePath = picturePath;
        this.storageLocation = storageLocation;
        this._share = _share;
        this.volume = volume;
        this.weightNum = weightNum;
    }

    public Integer getSuitId() {
        return suitId;
    }

    public void setSuitId(Integer suitId) {
        this.suitId = suitId;
    }

    public String getParentSku() {
        return parentSku;
    }

    public void setParentSku(String parentSku) {
        this.parentSku = parentSku;
    }

    public String getSuitName() {
        return suitName;
    }

    public void setSuitName(String suitName) {
        this.suitName = suitName;
    }

    public Integer getSuitAmount() {
        return suitAmount;
    }

    public void setSuitAmount(Integer suitAmount) {
        this.suitAmount = suitAmount;
    }

    public BigDecimal getOriginalPrice() {
        return originalPrice;
    }

    public void setOriginalPrice(BigDecimal originalPrice) {
        this.originalPrice = originalPrice;
    }

    public Boolean get_share() {
        return _share;
    }

    public void set_share(Boolean _share) {
        this._share = _share;
    }

    public Integer getStorageLocation() {
        return storageLocation;
    }

    public void setStorageLocation(Integer storageLocation) {
        this.storageLocation = storageLocation;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku == null ? null : sku.trim();
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo == null ? null : orderNo.trim();
    }

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public String getPdName() {
        return pdName;
    }

    public void setPdName(String pdName) {
        this.pdName = pdName;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public String getMaturity() {
        return maturity;
    }

    public void setMaturity(String maturity) {
        this.maturity = maturity;
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public String getPicturePath() {
        return picturePath;
    }

    public void setPicturePath(String picturePath) {
        this.picturePath = picturePath;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getRebateType() {
        return rebateType;
    }

    public void setRebateType(Integer rebateType) {
        this.rebateType = rebateType;
    }

    public Double getRebateNumber() {
        return rebateNumber;
    }

    public void setRebateNumber(Double rebateNumber) {
        this.rebateNumber = rebateNumber;
    }

    public Double getmPrice() {
        return mPrice;
    }

    public void setmPrice(Double mPrice) {
        this.mPrice = mPrice;
    }

    public String getVolume() {
        return volume;
    }

    public void setVolume(String volume) {
        this.volume = volume;
    }

    public BigDecimal getWeightNum() {
        return weightNum;
    }

    public void setWeightNum(BigDecimal weightNum) {
        this.weightNum = weightNum;
    }

    public Integer getUseCoupon() {
        return useCoupon;
    }

    public void setUseCoupon(Integer useCoupon) {
        this.useCoupon = useCoupon;
    }

    public Integer getMaxThreshold() {
        return maxThreshold;
    }

    public void setMaxThreshold(Integer maxThreshold) {
        this.maxThreshold = maxThreshold;
    }

    public Integer getProductType() {
        return productType;
    }

    public void setProductType(Integer productType) {
        this.productType = productType;
    }

    public Long getInvId() {
        return invId;
    }

    public void setInvId(Long invId) {
        this.invId = invId;
    }

    public BigDecimal getActualTotalPrice() {
        return actualTotalPrice;
    }

    public void setActualTotalPrice(BigDecimal actualTotalPrice) {
        this.actualTotalPrice = actualTotalPrice;
    }

    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    public String getSkuPic() {
        return skuPic;
    }

    public void setSkuPic(String skuPic) {
        this.skuPic = skuPic;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    /**
     * 优先取SKU名称，如果为空，则兜底为SPU名称+SKU规格信息
     * @return
     */
    public OrderItem resetSkuName() {
        if (StringUtils.isBlank(skuName)) {
            skuName = pdName + " " + weight;
        }
        return this;
    }

    /**
     * 优先取SKU头图，如果为空，则兜底为SPU橱窗图
     * @return
     */
    public OrderItem resetSkuPic() {
        if (StringUtils.isBlank(skuPic)) {
            skuPic = picturePath;
        }
        return this;
    }

    /**
     * 优先取SKU名称，如果为空，则兜底为SPU名称+SKU规格信息
     * && 优先取SKU头图，如果为空，则兜底为SPU橱窗图
     * @return
     */
    public OrderItem resetSkuNameAndSkuPic() {
        resetSkuName();
        resetSkuPic();
        return this;
    }
}