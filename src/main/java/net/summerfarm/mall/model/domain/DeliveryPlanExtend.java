package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * delivery_plan_extend
 * <AUTHOR>
@Data
public class DeliveryPlanExtend implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 配送计划id
     */
    private Integer deliveryPlanId;

    /**
     * 操作人类型：0 用户 1内部员工
     */
    private Integer operatorType;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作类型：10 新增，11删除，12修改，13查询
     */
    private Integer type;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}