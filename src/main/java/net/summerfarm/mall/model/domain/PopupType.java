package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 弹窗类型
 */
@Data
public class PopupType implements Serializable {
    /**
    * primary key
    */
    private Long id;

    /**
    * create time
    */
    private LocalDateTime createTime;

    /**
    * update time
    */
    private LocalDateTime updateTime;

    /**
    * 弹窗类型
    */
    private String typeName;

    /**
    * 弹窗间隔（小时）null表示只弹一次
    */
    private Integer popupInterval;

    private static final long serialVersionUID = 1L;
}