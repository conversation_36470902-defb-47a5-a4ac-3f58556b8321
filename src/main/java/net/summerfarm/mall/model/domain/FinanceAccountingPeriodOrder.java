package net.summerfarm.mall.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class FinanceAccountingPeriodOrder implements Serializable {
    /**
     * 账期订单表id
     */
    private Long id;

    /**
     * 账单生成日
     */
    private Date billGenerationTime;

    /**
     * 账单编号
     */
    private String billNumber;

    /**
     * 发票抬头id
     */
    private Long invoiceId;

    /**
     * 企业工商名称
     */
    private String invoiceTitle;

    /**
     * 品牌名称
     */
    private String nameRemakes;

    /**
     * 账单确认时间
     */
    private Date billConfirmationTime;

    /**
     * 账单状态： 0、待确认 1、已确认 2、合并后的账单（作废不展示）
     */
    private Integer type;

    /**
     * 品牌adminId
     */
    private Integer adminId;

    /**
     * 所属销售/地推人员
     */
    private String salerName;

    /**
     * 售后总金额
     */
    private BigDecimal afterSaleAmount;

    /**
     * 配送费用
     */
    private BigDecimal deliveryFee;

    /**
     * 实付价格
     */
    private BigDecimal totalPrice;

    /**
     * 门店数量
     */
    private Integer storeQuantity;

    /**
     * 添加时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 账单起止日
     */
    private String billCycle;

    /**
     * 超时加单费用
     */
    private BigDecimal outTimesFee;

    /**
     * 收款状态；0-未收款；1-部分收款；2-已收款
     */
    private Byte receiptStatus;

    /**
     * 客户确认状态；0-客户待确认；1-客户已确认；
     */
    private Byte customerConfirmStatus;

    /**
     * 核销金额
     */
    private BigDecimal writeOffAmount;

    /**
     * 销售id
     */
    private Integer salerId;

    /**
     * 财务审核状态  0-未审核；1-审核通过；2-驳回
     */
    private Integer financialAudit;

    /**
     * 销售申请开票 0 未申请 1 申请中 2 申请成功 3 驳回
     */
    private Integer salesInvoicing;

    /**
     * 财务审核备注
     */
    private String remarks;

    /**
     * 客户类型:0:品牌;1:门店
     */
    private Byte customerType;

    private static final long serialVersionUID = 1L;
}