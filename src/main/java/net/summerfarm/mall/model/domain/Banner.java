package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
public class Banner implements Serializable {
    private Integer id;

    private String name;

    private String type;

    private String url;

    @Deprecated
    private String link;

    private Boolean status;

    /**
     * 排序值，现替换成权重值，权重值大的优先展示
     */
    @Deprecated
    private Integer position;

    private Date updateTime;

    private Integer showRule;

    /**
     * 展示形式,0 图片,1 文字,2 商品卡片
     *
     * @see net.summerfarm.enums.BannerEnum.DisplayFormatEnum
     */
    private Integer displayFormat;

    /**
     * 投放文字
     */
    private String word;

    /**
     * 投放文字颜色编码
     */
    private String color;

    /**
     * 跳转类型
     * 0 无跳转，1 商品，2 省心送，3 抽奖，4 专题，5 频道，6 直播间
     * @see net.summerfarm.enums.BannerEnum.LinkTypeEnum
     */
    private Integer linkType;

    /**
     * 跳转业务id，需要结合跳转类型；
     * 频道跳转类型对应的业务id固定枚举值：0 首页，1 分类，2 省心送，3 购物车，4 领券中心，5 优惠卡，6 邀请好友
     * @see net.summerfarm.enums.BannerEnum.LinkChannelEnum
     */
    private String linkBizId;

    /**
     * 权重值，值越大权重越高
     */
    private Integer weight;

    /**
     * 兼容历史跳转专题页id
     */
    private String oldLinkBizId;

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    public void setUrl(String url) {
        this.url = url == null ? null : url.trim();
    }

    public void setLink(String link) {
        this.link = link == null ? null : link.trim();
    }
}