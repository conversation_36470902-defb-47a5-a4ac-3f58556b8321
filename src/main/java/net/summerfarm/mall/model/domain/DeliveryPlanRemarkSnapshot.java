package net.summerfarm.mall.model.domain;

import cn.hutool.json.JSONUtil;
import com.alibaba.schedulerx.shade.org.apache.commons.lang.StringUtils;
import lombok.Data;
import net.summerfarm.mall.common.util.StringUtil;
import net.summerfarm.mall.model.vo.merchant.contact.ContactAddressRemark;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * delivery_plan_remark_snapshot
 * <AUTHOR>
@Data
public class DeliveryPlanRemarkSnapshot implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 样品审核表id/售后表No/配送计划 id
     */
    private String businessId;

    /**
     * 0样品申请,1售后单,2配送计划
     */
    private Integer type;

    /**
     * 地址备注json
     */
    private String addressRemark;

    /**
     * 联系人
     */
    private String contact;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 地区
     */
    private String area;

    /**
     * 地址
     */
    private String address;

    /**
     * 状态(1正常或审核通过、2删除、3待审核、4审核不通过)
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 1默认地址 与merchat中一致
     */
    private Integer isDefault;

    /**
     * 高德地图poi坐标
     */
    private String poiNote;

    /**
     * 与仓库的距离
     */
    private BigDecimal distance;

    /**
     * 门牌号
     */
    private String houseNumber;

    /**
     * 配送仓编号
     */
    private Integer storeNo;

    /**
     * 归属区域id adCodeMsg表
     */
    private Integer acmId;

    /**
     * 配送仓编号 备注
     */
    private Integer backStoreNo;

    /**
     * 配送周期
     */
    private String deliveryFrequent;

    /**
     * 运费规则
     */
    private String deliveryRule;

    /**
     * 运费
     */
    private BigDecimal deliveryFee;

    private static final long serialVersionUID = 1L;

    private ContactAddressRemark contactAddressRemark;

    public ContactAddressRemark getContactAddressRemark() {
        if (!StringUtils.isEmpty(addressRemark)) {
           return JSONUtil.toBean(addressRemark,ContactAddressRemark.class);
        }
        return contactAddressRemark;
    }
}