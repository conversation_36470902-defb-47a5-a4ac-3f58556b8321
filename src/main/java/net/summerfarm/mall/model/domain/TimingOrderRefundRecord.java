package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * timing_order_refund_record
 * <AUTHOR>
@Data
public class TimingOrderRefundRecord implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 弹窗类型: 1 历史逻辑/48小时弹窗提醒 2 省心送订单退款提示弹窗
     */
    private Integer type;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}