package net.summerfarm.mall.model.domain;

import java.util.Date;

public class Products {
    private Long pdId;

    private Integer categoryId;

    private Integer brandId;

    private String pdName;

    private Date createTime;

    /**
     * @deprecated 售后单位放到 {@link Inventory#afterSaleUnit}
     */
    @Deprecated
    private String afterSaleUnit;

    private Integer afterSaleTime;

    private String afterSaleType;

    private String pddetail;

    private String detailPicture;

    private String refundType;

    private Integer warnTime;

    private String picturePath;

    private Integer storageLocation;

    public String getPicturePath() {
        return picturePath;
    }

    public void setPicturePath(String picturePath) {
        this.picturePath = picturePath;
    }

    public String getDetailPicture() {
        return detailPicture;
    }

    public void setDetailPicture(String detailPicture) {
        this.detailPicture = detailPicture;
    }

    public Long getPdId() {
        return pdId;
    }

    public void setPdId(Long pdId) {
        this.pdId = pdId;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public String getPdName() {
        return pdName;
    }

    public void setPdName(String pdName) {
        this.pdName = pdName == null ? null : pdName.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getPddetail() {
        return pddetail;
    }

    public void setPddetail(String pddetail) {
        this.pddetail = pddetail == null ? null : pddetail.trim();
    }

    public String getAfterSaleUnit() {
        return afterSaleUnit;
    }

    public void setAfterSaleUnit(String afterSaleUnit) {
        this.afterSaleUnit = afterSaleUnit;
    }

    public Integer getAfterSaleTime() {
        return afterSaleTime;
    }

    public void setAfterSaleTime(Integer afterSaleTime) {
        this.afterSaleTime = afterSaleTime;
    }

    public String getAfterSaleType() {
        return afterSaleType;
    }

    public void setAfterSaleType(String afterSaleType) {
        this.afterSaleType = afterSaleType;
    }

    public String getRefundType() {
        return refundType;
    }

    public void setRefundType(String refundType) {
        this.refundType = refundType;
    }

    public Integer getWarnTime() {
        return warnTime;
    }

    public void setWarnTime(Integer warnTime) {
        this.warnTime = warnTime;
    }

    public Integer getStorageLocation() {
        return storageLocation;
    }

    public void setStorageLocation(Integer storageLocation) {
        this.storageLocation = storageLocation;
    }
}