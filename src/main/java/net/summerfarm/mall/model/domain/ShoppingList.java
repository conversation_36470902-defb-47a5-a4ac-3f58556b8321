package net.summerfarm.mall.model.domain;

import net.summerfarm.enums.TrolleyProductTypeEnum;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> 2020/05/11 购物单
 */
@Deprecated
public class ShoppingList implements Serializable {

    private Long mId;

    private Long accountId;

    private String sku;

    //商品类型：0、普通商品 1、赠品
    private Integer productType;

    //默认为0
    private int suitId;

    private Integer quantity;

    private Byte check;

    private Date updateTime;

    private Boolean delFlag;

    public ShoppingList() {
        //默认普通商品
        this.productType = TrolleyProductTypeEnum.NORMAL.ordinal();
    }
    public ShoppingList(Long merchantId,Long accountId ,String sku, Integer suitId, Integer quantity) {
        this(merchantId, accountId, sku, suitId, TrolleyProductTypeEnum.NORMAL.ordinal(), quantity);
    }

    public ShoppingList(Long merchantId,Long accountId ,String sku, Integer suitId, Integer productType, Integer quantity) {
        this.mId = merchantId;
        this.accountId = accountId;
        this.sku = sku;
        this.suitId = suitId;
        this.productType = productType;
        this.quantity = quantity;
        this.updateTime = new Date();
    }

    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getProductType() {
        return productType;
    }

    public void setProductType(Integer productType) {
        this.productType = productType;
    }

    public int getSuitId() {
        return suitId;
    }

    public void setSuitId(int suitId) {
        this.suitId = suitId;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public Byte getCheck() {
        return check;
    }

    public void setCheck(Byte check) {
        this.check = check;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Boolean delFlag) {
        this.delFlag = delFlag;
    }

    @Override
    public String toString() {
        return "ShoppingList{" +
                "mId=" + mId +
                ", sku='" + sku + '\'' +
                ", productType=" + productType +
                ", suitId=" + suitId +
                ", quantity=" + quantity +
                ", check=" + check +
                ", updateTime=" + updateTime +
                ", delFlag=" + delFlag +
                '}';
    }
}
