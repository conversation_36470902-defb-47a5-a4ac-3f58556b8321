package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ExchangeBaseInfo implements Serializable {

    private Long id;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 备注
     */
    private String remark;

    /**
     * 勾选多少件可以参与
     */
    private Integer triggerNum;

    /**
     * 每件商品限购数量-单位：件
     */
    private Integer purchaseLimit;

    /**
     * 每件商品优惠幅度等于毛利的百分之X
     */
    private Integer discountPercentage;

    /**
     * 每件商品最多优惠金额，元
     */
    private BigDecimal discount;

    /**
     * 活动类型
     */
    private Integer type;

    /**
     * 活动状态，0 未生效 ，1 生效中
     */
    private Integer status;

    /**
     * 生效时间类型，0 固定时间， 1 长期有效
     */
    private Integer effectTimeType;

    /**
     * 活动开始时间
     */
    private Date startTime;

    /**
     * 活动结束时间
     */
    private Date endTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 是否已删除，0 否， 1 是
     */
    private Integer isDelete;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;

}