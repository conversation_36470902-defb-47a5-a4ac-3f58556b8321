package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * timing_order_refund_white_list
 * <AUTHOR>
@Data
public class TimingOrderRefundWhiteList implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单生成时间
     */
    private LocalDateTime orderTime;

    /**
     * 商户名称
     */
    private String mname;

    /**
     * 商品名称（快照）
     */
    private String pdName;

    /**
     * sku
     */
    private String sku;

    /**
     * 操作人
     */
    private String operator;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}