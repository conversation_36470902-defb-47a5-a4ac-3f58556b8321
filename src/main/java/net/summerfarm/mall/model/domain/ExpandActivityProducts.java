package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * expand_activity_products
 * <AUTHOR>
@Data
public class ExpandActivityProducts implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 商户ID
     */
    private Long mId;

    /**
     * areaNo
     */
    private Integer areaNo;

    /**
     * sku
     */
    private String sku;

    /**
     * 0流失风险商品，1召回商品，2拉新商品
     */
    private Integer type;

    /**
     * 同步时间标记(yyyyMMdd/yyyyMM)
     */
    private Integer dateFlag;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}