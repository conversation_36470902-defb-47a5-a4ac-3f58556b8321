package net.summerfarm.mall.model.domain;

/**
 * Created by wjd on 2018/9/19.
 */
public class MarketRuleHistory {

    private Integer id;

    private String detail;

    private String orderNo;

    private Integer marketRuleId;

    private Integer value;

    private Integer ruleLevel;
    private Integer type;

    /**
     * 满返发放状态  0：待发放 1：发放中  2：已发放
     */
    private Integer sendStatus;


    public MarketRuleHistory() {
    }

    public MarketRuleHistory(String detail, String orderNo, Integer marketRuleId, Integer value, Integer ruleLevel, Integer type, Integer sendStatus) {
        this.detail = detail;
        this.orderNo = orderNo;
        this.marketRuleId = marketRuleId;
        this.value = value;
        this.ruleLevel = ruleLevel;
        this.type = type;
        this.sendStatus = sendStatus;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getMarketRuleId() {
        return marketRuleId;
    }

    public void setMarketRuleId(Integer marketRuleId) {
        this.marketRuleId = marketRuleId;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public Integer getRuleLevel() {
        return ruleLevel;
    }

    public void setRuleLevel(Integer ruleLevel) {
        this.ruleLevel = ruleLevel;
    }

    public Integer getSendStatus() {
        return sendStatus;
    }

    public void setSendStatus(Integer sendStatus) {
        this.sendStatus = sendStatus;
    }
}
