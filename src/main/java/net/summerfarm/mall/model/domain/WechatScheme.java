package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * wechat_scheme
 * <AUTHOR>
@Data
public class WechatScheme implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 请求ip
     */
    private String hostIp;

    /**
     * scheme码
     */
    private String scheme;

    /**
     * 过期时间
     */
    private LocalDateTime expireTime;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}