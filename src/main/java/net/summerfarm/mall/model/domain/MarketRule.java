package net.summerfarm.mall.model.domain;

import net.summerfarm.mall.common.util.DateUtils;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.beans.BeanUtils;

import java.util.Date;
import java.util.List;

public class MarketRule {
    private Integer id;

    private String name;

    private Integer type;

    private String detail;

    private String showName;

    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private Date startTime;

    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private Date endTime;

    private Integer areaNo;

    private Date updateTime;

    private String ruleDetail;

    private Integer ruleLevel;

    private Integer value;


    private Integer status;


    private List<MarketRuleDetail> marketRuleDetailList;

    private String sku;

    private List<String> skus;

    // 0不支持省心送 1 支持省心送
    private Integer supportType;

    /**
     * 返券规则  1-确认收货后（默认）  2-支付完成后
     */
    private Integer couponRule;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail == null ? null : detail.trim();
    }

    public String getShowName() {
        return showName;
    }

    public void setShowName(String showName) {
        this.showName = showName == null ? null : showName.trim();
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getAreaNo() {
        return areaNo;
    }

    public void setAreaNo(Integer areaNo) {
        this.areaNo = areaNo;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public List<MarketRuleDetail> getMarketRuleDetailList() {
        return marketRuleDetailList;
    }

    public void setMarketRuleDetailList(List<MarketRuleDetail> marketRuleDetailList) {
        this.marketRuleDetailList = marketRuleDetailList;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku == null ? null : sku.trim();
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getRuleDetail() {
        return ruleDetail;
    }

    public void setRuleDetail(String ruleDetail) {
        this.ruleDetail = ruleDetail;
    }


    public Integer getRuleLevel() {
        return ruleLevel;
    }

    public void setRuleLevel(Integer ruleLevel) {
        this.ruleLevel = ruleLevel;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public Integer getSupportType() {
        return supportType;
    }

    public void setSupportType(Integer supportType) {
        this.supportType = supportType;
    }

    public List<String> getSkus() {
        return skus;
    }

    public void setSkus(List<String> skus) {
        this.skus = skus;
    }

    public Integer getCouponRule() {
        return couponRule;
    }

    public void setCouponRule(Integer couponRule) {
        this.couponRule = couponRule;
    }

    public static MarketRule clone(MarketRule source) {
        if (source == null) {
            return null;
        }
        try {
            MarketRule clone = new MarketRule();
            BeanUtils.copyProperties(source, clone);
            return clone;
        } catch (Exception e) {
            throw new RuntimeException("unable to clone:" + source, e);
        }
    }
}