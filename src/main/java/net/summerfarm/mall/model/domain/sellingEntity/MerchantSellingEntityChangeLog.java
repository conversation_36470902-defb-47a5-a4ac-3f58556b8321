package net.summerfarm.mall.model.domain.sellingEntity;

import java.time.LocalDateTime;
import lombok.Data;
import net.summerfarm.mall.enums.SellingEntityAgreeEnum;


/**
 * <AUTHOR>
 * @date 2025-03-17 10:33:51
 * @version 1.0
 *
 */
@Data
public class MerchantSellingEntityChangeLog {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 门店id
	 */
	private Long mId;

	/**
	 * 销售主体名称
	 */
	private String sellingEntityName;

	/**
	 * 是否已同意（0：否，1：是）
	 */
	private Integer agree;


	public boolean getAgreeValue(){
		return SellingEntityAgreeEnum.AGREE.getCode().equals(this.agree);
	}



}