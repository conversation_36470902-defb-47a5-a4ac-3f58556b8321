package net.summerfarm.mall.model.domain;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

public class Inventory {
    private Long invId;

    private String sku;

    private String skuName;
    //库存
    private Integer quantity;

    private Integer aitId;

    private Long pdId;

    private Long picId;

    private String origin;

    private String unit;

    private String maturity;

    private String pack;

    private String weight;

    private Date productionDate;

    private String storageMethod;

    private String slogan;

    private String otherSlogan;

    private Integer saleCount;

    private Integer alertInventory;

    private Integer safeInventory;

    private BigDecimal salePrice;

    private BigDecimal costPrice;

    private BigDecimal promotionPrice;

    private Boolean onSale;

    private Integer priority;

    private String introduction;

    private Integer afterSaleQuantity;

    private Integer baseSaleUnit;

    private Integer baseSaleQuantity;

    private String volume;

    private BigDecimal weightNum;
    /**
    * 类型 0 自营 1 代仓
    */
    private Integer type;
    /**
     * 商品二级性质，1 自营-代销不入仓、2 自营-代销入仓、3 自营-经销、4 代仓-代仓
     */
    @Getter
    @Setter
    private Integer subType;
    /**
     * sku头图
     */
    private String skuPic;

    /** 售后单位 **/
    private String afterSaleUnit;

    /**
     * 买手id
     */
    private Long buyerId;

    /**
     * 供应商报价类型：0-默认类型，1-按斤报价，2-按件报价
     */
    private Integer quoteType;

    /**
     * 自动补差售后量阈值，自动补差售后量阈值，单位等同于售后单位
     */
    private Integer minAutoAfterSaleThreshold;

    /**
     * sku性质
     */
    private Integer extType;

    public Integer getQuoteType() {
        return quoteType;
    }

    public void setQuoteType(Integer quoteType) {
        this.quoteType = quoteType;
    }

    public Integer getMinAutoAfterSaleThreshold() {
        return minAutoAfterSaleThreshold;
    }

    public void setMinAutoAfterSaleThreshold(Integer minAutoAfterSaleThreshold) {
        this.minAutoAfterSaleThreshold = minAutoAfterSaleThreshold;
    }

    public Long getBuyerId() {
        return buyerId;
    }

    public void setBuyerId(Long buyerId) {
        this.buyerId = buyerId;
    }

    public String getAfterSaleUnit() {
        return this.afterSaleUnit;
    }

    public void setAfterSaleUnit(String afterSaleUnit) {
        this.afterSaleUnit = afterSaleUnit;
    }

    public Integer getBaseSaleUnit() {
        return baseSaleUnit;
    }

    public void setBaseSaleUnit(Integer baseSaleUnit) {
        this.baseSaleUnit = baseSaleUnit;
    }

    public Integer getBaseSaleQuantity() {
        return baseSaleQuantity;
    }

    public void setBaseSaleQuantity(Integer baseSaleQuantity) {
        this.baseSaleQuantity = baseSaleQuantity;
    }

    public Long getInvId() {
        return invId;
    }

    public void setInvId(Long invId) {
        this.invId = invId;
    }

    public Integer getAfterSaleQuantity() {
        return afterSaleQuantity;
    }

    public void setAfterSaleQuantity(Integer afterSaleQuantity) {
        this.afterSaleQuantity = afterSaleQuantity;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku == null ? null : sku.trim();
    }

    public Integer getAitId() {
        return aitId;
    }

    public void setAitId(Integer aitId) {
        this.aitId = aitId;
    }

    public Long getPdId() {
        return pdId;
    }

    public void setPdId(Long pdId) {
        this.pdId = pdId;
    }

    public Long getPicId() {
        return picId;
    }

    public void setPicId(Long picId) {
        this.picId = picId;
    }

    public String getOrigin() {
        return origin;
    }

    public void setOrigin(String origin) {
        this.origin = origin == null ? null : origin.trim();
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit == null ? null : unit.trim();
    }

    public String getPack() {
        return pack;
    }

    public void setPack(String pack) {
        this.pack = pack == null ? null : pack.trim();
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight == null ? null : weight.trim();
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public String getStorageMethod() {
        return storageMethod;
    }

    public void setStorageMethod(String storageMethod) {
        this.storageMethod = storageMethod == null ? null : storageMethod.trim();
    }

    public String getSlogan() {
        return slogan;
    }

    public void setSlogan(String slogan) {
        this.slogan = slogan == null ? null : slogan.trim();
    }

    public Integer getSaleCount() {
        return saleCount;
    }

    public void setSaleCount(Integer saleCount) {
        this.saleCount = saleCount;
    }

    public Integer getAlertInventory() {
        return alertInventory;
    }

    public void setAlertInventory(Integer alertInventory) {
        this.alertInventory = alertInventory;
    }

    public Integer getSafeInventory() {
        return safeInventory;
    }

    public void setSafeInventory(Integer safeInventory) {
        this.safeInventory = safeInventory;
    }

    public BigDecimal getSalePrice() {
        return salePrice;
    }

    public void setSalePrice(BigDecimal salePrice) {
        this.salePrice = salePrice;
    }

    public BigDecimal getCostPrice() {
        return costPrice;
    }

    public void setCostPrice(BigDecimal costPrice) {
        this.costPrice = costPrice;
    }

    public BigDecimal getPromotionPrice() {
        return promotionPrice;
    }

    public void setPromotionPrice(BigDecimal promotionPrice) {
        this.promotionPrice = promotionPrice;
    }

    public Boolean getOnSale() {
        return onSale;
    }

    public void setOnSale(Boolean onSale) {
        this.onSale = onSale;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public String getIntroduction() {
        return introduction;
    }

    public void setIntroduction(String introduction) {
        this.introduction = introduction == null ? null : introduction.trim();
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public String getMaturity() {
        return maturity;
    }

    public void setMaturity(String maturity) {
        this.maturity = maturity;
    }

    public String getOtherSlogan() {
        return otherSlogan;
    }

    public void setOtherSlogan(String otherSlogan) {
        this.otherSlogan = otherSlogan;
    }

    public String getVolume() {
        return volume;
    }

    public void setVolume(String volume) {
        this.volume = volume;
    }

    public BigDecimal getWeightNum() {
        return weightNum;
    }

    public void setWeightNum(BigDecimal weightNum) {
        this.weightNum = weightNum;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getSkuPic() {
        return skuPic;
    }

    public void setSkuPic(String skuPic) {
        this.skuPic = skuPic;
    }

    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    public Integer getExtType() {
        return extType;
    }

    public void setExtType(Integer extType) {
        this.extType = extType;
    }
}