package net.summerfarm.mall.model.domain.market;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ActivitySkuPrice implements Serializable {

    private Long id;

    /**
     * 基本信息id
     */
    private Long basicInfoId;

    /**
     * sku配置明细id
     */
    private Long skuDetailId;

    /**
     * 活动sku
     */
    private String sku;

    /**
     * 运营城市
     */
    private Integer areaNo;

    /**
     * 原价
     */
    private BigDecimal salePrice;

    /**
     * 阶梯价
     */
    private String ladderPrice;

    /**
     * 活动价
     */
    private BigDecimal activityPrice;

    /**
     * 最后一次修改人id
     */
    private Integer updaterId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}