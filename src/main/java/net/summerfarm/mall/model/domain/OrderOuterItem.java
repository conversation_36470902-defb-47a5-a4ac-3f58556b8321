package net.summerfarm.mall.model.domain;

import lombok.Data;
import org.joda.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description AOL订单明细
 * @createTime 2021年09月10日 11:29:00
 */
@Data
public class OrderOuterItem {

    private Long id;
    /**
     * 明细行号
     */
    private Integer itemId;
    /**
     * aol订单编号
     */
    private String orderNo;
    /**
     * aolsku信息
     */
    private String sku;
    /**
     * 数量
     */
    private Integer amount;
    /**
     * 规格
     */
    private String standard;
    /**
     * 件
     */
    private String unit;
    /**
     * 商品名称
     */
    private String name;
    /**
     * xmsku信息
     */
    private String xmSku;
    /**
     * xm商品名称
     */
    private String pdName;

    private LocalDateTime createTime;

    /**
     * 外部平台id
     */
    private Integer outerPlatformId;

}
