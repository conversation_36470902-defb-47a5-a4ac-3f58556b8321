package net.summerfarm.mall.model.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

@Data
public class Area implements Serializable {
    private Integer id;

    private Integer areaNo;

    private String areaName;

    private Integer adminId;

    private Integer parentNo;

    private BigDecimal deliveryFee;

    private BigDecimal expressFee;

    private Boolean status;

    private String deliveryRule;

    private String memberRule;

    private Integer companyAccountId;

    private String mapSection;

    private String freeDay;

    private Integer type;

    private LocalDate nextDeliveryDate;

    @ApiModelProperty(value = "支付通道 0 微信 1 中银支付")
    private Integer payChannel;

    @ApiModelProperty(value = "是否正在切换城市")
    private Boolean changeFlag;

    @ApiModelProperty(value = "切换的城市编号(处理完后清空)")
    private Integer changeStoreNo;

    @ApiModelProperty(value = "切换状态：0、默认 1、预约中 2、大客户停服 3、城市停服")
    private Integer changeStatus;

    @ApiModelProperty(value = "是否支持加单 加单时间")
    private Integer supportAddOrder;

    /**
    * 运营大区编号
    */
    private Integer largeAreaNo;

    /**
     * 微信小程序是否使用招行收款：0、不使用（默认）1、使用
     */
    private Integer wxlitePayChannel;

    public Area() {
    }

    public Area(Integer areaNo) {
        this.areaNo = areaNo;
    }

    public Area(Integer areaNo, String deliveryRule) {
        this.areaNo = areaNo;
        this.deliveryRule = deliveryRule;
    }
}