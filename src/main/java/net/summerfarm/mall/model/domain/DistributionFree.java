package net.summerfarm.mall.model.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR> ct
 * create at:  2019/7/8  3:54 PM
 */
@Data
@ApiModel(value = "免配送费条件实体类")
public class DistributionFree implements Serializable {

    @ApiModelProperty("类目 0 全部, 1 代仓, 2 自营, 3 乳制品, 4 非乳制品")
    private Integer category;

    @ApiModelProperty("类型 1 金额  2 件数")
    private Integer type;

    @ApiModelProperty("金额 type = 0 生效 单位:元")
    private BigDecimal amount;

    @ApiModelProperty("数量  type = 1 生效")
    private Integer number;

}