package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * discount_card_to_merchant
 * <AUTHOR>
@Data
public class DiscountCardToMerchant implements Serializable {
    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * 商户id
     */
    private Long mId;

    /**
     * 折扣卡id
     */
    private Integer discountCardId;

    /**
     * 状态：0、失效 1、有效
     */
    private Integer status;

    /**
     * 总次数
     */
    private Integer totalTimes;

    /**
     * 已用次数
     */
    private Integer usedTimes;

    /**
     * 截至日期
     */
    private LocalDate deadline;

    /**
     * 更新人
     */
    private String updator;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    private static final long serialVersionUID = 1L;
}