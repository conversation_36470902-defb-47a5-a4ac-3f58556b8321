package net.summerfarm.mall.model.domain;

import lombok.Data;
import net.summerfarm.mall.common.util.DateUtils;
import net.summerfarm.common.util.validation.groups.Add;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class Card {

    private Integer id;

    @NotNull(groups = {Add.class},message = "param.not.null.illegal")
    private String name;

    private String code;

    @NotNull(groups = {Add.class},message = "param.not.null.illegal")
    private Integer cardType;

    private BigDecimal money;

    private BigDecimal threshold;

    @NotNull(groups = {Add.class},message = "param.not.null.illegal")
    private Integer type;

    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime vaildDate;

    private Integer vaildTime;

    @NotNull(groups = {Add.class},message = "param.not.null.illegal")
    private Integer grouping;

    private Integer times;

    private String remark;

    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime addTime;

    private Integer status;
}
