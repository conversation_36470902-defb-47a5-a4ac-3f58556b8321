package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class MerchantPoolDetail implements Serializable {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 人群包id
     */
    private Long poolInfoId;

    /**
     * 商户id
     */
    private Long mId;

    /**
     * 店铺类型
     */
    private String size;

    /**
     * 运营服务区域
     */
    private Integer areaNo;

    /**
     * 版本(只会存在两个版本)
     */
    private Integer version;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}