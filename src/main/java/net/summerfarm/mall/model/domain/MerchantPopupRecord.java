package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Builder;
import lombok.Data;

/**
 * 商户弹窗记录
 */
@Data
@Builder
public class MerchantPopupRecord implements Serializable {
    /**
    * primary key
    */
    private Long id;

    /**
    * create time
    */
    private LocalDateTime createTime;

    /**
    * update time
    */
    private LocalDateTime updateTime;

    /**
    * 商户ID
    */
    private Long mId;

    /**
    * 弹窗类型 FK-popup_types.id
    */
    private Long typeId;

    /**
    * 上次弹出日期时间
    */
    private LocalDateTime lastPopupTime;

    /**
    * 已弹出次数
    */
    private Integer count;

    private static final long serialVersionUID = 1L;
}