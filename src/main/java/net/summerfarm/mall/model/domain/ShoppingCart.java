package net.summerfarm.mall.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class ShoppingCart implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * m_id
     */
    private Long mId;

    /**
     * 子账号id
     */
    private Long accountId;

    /**
     * 业务id-换购ID
     */
    private Long bizId;

    /**
     * sku
     */
    private String sku;

    /**
     * 搭配购上级sku
     */
    private String parentSku;

    /**
     * 商品类型：0、普通商品 1、赠品   2、换购
     */
    private Integer productType;

    /**
     * 购买数量
     */
    private Integer quantity;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}