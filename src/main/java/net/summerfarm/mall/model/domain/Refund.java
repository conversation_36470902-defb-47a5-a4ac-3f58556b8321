package net.summerfarm.mall.model.domain;

import lombok.ToString;
import net.summerfarm.enums.RefundStatusEnum;

import java.math.BigDecimal;
import java.util.Date;

@ToString
public class Refund {
    private Long refundId;

    private String orderNo;

    private String refundNo;

    private String transactionNumber;

    private String afterSaleOrderNo;

    private BigDecimal totalFee;

    private BigDecimal refundFee;

    private BigDecimal cashFee;

    private BigDecimal cashRefundFee;

    private String refundDesc;

    private Date endTime;

    private Date onlineRefundEndTime;

    private String refundChannel;

    private Byte status;

    private String errCode;

    private String errCodeDes;

    private String couponId;

    /**
     * 主订单号
     */
    private String masterOrderNo;

    /**
     * 主订单支付金额
     */
    private BigDecimal masterTotalFee;

    public Date getOnlineRefundEndTime() {
        return onlineRefundEndTime;
    }

    public void setOnlineRefundEndTime(Date onlineRefundEndTime) {
        this.onlineRefundEndTime = onlineRefundEndTime;
    }

    public Refund() {
    }

    public Refund(String orderNo, String afterSaleOrderNo, String refundNo, BigDecimal totalFee, BigDecimal refundFee) {
        this.orderNo = orderNo;
        this.refundNo = refundNo;
        this.afterSaleOrderNo = afterSaleOrderNo;
        this.totalFee = totalFee;
        this.refundFee = refundFee;
        this.status = (byte) RefundStatusEnum.APPLY.ordinal();
    }

    public Long getRefundId() {
        return refundId;
    }

    public void setRefundId(Long refundId) {
        this.refundId = refundId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo == null ? null : orderNo.trim();
    }

    public String getRefundNo() {
        return refundNo;
    }

    public void setRefundNo(String refundNo) {
        this.refundNo = refundNo == null ? null : refundNo.trim();
    }

    public String getTransactionNumber() {
        return transactionNumber;
    }

    public void setTransactionNumber(String transactionNumber) {
        this.transactionNumber = transactionNumber == null ? null : transactionNumber.trim();
    }

    public String getAfterSaleOrderNo() {
        return afterSaleOrderNo;
    }

    public void setAfterSaleOrderNo(String afterSaleOrderNo) {
        this.afterSaleOrderNo = afterSaleOrderNo;
    }

    public String getRefundDesc() {
        return refundDesc;
    }

    public void setRefundDesc(String refundDesc) {
        this.refundDesc = refundDesc;
    }

    public BigDecimal getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(BigDecimal totalFee) {
        this.totalFee = totalFee;
    }

    public BigDecimal getRefundFee() {
        return refundFee;
    }

    public void setRefundFee(BigDecimal refundFee) {
        this.refundFee = refundFee;
    }

    public BigDecimal getCashFee() {
        return cashFee;
    }

    public void setCashFee(BigDecimal cashFee) {
        this.cashFee = cashFee;
    }

    public BigDecimal getCashRefundFee() {
        return cashRefundFee;
    }

    public void setCashRefundFee(BigDecimal cashRefundFee) {
        this.cashRefundFee = cashRefundFee;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getRefundChannel() {
        return refundChannel;
    }

    public void setRefundChannel(String refundChannel) {
        this.refundChannel = refundChannel == null ? null : refundChannel.trim();
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public String getErrCode() {
        return errCode;
    }

    public void setErrCode(String errCode) {
        this.errCode = errCode == null ? null : errCode.trim();
    }

    public String getErrCodeDes() {
        return errCodeDes;
    }

    public void setErrCodeDes(String errCodeDes) {
        this.errCodeDes = errCodeDes == null ? null : errCodeDes.trim();
    }

    public String getCouponId() {
        return couponId;
    }

    public void setCouponId(String couponId) {
        this.couponId = couponId;
    }

    public String getMasterOrderNo() {
        return masterOrderNo;
    }

    public void setMasterOrderNo(String masterOrderNo) {
        this.masterOrderNo = masterOrderNo;
    }

    public BigDecimal getMasterTotalFee() {
        return masterTotalFee;
    }

    public void setMasterTotalFee(BigDecimal masterTotalFee) {
        this.masterTotalFee = masterTotalFee;
    }
}