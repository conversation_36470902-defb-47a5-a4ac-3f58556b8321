package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * circle_people_rule_admin
 * <AUTHOR>
@Data
public class CirclePeopleRuleAdmin implements Serializable {
    /**
     * 主键id
     */
    private Integer id;

    /**
     * 具体规则id
     */
    private Integer ruleId;

    /**
     * 客户id
     */
    private Long mId;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    private static final long serialVersionUID = 1L;
}