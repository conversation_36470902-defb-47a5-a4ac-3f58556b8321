package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * mch_popup_otification
 * <AUTHOR>
@Data
public class MchPopupOtification implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * banner表id
     */
    private Integer bannerId;

    /**
     * 用户mId
     */
    private Long mId;

    /**
     * banner状态：0未展示，1已经展示
     */
    private Boolean status;

    /**
     * 弹窗日期
     */
    private LocalDate recognitionTime;

    private static final long serialVersionUID = 1L;
}