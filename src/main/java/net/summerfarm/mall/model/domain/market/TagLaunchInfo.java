package net.summerfarm.mall.model.domain.market;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;
import net.summerfarm.mall.enums.market.ItemTypeEnum;
import net.summerfarm.mall.enums.market.ScopeTypeEnum;

/**
 * <AUTHOR>
 */
@Data
public class TagLaunchInfo implements Serializable {

    private Long id;

    /**
     * 标签名称
     */
    private String name;

    /**
     * 标签类型，0 氛围，1 利益，2 业务
     */
    private Integer type;

    /**
     * 标签样式，0 文案，1 图片
     */
    private Integer style;

    /**
     * 标签文案
     */
    private String content;

    /**
     * 标签图片
     */
    private String picture;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 活动范围类型，0 全部，1 人群包，2 运营城市，3 运营大区
     * @see ScopeTypeEnum
     */
    private Integer scopeType;

    /**
     * 商品选择类型，0 全部，1 指定商品，2 指定类目
     * @see ItemTypeEnum
     */
    private Integer itemType;

    /**
     * 生效时间
     */
    private LocalDateTime startTime;

    /**
     * 失效时间
     */
    private LocalDateTime endTime;

    /**
     * 创建人id
     */
    private Integer creatorId;

    /**
     * 最后一次修改人id
     */
    private Integer updaterId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}