package net.summerfarm.mall.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * tms_stop_delivery
 * <AUTHOR>
@Data
public class TmsStopDelivery implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建时间
     */
    private LocalDateTime updateTime;

    /**
     * 城配仓
     */
    private Integer storeNo;

    /**
     * 停运开始时间
     */
    private LocalDate shutdownStartTime;

    /**
     * 停运结束时间
     */
    private LocalDate shutdownEndTime;

    /**
     * 是否删除
     */
    private Byte deleteFlag;

    private static final long serialVersionUID = 1L;
}