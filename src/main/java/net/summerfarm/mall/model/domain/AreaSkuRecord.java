package net.summerfarm.mall.model.domain;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class AreaSkuRecord {
    private Integer id;

    private String recorder;

    private String sku;

    private Integer areaNo;

    private String typeName;

    private Boolean resultStatus;

    private LocalDateTime addtime;

    public AreaSkuRecord(){

    }

    public AreaSkuRecord(String sku,Integer areaNo,String typeName){
        this.sku = sku;
        this.areaNo = areaNo;
        this.typeName = typeName;
    }

    public AreaSkuRecord(String recorder,String sku,Integer areaNo,String typeName,Boolean resultStatus,LocalDateTime addtime){
        this.recorder = recorder;
        this.sku = sku;
        this.areaNo = areaNo;
        this.typeName = typeName;
        this.resultStatus = resultStatus;
        this.addtime = addtime;
    }



}
