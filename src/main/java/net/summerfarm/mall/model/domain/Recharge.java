package net.summerfarm.mall.model.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@ApiModel(description = "充值model类")
@Data
public class Recharge implements Serializable {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "充值编号")
    private String rechargeNo;

    @ApiModelProperty(value = "交易号")
    private String transactionNumber;

    @ApiModelProperty(value = "客户编号")
    private Long mId;

    @ApiModelProperty(value = "充值金额")
    private BigDecimal rechargeNum;

    @ApiModelProperty(value = "支付类型")
    private Integer rechagreType;

    @ApiModelProperty(value = "充值申请人")
    private Integer applicant;

    @ApiModelProperty(value = "充值评审人")
    private Integer handler;

    @ApiModelProperty(value = "状态：0审核中，1成功，2失败")
    private Integer status;

    @ApiModelProperty(value = "添加时间")
    private LocalDateTime addtime;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updatetime;

    @ApiModelProperty(value = "备注")
    private String remark;

    private Integer rechargeType;

    private Integer sendCoupon;

    private Integer fundType;

    private Integer level;

    @ApiModelProperty(value = "招银流水表id")
    private Long financeBankFlowingWaterId;
}
