package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ExchangeItem implements Serializable {

    private Long id;

    private Long mId;

    private String sku;

    private Integer type;

    private Integer priority;

    private Integer dateFlag;

    private Date updateTime;

    private Date createTime;

}