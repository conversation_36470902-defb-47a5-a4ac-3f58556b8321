package net.summerfarm.mall.model.domain;

import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * Created by wjd on 2017/9/25.
 */
public class Activity {

    private Integer id;

    private String name;

    private String logo;

    private Integer areaNo;

    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date startTime;

    @DateTimeFormat
    private Date endTime;

    private Integer status;

    private String startJob;

    private String endJob;

    private Integer priority;

    private String inLogo;

    private String info;

    private Integer type;

    private Boolean bannerShow;

    /**
     * 活动类型 1-临保活动 0-非临保活动
     */
    private Integer activityType;

    private List<ActivitySku> skuList;


    public List<ActivitySku> getSkuList() {
        return skuList;
    }

    public void setSkuList(List<ActivitySku> skuList) {
        this.skuList = skuList;
    }


    public Activity() {
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public Integer getAreaNo() {
        return areaNo;
    }

    public void setAreaNo(Integer areaNo) {
        this.areaNo = areaNo;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getStartJob() {
        return startJob;
    }

    public void setStartJob(String startJob) {
        this.startJob = startJob;
    }

    public String getEndJob() {
        return endJob;
    }

    public void setEndJob(String endJob) {
        this.endJob = endJob;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public String getInLogo() {
        return inLogo;
    }

    public void setInLogo(String inLogo) {
        this.inLogo = inLogo;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Boolean getBannerShow() {
        return bannerShow;
    }

    public void setBannerShow(Boolean bannerShow) {
        this.bannerShow = bannerShow;
    }

    public Integer getActivityType() {
        return activityType;
    }

    public void setActivityType(Integer activityType) {
        this.activityType = activityType;
    }
}

