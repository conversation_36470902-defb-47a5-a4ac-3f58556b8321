package net.summerfarm.mall.model.domain;

import java.io.Serializable;

/**
 * Created by wjd on 2018/8/7.
 */
public class Config implements Serializable {

    private Integer id;

    private String key;

    private String value;

    private String remark;


    public Config() {
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
