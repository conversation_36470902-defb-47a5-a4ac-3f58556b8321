package net.summerfarm.mall.model.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.common.util.validation.annotation.InRange;
import net.summerfarm.common.util.validation.groups.Add;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class AfterSaleProof {

    @ApiModelProperty(value = "售后id",hidden = true)
    @Null(message = "param.illegal", groups = Add.class)
    private Integer id;

    @ApiModelProperty(value = "售后编号")
    @Null(message = "param.illegal", groups = Add.class)
    private String afterSaleOrderNo;

    @ApiModelProperty(value = "售后数量")
    @Min(value = 1,groups = Add.class)
    @NotNull(groups = Add.class)
    private Integer quantity;

    @ApiModelProperty(value = "售后类型")
    @NotNull(message = "param.illegal", groups = Add.class)
    private String afterSaleType;

    private String applySecondaryRemark;
    /**
     * 二级审核原因
     */
    private String handleSecondaryRemark;

    @ApiModelProperty(value = "退款类型")
    @NotNull(message = "param.illegal", groups = Add.class)
    private String refundType;

    @ApiModelProperty(value = "售后图片",hidden = true)
    private String proofPic;

    @ApiModelProperty(value = "售后处理方式,0返券，1补发，2退款 3 录入账单 4 退货退款 5 退货录入账单 6 换货,7 补发",hidden = true)
    @InRange(rangeNums = {0,1}, groups = Add.class)
    private Integer handleType;

    @ApiModelProperty(value = "补偿数量",hidden = true)
    private BigDecimal handleNum;

    @ApiModelProperty(value = "补充说明",required = false)
    private String applyRemark;

    @ApiModelProperty(value = "售后状态",hidden = true)
    private Integer status;

    @ApiModelProperty(value = "处理结果",hidden = true)
    private String handleRemark;

    @ApiModelProperty(value = "更新时间",hidden = true)
    private LocalDateTime updatetime;


    private String auditer;

    private String auditeRemark;

    private String extraRemark;

    private LocalDateTime auditetime;

    private String applyer;

    private LocalDateTime handletime;

    private String handler;


    /**
    * 回收费金额
    */
    private BigDecimal recoveryNum;

    /**
     * 售后视频
     */
    private String proofVideo;

    private LocalDateTime createTime;

    public AfterSaleProof() {
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getAfterSaleOrderNo() {
        return afterSaleOrderNo;
    }

    public void setAfterSaleOrderNo(String afterSaleOrderNo) {
        this.afterSaleOrderNo = afterSaleOrderNo;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public String getAfterSaleType() {
        return afterSaleType;
    }

    public void setAfterSaleType(String afterSaleType) {
        this.afterSaleType = afterSaleType;
    }

    public String getRefundType() {
        return refundType;
    }

    public void setRefundType(String refundType) {
        this.refundType = refundType;
    }

    public String getProofPic() {
        return proofPic;
    }

    public void setProofPic(String proofPic) {
        this.proofPic = proofPic;
    }

    public Integer getHandleType() {
        return handleType;
    }

    public void setHandleType(Integer handleType) {
        this.handleType = handleType;
    }

    public BigDecimal getHandleNum() {
        return handleNum;
    }

    public void setHandleNum(BigDecimal handleNum) {
        this.handleNum = handleNum;
    }

    public String getApplyRemark() {
        return applyRemark;
    }

    public void setApplyRemark(String applyRemark) {
        this.applyRemark = applyRemark;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getHandleRemark() {
        return handleRemark;
    }

    public void setHandleRemark(String handleRemark) {
        this.handleRemark = handleRemark;
    }

    public LocalDateTime getUpdatetime() {
        return updatetime;
    }

    public void setUpdatetime(LocalDateTime updatetime) {
        this.updatetime = updatetime;
    }

    public String getAuditer() {
        return auditer;
    }

    public void setAuditer(String auditer) {
        this.auditer = auditer;
    }

    public String getAuditeRemark() {
        return auditeRemark;
    }

    public void setAuditeRemark(String auditeRemark) {
        this.auditeRemark = auditeRemark;
    }

    public LocalDateTime getAuditetime() {
        return auditetime;
    }

    public void setAuditetime(LocalDateTime auditetime) {
        this.auditetime = auditetime;
    }

    public String getApplyer() {
        return applyer;
    }

    public void setApplyer(String applyer) {
        this.applyer = applyer;
    }

    public LocalDateTime getHandletime() {
        return handletime;
    }

    public void setHandletime(LocalDateTime handletime) {
        this.handletime = handletime;
    }

    public String getHandler() {
        return handler;
    }

    public void setHandler(String handler) {
        this.handler = handler;
    }

    public BigDecimal getRecoveryNum() {
        return recoveryNum;
    }

    public void setRecoveryNum(BigDecimal recoveryNum) {
        this.recoveryNum = recoveryNum;
    }

    public String getExtraRemark() {
        return extraRemark;
    }

    public void setExtraRemark(String extraRemark) {
        this.extraRemark = extraRemark;
    }
}
