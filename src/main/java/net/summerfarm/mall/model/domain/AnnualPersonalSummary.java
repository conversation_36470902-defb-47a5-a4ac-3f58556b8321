package net.summerfarm.mall.model.domain;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> ct
 * create at:  2019/12/27  14:29
 * 年度总结
 */
@Data
public class AnnualPersonalSummary {

    private Integer id;
    /**
    * 用户id
    */
    private Long mId;
    /**
    * 用户名称
    */
    private String mName;
    /**
    * 采购总金额
    */
    private BigDecimal consumeAmount;
    /**
    * 下单总数
    */
    private Integer orderNumber;
    /**
    * 城市编号
    */
    private Integer areaNo;
    /**
     * 城市名称
     */
    private String areaName;
    /**
    * 订单金额占总金额比例
    */
    private String orderRate;
    /**
    * 优惠金额
    */
    private BigDecimal preferentialAmount;
    /**
    * 订单最大金额
    */
    private BigDecimal maxOrderAmount;
    /**
    * 金额最大订单下单时间
    */
    private Date maxOrderTime;
    /**
    * 最早下单时间
    */
    private Date earliestOrderTime;
    /**
    * 最晚下单时间
    */
    private Date latestOrderTime;
    /**
    * 会员等级
    */
    private Integer grade;
    /**
    * 超时下单次数
    */
    private Integer overTimeOrder;
    /**
    * 极速售后总金额
    */
    private BigDecimal speedAfterOrderAmount;
    /**
    * 购买类目总数
    */
    private Integer categoryNumber;
    /**
    * 类目名称 多个类目","隔开
    */
    private String categoryName;
    /**
    * 类目占比
    */
    private String categoryRate;
    /**
    * 购买最多的sku名称
    */
    private String likePdName;
    /**
    * 购买最多sku的总数量
    */
    private Integer  likeSkuAmount;
    /**
     * 购买最多的sku单位
     */
    private String  likeSkuUnit;
    /**
    * 所在市
    */
    private String city;
    /**
    * 所在区
    */
    private String area;

}
