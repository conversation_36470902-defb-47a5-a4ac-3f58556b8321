package net.summerfarm.mall.model.domain;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class Payment {
    private Long paymentId;

    private String payType;

    private String orderNo;

    private String transactionNumber;

    private BigDecimal money;

    private Date endTime;

    private Date onlinePayEndTime;

    private String tradeType;

    private String bankType;

    private Integer status;

    private String errCode;

    private String errCodeDes;

    private Integer companyAccountId;

    private String scanCode;

    /**
     * 中银支付类型：ZFBA-支付宝 WEIX-微信 UPAY-银联二维码
     */
    private String bocPayType;
}