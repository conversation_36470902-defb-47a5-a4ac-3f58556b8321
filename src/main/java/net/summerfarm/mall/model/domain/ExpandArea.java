package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * expand_area
 * <AUTHOR>
@Data
public class ExpandArea implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 拓展购买关联id
     */
    private Long expandId;

    /**
     * 城市area_no
     */
    private Integer areaNo;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}