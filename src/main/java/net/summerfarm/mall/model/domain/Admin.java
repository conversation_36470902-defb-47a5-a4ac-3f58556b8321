package net.summerfarm.mall.model.domain;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

public class Admin implements Serializable{


    private Integer adminId;


    private Date createTime;


    private Integer loginFailTimes;


    private Boolean isDisabled;


    private String username;

    private transient String password;

    private Date loginTime;

    private String realname;

    private Boolean gender;

    private String department;

    private String phone;

    private String kp;

    private Integer salerId;

    private String salerName;

    private String contract;

    private Integer operateId;

    @ApiModelProperty(value = "是否支持提前截单 0 不支持 1 支持")
    private Integer closeOrderType;

    private String contractMethod;

    @ApiModelProperty(value = "提前截单时间")
    private String closeOrderTime;

    private Integer adminSwitch;
    @ApiModelProperty(value = "客户类别 0大客户 1普通客户 2批发客户")
    private Integer adminType;
    @ApiModelProperty(value = "连锁范围 0(NKA) 1(LKA) 2(其他连锁)")
    private Integer adminChain;
    @ApiModelProperty(value = "品牌等级 0普通 1KA")
    private Integer adminGrade;
    @ApiModelProperty(value = "是否开启低价监控：t、开启 f、关闭")
    private Boolean lowPriceRemainder;

    @ApiModelProperty(value = "低价监控排除城市")
    private String notIncludedArea;
    /**
     * 品牌名称
     */
    private String nameRemakes;

    private String userId;
    private Integer mId;
    private String mName;

    /**
     * 拣货类型(是否独立拣货)
     */
    private Integer skuSorting;

    public Admin() {
    }

    public Admin(Admin admin) {
        this.adminId = admin.getAdminId();
        this.createTime = admin.getCreateTime();
        this.loginFailTimes = admin.getLoginFailTimes();
        this.isDisabled = admin.getIsDisabled();
        this.username = admin.getUsername();
        this.password = admin.getPassword();
        this.loginTime = admin.getLoginTime();
        this.realname = admin.getRealname();
        this.gender = admin.getGender();
        this.department = admin.getDepartment();
        this.phone = admin.getPhone();
    }

    public Integer getAdminType() {
        return adminType;
    }

    public void setAdminType(Integer adminType) {
        this.adminType = adminType;
    }

    public Integer getAdminChain() {
        return adminChain;
    }

    public void setAdminChain(Integer adminChain) {
        this.adminChain = adminChain;
    }

    public Integer getAdminGrade() {
        return adminGrade;
    }

    public void setAdminGrade(Integer adminGrade) {
        this.adminGrade = adminGrade;
    }

    public String getKp() {
        return kp;
    }

    public void setKp(String kp) {
        this.kp = kp;
    }

    public Integer getSalerId() {
        return salerId;
    }

    public void setSalerId(Integer salerId) {
        this.salerId = salerId;
    }

    public String getSalerName() {
        return salerName;
    }

    public void setSalerName(String salerName) {
        this.salerName = salerName;
    }

    public String getContract() {
        return contract;
    }

    public void setContract(String contract) {
        this.contract = contract;
    }

    public Boolean getDisabled() {
        return isDisabled;
    }

    public void setDisabled(Boolean disabled) {
        isDisabled = disabled;
    }

    public Integer getAdminId() {
        return adminId;
    }

    public void setAdminId(Integer adminId) {
        this.adminId = adminId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getLoginFailTimes() {
        return loginFailTimes;
    }

    public void setLoginFailTimes(Integer loginFailTimes) {
        this.loginFailTimes = loginFailTimes;
    }

    public Boolean getIsDisabled() {
        return isDisabled;
    }

    public void setIsDisabled(Boolean isDisabled) {
        this.isDisabled = isDisabled;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username == null ? null : username.trim();
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password == null ? null : password.trim();
    }

    public Date getLoginTime() {
        return loginTime;
    }

    public void setLoginTime(Date loginTime) {
        this.loginTime = loginTime;
    }

    public String getRealname() {
        return realname;
    }

    public void setRealname(String realname) {
        this.realname = realname == null ? null : realname.trim();
    }

    public Boolean getGender() {
        return gender;
    }

    public void setGender(Boolean gender) {
        this.gender = gender;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department == null ? null : department.trim();
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone == null ? null : phone.trim();
    }

    public Integer getOperateId() {
        return operateId;
    }

    public void setOperateId(Integer operateId) {
        this.operateId = operateId;
    }


    public Integer getCloseOrderType() {
        return closeOrderType;
    }

    public void setCloseOrderType(Integer closeOrderType) {
        this.closeOrderType = closeOrderType;
    }

    public String getContractMethod() {
        return contractMethod;
    }

    public void setContractMethod(String contractMethod) {
        this.contractMethod = contractMethod;
    }

    public String getCloseOrderTime() {
        return closeOrderTime;
    }

    public void setCloseOrderTime(String closeOrderTime) {
        this.closeOrderTime = closeOrderTime;
    }

    public Integer getAdminSwitch() {
        return adminSwitch;
    }

    public void setAdminSwitch(Integer adminSwitch) {
        this.adminSwitch = adminSwitch;
    }

    public String getNameRemakes() {
        return nameRemakes;
    }

    public void setNameRemakes(String nameRemakes) {
        this.nameRemakes = nameRemakes;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Integer getmId() {
        return mId;
    }

    public void setmId(Integer mId) {
        this.mId = mId;
    }

    public String getmName() {
        return mName;
    }

    public void setmName(String mName) {
        this.mName = mName;
    }
    public Integer getSkuSorting() {
        return skuSorting;
    }

    public void setSkuSorting(Integer skuSorting) {
        this.skuSorting = skuSorting;
    }

    public Boolean getLowPriceRemainder() {
        return lowPriceRemainder;
    }

    public void setLowPriceRemainder(Boolean lowPriceRemainder) {
        this.lowPriceRemainder = lowPriceRemainder;
    }

    public String getNotIncludedArea() {
        return notIncludedArea;
    }

    public void setNotIncludedArea(String notIncludedArea) {
        this.notIncludedArea = notIncludedArea;
    }
}