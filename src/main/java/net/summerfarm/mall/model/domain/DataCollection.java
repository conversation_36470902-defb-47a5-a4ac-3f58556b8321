package net.summerfarm.mall.model.domain;

import java.io.Serializable;

public class DataCollection implements Serializable{
    private Integer id;
    private String ip;
    private String uri;
    private String paramData;
    private String method;
    private String interviewTime;
    private Integer source;
    private String jsessionid;
    private Long mid;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }

    public String getParamData() {
        return paramData;
    }

    public void setParamData(String paramData) {
        this.paramData = paramData;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getInterviewTime() {
        return interviewTime;
    }

    public void setInterviewTime(String interviewTime) {
        this.interviewTime = interviewTime;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public String getJsessionid() {
        return jsessionid;
    }

    public void setJsessionid(String jsessionid) {
        this.jsessionid = jsessionid;
    }

    public Long getMid() {
        return mid;
    }

    public void setMid(Long mid) {
        this.mid = mid;
    }

    @Override
    public String toString() {
        return "DataCollection{" +
                "id=" + id +
                ", ip='" + ip + '\'' +
                ", uri='" + uri + '\'' +
                ", paramData='" + paramData + '\'' +
                ", method='" + method + '\'' +
                ", interviewTime='" + interviewTime + '\'' +
                ", source=" + source +
                ", jsessionid='" + jsessionid + '\'' +
                ", mid=" + mid +
                '}';
    }
}
