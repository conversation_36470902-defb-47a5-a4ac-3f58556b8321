package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * interest_rate_config
 * <AUTHOR>
@Data
public class InterestRateConfig implements Serializable {
    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * sku
     */
    private String sku;

    /**
     * 城市
     */
    private Integer areaNo;

    /**
     * 毛利率
     */
    private BigDecimal interestRate;

    /**
     * 自动调整标识：0、否 1、是
     */
    private Integer autoFlag;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人员
     */
    private String createAdminName;

    private static final long serialVersionUID = 1L;
}