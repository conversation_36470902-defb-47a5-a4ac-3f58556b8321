package net.summerfarm.mall.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CouponSenderRule implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 发放设置ID
     */
    private Integer couponSenderId;

    /**
     * 范围ID
     */
    private Long scopeId;

    /**
     * 范围类型，1 人群包，2 运营城市，3 运营大区
     */
    private Integer scopeType;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}