package net.summerfarm.mall.model.domain;

import cn.hutool.json.JSONUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.mall.model.vo.merchant.contact.ContactAddressRemark;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

@ApiModel(description = "联系人实体类")
public class Contact {
    @ApiModelProperty(value = "联系人id",required = true)
    private Long contactId;

    @ApiModelProperty(value = "商户id",required = true)
    private Long mId;

    @ApiModelProperty(value = "联系人名称",required = true)
    private String contact;

    @ApiModelProperty(value = "职位")
    private String position;

    @ApiModelProperty(value = "性别")
    private Boolean gender;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "微信号")
    private String weixincode;

    @ApiModelProperty(value = "省")
    private String province;

    @ApiModelProperty(value = "市")
    private String city;

    @ApiModelProperty(value = "地区")
    private String area;

    @ApiModelProperty(value = "详细地址")
    private String address;

    @ApiModelProperty(value = "状态(1正常或审核通过、2删除、3待审核、4审核不通过)",hidden = true)
    private Integer status;

    @ApiModelProperty(value = "补充说明",required = false)
    private String remark;

    @ApiModelProperty(value = "1默认地址 与merchat中一致")
    private Integer isDefault;

    @ApiModelProperty(value = "配送车辆")
    private String deliveryCar;

    private String poiNote;

    private BigDecimal distance;

    private String path;

    /**
    * 门牌号
    */
    private String houseNumber;

    /**
    * 城配仓编号
    */
    private  Integer storeNo;
    /**
     * 配送周期
     */
    private String deliveryFrequent;

    /**
     * 配送周期
     */
    private List<LocalDate> deliveryFrequentNew;

    /**
     * 是否是有效的地址
     */
    private Integer effectFlag;

    /**
     * 运费规则
     */
    @Getter
    @Setter
    private String deliveryRule;

    /**
     * 运费
     */
    @Getter
    @Setter
    private BigDecimal deliveryFee;

    /**
     * 是否支持加单 0：是  1：否
     */
    @Getter
    @Setter
    private Integer supportAddOrder;

    /**
     * 截单时间
     */
    @Getter
    @Setter
    private Long cutOffTime;

    /**
     * 地址备注
     */
    @Getter
    @Setter
    private ContactAddressRemark contactAddressRemark;
    /**
     * 地址备注
     */
    private String addressRemark;

    /**
     * 围栏状态  0：正常，3：暂停
     */
    @Getter
    @Setter
    private Integer fenceStatus;

    /**
     *预计送达时间
     */
    @Getter
    @Setter
    private LocalTime lastDeliveryTime;

    /**
     * 地址完善标记。0-未完善，1-已完善（目前仅用于下单环节）
     */
    @Getter
    @Setter
    private Integer addressCompletionFlag;

    /**
     * 城配仓履约类型，0：城配履约，1：快递履约
     */
    @Getter
    @Setter
    private Integer fulfillmentType;

    public ContactAddressRemark getContactAddressRemark() {
        return contactAddressRemark;
    }

    public void setContactAddressRemark(ContactAddressRemark contactAddressRemark) {
        this.contactAddressRemark = contactAddressRemark;
    }

    public String getAddressRemark() {
        return addressRemark;
    }

    public void setAddressRemark(String addressRemark) {
        this.addressRemark = addressRemark;
    }

    public Integer getEffectFlag() {
        return effectFlag;
    }

    public void setEffectFlag(Integer effectFlag) {
        this.effectFlag = effectFlag;
    }

    public String getDeliveryFrequent() {
        return deliveryFrequent;
    }

    public void setDeliveryFrequent(String deliveryFrequent) {
        this.deliveryFrequent = deliveryFrequent;
    }

    public String getPoiNote() {
        return poiNote;
    }

    public void setPoiNote(String poiNote) {
        this.poiNote = poiNote;
    }

    public BigDecimal getDistance() {
        return distance;
    }

    public void setDistance(BigDecimal distance) {
        this.distance = distance;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public Long getContactId() {
        return contactId;
    }

    public void setContactId(Long contactId) {
        this.contactId = contactId;
    }

    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact == null ? null : contact.trim();
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position == null ? null : position.trim();
    }

    public Boolean getGender() {
        return gender;
    }

    public void setGender(Boolean gender) {
        this.gender = gender;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone == null ? null : phone.trim();
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email == null ? null : email.trim();
    }

    public String getWeixincode() {
        return weixincode;
    }

    public void setWeixincode(String weixincode) {
        this.weixincode = weixincode == null ? null : weixincode.trim();
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Integer isDefault) {
        this.isDefault = isDefault;
    }

    public String getDeliveryCar() {
        return deliveryCar;
    }

    public void setDeliveryCar(String deliveryCar) {
        this.deliveryCar = deliveryCar;
    }

    public String getHouseNumber() {
        return houseNumber;
    }

    public void setHouseNumber(String houseNumber) {
        this.houseNumber = houseNumber;
    }

    public Integer getStoreNo() {
        return storeNo;
    }

    public void setStoreNo(Integer storeNo) {
        this.storeNo = storeNo;
    }

    public List<LocalDate> getDeliveryFrequentNew() {
        return deliveryFrequentNew;
    }

    public void setDeliveryFrequentNew(List<LocalDate> deliveryFrequentNew) {
        this.deliveryFrequentNew = deliveryFrequentNew;
    }


    public void initAddrRemark(){
        if (StringUtils.isEmpty(addressRemark)){
            return;
        }
        ContactAddressRemark contactAddressRemark = JSONUtil.toBean(addressRemark, ContactAddressRemark.class);
        setContactAddressRemark(contactAddressRemark);
    }
}