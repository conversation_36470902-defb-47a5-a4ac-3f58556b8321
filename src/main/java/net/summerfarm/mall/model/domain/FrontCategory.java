package net.summerfarm.mall.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * front_category
 * <AUTHOR>
@Data
public class FrontCategory implements Serializable {
    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * 父级id
     */
    private Integer parentId;

    /**
     * 名称
     */
    private String name;

    /**
     * 有效标识 0、有效 1、失效
     */
    private Integer outdated;

    /**
     * 图标链接
     */
    private String icon;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    private static final long serialVersionUID = 1L;
}