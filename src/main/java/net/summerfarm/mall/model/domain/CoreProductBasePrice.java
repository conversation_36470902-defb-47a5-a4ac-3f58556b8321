package net.summerfarm.mall.model.domain;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.common.util.validation.groups.Update;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 核心商品底价
 *
 * <AUTHOR>
 * @Date 2023/2/28 17:36
 */
@Data
@EqualsAndHashCode
public class CoreProductBasePrice {
    private Integer id;

    /**
     * 运营大区名称
     */
    private String largeAreaName;

    /**
     * 运营大区编号
     */
    private Integer largeAreaNo;

    /**
     * sku
     */
    private String sku;


    /**
     * 产品id
     */
    private Long pdId;

    /**
     * 产品名称
     */
    private String pdName;

    /**
     * 重量
     */
    private String weight;

    /**
     * 底价
     */
    private BigDecimal basePrice;

    /**
     * 品类拓宽底价
     */
    private BigDecimal merchantSituationCategoryBasePrice;

    /**
     * 品类拓宽红线底价
     */
    private BigDecimal merchantSituationCategoryRedLinePrice;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;
}
