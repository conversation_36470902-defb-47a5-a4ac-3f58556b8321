package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * coupon_config_detail
 * <AUTHOR>
@Data
public class CouponConfigDetail implements Serializable {
    private Integer id;

    /**
     * 优惠券id
     */
    private Integer couponId;

    /**
     * 优惠券配置id
     */
    private Integer couponConfigId;

    /**
     * 优惠券类别 1 全品类，2 水果券，3 乳制品券
     */
    private Integer name;

    /**
     * 额度
     */
    private BigDecimal money;

    /**
     * 使用阈值
     */
    private BigDecimal threshold;

    /**
     * 优惠券数量
     */
    private Integer number;

    /**
     * 有效天数
     */
    private Integer effectiveTime;

    /**
     * 添加时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 0 存在 1 失效
     */
    private Integer status;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    private String updater;

    private static final long serialVersionUID = 1L;
}