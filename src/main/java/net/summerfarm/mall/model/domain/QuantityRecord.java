package net.summerfarm.mall.model.domain;

/**
 * Created by wjd on 2018/10/26.
 */
import net.summerfarm.mall.enums.StockChangeType;

import java.time.LocalDateTime;
public class QuantityRecord {

    private Integer id;

    private String recorder;

    private LocalDateTime addtime;

    private Integer storeNo;

    private String storeName;

    private String quantityType;


    private String sku;

    private Integer oldQuantity;

    private Integer newQuantity;

    private String typeName;

    private String recordNo;

    public QuantityRecord() {
    }

    public QuantityRecord(String recorder, Integer storeNo, String storeName, String sku, Integer oldQuantity, Integer newQuantity, StockChangeType changeType, String recordNo) {
        this.recorder = recorder;
        this.addtime = LocalDateTime.now();
        this.storeNo = storeNo;
        this.storeName = storeName;
        this.sku = sku;
        this.oldQuantity = oldQuantity;
        this.newQuantity = newQuantity;
        this.typeName = changeType.getTypeName();
        this.recordNo=recordNo;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getRecorder() {
        return recorder;
    }

    public void setRecorder(String recorder) {
        this.recorder = recorder;
    }

    public LocalDateTime getAddtime() {
        return addtime;
    }

    public void setAddtime(LocalDateTime addtime) {
        this.addtime = addtime;
    }

    public Integer getStoreNo() {
        return storeNo;
    }

    public void setStoreNo(Integer storeNo) {
        this.storeNo = storeNo;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getOldQuantity() {
        return oldQuantity;
    }

    public void setOldQuantity(Integer oldQuantity) {
        this.oldQuantity = oldQuantity;
    }

    public Integer getNewQuantity() {
        return newQuantity;
    }

    public void setNewQuantity(Integer newQuantity) {
        this.newQuantity = newQuantity;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getQuantityType() {
        return quantityType;
    }

    public void setQuantityType(String quantityType) {
        this.quantityType = quantityType;
    }

    public String getRecordNo() {
        return recordNo;
    }

    public void setRecordNo(String recordNo) {
        this.recordNo = recordNo;
    }
}

