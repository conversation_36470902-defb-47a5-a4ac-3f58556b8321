package net.summerfarm.mall.model.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class StockTask implements Serializable {
    private Integer id;

    private String taskNo;

    private Integer areaNo;

    /**
     * 仓库名称
     */
    private String areaName;

    private Integer type;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expectTime;

    @ApiModelProperty(value = "出入库进展: 0待入(出)库 1部分入(出)库 2已入(出)库")
    private Integer state;

    private Integer adminId;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime addtime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatetime;

    @ApiModelProperty(value = "出库仓")
    private Integer outStoreNo;

    @ApiModelProperty(value = "出库性质,0普通 1越库")
    private Integer outType;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "盘点维度：0、SKU 1、类目 2、批次 3、货位,\n" +
            "                  转换维度：0、库存转换 1、降级转换")
    private Integer dimension;

    @ApiModelProperty(value = "入库进度:0.全未入库1.部分入库2.完全入库")
    private Integer processState;

    private String mismatchReason;

    //任务类型 1、退货 2、拒收 3、拦截
    private Integer taskType;

    //类目名称 鲜果/非鲜果
    private String category;

    @ApiModelProperty(value = "关闭原因")
    private String closeReason;

    @ApiModelProperty(value = "最后修改人admin_id")
    private String updater;

    /**
     * 标志位
     */
    private Long optionFlag;

    /** 租户ID **/
    private Long tenantId;

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }
}
