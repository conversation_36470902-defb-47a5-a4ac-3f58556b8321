package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * prepay_inventory
 * <AUTHOR>
@Data
public class PrepayInventory implements Serializable {
    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * 大客户账号id
     */
    private Integer adminId;

    /**
     * sku
     */
    private String sku;

    /**
     * 预付总额
     */
    private BigDecimal prepayPrice;

    /**
     * 预付总量
     */
    private Integer prepayAmount;

    /**
     * 已用总量
     */
    private Integer usedAmount;

    /**
     * 可用账户类型：0、全部 1、账期门店 2、现结门店
     */
    private Integer usableType;

    /**
     * 支付类型：0、全额支付 1、订金尾款支付
     */
    private Integer payType;

    /**
     * 配送周期
     */
    private Integer deliveryCycle;

    /**
     * 状态：0、待审核 1、审核失败 2、待生效 3、生效中 4、已完成 5、已失效
     */
    private Integer status;

    /**
     * 生效时间
     */
    private LocalDateTime effectTime;

    /**
     * 生效时间
     */
    private LocalDateTime loseEffectTime;

    /**
     * 审核人
     */
    private String auditor;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    private static final long serialVersionUID = 1L;
}