package net.summerfarm.mall.model.domain;

import lombok.Data;
import net.summerfarm.mall.model.input.AOLOrderInput;

import java.time.LocalDate;

/**
 * <AUTHOR> ct
 * create at:  2020/9/22  11:50
 */
@Data
public class OrderOuterInfo {
    /**
     * AOL的门店id
     */
    private String mId;

    /**
     * AOL订单编号
     */
    private String orderNo;

    /**
     * 配送日期
     */
    private LocalDate deliveryDate;

    /**
     * 联系方式
     */
    private String mphone;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 备注
     */
    private String remark;

    /**
    * sku
    */
    private String sku;

    /**
    * 数量
    */
    private Integer amount;

    /**
    * 鲜沐订单号
    */
    private String xmOrderNo;

    private Long contactId;

    private Integer status;

    /**
     * 外部平台id
     */
    private Integer outerPlatformId;

    public  OrderOuterInfo(){

    }

    public OrderOuterInfo(AOLOrderInput aolOrderInput){
        this.mId = aolOrderInput.getMId();
        this.orderNo = aolOrderInput.getOrderNo();
        this.deliveryDate = aolOrderInput.getDeliveryDate();
        this.mphone = aolOrderInput.getMphone();
        this.province = aolOrderInput.getProvince();
        this.city = aolOrderInput.getCity();
        this.area = aolOrderInput.getArea();
        this.address = aolOrderInput.getAddress();
        this.remark = aolOrderInput.getRemark();
    }

}
