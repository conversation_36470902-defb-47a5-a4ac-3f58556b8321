package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class MerchantVisit implements Serializable {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 商家id
     */
    private Long mId;

    /**
     * pv统计
     */
    private Integer count;

    /**
     * pv类型,0 换购蒙层
     */
    private Integer type;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}