package net.summerfarm.mall.model.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * mch_enterprise_information_management
 * <AUTHOR>
@Data
public class MchEnterpriseInformationManagement implements Serializable {
    /**
     * 自增长主键
     */
    private Long id;

    /**
     * 取自merchant表中m_id
     */
    private Long mId;

    /**
     * 企业工商名称
     */
    private String invoiceTitle;

    /**
     * 统一社会信用代码
     */
    private String taxNumber;

    /**
     * 0:生效中（默认), 1:(失效)
     */
    private Integer validStatus;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 联系方式
     */
    private String linkMethod;

    /**
     * 法人
     */
    private String legalPersonName;

    /**
     * 验证 0:不需审核, 1:需要审核
     */
    private Integer verification;

    /**
     * 营业执照地址
     */
    private String businessLicenseAddress;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 修改人
     */
    private String updater;

    private static final long serialVersionUID = 1L;
}