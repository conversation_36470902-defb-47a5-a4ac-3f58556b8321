package net.summerfarm.mall.model.domain;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class PriceStrategy {

    private Integer id;

    private Long businessId;

    private Integer type;

    private Integer adjustType;

    private BigDecimal amount;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     *更新人
     */
    private Integer updater;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 生效的价格（为空时根据策略实时计算）
     */
    private BigDecimal effectivePrice;

    /**
     * 小数处理逻辑：0、四舍五入保留两位小数 1、向上取整
     */
    private Integer roundingMode;
}
