package net.summerfarm.mall.model.domain;

import java.util.Date;

public class Action {
    private Integer id;

    private String actionName;      //名称

    private String instruction;     //介绍

    private Integer onWatch;        //是否启用，1启用，0禁用

    private Date updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getActionName() {
        return actionName;
    }

    public void setActionName(String actionName) {
        this.actionName = actionName == null ? null : actionName.trim();
    }

    public String getInstruction() {
        return instruction;
    }

    public void setInstruction(String instruction) {
        this.instruction = instruction == null ? null : instruction.trim();
    }

    public Integer getOnWatch() {
        return onWatch;
    }

    public void setOnWatch(Integer onWatch) {
        this.onWatch = onWatch;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}