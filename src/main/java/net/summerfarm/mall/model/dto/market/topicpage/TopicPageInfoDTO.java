package net.summerfarm.mall.model.dto.market.topicpage;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * @author: <EMAIL>
 * @create: 2023/6/27
 */
@Data
public class TopicPageInfoDTO implements Serializable {

    /**
     * 专题页id
     */
    private Long id;

    /**
     * 专题名称
     */
    private String name;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 发布状态，0 草稿，1 已发布，2 已删除
     */
    private Integer publishStatus;

    /**
     * 背景颜色
     */
    private String backgroundColor;

    /**
     * 悬浮按钮位置，0 无， 1 首页， 2 购物车(可多选);多个值逗号分割
     */
    private String buttonType;

    /**
     * 是否允许页面分享，0 不允许，1 允许
     */
    private Integer pageShare;

    /**
     * 分享图片链接
     */
    private String sharePicture;

    /**
     * 分享标题
     */
    private String shareTitle;

    /**
     * 分享描述
     */
    private String shareDesc;

}
