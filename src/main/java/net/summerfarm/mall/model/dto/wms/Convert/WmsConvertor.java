package net.summerfarm.mall.model.dto.wms.Convert;

import net.summerfarm.mall.model.dto.wms.SkuBatchCodeTraceBatchDTO;
import net.summerfarm.wms.skucodetrace.resp.SkuBatchCodeTraceResp;

/**
 * @description:
 * @author: George
 * @date: 2024-08-15
 **/
public class WmsConvertor {

    public static SkuBatchCodeTraceBatchDTO convertSkuBatchCodeTraceBatchDTO(SkuBatchCodeTraceResp skuBatchCodeTraceResp) {
        if (skuBatchCodeTraceResp == null) {
            return null;
        }
        SkuBatchCodeTraceBatchDTO skuBatchCodeTraceBatchDTO = new SkuBatchCodeTraceBatchDTO();
        skuBatchCodeTraceBatchDTO.setSkuBatchTraceCode(skuBatchCodeTraceResp.getSkuBatchTraceCode());
        skuBatchCodeTraceBatchDTO.setOrderNo(skuBatchCodeTraceResp.getOrderNo());
        skuBatchCodeTraceBatchDTO.setWeight(skuBatchCodeTraceResp.getWeight());
        skuBatchCodeTraceBatchDTO.setSku(skuBatchCodeTraceResp.getSku());
        return skuBatchCodeTraceBatchDTO;
    }
}
