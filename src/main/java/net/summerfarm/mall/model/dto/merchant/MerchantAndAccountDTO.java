package net.summerfarm.mall.model.dto.merchant;

import cn.hutool.core.util.ObjectUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.summerfarm.mall.model.domain.Merchant;
import net.xianmu.usercenter.client.merchant.enums.MerchantStoreEnums;

import static net.xianmu.usercenter.client.merchant.enums.RegionalOrganizationEnums.Size.ADMIN;
import static net.xianmu.usercenter.client.merchant.enums.RegionalOrganizationEnums.Size.MERCHANT;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/11/30 15:29
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MerchantAndAccountDTO extends Merchant {

    /**
     * 账户id
     */
    private Long accountId;




}
