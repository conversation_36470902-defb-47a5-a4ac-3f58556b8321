package net.summerfarm.mall.model.dto.merchant;

import lombok.Data;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2025-01-18
 **/
@Data
public class MerchantRetailStoreInfoDTO {

    /**
     * 手机号
     * 示例值：18706***960
     */
    private String mobilePhone;

    /**
     * 一级门店类型
     */
    private String retailType;

    /**
     * 二级门店类型
     */
    private String subRetailType;

    /**
     * 门店地址
     */
    private String retailAddress;

    /**
     * 门店名称
     */
    private String retailName;

    /**
     * 营业执照注册号
     */
    private String identification;

    /**
     * 企业名称
     */
    private String principal;

    /**
     * 法人名称
     */
    private String legalPersonName;

    /**
     * 门店负责人openid
     */
    private String openId;

    /**
     * 认证状态
     * 1：已完成认证
     */
    private String status;

    /**
     * 认证完成那一刻的时间戳
     */
    private String authTime;

    /**
     * 授权给小程序那一刻的时间戳
     */
    private String grantTime;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;
}
