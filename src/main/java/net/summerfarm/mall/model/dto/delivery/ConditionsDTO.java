package net.summerfarm.mall.model.dto.delivery;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @project marketing-center
 * @description
 * @date 2023/9/27 15:29:33
 */
@Data
public class ConditionsDTO implements Serializable {

    private static final long serialVersionUID = -8968169351859791437L;

    /**
     * 商品类目  1：全部商品 2：乳制品商品  3：非乳制品商品
     */
    private Integer productType;

    /**
     * 门槛类型 1：金额  2：件数
     */
    private Integer sillType;

    /**
     * 件数  针对门槛类型为件数
     */
    private Integer number;

    /**
     * 金额 针对门槛类型为金额
     */
    private BigDecimal amount;
}
