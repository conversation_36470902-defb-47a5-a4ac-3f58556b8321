package net.summerfarm.mall.model.dto.delivery;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/9/9 16:31
 * @PackageName:net.summerfarm.mall.model.dto.delivery
 * @ClassName: FreeDeliveryDifferenceDTO
 * @Description: TODO
 * @Version 1.0
 */
@Data
public class FreeDeliveryDifferenceDTO implements Serializable {

    private static final long serialVersionUID = -1L;

    /**
     * 货品类目  1：全部 2：乳制品 3：非乳制品
     */
    private Integer categoryType;

    /**
     * 当前阶段值
     */
    private String stepValue;

    /**
     * 当前阶段配送费
     */
    private BigDecimal deliveryFee;

    /**
     * 差额
     */
    private BigDecimal currentDifference;
}
