package net.summerfarm.mall.model.dto.market.topicpage;

import java.io.Serializable;
import lombok.Data;

/**
 * @author: <EMAIL>
 * @create: 2023/7/31
 */
@Data
public class TopicDetailLinkReq implements Serializable {

    /**
     * 跳转类型,0 无跳转，1 商品，2 省心送，3 抽奖，4 专题，5 频道，6 直播间
     * @see net.summerfarm.enums.BannerEnum.LinkTypeEnum
     */
    private Integer linkType;

    /**
     * 跳转id
     */
    private String linkBizId;

}
