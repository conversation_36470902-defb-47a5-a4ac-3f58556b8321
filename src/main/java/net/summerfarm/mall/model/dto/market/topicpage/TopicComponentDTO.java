package net.summerfarm.mall.model.dto.market.topicpage;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * @author: <EMAIL>
 * @create: 2023/6/27
 */
@Data
public class TopicComponentDTO implements Serializable {

    /**
     * 组件基础信息id
     */
    private Long id;

    /**
     * 专题页id
     */
    private Long pageInfoId;

    /**
     * 是否根组件，0 否，1 是
     */
    private Integer root;

    /**
     * 是否容器组件，0 内容组件，1 容器组件
     */
    private Integer componentType;

    /**
     * 楼层号（如果是子级组件，则此表示在父组件中的楼层，如tab组件中的子组件）
     */
    private Integer priority;

    /**
     * 组件标题
     */
    private String title;

    /**
     * 组件类型，0 图片，1 商品，2 优惠券，3 tab
     */
    private Integer type;

    /**
     * 组件模板样式
     */
    private Integer styleType;

    /**
     * 组件样式信息json
     */
    private String style;

    /**
     * 最终命中的组件投放信息
     */
    private TopicComponentLaunchDTO componentLaunch;

    /**
     * 所有可能的投放明细
     */
    private List<TopicComponentLaunchDTO> componentLaunchList;
}
