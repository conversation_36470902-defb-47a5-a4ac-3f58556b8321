package net.summerfarm.mall.model.dto.market.activity;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Date 2022-12-14 00:07 Copyright (c)
 * @description
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ActivityScopeDTO implements Serializable {

    /**
     * 活动范围
     */
    private Long scopeId;

    /**
     * 活动范围类型
     */
    private Integer scopeType;

}
