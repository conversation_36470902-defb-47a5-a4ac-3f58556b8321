package net.summerfarm.mall.model.dto.market.activity;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * @author: <EMAIL>
 * @create: 2022/12/15
 */
@Data
public class ActivityInfoDTO implements Serializable {

    private Long id;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 活动开始时间
     */
    private Date startTime;

    /**
     * 活动结束时间
     */
    private Date endTime;

    /**
     * 是否永久，0 否，1 是
     */
    private Integer isPermanent;

    /**
     * 活动状态，0 开启，1 暂停
     */
    private Integer status;

    /**
     * 活动类型，0 特价活动，1 临保活动，2 换购，3 拓展购买，4 秒杀，5 多人拼团，6 满减，7 满返，8 预售，9 省心送
     */
    private Integer type;

    /**
     * 活动商品配置id
     */
    private Long itemConfigId;

    /**
     * 活动范围
     */
    private Long scopeId;

    /**
     * 活动范围类型
     */
    private Integer scopeType;

    /**
     * 生效平台，0 商城，1 直播，2 商城、直播同渠道
     */
    private Integer platform;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}
