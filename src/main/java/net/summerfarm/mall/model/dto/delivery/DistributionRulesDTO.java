package net.summerfarm.mall.model.dto.delivery;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @project marketing-center
 * @description 运费规则信息
 * @date 2023/9/27 15:26:11
 */
@Data
public class DistributionRulesDTO implements Serializable {

    private static final long serialVersionUID = -8958169351859791437L;

    /**
     * 类型ID  服务区域、地址、品牌主键ID
     */
    private Long typeId;

    /**
     * 规则类型  1：门店管理  2：品牌管理  3：服务区域
     */
    private Integer type;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 运费规则明细
     */
    private List<RulesDTO> rulesDTOS;
}
