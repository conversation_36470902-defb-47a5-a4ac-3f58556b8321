package net.summerfarm.mall.model.dto.merchant;

import lombok.Data;
import lombok.EqualsAndHashCode;
import net.summerfarm.mall.model.domain.Contact;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/11/30 14:24
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ContactDTO extends Contact {


    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 门店id
     */
    private Long storeId;
}
