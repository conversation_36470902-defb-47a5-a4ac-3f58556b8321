package net.summerfarm.mall.model.dto.delivery;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/9/9 16:05
 * @PackageName:net.summerfarm.mall.model.dto.delivery
 * @ClassName: DeliveryFeeRuleDetailDTO
 * @Description: TODO
 * @Version 1.0
 */
@Data
public class DeliveryFeeRuleDetailDTO implements Serializable {

    private static final long serialVersionUID = -6958169351859791439L;

    /**
     * id
     */
    private Long id;

    /**
     * 货品类目  1：全部 2：乳制品 3：非乳制品
     */
    private Integer categoryType;

    /**
     * 计算运费的方式 1：金额 2：件数
     */
    private Integer feeMode;

    /**
     * 阶梯值
     */
    private String stepValue;

    /**
     * 配送费
     */
    private BigDecimal deliveryFee;

    /**
     * 快递费
     */
    private BigDecimal expressFee;

    /**
     * 起送费
     */
    private BigDecimal startPrice;
}
