package net.summerfarm.mall.model.dto.trolley;

import lombok.Data;

import java.io.Serializable;

@Data
public class ShoppingCartDTO implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * m_id
     */
    private Long mId;

    /**
     * 子账号id
     */
    private Long accountId;

    /**
     * 业务id-换购ID
     */
    private Long bizId;

    /**
     * sku
     */
    private String sku;

    /**
     * 搭配购上级sku
     */
    private String parentSku;

    /**
     * 商品类型：0、普通商品 1、赠品   2、换购
     */
    private Integer productType;

    /**
     * 购买数量
     */
    private Integer quantity;

    /**
     * 类目
     */
    private Integer categoryId;

    private static final long serialVersionUID = 1L;
}