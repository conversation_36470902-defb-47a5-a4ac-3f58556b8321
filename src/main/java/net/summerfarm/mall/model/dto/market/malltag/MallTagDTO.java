package net.summerfarm.mall.model.dto.market.malltag;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;
import net.summerfarm.mall.enums.market.ItemTypeEnum;
import net.summerfarm.mall.enums.market.ScopeTypeEnum;

/**
 * @author: <EMAIL>
 * @create: 2023/4/17
 */
@Data
public class MallTagDTO implements Serializable {

    private Long id;

    /**
     * 标签名称
     */
    private String name;

    /**
     * 业务枚举
     * @see net.summerfarm.mall.enums.market.MallTagEnum
     */
    private Integer bizType;

    /**
     * 标签类型 0 氛围，1 利益，2 业务
     */
    private Integer type;

    /**
     * 标签样式 0 文案，1 图片
     */
    private Integer style;

    /**
     * 标签文案
     */
    private String content;

    /**
     * 标签图片
     */
    private String picture;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 活动范围类型，0 全部，1 人群包，2 运营城市，3 运营大区
     * @see ScopeTypeEnum
     */
    private Integer scopeType;

    /**
     * 商品选择类型，0 全部，1 指定商品，2 指定类目
     * @see ItemTypeEnum
     */
    private Integer itemType;

    /**
     * 标签生效时间
     */
    private LocalDateTime startTime;

    /**
     * 标签失效时间
     */
    private LocalDateTime endTime;

    private String bizId;

    public MallTagDTO copy() {
        MallTagDTO copy = new MallTagDTO();
        copy.setId(id);
        copy.setName(name);
        copy.setType(type);
        copy.setStyle(style);
        copy.setPicture(picture);
        copy.setContent(content);
        copy.setWeight(weight);
        copy.setStartTime(startTime);
        copy.setEndTime(endTime);
        return copy;
    }

}
