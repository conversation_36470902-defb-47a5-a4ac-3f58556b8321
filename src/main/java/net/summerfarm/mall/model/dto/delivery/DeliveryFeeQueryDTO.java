package net.summerfarm.mall.model.dto.delivery;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import lombok.Data;
import net.summerfarm.mall.model.domain.Area;
import net.summerfarm.mall.model.vo.MerchantSubject;

/**
 * @author: <EMAIL>
 * @create: 2022/7/18
 */
@Data
public class DeliveryFeeQueryDTO implements Serializable {

    private Long contactId;

    private BigDecimal fruitPrice;

    private BigDecimal totalPrice;

    private LocalDate deliveryDate;

    private Boolean express = false;

    private Area area;

    private Long mId;

    private Boolean cartTiming = false;

    private List<Integer> categoryList;

    private Boolean refund = false;

    private Map<String, Integer> cardMap;

    private Integer couponId;

    private MerchantSubject merchantSubject;
}
