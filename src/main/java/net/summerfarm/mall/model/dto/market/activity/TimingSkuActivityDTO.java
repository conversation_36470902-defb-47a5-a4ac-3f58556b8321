package net.summerfarm.mall.model.dto.market.activity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class TimingSkuActivityDTO implements Serializable {

    /**
     * sku
     */
    private String sku;

    /**
     * 优惠后总价
     */
    private BigDecimal totalPriceAfterDiscount;

    /**
     * 优惠数量
     */
    private Integer discountNum;

    /**
     * 活动已购买数量
     */
    private Integer quantityPurchased;
}
