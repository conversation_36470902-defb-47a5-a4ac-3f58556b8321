package net.summerfarm.mall.model.dto.tms;

import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class DistOrderDTO implements Serializable {

    private static final long serialVersionUID = -1L;

    /**
     * 委托单ID
     */
    private Long distId;
    /**
     * 期望送达时间
     */
    private LocalDateTime expectBeginTime;
    /**
     * 实际送达时间
     */
    private LocalDateTime realArrivalTime;
    /**
     * 外部订单号
     */
    private String outOrderId;
    /**
     * 委托单状态
     * 10：待排线，11：排线中，20：待捡货，30：配送中，39：完成排线后订单拦截，40：配送完成，
     */
    private Integer status;
    /**
     * 配送批次列表
     */
    private DistDeliveryBatchDTO deliveryBatch;
    /**
     * 点位配送详情列表
     */
    private DistDeliverySiteDTO deliverySite;

}
