package net.summerfarm.mall.model.dto.market.malltag;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: <EMAIL>
 * @create: 2023/5/5
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class TagScopeDTO implements Serializable {

    /**
     * 活动范围
     */
    private Long scopeId;

    /**
     * 活动范围类型
     */
    private Integer scopeType;

}
