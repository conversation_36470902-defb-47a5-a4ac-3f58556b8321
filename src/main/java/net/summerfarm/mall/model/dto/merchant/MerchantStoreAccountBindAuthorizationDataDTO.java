package net.summerfarm.mall.model.dto.merchant;

import lombok.Data;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2025-01-13
 **/
@Data
public class MerchantStoreAccountBindAuthorizationDataDTO {

    /**
     * 该小程序是否允许使用此插件
     */
    private Boolean isEnableUsePlugin;

    /**
     * 该小程序是否已被授权
     */
    private Boolean isGrantRetailInfo;

    /**
     * 门店登记手机号
     */
    private String phone;

    /**
     * 门店名称
     */
    private String retailName;

    /**
     * 门店类型
     */
    private String storeType;

    /**
     * 门店地址
     */
    private String storeAddress;

    /**
     * 企业名称
     */
    private String businessName;

    /**
     * 营业执照注册号
     */
    private String businessId;

    /**
     * 经营者姓名
     */
    private String managerName;

    /**
     * 用于发送消息的ID
     */
    private String openid;
}
