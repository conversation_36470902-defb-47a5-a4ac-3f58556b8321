package net.summerfarm.mall.model.dto.merchant;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;
import net.summerfarm.mall.model.domain.Merchant;
import net.xianmu.usercenter.client.merchant.enums.MerchantStoreEnums;

import static net.xianmu.usercenter.client.merchant.enums.RegionalOrganizationEnums.Size.ADMIN;
import static net.xianmu.usercenter.client.merchant.enums.RegionalOrganizationEnums.Size.MERCHANT;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/11/30 15:29
 */
@Data
public class MerchantDTO extends Merchant {

    /**
     * 模拟登录标记,为1的时候代表支持模拟登录
     */
    private Integer mockLoginFlag;


    /**
     * 买家中心门店id
     */
    private Long storeId;
    /**
     * 租户id
     */
    private Long tenantId;




    public static String transSizeFromUserCenter(Integer sourceSize) {
        return ObjectUtil.equal(sourceSize, ADMIN.getCode()) ? ADMIN.getDesc() : MERCHANT.getDesc();
    }

    public static String transTypeFromUserCenter(Integer sourceType) {
        if (null == sourceType) {
            return MerchantStoreEnums.Type.UN_KNOW.getDesc();
        }
        String type;
        switch (sourceType) {
            case 3:
                type = MerchantStoreEnums.Type.PERSONAL.getDesc();
                break;
            case 2:
                type = MerchantStoreEnums.Type.MANAGED.getDesc();
                break;
            case 1:
                type = MerchantStoreEnums.Type.JOINING.getDesc();
                break;
            case 0:
                type = MerchantStoreEnums.Type.DIRECT.getDesc();
                break;
            case 4:
                type = MerchantStoreEnums.Type.CHAIN.getDesc();
                break;
            default:
                type = MerchantStoreEnums.Type.UN_KNOW.getDesc();
        }
        return type;
    }



    /**
     * 转换鲜沐门店的状态
     *
     * @return
     */
    public static Integer transStatusFromUserCenter(Integer sourceStatus) {
        if (sourceStatus == null) {
            return null;
        }
        Integer status;
        switch (sourceStatus) {
            case 1:
                status = 0;
                break;
            case 0:
                status = 1;
                break;
            case 2:
                status = 2;
                break;
            case 4:
                status = 3;
                break;
            case 5:
                status = 4;
                break;
            default:
                status = 0;
        }
        return status;
    }
}
