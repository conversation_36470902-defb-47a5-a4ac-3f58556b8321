package net.summerfarm.mall.model.dto.tms;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class DeliverySiteItemDTO implements Serializable {

    private static final long serialVersionUID = 4947971318887921951L;

    /**
     * 无码数量
     */
    private Integer noscanCount;

    /**
     * sku
     */
    private String outItemId;

    /**
     * 商品名称
     */
    private String outItemName;

    /**
     * 计划签收数量
     */
    private Integer planReceiptCount;

    /**
     * 实际签收数量
     */
    private Integer realReceiptCount;

    /**
     * 扫码数量
     */
    private Integer scanCount;

    /**
     * 缺货数量
     */
    private Integer shortCount;

    /**
     * 配送类型 0配送1回收
     */
    private Integer type;
}
