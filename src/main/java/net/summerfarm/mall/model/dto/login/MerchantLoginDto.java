package net.summerfarm.mall.model.dto.login;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @descripton
 * @date 2024/6/24 17:46
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MerchantLoginDto {

    Integer type;
    /**
     * 微信带过来的code（用来换openid）
     */
    String code;

    /**
     * 是否pop客户
     */
    private boolean popMerchant;


    /**
     * mock登录才有，手机号
     */
    String phone;

    /**
     * mock登录才有，签名
     */
    String sign;
}
