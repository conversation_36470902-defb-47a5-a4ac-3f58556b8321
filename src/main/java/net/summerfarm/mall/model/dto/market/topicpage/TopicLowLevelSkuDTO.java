package net.summerfarm.mall.model.dto.market.topicpage;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @ClassName TopicLowLevelSkuDTO
 * @Description
 * <AUTHOR>
 * @Date 11:10 2024/3/12
 * @Version 1.0
 **/
@Data
public class TopicLowLevelSkuDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * sku
     */
    private String sku;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * sku图片
     */
    private String skuPic;

    /**
     * 售卖价格
     */
    private BigDecimal salePrice;

    /**
     * 规格
     */
    private String weight;

    /**
     * 商品名称
     */
    private String pdName;

    /**
     * 商品图片
     */
    private String picturePath;
}
