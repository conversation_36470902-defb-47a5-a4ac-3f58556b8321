package net.summerfarm.mall.model.dto.delivery;

import lombok.Data;
import net.xianmu.marketing.center.client.freight.resp.DeliveryFeeRuleDetailResp;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/9/9 16:02
 * @PackageName:net.summerfarm.mall.model.dto.delivery
 * @ClassName: DeliveryFeeRuleInfoDTO
 * @Description: TODO
 * @Version 1.0
 */
@Data
public class DeliveryFeeRuleInfoDTO implements Serializable {

    private static final long serialVersionUID = -8958169351859791439L;

    /**
     * 履约时效，0：日配  1：非日配
     */
    private Integer ageing;

    /**
     * 起送金额
     */
    private BigDecimal startDeliveryAmount;

    /**
     * 运费规则详情
     */
    private List<DeliveryFeeRuleDetailDTO> deliveryFeeRuleDetailDTOS;
}
