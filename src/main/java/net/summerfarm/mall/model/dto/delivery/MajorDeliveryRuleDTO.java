package net.summerfarm.mall.model.dto.delivery;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * @author: <EMAIL>
 * @create: 2022/7/12
 */
@Data
public class MajorDeliveryRuleDTO implements Serializable {

    /**
     * 件数
     */
    private Integer number;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 类型 全部：0 代仓：1 自营：2 乳制品：3 非乳制品：4
     */
    private Integer category;

    /**
     * 金额免邮：1 件数免邮：2
     */
    private Integer type;

}
