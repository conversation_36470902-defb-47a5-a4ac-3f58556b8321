package net.summerfarm.mall.model.dto.market.exchange;

import java.io.Serializable;
import lombok.Data;
import net.summerfarm.mall.model.domain.ExchangeBaseInfo;
import net.summerfarm.mall.model.domain.ExchangeScopeConfig;

/**
 * @author: <EMAIL>
 * @create: 2022/9/19
 */
@Data
public class ExchangeBuyDTO implements Serializable {

    private ExchangeBaseInfo exchangeBaseInfo;

    private ExchangeScopeConfig exchangeScopeConfig;

}
