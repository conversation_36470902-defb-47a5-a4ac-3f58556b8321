package net.summerfarm.mall.model.dto.login;

import lombok.Data;
import net.summerfarm.mall.model.vo.MerchantSubject;

import java.util.Set;

/**
 * 登录商户缓存对象
 * <AUTHOR> href="mailto:<EMAIL>>黄棽</a>
 * @since 2021-12-03
 */
@Data
public class LoginMerchantCacheDTO {

    /**
     * 登录应用类型 1-默认(公众号) 2-微信小程序
     */
    private String loginWay;

    /**
     * 小程序openID
     */
    private String mpOpenId;

    /**
     * 微信openID
     */
    private String openId;

    /**
     * 微信unionID
     */
    private String unionid;

    /**
     * 当前登录的商户信息
     */
    private MerchantSubject merchantSubject;

    /**
     * 当前登录商户id
     */
    private Long merchantId;

    /**
     * 当前登录商户帐号id
     */
    private Long accountId;

    /**
     * 商户手机号
     */
    private String phone;

    /**
     * 短信验证码
     */
    private String code;

    /**
     * AB实验标记信息
     */
    private Set<AbExpDTO> abExpDTOSet;
}
