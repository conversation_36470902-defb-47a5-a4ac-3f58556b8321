package net.summerfarm.mall.model.dto.merchant;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: George
 * @date: 2025-01-14
 **/
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class MerchantStoreAccountAuthorizationDTO {

    /**
     * @see net.summerfarm.mall.enums.MerchantStoreAccountAuthorizationStatusEnum
     * -1、空 0、预录入 1、取消授权 2、已授权
     */
    private Integer status;

    /**
     * v1-无优惠券流程
     * v2-有优惠券流程
     */
    private String version;

    /**
     * true-已展示 false-未展示
     */
    private Boolean displayed;

    /**
     * 优惠券文案
     */
    private String couponText;

}
