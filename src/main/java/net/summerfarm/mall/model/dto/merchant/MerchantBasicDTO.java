package net.summerfarm.mall.model.dto.merchant;

import java.io.Serializable;
import java.util.Optional;

import lombok.Data;

/**
 * @author: <EMAIL>
 * @create: 2023/5/10
 */
@Data
public class MerchantBasicDTO implements Serializable {

    /**
     * 运营服务区域
     */
    private Integer areaNo;

    /**
     * mId
     */
    private Long mId;

    /**
     * 用户子账号id
     */
    private Integer adminId;

    /**
     * 是否大客户
     */
    private boolean isMajor;

    public void setIsMajor(Boolean isMajor){
        this.isMajor = Optional.ofNullable(isMajor).orElse(false);
    }

    public boolean getIsMajor(){
        return this.isMajor;
    }
}
