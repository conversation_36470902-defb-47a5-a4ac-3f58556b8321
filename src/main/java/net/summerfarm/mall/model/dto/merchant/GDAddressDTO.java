package net.summerfarm.mall.model.dto.merchant;

public class GDAddressDTO {
    private String formatted_address;
    private String country;
    private String province;
    private String citycode;
    private String city;
    private String district;
    private String adcode;
    private String location;
    private String level;
    public void setFormatted_address(String formatted_address) {
        this.formatted_address = formatted_address;
    }
    public String getFormatted_address() {
        return formatted_address;
    }

    public void setCountry(String country) {
        this.country = country;
    }
    public String getCountry() {
        return country;
    }

    public void setProvince(String province) {
        this.province = province;
    }
    public String getProvince() {
        return province;
    }

    public void setCitycode(String citycode) {
        this.citycode = citycode;
    }
    public String getCitycode() {
        return citycode;
    }

    public void setCity(String city) {
        this.city = city;
    }
    public String getCity() {
        return city;
    }

    public void setDistrict(String district) {
        this.district = district;
    }
    public String getDistrict() {
        return district;
    }

    public void setAdcode(String adcode) {
        this.adcode = adcode;
    }
    public String getAdcode() {
        return adcode;
    }

    public void setLocation(String location) {
        this.location = location;
    }
    public String getLocation() {
        return location;
    }

    public void setLevel(String level) {
        this.level = level;
    }
    public String getLevel() {
        return level;
    }

}
