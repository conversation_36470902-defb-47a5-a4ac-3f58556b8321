package net.summerfarm.mall.model.dto.product;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class CommonlyRecommendedQueryDTO {

    /**
     * 页码
     */
    @NotNull(message = "参数错误")
    @Min(value = 1, message = "参数错误")
    private Integer pageIndex;

    /**
     * 每页记录数
     */
    @NotNull(message = "参数错误")
    @Min(value = 1, message = "参数错误")
    private Integer pageSize;
}
