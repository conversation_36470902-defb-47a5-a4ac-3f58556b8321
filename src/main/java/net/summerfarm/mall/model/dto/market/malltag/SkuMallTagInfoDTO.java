package net.summerfarm.mall.model.dto.market.malltag;

import lombok.Data;
import net.summerfarm.mall.enums.market.MallTagEnum;
import net.summerfarm.mall.model.domain.Coupon;
import net.summerfarm.mall.model.domain.MarketRule;
import net.summerfarm.mall.model.dto.market.exchange.ExchangeBuyDTO;
import net.summerfarm.mall.model.vo.DiscountCardToMerchantVO;
import net.summerfarm.mall.model.vo.LadderPriceVO;
import net.summerfarm.mall.model.vo.TimingRuleVO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @author: <EMAIL>
 * @create: 2023/4/17
 */
@Data
public class SkuMallTagInfoDTO implements Serializable {

    /**
     * sku
     */
    private String sku;

    /**
     * 标签集合
     */
    private List<MallTagDTO> mallTags;

    /**
     * 业务标签集合
     */
    private List<MallTagDTO> businessTags;

    /**
     * 利益标签集合
     */
    private List<MallTagDTO> profitTags;

    /**
     * 氛围标签集合
     */
    private List<MallTagDTO> atmosphereTags;

    /**
     * 需要保留的标签枚举
     */
    private List<MallTagEnum> remainTagEnums;

    /**
     * 阶梯价
     */
    @Deprecated
    private List<LadderPriceVO> ladderPrices;

    /**
     * 满减规则
     */
    private List<MarketRule> marketRuleList;

    /**
     * 省心送信息
     */
    private TimingRuleVO timingRule;

    /**
     * 特价
     */
    private BigDecimal activityPrice;

    /**
     * 特价渠道
     */
    private Integer platform;
//
//    /**
//     * 省心送sku是否支持特价活动，true 是， false 否
//     */
//    private Integer isSupportTiming;

    /**
     * sku类型
     */
    private Integer extType;

    /**
     * 所有非发放的优惠券(包含未领取的),排除仅省心送可用券
     */
    private List<Coupon> couponList;

    /**
     * 省心送可用券
     */
    private List<Coupon> timingCouponList;

    /**
     * 权益卡
     */
    private List<DiscountCardToMerchantVO> discountCard;

    /**
     * 换购活动信息
     */
    private ExchangeBuyDTO exchangeBuyDTO;

    /**
     * 折扣率标签  0：不展示  1：展示  默认为不展示
     */
    private Integer discountLabel;

    /**
     * 活动阶梯价
     */
    private List<LadderPriceVO> activityLadderPrices;
}
