package net.summerfarm.mall.model.dto.order;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.mall.model.domain.ActivitySkuPurchaseQuantity;
import net.summerfarm.mall.model.domain.ExpandActivityQuantity;
import net.summerfarm.mall.model.domain.OrderItem;
import net.summerfarm.mall.model.domain.OrderItemPreferential;
import net.summerfarm.mall.model.vo.price.SkuPreferentialVO;
import net.xianmu.common.exception.BizException;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @Description 订单项计算实体
 */
@Data
public class OrderItemCalcDTO extends OrderItem {
    /**
     * 订单项原价总计
     */
    private BigDecimal actualTotalOriginalPrice;

    /**
     * 订单项参与免邮金额
     */
    private BigDecimal calcPartDeliveryFee;

    /**
     * sku类型：0、自营 1、代仓
     */
    private Integer skuType;

    /**
     * 商品二级性质，1 自营-代销不入仓、2 自营-代销入仓、3 自营-经销、4 代仓-代仓
     */
    @Getter
    @Setter
    private Integer subType;

    /**
     * 是否预付
     */
    private Boolean isPrePay;

    /**
     * 代仓支付方式：0、无需支付 1、下单时支付
     */
    private Integer agentPayMethod;

    /**
     * 类目类型
     */
    private Integer categoryType;

    /**
     * 搭配购主商品
     */
    private String parentSku;

    /**
     * 搭配购item id
     */
    private Integer suitItemId;

    /**
     * 售卖规格
     */
    private Integer baseSaleUnit;

    /**
     * 最小起售量
     */
    private Integer baseSaleQuantity;

    /**
     * 可以预付数量，为null不可预付
     */
    private Integer prepayUsableQuantity;

    /**
     * 预付定价
     */
    private BigDecimal prepaySetPrice;

    /**
     * 阶梯价
     */
    private String ladderPrice;

    /**
     * 是否需要处理库存
     */
    private Boolean notNeedStock;

    /**
     * 供应商skuId
     */
    private Long supplySkuId;

    /**
     * pdId
     */
    private Long pdId;

    /**
     * 日配 0
     */
    private Integer isEveryDayFlag;

    /**
     * 订单项具体优惠list
     */
    List<OrderItemPreferential> itemPreferentialList;

    /**
     * 到手价明细展示信息
     */
    List<SkuPreferentialVO> skuPreferentialVOS;

    /**
     * 活动优惠用户购买数量记录->用于活动库存和限购数量
     */
    private ActivitySkuPurchaseQuantity activitySkuPurchaseQuantity;

    /**
     * 活动优惠用户购买数量记录->用于限购数量
     */
    private ExpandActivityQuantity expandActivityQuantity;

    private transient BigDecimal unCouponSinglePrice;

    /**
     * 控价品是否隐藏实付价 2-隐藏  1-或者空不隐藏
     */
    private Integer priceHide;

    /**
     * 买手id（xianmudb.pop_buyer表id）
     */
    private Long buyerId;

    /**
     * 买手所对应的admin_id(admin表id)
     */
    private Long buyerAdminId;

    /**
     * 买手名称(花名)
     */
    private String buyerName;

    /**
     * 不同阶梯对应的活动价格
     */
    private BigDecimal activityPrice;

    /**
     * 核心低价品保底价
     */
    private BigDecimal floorPrice;

    /**
     * 扣减实付总价
     *
     * @param decrease 扣减额度
     */
    public void decreaseActualTotalPrice(BigDecimal decrease) {
        if (this.getActualTotalPrice() == null) {
            throw new BizException("实付总价未初始化");
        }

        if (this.getActualTotalPrice().compareTo(decrease) < 0) {
            throw new DefaultServiceException(1, "实付总价不可低于0元");
        }
        this.setActualTotalPrice(this.getActualTotalPrice().subtract(decrease));
    }

    /**
     * 扣减免邮门槛
     *
     * @param decrease 扣减额度
     */
    public void decreaseCalcPartDeliveryFee(BigDecimal decrease) {
        if (this.calcPartDeliveryFee == null) {
            throw new BizException("免邮门槛未初始化");
        }

        if (this.calcPartDeliveryFee.compareTo(decrease) < 0) {
            throw new DefaultServiceException(1, "运费计算错误");
        }
        this.calcPartDeliveryFee = this.calcPartDeliveryFee.subtract(decrease);
    }

    /**
     * 添加优惠信息
     *
     * @param supplier 优惠
     */
    public void addItemPreferential(Supplier<OrderItemPreferential> supplier) {
        if (itemPreferentialList == null) {
            itemPreferentialList = new ArrayList<>();
        }
        itemPreferentialList.add(supplier.get());
    }

    /**
     * 到手价优惠明细
     *
     * @param supplier 优惠
     */
    public void addSkuPreferential(Supplier<SkuPreferentialVO> supplier) {
        if (skuPreferentialVOS == null) {
            skuPreferentialVOS = new ArrayList<>();
        }
        skuPreferentialVOS.add(supplier.get());
    }

}
