package net.summerfarm.mall.model.dto.order;

import lombok.Data;
import net.summerfarm.mall.model.vo.EvaluationDetailVO;
import net.summerfarm.mall.model.vo.OrderItemVO;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class DeliveryEvaluationDTO {

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 配送计划id
     */
    private Integer deliveryPlanId;

    /**
     * 评价内容
     */
    private List<EvaluationDetailVO> evaluationDetailList;

    /**
     * 评价人
     */
    private String operator;

    /**
     * 评价人电话
     */
    private String operatorPhone;

    private Long operatorAccountId;

    /**
     * 配送评价状态0 -未评价 1-已评价
     */
    private Integer deliveryEvaluationStatus;

    /**
     * 订单内容
     */
    private List<OrderItemVO> orderItems;

    /**
     * 下单时间
     */
    private Date orderTime;

    /**
     * 订单类型
     */
    private Integer type;

    /**
     * 订单状态
     */
    private Short status;

    /**
     * 订单运费
     */
    private BigDecimal deliveryFee;

    /**
     * 订单实付
     */
    private BigDecimal totalPrice;

}
