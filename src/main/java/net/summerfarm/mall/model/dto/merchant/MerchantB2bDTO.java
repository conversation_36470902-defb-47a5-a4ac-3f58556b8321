package net.summerfarm.mall.model.dto.merchant;

import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.mall.enums.B2BEnum;

import java.io.Serializable;

/**
 * 小程序B2b
 */
@Data
@NoArgsConstructor
public class MerchantB2bDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 店铺id
     */
    private Long mId;
    /**
     * 账号id
     */
    private Long accountId;
    /**
     * openId
     */
    private String openId;

    /**
     * 0预录入 1取消授权 2录入完成
     */
    private Integer b2bStatus;

    /**
     * 发生优化卷状态 店铺直发一次
     */
    private Integer couponStatus;

    /**
     * 弹窗状态 0未弹出 1已弹出
     */
    private Integer popupStatus;

    /**
     * 0未弹出 1已弹出
     */
    private Integer coverStatus;
    /**
     * true 灰度 false不是灰度
     */
    private Boolean carry;


    public  static MerchantB2bDTO initMerchantB2bDTO(Long mId,Long accountId){
        MerchantB2bDTO merchantB2bDTO = new MerchantB2bDTO();
        merchantB2bDTO.setMId(mId);
        merchantB2bDTO.setCouponStatus(B2BEnum.CouponStatus.END.ordinal());
        merchantB2bDTO.setPopupStatus(B2BEnum.PopupStatus.END.ordinal());
        merchantB2bDTO.setB2bStatus(B2BEnum.B2BStatus.END.ordinal());
        merchantB2bDTO.setCoverStatus(B2BEnum.CoverStatus.END.ordinal());
        merchantB2bDTO.setAccountId(accountId);
        merchantB2bDTO.setCarry(false);
        return merchantB2bDTO;

    }

}
