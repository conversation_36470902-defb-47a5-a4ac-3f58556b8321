package net.summerfarm.mall.model.dto.order;

import lombok.Data;

/**
 * @ClassName OrderItemInfoDTO
 * @Description  订单商品快照信息
 * <AUTHOR>
 * @Date 16:07 2024/4/29
 * @Version 1.0
 **/
@Data
public class OrderItemInfoDTO {

    /**
     * 商品标签快照
     */
    private String itemLabel;

    /**
     * 商品有效期快照
     */
    private String validity;

    /**
     * 控价品是否隐藏实付价 2-隐藏  1-或者空不隐藏
     */
    private Integer priceHide;
}
