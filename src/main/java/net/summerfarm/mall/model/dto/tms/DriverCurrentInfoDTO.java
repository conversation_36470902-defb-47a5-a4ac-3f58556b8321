package net.summerfarm.mall.model.dto.tms;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class DriverCurrentInfoDTO implements Serializable {

    private static final long serialVersionUID = -6574150548428778039L;

    /**
     * 上一次配送点位
     */
    private String lastPoi;

    /**
     * 上一次更新定位时间
     */
    private LocalDateTime lastPositionTime;

    /**
     * 路线剩余待配送客户数
     */
    private Integer unFinishCount;

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /**
     * 所在路线
     */
    private String path;

    /**
     * 配送时间
     */
    private LocalDate deliveryTime;

    /**
     * 店铺位置
     */
    private String poiNote;

    /**
     * 上一个点位的配送省
     */
    private String lastProvince;
    /**
     * 上一个点位的配送城市
     */
    private String lastCity;
    /**
     * 上一个点位的配送区域
     */
    private String lastArea;
    /**
     * 上一个点位的配送地址
     */
    private String lastAddress;

    /**
     * 城配仓的省
     */
    private String storeProvince;
    /**
     * 城配仓的城市
     */
    private String storeCity;
    /**
     * 城配仓的区域
     */
    private String storeArea;
    /**
     * 城配仓的详细地址
     */
    private String storeAddress;
}
