package net.summerfarm.mall.model.dto.merchant;

import cn.hutool.json.JSONUtil;
import lombok.Data;
import net.summerfarm.mall.enums.B2BEnum;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

@Data
public class MerchantB2bRespDTO {
    private Integer errcode;
    private String errmsg;
    private List<MerchantB2bResp> info;

    public Integer getStatus(){
        if (CollectionUtils.isEmpty(getInfo())){
            return 0;
        }
        return Objects.equals(info.get(0).getStatus(), B2BEnum.B2BStatus.CLOSE.ordinal()) ?
                B2BEnum.B2BStatus.END.ordinal() : B2BEnum.B2BStatus.CLOSE.ordinal();
    }

    public String getInfoJson(){
        return JSONUtil.toJsonStr(info);
    }


}
@Data
class MerchantB2bResp{
    private String mobile_phone;
    private String retail_type;
    private String sub_retail_type;
    private String retail_address;
    private String retail_name;
    private String identification;
    private String principal;
    private String legal_person_name;
    private String openid;
    private Integer status;
    private Integer auth_mode;
    private Long auth_time;
    private Long grant_time;
 }