package net.summerfarm.mall.model.dto.order;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class OrderDeliveryDTO implements Serializable {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 配送地址id
     */
    private Long contactId;

    /**
     * 下单时间
     */
    private Date orderTime;

    /**
     * 订单状态
     */
    private Integer status;

    /**
     * 订单扩展类型
     */
    private Integer orderSaleType;
}
