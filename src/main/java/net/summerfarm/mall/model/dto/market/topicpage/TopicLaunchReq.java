package net.summerfarm.mall.model.dto.market.topicpage;

import java.io.Serializable;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * @author: <EMAIL>
 * @create: 2023/7/3
 */
@Data
public class TopicLaunchReq implements Serializable {

    /**
     * 投放id
     */
    @NotNull
    private Long launchId;

    /**
     * 页码
     */
    private Integer pageNo;

    /**
     * 页大小
     */
    private Integer pageSize;
}
