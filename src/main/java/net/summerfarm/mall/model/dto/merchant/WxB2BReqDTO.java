package net.summerfarm.mall.model.dto.merchant;

import com.alibaba.schedulerx.shade.org.apache.commons.lang.StringUtils;
import lombok.Data;
import net.summerfarm.mall.common.util.StringUtil;
import net.summerfarm.mall.model.domain.Merchant;

import static net.summerfarm.mall.contexts.Global.B2B_RETAIL_TYPE;

/**
 * 微信预录入信息
 */
@Data
public class WxB2BReqDTO {
    /**
     * mobile_phone：手机号【必填】
     * retail_name：门店名称（长度为1-30个字符，一个中文字等于2个字符）【必填】
     * retail_type：一级门店类型（杂货店、便利店、超市、餐饮店、母婴店、烟酒店、其他）
     * sub_retail_type：二级门店类型（一级门店类型为“其他”时必填）
     * address_province：门店地址，省【必填】address_city：门店地址，市【必填】
     * address_region：门店地址，区县【必填】
     * address_street：门店地址：街道详细地址【必填】
     * registration_number：营业执照注册号
     * biz_name：企业名称
     * corporation_name：法人姓名
     */
    private String mobile_phone;
    private String retail_name;
    private String retail_type;
    private String address_province;
    private String address_city;
    private String address_region;
    private String address_street;
    private String registration_number;
    private String biz_name;
    private String corporation_name;



    public static WxB2BReqDTO merge(TXAddressResp txAddressResp, Merchant merchant) {
        WxB2BReqDTO wxB2BReqDTO = new WxB2BReqDTO();
        wxB2BReqDTO.setMobile_phone(merchant.getPhone());
        wxB2BReqDTO.setRetail_name(merchant.getMname());
        wxB2BReqDTO.setRetail_type(B2B_RETAIL_TYPE);
        wxB2BReqDTO.setAddress_province(txAddressResp.getProvince());
        // todo城市需要特殊处理
        wxB2BReqDTO.setAddress_city(txAddressResp.getCity());
        wxB2BReqDTO.setAddress_region(txAddressResp.getDistrict());

        if (StringUtils.isEmpty(txAddressResp.getDistrict())) {
            wxB2BReqDTO.setAddress_region(merchant.getArea());
        }
        wxB2BReqDTO.setAddress_street(txAddressResp.getStreet());
        if (StringUtils.isEmpty(txAddressResp.getStreet())) {
            wxB2BReqDTO.setAddress_street(merchant.getAddress());
        }
        return wxB2BReqDTO;
    }

}
