package net.summerfarm.mall.model.dto;

import com.alibaba.schedulerx.shade.org.apache.commons.lang.StringUtils;
import lombok.Data;
import net.summerfarm.mall.model.dto.merchant.MerchantAndAccountDTO;
import net.summerfarm.mall.model.dto.merchant.TXAddressResp;

/**
 * <AUTHOR>
 * @descripton
 * @date 2025/1/14 15:40
 */
@Data
public class MerchantRetailInfo {

    // 手机号码
    private String mobilePhone;
    // 零售商名称
    private String retailName;
    // 零售类型
    private String retailType;
    // 地址省份
    private String addressProvince;
    // 地址城市
    private String addressCity;
    // 地址地区
    private String addressRegion;
    // 地址街道
    private String addressStreet;

    private String failureCode;



    public static MerchantRetailInfo buildRecord(MerchantAndAccountDTO accountDTO, TXAddressResp txAddressResp){
        MerchantRetailInfo retailInfo = new MerchantRetailInfo();
        retailInfo.setRetailName(accountDTO.getMname());
        retailInfo.setRetailType("餐饮店");
        retailInfo.setMobilePhone(accountDTO.getPhone());
        retailInfo.setAddressProvince(txAddressResp.getProvince());
        retailInfo.setAddressCity(txAddressResp.getCity());
        retailInfo.setAddressRegion(txAddressResp.getDistrict());
        retailInfo.setAddressStreet(txAddressResp.getStreet());
        if (StringUtils.isEmpty(txAddressResp.getDistrict())) {
            retailInfo.setAddressRegion(accountDTO.getArea());
        }
        if (StringUtils.isEmpty(txAddressResp.getStreet())) {
            retailInfo.setAddressStreet(accountDTO.getAddress());
        }
        return retailInfo;
    }
}
