package net.summerfarm.mall.model.dto.product;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/11/10 14:45
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MarketProviderQueryDTO {
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 商品ID
     */
    private Long pdId;
    /**
     * 区域no
     */
    private Integer areaNo;
    /**
     * 后台类目id集合
     */
    private List<Integer> categoryIds;

    /**
     * 省心送sku集合
     */
    private List<String> skus;

    /**
     * 大客户展示
     * null -> m_type=0 and sku in
     * 1    -> sku in
     * 2    -> m_type=0 or sku in
     */
    private Integer adminShow;

    private Integer pageIndex;
    private Integer pageSize;
    private List<String> sortDescList;
}
