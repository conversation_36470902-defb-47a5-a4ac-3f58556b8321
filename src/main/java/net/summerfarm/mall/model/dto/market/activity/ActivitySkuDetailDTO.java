package net.summerfarm.mall.model.dto.market.activity;

import lombok.Data;
import net.summerfarm.mall.model.domain.market.ActivitySkuDetail;
import net.summerfarm.mall.model.vo.LadderPriceVO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * @author: <EMAIL>
 * @create: 2022/12/18
 */
@Data
public class ActivitySkuDetailDTO extends ActivitySkuDetail implements Serializable {

    private Long basicInfoId;

    private String activityName;

    private Long itemConfigId;

    private BigDecimal activityPrice;

    /**
     * 平台
     * @see net.summerfarm.mall.enums.market.PlatformEnum
     */
    private Integer platform;

    /**
     * 是否支持省心送特价，0 否，1 是
     */
    private Integer isSupportTiming;

    /**
     * 阶梯价
     */
    private String ladderPrice;

    /**
     * 阶梯价集合
     */
    private List<LadderPriceVO> ladderPrices;

    /**
     * 活动创建时间
     */
    private LocalDateTime activityCreateTime;

    /**
     * 是否人群包活动 1：是
     */
    private Integer isCrowdPack;

    /**
     * 活动已购买数量
     */
    private Integer purchasedQuantity;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        ActivitySkuDetailDTO that = (ActivitySkuDetailDTO) o;
        return Objects.equals(basicInfoId, that.basicInfoId) && Objects.equals(activityName, that.activityName) && Objects.equals(itemConfigId, that.itemConfigId) && Objects.equals(activityPrice, that.activityPrice) && Objects.equals(platform, that.platform) && Objects.equals(isSupportTiming, that.isSupportTiming);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), basicInfoId, activityName, itemConfigId, activityPrice, platform, isSupportTiming);
    }
}
