package net.summerfarm.mall.model.dto;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR> ct
 * create at:  2020/12/4  16:38
 *
 * 售后配送单信息
 */

@Data
public class AfterSaleDeliveryPathDTO {


    private Integer id ;

    /**
    * 用户id
    */
    private Long mId;

    /**
     * 配送地址id
    */
    private Long concatId;

    /**
    * 售后单号
    */
    private String afterSaleNo;

    /**
    * 配送回收时间
    */
    private LocalDate deliveryTime;

    /**
    * 配送城市
    */
    private Integer outStoreNo;

    /**
    * 状态 0 失效 1 待完成 2 完成中 3 已完成
    */
    private Integer status;

    /**
    *  0 配送 1 回收
    */
    private  Integer type;

    /**
     *  回收sku
     */
    private String sku;
}
