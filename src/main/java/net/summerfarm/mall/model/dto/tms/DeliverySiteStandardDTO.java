package net.summerfarm.mall.model.dto.tms;

import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class DeliverySiteStandardDTO implements Serializable {

    private static final long serialVersionUID = 9058259030205404121L;

    /**
     * 配送点位ID
     */
    private Long id;

    /**
     * 配送批次ID
     */
    private Long deliveryBatchId;

    /**
     *计划配送日期
     */
    private LocalDateTime planArriveTime;

    /**
     * 点位在路线上的顺序
     */
    private Integer sequence;

    /**
     * 配送照片
     */
    private String signInPics;

    /**
     * 配送照片签收面单
     */
    private String signInSignPic;

    /**
     * 配送照片货物照片
     */
    private String signInProductPic;

    /**
     * 签到备注
     */
    private String signInRemark;

    /**
     * 是否正常签收，0：正常，1：不正常
     */
    private Integer signInStatus;

    /**
     * 实际到达时间
     */
    private LocalDateTime signInTime;

    /**
     * 10未到站,20已到站,22.已配送,25已拣货30已出发
     */
    private Integer status;

    /**
     * 订单号
     */
    private List<String> outOrderIdList;
    /**
     * 车辆信息
     */
    private DistDeliveryBatchDTO batchMessage;

    /**
     * 配送详情
     */
    private List<DeliverySiteItemDTO> deliverySiteItemList;
}
