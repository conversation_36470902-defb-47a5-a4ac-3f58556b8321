package net.summerfarm.mall.model.dto.market.banner;

import java.io.Serializable;
import java.util.List;
import lombok.Data;
import net.summerfarm.mall.model.domain.LuckyDrawActivity;
import net.summerfarm.mall.model.vo.ProductInfoVO;
import net.summerfarm.mall.model.vo.TimingProductVO;

/**
 * @author: <EMAIL>
 * @create: 2023/6/2
 */
@Data
public class BannerDetailDTO implements Serializable {

    /**
     * 商品详情页信息,linkType=1
     */
    private List<ProductInfoVO> skuList;

    /**
     * 省心送信息,linkType=2
     */
    private TimingProductVO timingProduct;

    /**
     * 抽奖活动信息,linkType=3
     */
    private LuckyDrawActivity luckDrawActivity;

    /**
     * 跳转类型,0 无跳转，1 商品，2 省心送，3 抽奖，4 专题，5 频道，6 直播间
     * @see net.summerfarm.enums.BannerEnum.LinkTypeEnum
     */
    private Integer linkType;

    /**
     * 是否能跳转,0 不能, 1 能
     */
    private Boolean linkFlag;

    /**
     * 商详、省心送sku
     */
    private String sku;

    /**
     * 商详是pdId,省心送是ruleId,抽奖是抽奖活动id
     */
    private String id;



}
