package net.summerfarm.mall.model.dto.inventory;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @author: monna.chen
 * @Date: 2023/11/7 18:59
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MarketItemDTO {

    /**
     * 商品item主键
     */
    private Long itemId;

    /**
     * tenant_id
     */
    private Long tenantId;

    /**
     * sku主键
     */
    private Long skuId;

    /**
     * 商品名称
     */
    private String itemTitle;

    /**
     * 商品头图
     */
    private String mainPicture;

    /**
     * 详情图
     */
    private String detailPicture;

    /**
     * 规格
     */
    private String specification;

    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 副标题
     */
    private String subTitle;

    /**
     * 产地
     */
    private String origin;

    /**
     * 商品主键
     */
    private Long marketId;

    /**
     * 品牌Id
     */
    private Long brandId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 自有编码
     */
    private String itemCode;

    /**
     * 规格备注（区间）
     */
    private String weightNotes;

    /**
     * 供应商Id
     */
    private String supplierId;

    /**
     * 最大售后数
     */
    private Integer maxAfterSaleAmount;

    /**
     * 售后单位
     */
    private String afterSaleUnit;

    /**
     * 删除标识 0、已删除 1、正在使用
     */
    private Integer deleteFlag;

    /**
     * 最小起订量
     */
    private Integer miniOrderQuantity;

    /**
     * 0 下架 1 上架
     *
     * @see com.cofso.item.client.enums.OnSaleTypeEnum
     */
    private Integer onSale;
    /**
     * 视频链接
     */
    private String videoUrl;
    /**
     * 售后规则详情
     */
    private String afterSaleRuleDetail;

//  **  MarketItemDetail
    /**
     * marketitemid
     */
    private Long marketItemId;

    /**
     * sku性质：0、常规 2、临保 3、拆包 4、不卖 5、破袋
     */
    private Integer extType;

    /**
     * 起售规格
     */
    private Integer baseSaleUnit;

    /**
     * 0 不展示平均价  1 展示平均价
     */
    private Integer averagePriceFlag;

    /**
     *  是否加入样本池 0 不加入 1加入
     */
    private Integer samplePool;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 外部id
     */
    private Long outId;
//  **  MarketItemDetail
//  ** market
    /**
     * 类目Id
     */
    private Long categoryId;
    /**
     * 主标题
     */
    private String marketTitle;
    /**
     * 副标题
     */
    private String marketSubTitle;
    /**
     * 主图
     */
    private String marketMainPicture;
    /**
     * 详情图
     */
    private String marketDetailPicture;
    /**
     * 删除标记
     */
    private String marketDeleteFlag;

//  ** market
//  ** market_detail
    /**
     * 从截单开始的售后时间
     */
    private Integer afterSaleTime;

    /**
     * 售后原因
     */
    private String afterSaleReason;

    /**
     * 退款原因,拍多/拍错/不想要
     */
    private String refundReason;

    /**
     * 描述
     */
    private String description;

    /**
     * 广告语
     */
    private String slogan;
    /**
     * market外部id
     */
    private Long marketOutId;
//  ** market_detail

}
