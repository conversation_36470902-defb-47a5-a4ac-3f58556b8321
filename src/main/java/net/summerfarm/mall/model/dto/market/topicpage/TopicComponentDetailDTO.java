package net.summerfarm.mall.model.dto.market.topicpage;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * @author: <EMAIL>
 * @create: 2023/6/28
 */
@Data
public class TopicComponentDetailDTO implements Serializable {

    /**
     * 投放明细id
     */
    private Long id;

    /**
     * 投放id
     */
    private Long componentLaunchId;

    /**
     * 排序值
     */
    private Integer priority;

    /**
     * 业务信息，图片地址/sku/优惠券id/tab名称
     */
    private String bizInfo;

    /**
     * 业务信息扩展
     */
    private String bizInfoExt;

    /**
     * 跳转类型,0 无跳转，1 商品，2 省心送，3 抽奖，4 专题，5 频道'
     */
    private Integer linkType;

    /**
     * 跳转业务id，需要结合跳转类型
     */
    private String linkBizId;

    /**
     * tab标签关联的组件id
     */
    private List<Long> tabComponentInfoId;

    /**
     * tab标签关联的子组件
     */
    private List<TopicComponentDTO> childrenComponentList;

}
