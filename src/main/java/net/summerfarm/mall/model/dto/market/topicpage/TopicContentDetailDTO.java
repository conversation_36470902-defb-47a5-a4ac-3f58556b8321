package net.summerfarm.mall.model.dto.market.topicpage;

import java.io.Serializable;
import java.util.List;
import lombok.Data;
import net.summerfarm.mall.model.domain.Coupon;
import net.summerfarm.mall.model.vo.ProductInfoVO;

/**
 * @author: <EMAIL>
 * @create: 2023/7/3
 */
@Data
public class TopicContentDetailDTO implements Serializable {

    /**
     * 商品列表
     */
    private List<ProductInfoVO> products;

    /**
     * 优惠券列表
     */
    private List<Coupon> coupons;

}
