package net.summerfarm.mall.model.dto.trolley;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * @author: <EMAIL>
 * @create: 2022/9/14
 */
@Data
public class TrolleyReqDTO implements Serializable {

    /**
     * sku编号
     */
    private String sku;

    /**
     * 主商品sku
     */
    private String parentSku;

    /**
     * 商品类型
     */
    private Integer productType;

    /**
     * 组合包id
     */
    private Integer suitId;

    /**
     * 加购数量
     */
    private Integer quantity;

    /**
     * 是否累加
     */
    private Boolean sumFlag = false;

    /**
     * 是否选中,0否，1选中
     */
    private Byte check;

    /**
     * 删除时有效的副商品sku集合
     */
    List<String> childSkus;

    /**
     * 关联活动的Id
     */
    private Long bizId;

}
