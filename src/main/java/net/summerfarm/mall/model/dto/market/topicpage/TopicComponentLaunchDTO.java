package net.summerfarm.mall.model.dto.market.topicpage;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * @author: <EMAIL>
 * @create: 2023/6/28
 */
@Data
public class TopicComponentLaunchDTO implements Serializable {

    /**
     * 投放id
     */
    private Long id;

    /**
     * 组件基础信息id
     */
    private Long componentInfoId;

    /**
     * 范围类型，0 全部，1 人群包，2 运营城市，3 运营大区
     */
    private Integer scopeType;

    /**
     * 范围id(子组件继承父组件的范围)
     */
    private Long scopeId;

    /**
     * 投放排序值(子组件只能是一个投放)
     */
    private Integer priority;

    /**
     * 过滤项，1 商品无库存、优惠券已抢光  2 商品已下架、优惠券已失效（默认，数据库没有记录）
     */
    private String filterType;

    /**
     * 最多展示数量
     */
    private Integer showLimit;

    /**
     * 组件类型，0 图片，1 商品，2 优惠券，3 tab
     */
    private Integer type;

    /**
     * 组件模板样式
     */
    private Integer styleType;

    /**
     * 本次投放的内容明细（只有tab组件时会返回数据）
     */
    private List<TopicComponentDetailDTO> componentDetailList;

    /**
     * 投放明细总数
     */
    private Long total;

}
