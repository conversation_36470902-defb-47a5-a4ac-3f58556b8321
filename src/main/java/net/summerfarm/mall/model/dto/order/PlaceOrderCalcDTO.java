package net.summerfarm.mall.model.dto.order;

import lombok.Data;
import net.summerfarm.mall.model.domain.Orders;
import net.summerfarm.mall.model.vo.order.PlaceOrderVO;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 下单计算订单实体
 */
@Data
public class PlaceOrderCalcDTO extends Orders {
    /**
     * 下单信息
     */
    private PlaceOrderVO placeOrderVO;

    /**
     * 订单项信息
     */
    private List<OrderItemCalcDTO> itemCalcDTOList;
}
