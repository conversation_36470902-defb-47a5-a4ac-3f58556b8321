package net.summerfarm.mall.model.dto.merchant;

import lombok.Data;
import org.bouncycastle.asn1.ocsp.ResponseData;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2025-01-13
 **/
@Data
public class MerchantStoreAccountBindAuthorizationDTO {

    /**
     * 调用状态，若未授权则为 false
     */
    private boolean status;

    /**
     * 返回数据
     */
    private MerchantStoreAccountBindAuthorizationDataDTO data;

    /**
     * 错误信息
     */
    private String errorMsg;

}
