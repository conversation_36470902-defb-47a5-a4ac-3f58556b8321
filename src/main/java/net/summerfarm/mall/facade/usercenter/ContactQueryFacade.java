package net.summerfarm.mall.facade.usercenter;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.constant.MerchantConstants;
import net.summerfarm.mall.model.dto.merchant.ContactDTO;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.merchant.enums.MerchantAddressEnums;
import net.xianmu.usercenter.client.merchant.provider.MerchantAddressQueryProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantAddressQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantContactResultResp;
import net.xianmu.usercenter.client.merchant.resp.domain.MerchantAddressDomainResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 买家中心地址-联系人信息接口
 * 注意买家域不涉及 运费、配送周期等信息
 *
 * <AUTHOR>
 * @date 2023/11/13 18:02
 */
@Slf4j
@Component
public class ContactQueryFacade {

    @DubboReference
    private MerchantAddressQueryProvider merchantAddressQueryProvider;


    /**
     * 根据商户id获取商户所有状态正常的联系人信息
     * @param mId
     * @return
     */
    public List<ContactDTO> getMerchantContact(Long mId) {
        MerchantAddressQueryReq req = new MerchantAddressQueryReq();
        req.setTenantId(MerchantConstants.XM_TENANT_ID);
        req.setMId(mId);
        req.setStatus(MerchantAddressEnums.status.NORMAL.getCode());
        return getContact(req);
    }


    /**
     * 获取指定门店下状态正常的默认地址，注意鲜沐可能存在没有默认地址的情况
     * @param mId
     * @return
     */
    public List<ContactDTO> getDefaultMerchantContact(Long mId) {
        MerchantAddressQueryReq req = new MerchantAddressQueryReq();
        req.setTenantId(MerchantConstants.XM_TENANT_ID);
        req.setMId(mId);
        req.setDefaultFlag(1);
        req.setStatus(MerchantAddressEnums.status.NORMAL.getCode());
        return getContact(req);
    }


    /**
     * 根据contact_id 列表查询地址信息
     * @param contactIds
     * @return
     */
    public List<ContactDTO> getMerchantContactList(List<Long> contactIds) {
        MerchantAddressQueryReq req = new MerchantAddressQueryReq();
        req.setTenantId(MerchantConstants.XM_TENANT_ID);
        req.setXmContactIdList(contactIds);
        return getContact(req);
    }





    // -------------------   下面是私有方法   -------------------------------


    private List<ContactDTO> getContact(MerchantAddressQueryReq req){
        DubboResponse<List<MerchantAddressDomainResp>> addressAndContacts = merchantAddressQueryProvider.getAddressAndContacts(req);
        if (addressAndContacts.isSuccess()) {
            return this.converterToContactDTOList(addressAndContacts.getData());
        }
        throw new BizException(addressAndContacts.getMsg());
    }


    private List<ContactDTO> converterToContactDTOList(List<MerchantAddressDomainResp> addressDomainResps) {
        if (CollUtil.isEmpty(addressDomainResps)) {
            return Collections.emptyList();
        }
        List<ContactDTO> contactEntities = new ArrayList<>();
        addressDomainResps.forEach(address -> {
            ContactDTO dto = new ContactDTO();
            dto.setTenantId(address.getTenantId());
            dto.setStoreId(address.getStoreId());
            dto.setContactId(address.getXmContactId());
            dto.setmId(address.getMId());
            dto.setProvince(address.getProvince());
            dto.setCity(address.getCity());
            dto.setArea(address.getArea());
            dto.setAddress(address.getAddress());
            dto.setHouseNumber(address.getHouseNumber());
            dto.setPoiNote(address.getPoiNote());
            dto.setIsDefault(address.getDefaultFlag());
            dto.setStatus(address.getStatus());
            dto.setContactId(address.getXmContactId());
            dto.setRemark(address.getRemark());
            dto.setAddressRemark(address.getAddressRemark());
            List<MerchantContactResultResp> list = address.getContactList();
            if (CollUtil.isNotEmpty(list)) {
                MerchantContactResultResp merchantContactResultResp = list.get(0);
                dto.setPhone(merchantContactResultResp.getPhone());
                dto.setContact(merchantContactResultResp.getName());
            }
            contactEntities.add(dto);
        });
        return contactEntities;
    }




}
