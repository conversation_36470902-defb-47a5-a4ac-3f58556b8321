package net.summerfarm.mall.facade.usercenter;

import cn.hutool.core.collection.CollUtil;
import com.aliyun.odps.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.common.util.StringUtil;
import net.summerfarm.mall.constant.MerchantConstants;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.merchant.enums.MerchantAccountEnums;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreAccountQueryProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreAccountQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 买家中心门店账号查询接口
 *
 * <AUTHOR>
 * @date 2023/11/14 14:10
 */
@Slf4j
@Component
public class MerchantAccountQueryFacade {
    @DubboReference
    private MerchantStoreAccountQueryProvider merchantStoreAccountQueryProvider;




    /**
     * 获取指定mid门店下所有正常的账户
     */
    public List<MerchantStoreAccountResultResp> getMerchantAccount(Long mid,
                                                                   MerchantAccountEnums.DeleteFlag deleteFlag,
                                                                   MerchantAccountEnums.Status status
                                                                   ) {
        MerchantStoreAccountQueryReq req = new MerchantStoreAccountQueryReq();
        req.setMId(mid);
        if (status != null){
            req.setStatus(status.getCode());
        }
        if (deleteFlag != null){
            req.setDeleteFlag(deleteFlag.getCode());
        }
        return this.getAccounts(req);
    }

    /**
     * 获取指定mid门店下所有正常的账户
     */
    public List<MerchantStoreAccountResultResp> getMerchantAccountByAccountIds(List<Long> accountIds,String phone,
                                                                   MerchantAccountEnums.DeleteFlag deleteFlag) {
        if (CollectionUtils.isEmpty(accountIds) && StringUtils.isEmpty(phone)){
            return new ArrayList<>();
        }
        MerchantStoreAccountQueryReq req = new MerchantStoreAccountQueryReq();
        req.setXmAccountIdList(accountIds);
        if (deleteFlag!=null){
            req.setDeleteFlag(deleteFlag.getCode());
        }
        req.setPhone(phone);
        return this.getAccounts(req);
    }




    private List<MerchantStoreAccountResultResp> getAccounts(MerchantStoreAccountQueryReq req){
        req.setTenantId(MerchantConstants.XM_TENANT_ID);
        DubboResponse<List<MerchantStoreAccountResultResp>> accounts = merchantStoreAccountQueryProvider.getMerchantStoreAccountsByPrimaryKeys(req);
        if (!accounts.isSuccess()) {
            throw new BizException("获取商户联系人信息失败");
        }
        return accounts.getData();
    }
}
