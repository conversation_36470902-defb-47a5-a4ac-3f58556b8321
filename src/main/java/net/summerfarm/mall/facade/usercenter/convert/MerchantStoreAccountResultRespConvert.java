package net.summerfarm.mall.facade.usercenter.convert;

import net.summerfarm.common.util.BaseDateUtils;
import net.summerfarm.mall.model.domain.MerchantSubAccount;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountResultResp;

import java.time.ZoneId;
import java.util.Date;

public class MerchantStoreAccountResultRespConvert {

    public static MerchantSubAccount convertMerchant(MerchantStoreAccountResultResp merchantStoreAccountResultResp) {
        MerchantSubAccount merchantSubAccount = new MerchantSubAccount();
        merchantSubAccount.setmId(merchantStoreAccountResultResp.getMId());
        merchantSubAccount.setAccountId(merchantStoreAccountResultResp.getXmAccountId());
        merchantSubAccount.setPhone(merchantStoreAccountResultResp.getPhone());
        merchantSubAccount.setStatus(merchantStoreAccountResultResp.getStatus());
        merchantSubAccount.setDeleteFlag(merchantStoreAccountResultResp.getDeleteFlag());
        if (merchantStoreAccountResultResp.getRegisterTime() != null) {
            merchantSubAccount.setRegisterTime(BaseDateUtils.localDateTime2Date(merchantStoreAccountResultResp.getRegisterTime()));
        }
        if (merchantStoreAccountResultResp.getLastLoginTime() != null) {
            merchantSubAccount.setLoginTime(BaseDateUtils.localDateTime2Date(merchantStoreAccountResultResp.getLastLoginTime()));
        }
        merchantSubAccount.setType(merchantStoreAccountResultResp.getType());
        merchantSubAccount.setOpenid(merchantStoreAccountResultResp.getOaOpenId());
        merchantSubAccount.setUnionid(merchantStoreAccountResultResp.getUnionId());
        merchantSubAccount.setMpOpenid(merchantStoreAccountResultResp.getOpenId());
        merchantSubAccount.setContact(merchantStoreAccountResultResp.getAccountName());
        return merchantSubAccount;
    }

}
