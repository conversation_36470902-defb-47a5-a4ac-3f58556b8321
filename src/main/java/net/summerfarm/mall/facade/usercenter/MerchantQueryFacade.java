package net.summerfarm.mall.facade.usercenter;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.mall.constant.MerchantConstants;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.merchant.provider.MerchantAddressQueryProvider;
import net.xianmu.usercenter.client.merchant.provider.MerchantExtendQueryProvider;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreChangeLogQueryProvider;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreQueryProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreChangeLogQueryReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAndExtendResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreChangeLogResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 买家中心门店信息查询接口
 *
 * <AUTHOR>
 * @date 2023/11/13 18:02
 */
@Slf4j
@Component
public class MerchantQueryFacade {
    @DubboReference
    private MerchantExtendQueryProvider merchantExtendQueryProvider;
    @DubboReference
    private MerchantAddressQueryProvider merchantAddressQueryProvider;
    @DubboReference
    private MerchantStoreChangeLogQueryProvider changeLogQueryProvider;
    @DubboReference
    private MerchantStoreQueryProvider merchantStoreQueryProvider;


    /**
     * -----------------------------------     查询门店基本信息信息--------------------------------
     */

    /**
     * 通过门店邀请码获取门店
     *
     * @param channelCode 邀请码
     * @return {@link MerchantStoreAndExtendResp}
     */
    public MerchantStoreResultResp getMerchantByChannelCode(String channelCode) {
        if(StringUtils.isBlank(channelCode)) {
            log.warn("邀请码为空!");
            return null;
        }
        MerchantStoreQueryReq req = new MerchantStoreQueryReq();
        req.setChannelCode(channelCode);
        List<MerchantStoreResultResp> merchants = getMerchant(req);
        if (CollectionUtil.isEmpty(merchants)) {
            return null;
        }
        return merchants.get(0);
    }

    /**
     * 通过门店名称获取门店（非模糊）
     *
     * @param storeName 商店名字
     * @return {@link MerchantStoreAndExtendResp}
     */
    public MerchantStoreResultResp getMerchantByExactStoreName(String storeName) {
        if(StringUtils.isBlank(storeName)) {
            log.warn("门店名称为空!");
            return null;
        }
        MerchantStoreQueryReq req = new MerchantStoreQueryReq();
        req.setExactStoreName(storeName);
        List<MerchantStoreResultResp> merchants = getMerchant(req);
        if (CollectionUtil.isEmpty(merchants)) {
            return null;
        }
        return merchants.get(0);
    }

    private List<MerchantStoreResultResp> getMerchant(MerchantStoreQueryReq req) {
        req.setTenantId(MerchantConstants.XM_TENANT_ID);
        DubboResponse<List<MerchantStoreResultResp>> merchantExtendResp = merchantStoreQueryProvider.getMerchantStoresByPrimaryKeys(req);
        if (!merchantExtendResp.isSuccess()) {
            return new ArrayList<>();
        }
        return merchantExtendResp.getData();
    }


    /**
     * -----------------------------------     查询门店拓展信息   --------------------------------
     */

    /**
     * mId 查询门店信息
     *
     * @param mId m id
     */
    public MerchantStoreAndExtendResp getMerchantExtendsByMid(Long mId) {
        List<MerchantStoreAndExtendResp> merchantExtends = this.getMerchantExtendsByMid(Collections.singletonList(mId));
        return CollUtil.isEmpty(merchantExtends) ? null : merchantExtends.get(0);
    }

    /**
     *  批量查询门店信息(默认最多查200条)
     *
     * @param mId m id
     */
    public List<MerchantStoreAndExtendResp> getMerchantExtendsByMid(List<Long> mId) {
        MerchantStoreQueryReq req = new MerchantStoreQueryReq();
        req.setMIds(mId);
        req.setQueryManageAccount(true);
        List<MerchantStoreAndExtendResp> merchantExtends = getMerchantExtendsPrimaryKeys(req);
        if (CollectionUtil.isEmpty(merchantExtends)) {
            return new ArrayList<>();
        }
        return merchantExtends;
    }





    /**
     * 批量查询门店信息(默认最多查200条)
     * // 必传参数：tenantId
     * // 这里面的至少传一个：storeId, storeIdList, mId, mIds。参数为空时默认返回空list
     *
     * @param req 要求事情
     * @return {@link List}<{@link MerchantStoreAndExtendResp}>
     */
    private List<MerchantStoreAndExtendResp> getMerchantExtendsPrimaryKeys(MerchantStoreQueryReq req) {
        req.setTenantId(MerchantConstants.XM_TENANT_ID);
        DubboResponse<List<MerchantStoreAndExtendResp>> merchantExtendResp = merchantExtendQueryProvider.getMerchantStoreAndExtendsByPrimaryKeys(req);
        if (!merchantExtendResp.isSuccess()) {
            return new ArrayList<>();
        }
        return merchantExtendResp.getData();
    }


    /**
     * 根据各种条件查询门店 ID,关联sql用
     * @param req 要求事情
     * @return {@link List}<{@link Long}>
     */
    public List<Long> getMerchantIds(MerchantStoreQueryReq req) {
        req.setTenantId(MerchantConstants.XM_TENANT_ID);
        DubboResponse<List<Long>> merchantExtendResp = merchantExtendQueryProvider.getMidList(req);
        if (!merchantExtendResp.isSuccess()) {
            return new ArrayList<>();
        }
        return merchantExtendResp.getData();
    }


    /**
     * ----------------- 查询门店操作记录 -------------------------
     */

    /**
     * 根据store_id（注意这个不是m_id，是用户中心的门店id）查询门店的客户来源（是哪个门店推荐的）
     *
     * @param storeId
     * @return
     */
    public MerchantStoreResultResp getMerchantSource(Long storeId) {

        // 先查询门店的邀请记录
        MerchantStoreChangeLogQueryReq req = new MerchantStoreChangeLogQueryReq();
        req.setStoreId(storeId);
        req.setTenantId(MerchantConstants.XM_TENANT_ID);
        req.setOpType(0);
        DubboResponse<List<MerchantStoreChangeLogResultResp>> changeLogs = changeLogQueryProvider.getMerchantStoreChangeLogs(req);
        if (!changeLogs.isSuccess()) {
            throw new BizException(changeLogs.getMsg());
        }
        List<MerchantStoreChangeLogResultResp> data = changeLogs.getData();
        if(CollUtil.isEmpty(data)) {
            return null;
        }
        String channelCode = data.get(0).getMerchantChannelCode();
        if(StringUtils.isBlank(channelCode)) {
            return null;
        }

        // 根据channelCode获取指定的merchant
        MerchantStoreQueryReq queryReq = new MerchantStoreQueryReq();
        queryReq.setTenantId(MerchantConstants.XM_TENANT_ID);
        queryReq.setChannelCode(channelCode);
        DubboResponse<List<MerchantStoreResultResp>> stores = merchantStoreQueryProvider.getMerchantStores(queryReq);
        if (!stores.isSuccess()) {
            throw new BizException(changeLogs.getMsg());
        }
        List<MerchantStoreResultResp> storesData = stores.getData();
        return CollUtil.isEmpty(storesData) ? null : storesData.get(0);
    }

    /**
     * 查询门店操作日志，注意这个不是m_id，是用户中心的门店id
     *
     * @param storeId
     * @return
     */
    public MerchantStoreChangeLogResultResp getMerchantChangeLog(Long storeId) {

        // 先查询门店的邀请记录
        MerchantStoreChangeLogQueryReq req = new MerchantStoreChangeLogQueryReq();
        req.setStoreId(storeId);
        req.setTenantId(MerchantConstants.XM_TENANT_ID);
        req.setOpType(0);
        DubboResponse<List<MerchantStoreChangeLogResultResp>> changeLogs = changeLogQueryProvider.getMerchantStoreChangeLogs(req);
        if (!changeLogs.isSuccess()) {
            throw new BizException(changeLogs.getMsg());
        }
        List<MerchantStoreChangeLogResultResp> data = changeLogs.getData();
        if(CollUtil.isEmpty(data)) {
            return null;
        }
        return data.get(0);
    }
}
