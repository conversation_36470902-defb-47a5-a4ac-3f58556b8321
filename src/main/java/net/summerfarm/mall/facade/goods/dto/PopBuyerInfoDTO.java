package net.summerfarm.mall.facade.goods.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description
 * @Date 2024/6/27 11:11
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PopBuyerInfoDTO implements Serializable {
    /**
     * 买手id
     */
    private Long buyerId;

    /**
     * 买手花名
     */
    private String buyerName;

    /**
     * 员工adminId
     */
    private Integer adminId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 操作人
     */
    private String operator;

}
