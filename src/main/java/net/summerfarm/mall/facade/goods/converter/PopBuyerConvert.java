package net.summerfarm.mall.facade.goods.converter;


import net.summerfarm.goods.client.dto.PopBuyerDTO;
import net.summerfarm.mall.facade.goods.dto.PopBuyerInfoDTO;

import java.util.Objects;

/**
 * @Description
 * @Date 2024/6/27 11:22
 * @<AUTHOR>
 */
public class PopBuyerConvert {

    public static PopBuyerInfoDTO convert(PopBuyerDTO popBuyerDTO) {
        if (Objects.isNull(popBuyerDTO)) {
            return null;
        }
        return PopBuyerInfoDTO.builder()
                .buyerId(popBuyerDTO.getBuyerId())
                .buyerName(popBuyerDTO.getBuyerName())
                .adminId(popBuyerDTO.getAdminId())
                .creator(popBuyerDTO.getCreator())
                .operator(popBuyerDTO.getOperator())
                .build();

    }

}
