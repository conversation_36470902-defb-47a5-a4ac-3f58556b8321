package net.summerfarm.mall.facade.goods;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.goods.client.provider.PopBuyerProvider;
import net.summerfarm.goods.client.req.PopBuyerQueryReq;
import net.summerfarm.goods.client.resp.PopBuyerQueryResp;
import net.summerfarm.mall.facade.goods.converter.PopBuyerConvert;
import net.summerfarm.mall.facade.goods.dto.PopBuyerInfoDTO;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @descripton
 * @date 2024/6/27 14:42
 */

@Slf4j
@Component
public class PopBuyerQueryFacade {


    @DubboReference
    private PopBuyerProvider popBuyerProvider;

    public List<PopBuyerInfoDTO> queryPopBuyerByIdList(List<Long> buyerIds){
        if (CollectionUtils.isEmpty(buyerIds)) {
            return Lists.newArrayList();
        }
        PopBuyerQueryReq queryReq = PopBuyerQueryReq
                .builder().buyerIdList(buyerIds).build();
        DubboResponse<PopBuyerQueryResp> resp = popBuyerProvider.queryPopBuyerByIdList(queryReq);
        if(!resp.isSuccess()){
            log.error("调用货品中心查询买手信息异常 buyerIdList:{} resp:{}", buyerIds, JSON.toJSONString(resp));
            throw new ProviderException("调用货品中心查询买手信息异常:" + resp.getMsg());
        }
        PopBuyerQueryResp popBuyerQueryResp = resp.getData();
        if (Objects.isNull(popBuyerQueryResp) || CollectionUtils.isEmpty(popBuyerQueryResp.getPopBuyerDTOList())) {
            return Lists.newArrayList();
        }
        return popBuyerQueryResp.getPopBuyerDTOList().stream().map(PopBuyerConvert::convert).collect(Collectors.toList());
    }
}
