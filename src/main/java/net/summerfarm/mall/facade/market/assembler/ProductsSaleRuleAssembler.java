package net.summerfarm.mall.facade.market.assembler;


import net.summerfarm.mall.facade.market.dto.ProductsSaleRuleDto;
import net.xianmu.marketing.center.client.product.resp.ProductsSaleRuleResultResp;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-11-27 15:33:27
 * @version 1.0
 *
 */
public class ProductsSaleRuleAssembler {


    private ProductsSaleRuleAssembler() {
        // 无需实现
    }

    public static List<ProductsSaleRuleResultResp> toProductsSaleRuleResultRespList(List<ProductsSaleRuleDto> productsSaleRuleDtoList) {
        if (productsSaleRuleDtoList == null) {
            return Collections.emptyList();
        }
        List<ProductsSaleRuleResultResp> productsSaleRuleResultRespList = new ArrayList<>();
        for (ProductsSaleRuleDto productsSaleRuleDto : productsSaleRuleDtoList) {
            productsSaleRuleResultRespList.add(toProductsSaleRuleResultResp(productsSaleRuleDto));
        }
        return productsSaleRuleResultRespList;
    }

    public static ProductsSaleRuleResultResp toProductsSaleRuleResultResp(ProductsSaleRuleDto productsSaleRuleDto) {
        if (productsSaleRuleDto == null) {
            return null;
        }
        ProductsSaleRuleResultResp productsSaleRuleResultResp = new ProductsSaleRuleResultResp();
        productsSaleRuleResultResp.setPdId(productsSaleRuleDto.getPdId());
        productsSaleRuleResultResp.setStatus(productsSaleRuleDto.getStatus());
        productsSaleRuleResultResp.setBaseSaleQuantity(productsSaleRuleDto.getBaseSaleQuantity());
// Not mapped TO fields:
// id
// operator
// createTime
// updateTime
        return productsSaleRuleResultResp;
    }

    public static List<ProductsSaleRuleDto> toProductsSaleRuleDtoList(List<ProductsSaleRuleResultResp> productsSaleRuleResultRespList) {
        if (productsSaleRuleResultRespList == null) {
            return Collections.emptyList();
        }
        List<ProductsSaleRuleDto> productsSaleRuleDtoList = new ArrayList<>();
        for (ProductsSaleRuleResultResp productsSaleRuleResultResp : productsSaleRuleResultRespList) {
            productsSaleRuleDtoList.add(toProductsSaleRuleDto(productsSaleRuleResultResp));
        }
        return productsSaleRuleDtoList;
    }

    public static ProductsSaleRuleDto toProductsSaleRuleDto(ProductsSaleRuleResultResp productsSaleRuleResultResp) {
        if (productsSaleRuleResultResp == null) {
            return null;
        }
        ProductsSaleRuleDto productsSaleRuleDto = new ProductsSaleRuleDto();
        productsSaleRuleDto.setPdId(productsSaleRuleResultResp.getPdId());
        productsSaleRuleDto.setStatus(productsSaleRuleResultResp.getStatus());
        productsSaleRuleDto.setBaseSaleQuantity(productsSaleRuleResultResp.getBaseSaleQuantity());
// Not mapped FROM fields:
// id
// operator
// createTime
// updateTime
        return productsSaleRuleDto;
    }
}
