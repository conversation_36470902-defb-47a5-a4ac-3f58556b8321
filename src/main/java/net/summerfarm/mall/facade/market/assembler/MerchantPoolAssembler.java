package net.summerfarm.mall.facade.market.assembler;

import net.summerfarm.mall.facade.market.dto.MerchantPoolDetailDTO;
import net.xianmu.marketing.center.client.merchantpool.resp.MerchantPoolInfoResp;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/12/20 15:22
 * @PackageName:net.summerfarm.mall.facade.market.assembler
 * @ClassName: MerchantPoolAssembler
 * @Description: TODO
 * @Version 1.0
 */
public class MerchantPoolAssembler {

    private MerchantPoolAssembler() {
        // 无需实现
    }


    public static List<MerchantPoolDetailDTO> toMerchantPoolDetailDTOList(List<MerchantPoolInfoResp> data) {
        if (CollectionUtils.isEmpty(data)) {
            return Collections.emptyList();
        }
        List<MerchantPoolDetailDTO> result = new ArrayList<>();
        data.forEach(item -> {
            MerchantPoolDetailDTO merchantPoolDetailDTO = new MerchantPoolDetailDTO();
            merchantPoolDetailDTO.setPoolInfoId(item.getPoolInfoId());
            result.add(merchantPoolDetailDTO);
        });
        return result;
    }
}
