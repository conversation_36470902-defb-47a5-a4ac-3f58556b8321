package net.summerfarm.mall.facade.market.dto;
import java.time.LocalDateTime;
import lombok.Data;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024-11-27 15:33:27
 * @version 1.0
 *
 */
@Data
public class ProductsSaleRuleDto implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 商品id
	 */
	private Long pdId;
	/**
	 * 状态：1-正常, 2-禁用
	 */
	private Integer status;
	/**
	 * spu最小起售量
	 */
	private Integer baseSaleQuantity;
	
}