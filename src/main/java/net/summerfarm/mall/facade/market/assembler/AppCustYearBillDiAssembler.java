package net.summerfarm.mall.facade.market.assembler;


import net.summerfarm.mall.model.vo.AppCustYearBillDiVO;
import net.xianmu.marketing.center.client.bill.resp.AppCustYearBillDiResultResp;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-05-30 10:27:32
 * @version 1.0
 *
 */
public class AppCustYearBillDiAssembler {

    private AppCustYearBillDiAssembler() {
        // 无需实现
    }


    public static List<AppCustYearBillDiResultResp> toAppCustYearBillDiResultRespList(List<AppCustYearBillDiVO> appCustYearBillDiVOList) {
        if (appCustYearBillDiVOList == null) {
            return Collections.emptyList();
        }
        List<AppCustYearBillDiResultResp> appCustYearBillDiResultRespList = new ArrayList<>();
        for (AppCustYearBillDiVO appCustYearBillDiVO : appCustYearBillDiVOList) {
            appCustYearBillDiResultRespList.add(toAppCustYearBillDiResultResp(appCustYearBillDiVO));
        }
        return appCustYearBillDiResultRespList;
    }

    public static AppCustYearBillDiResultResp toAppCustYearBillDiResultResp(AppCustYearBillDiVO appCustYearBillDiVO) {
        if (appCustYearBillDiVO == null) {
            return null;
        }
        AppCustYearBillDiResultResp appCustYearBillDiResultResp = new AppCustYearBillDiResultResp();
        appCustYearBillDiResultResp.setId(appCustYearBillDiVO.getId());
        appCustYearBillDiResultResp.setCustId(appCustYearBillDiVO.getCustId());
        appCustYearBillDiResultResp.setCustName(appCustYearBillDiVO.getCustName());
        appCustYearBillDiResultResp.setRegisterDate(appCustYearBillDiVO.getRegisterDate());
        appCustYearBillDiResultResp.setRegisterDateCnt(appCustYearBillDiVO.getRegisterDateCnt());
        appCustYearBillDiResultResp.setIsLoading(appCustYearBillDiVO.getIsLoading());
        appCustYearBillDiResultResp.setIsOrder(appCustYearBillDiVO.getIsOrder());
        appCustYearBillDiResultResp.setOrderCn(appCustYearBillDiVO.getOrderCn());
        appCustYearBillDiResultResp.setOriginTotalAmt(appCustYearBillDiVO.getOriginTotalAmt());
        appCustYearBillDiResultResp.setDeliveryCnt(appCustYearBillDiVO.getDeliveryCnt());
        appCustYearBillDiResultResp.setPerentialCnt(appCustYearBillDiVO.getPerentialCnt());
        appCustYearBillDiResultResp.setPerentialAmt(appCustYearBillDiVO.getPerentialAmt());
        appCustYearBillDiResultResp.setPerentialAmtRk(appCustYearBillDiVO.getPerentialAmtRk());
        appCustYearBillDiResultResp.setPtPerentialCnt(appCustYearBillDiVO.getPtPerentialCnt());
        appCustYearBillDiResultResp.setLastOrderTime(appCustYearBillDiVO.getLastOrderTime());
        appCustYearBillDiResultResp.setMaxViewTimeDate(appCustYearBillDiVO.getMaxViewTimeDate());
        appCustYearBillDiResultResp.setMaxViewTimeCnt(appCustYearBillDiVO.getMaxViewTimeCnt());
        appCustYearBillDiResultResp.setMaxOrderDate(appCustYearBillDiVO.getMaxOrderDate());
        appCustYearBillDiResultResp.setMaxOrderAmt(appCustYearBillDiVO.getMaxOrderAmt());
        appCustYearBillDiResultResp.setCreateTime(appCustYearBillDiVO.getCreateTime());
        appCustYearBillDiResultResp.setUpdateTime(appCustYearBillDiVO.getUpdateTime());
        appCustYearBillDiResultResp.setDateFlag(appCustYearBillDiVO.getDateFlag());
        return appCustYearBillDiResultResp;
    }

    public static List<AppCustYearBillDiVO> toAppCustYearBillDiVOList(List<AppCustYearBillDiResultResp> appCustYearBillDiResultRespList) {
        if (appCustYearBillDiResultRespList == null) {
            return Collections.emptyList();
        }
        List<AppCustYearBillDiVO> appCustYearBillDiVOList = new ArrayList<>();
        for (AppCustYearBillDiResultResp appCustYearBillDiResultResp : appCustYearBillDiResultRespList) {
            appCustYearBillDiVOList.add(toAppCustYearBillDiVO(appCustYearBillDiResultResp));
        }
        return appCustYearBillDiVOList;
    }

    public static AppCustYearBillDiVO toAppCustYearBillDiVO(AppCustYearBillDiResultResp appCustYearBillDiResultResp) {
        if (appCustYearBillDiResultResp == null) {
            return null;
        }
        AppCustYearBillDiVO appCustYearBillDiVO = new AppCustYearBillDiVO();
        appCustYearBillDiVO.setId(appCustYearBillDiResultResp.getId());
        appCustYearBillDiVO.setCustId(appCustYearBillDiResultResp.getCustId());
        appCustYearBillDiVO.setCustName(appCustYearBillDiResultResp.getCustName());
        appCustYearBillDiVO.setRegisterDate(appCustYearBillDiResultResp.getRegisterDate());
        appCustYearBillDiVO.setRegisterDateCnt(appCustYearBillDiResultResp.getRegisterDateCnt());
        appCustYearBillDiVO.setIsLoading(appCustYearBillDiResultResp.getIsLoading());
        appCustYearBillDiVO.setIsOrder(appCustYearBillDiResultResp.getIsOrder());
        appCustYearBillDiVO.setOrderCn(appCustYearBillDiResultResp.getOrderCn());
        appCustYearBillDiVO.setOriginTotalAmt(appCustYearBillDiResultResp.getOriginTotalAmt());
        appCustYearBillDiVO.setDeliveryCnt(appCustYearBillDiResultResp.getDeliveryCnt());
        appCustYearBillDiVO.setPerentialCnt(appCustYearBillDiResultResp.getPerentialCnt());
        appCustYearBillDiVO.setPerentialAmt(appCustYearBillDiResultResp.getPerentialAmt());
        appCustYearBillDiVO.setPerentialAmtRk(appCustYearBillDiResultResp.getPerentialAmtRk());
        appCustYearBillDiVO.setPtPerentialCnt(appCustYearBillDiResultResp.getPtPerentialCnt());
        appCustYearBillDiVO.setLastOrderTime(appCustYearBillDiResultResp.getLastOrderTime());
        appCustYearBillDiVO.setMaxViewTimeDate(appCustYearBillDiResultResp.getMaxViewTimeDate());
        appCustYearBillDiVO.setMaxViewTimeCnt(appCustYearBillDiResultResp.getMaxViewTimeCnt());
        appCustYearBillDiVO.setMaxOrderDate(appCustYearBillDiResultResp.getMaxOrderDate());
        appCustYearBillDiVO.setMaxOrderAmt(appCustYearBillDiResultResp.getMaxOrderAmt());
        appCustYearBillDiVO.setCreateTime(appCustYearBillDiResultResp.getCreateTime());
        appCustYearBillDiVO.setUpdateTime(appCustYearBillDiResultResp.getUpdateTime());
        appCustYearBillDiVO.setDateFlag(appCustYearBillDiResultResp.getDateFlag());
        appCustYearBillDiVO.setMaxSpuName(appCustYearBillDiResultResp.getMaxSpuName());
        return appCustYearBillDiVO;
    }
}
