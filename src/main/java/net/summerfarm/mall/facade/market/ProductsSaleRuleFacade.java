package net.summerfarm.mall.facade.market;

import net.summerfarm.mall.facade.market.assembler.ProductsSaleRuleAssembler;
import net.summerfarm.mall.facade.market.dto.ProductsSaleRuleDto;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.marketing.center.client.product.provider.ProductsSaleRuleQueryProvider;
import net.xianmu.marketing.center.client.product.resp.ProductsSaleRuleResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @descripton
 * @date 2024/11/28 17:38
 */
@Component
public class ProductsSaleRuleFacade {

    @DubboReference
    private ProductsSaleRuleQueryProvider productsSaleRuleQueryProvider;


    /**
     * 获取商品起售规格
     * @param pdIds
     * @return
     */
    public List<ProductsSaleRuleDto> queryProductsSaleRuleListByPdIds(List<Long> pdIds) {
        DubboResponse<List<ProductsSaleRuleResultResp>> dubboResponse = productsSaleRuleQueryProvider.queryProductsSaleRuleListByPdIds(pdIds);
        if (dubboResponse.isSuccess()) {
            return ProductsSaleRuleAssembler.toProductsSaleRuleDtoList(dubboResponse.getData());
        }
        throw new ProviderException(dubboResponse.getMsg());
    }


    /**
     * 获取商品起售规格
     * @param pdIds
     * @return
     */
    public Map<Long, ProductsSaleRuleDto> queryProductsSaleRuleMapByPdIds(List<Long> pdIds) {
        List<ProductsSaleRuleDto> list = this.queryProductsSaleRuleListByPdIds(pdIds);
        return list.stream().collect(Collectors.toMap(ProductsSaleRuleDto::getPdId, Function.identity()));
    }
}
