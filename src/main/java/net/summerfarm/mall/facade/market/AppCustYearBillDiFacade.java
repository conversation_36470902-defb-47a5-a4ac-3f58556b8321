package net.summerfarm.mall.facade.market;

import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.cache.InMemoryCache;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.marketing.center.client.bill.provider.AppCustYearBillDiQueryProvider;
import net.xianmu.marketing.center.client.bill.resp.AppCustYearBillDiResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @descripton
 * @date 2024/6/4 10:52
 */
@Component
@Slf4j
public class AppCustYearBillDiFacade {


    @DubboReference
    private AppCustYearBillDiQueryProvider appCustYearBillDiQueryProvider;

    @InMemoryCache(expiryTimeInSeconds = 60 * 2)
    public AppCustYearBillDiResultResp getDetail(Long mid) {
        try {
            DubboResponse<AppCustYearBillDiResultResp> dubboResponse = appCustYearBillDiQueryProvider.queryAppCustYearBillDiByMid(mid);
            if (dubboResponse.isSuccess()) {
                return dubboResponse.getData();
            }
        } catch (Exception e) {
            log.error("获取客户年度账单异常,入参:{}, cause:{}", mid, Throwables.getStackTraceAsString(e));
        }
        return null;
    }
}
