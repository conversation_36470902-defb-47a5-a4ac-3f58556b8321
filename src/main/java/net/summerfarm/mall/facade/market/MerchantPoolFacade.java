package net.summerfarm.mall.facade.market;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.constant.CommonRedisKey;
import net.summerfarm.mall.facade.market.assembler.MerchantPoolAssembler;
import net.summerfarm.mall.facade.market.dto.MerchantPoolDetailDTO;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.marketing.center.client.merchantpool.provide.MerchantPoolProvide;
import net.xianmu.marketing.center.client.merchantpool.req.MerchantPoolInfoReq;
import net.xianmu.marketing.center.client.merchantpool.resp.MerchantPoolInfoResp;
import net.xianmu.redis.support.cache.annotation.XmCache;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/12/20 15:17
 * @PackageName:net.summerfarm.mall.facade.market
 * @ClassName: MerchantPoolFacade
 * @Description: TODO
 * @Version 1.0
 */
@Component
@Slf4j
public class MerchantPoolFacade {

    @DubboReference
    private MerchantPoolProvide merchantPoolProvide;

    @XmCache(prefixKey = CommonRedisKey.Cache.CACHE_MERCHANT_POOL_INFO, key = "{mId}", expire = 300,
            cacheEmptyFlag = true, classType = MerchantPoolDetailDTO.class)
    public List<MerchantPoolDetailDTO> listMerchantPoolDetail(Long mId) {
        try {
            MerchantPoolInfoReq req = new MerchantPoolInfoReq();
            req.setMId(mId);
            DubboResponse<List<MerchantPoolInfoResp>> response = merchantPoolProvide.getMerchantPoolInfo(req);
            if (!response.isSuccess()) {
                log.error("MerchantPoolFacade[]listMerchantPoolDetail[]getMerchantPoolInfo[]error！mId:{}, response:{}", JSON.toJSONString(response));
                return Collections.emptyList();
            }
            return MerchantPoolAssembler.toMerchantPoolDetailDTOList(response.getData());
        } catch (Exception e) {
            log.error("MerchantPoolFacade[]listMerchantPoolDetail[]error！mId:{}, e:{}", mId, e);
            return Collections.emptyList();
        }
    }
}
