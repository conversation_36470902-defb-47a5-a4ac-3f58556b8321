package net.summerfarm.mall.facade.pms.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description
 * @Date 2024/6/27 11:11
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PopSkuAdditionQueryDto implements Serializable {

    private String sku;

    /**
     * 数量
     */
    private Integer quantity;


    /**
     * 库存仓
     */
    private Integer warehouseNo;

}
