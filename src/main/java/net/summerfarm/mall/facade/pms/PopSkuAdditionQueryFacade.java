package net.summerfarm.mall.facade.pms;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.facade.pms.converter.PopSkuAdditionConvert;
import net.summerfarm.mall.facade.pms.dto.PopSkuAdditionInfoDTO;
import net.summerfarm.mall.facade.pms.dto.PopSkuAdditionQueryDto;
import net.summerfarm.pms.client.provider.pop.PopSkuAdditionQueryProvider;
import net.summerfarm.pms.client.req.pop.PopSkuAdditionBatchQueryReq;
import net.summerfarm.pms.client.resp.pop.PopSkuAdditionBatchResp;
import net.summerfarm.pms.client.resp.pop.PopSkuAdditionResp;
import net.summerfarm.wnc.client.provider.warehouse.WarehouseSkuAreaNoQueryProvider;
import net.summerfarm.wnc.client.req.WarehouseBySkuStoreNoDataReq;
import net.summerfarm.wnc.client.req.WarehouseBySkuStoreNoQueryReq;
import net.summerfarm.wnc.client.resp.SkuWarehouseMappingResp;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @descripton
 * @date 2024/6/27 14:42
 */

@Slf4j
@Component
public class PopSkuAdditionQueryFacade {


    @DubboReference
    private PopSkuAdditionQueryProvider popSkuAdditionQueryProvider;
    @DubboReference
    private WarehouseSkuAreaNoQueryProvider warehouseSkuAreaNoQueryProvider;

    /**
     *
     * @param skuList sku列表
     * @param storeNo 城配仓
     * @return
     */
    public List<PopSkuAdditionInfoDTO> queryPopSkuAdditionBatch(List<PopSkuAdditionQueryDto> reqList, Integer storeNo) {
        if (CollectionUtils.isEmpty(reqList) || storeNo == null) {
            return Lists.newArrayList();
        }
        // 城配仓--》库存仓
        WarehouseBySkuStoreNoQueryReq warehouseBySkuStoreNoQueryReq = new WarehouseBySkuStoreNoQueryReq();
        List<WarehouseBySkuStoreNoDataReq> warehouseBySkuStoreNoDataReqList = reqList.stream().map(a -> {
            WarehouseBySkuStoreNoDataReq warehouseBySkuStoreNoDataReq = new WarehouseBySkuStoreNoDataReq();
            warehouseBySkuStoreNoDataReq.setSku(a.getSku());
            warehouseBySkuStoreNoDataReq.setStoreNo(storeNo);
            return warehouseBySkuStoreNoDataReq;
        }).collect(Collectors.toList());
        warehouseBySkuStoreNoQueryReq.setWarehouseBySkuStoreNoDataReqList(warehouseBySkuStoreNoDataReqList);
        DubboResponse<List<SkuWarehouseMappingResp>> warehouseDubboResponse = warehouseSkuAreaNoQueryProvider.querySkuWarehouseMappings(warehouseBySkuStoreNoQueryReq);
        if (!warehouseDubboResponse.isSuccess()) {
            log.error("调用wnc查询sku库存仓信息异常 req:{} , resp:{}", JSON.toJSONString(warehouseBySkuStoreNoQueryReq), JSON.toJSONString(warehouseDubboResponse));
            throw new ProviderException("调用wnc查询sku库存仓信息异常:" + warehouseDubboResponse.getMsg());
        }
        List<SkuWarehouseMappingResp> skuWarehouseMappingRespList = warehouseDubboResponse.getData();
        if (CollectionUtils.isEmpty(skuWarehouseMappingRespList)) {
            log.warn("调用wnc查询sku库存仓信息返回数据为空!");
            return Lists.newArrayList();
        }
        this.wrapReqList(reqList, skuWarehouseMappingRespList);




        // 查询佣金、成本信息
        PopSkuAdditionBatchQueryReq queryReq = new PopSkuAdditionBatchQueryReq();
        queryReq.setSkuAdditionQueryReqList(PopSkuAdditionConvert.toPopSkuAdditionQueryReqList(reqList));
        DubboResponse<PopSkuAdditionBatchResp> resp = popSkuAdditionQueryProvider.queryPopSkuAdditionBatch(queryReq);
        if (!resp.isSuccess()) {
            log.error("调用pms查询sku信息异常 reqList:{} , resp:{}", reqList, JSON.toJSONString(resp));
            throw new ProviderException("调用pms查询sku信息异常:" + resp.getMsg());
        }
        PopSkuAdditionBatchResp respData = resp.getData();
        if (Objects.isNull(respData) || CollectionUtils.isEmpty(respData.getSkuAdditionRespList())) {
            log.warn("调用pms查询sku信息返回数据为空!");
            return Lists.newArrayList();
        }
        return PopSkuAdditionConvert.toPopSkuAdditionInfoDTOList(respData.getSkuAdditionRespList());
    }
    
    private void wrapReqList(List<PopSkuAdditionQueryDto> reqList, List<SkuWarehouseMappingResp> skuWarehouseMappingRespList){
        Map<String, SkuWarehouseMappingResp> collect = skuWarehouseMappingRespList.stream()
                .collect(Collectors.groupingBy(SkuWarehouseMappingResp::getSku,
                        Collectors.collectingAndThen(Collectors.toList(),
                                list -> list.isEmpty() ? null : list.get(0))));
        for (PopSkuAdditionQueryDto popSkuAdditionQueryDto : reqList) {
            SkuWarehouseMappingResp skuWarehouseMappingResp = collect.get(popSkuAdditionQueryDto.getSku());
            if(skuWarehouseMappingResp != null) {
                popSkuAdditionQueryDto.setWarehouseNo(skuWarehouseMappingResp.getWarehouseNo());
            }
        }
    }
}
