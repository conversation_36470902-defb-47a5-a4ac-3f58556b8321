package net.summerfarm.mall.facade.pms.converter;


import com.google.common.collect.Lists;
import net.summerfarm.mall.facade.pms.dto.PopSkuAdditionInfoDTO;
import net.summerfarm.mall.facade.pms.dto.PopSkuAdditionQueryDto;
import net.summerfarm.pms.client.req.pop.PopSkuAdditionBatchQueryReq;
import net.summerfarm.pms.client.resp.pop.PopSkuAdditionResp;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @Description
 * @Date 2024/6/27 11:22
 * @<AUTHOR>
 */
public class PopSkuAdditionConvert {


    private PopSkuAdditionConvert() {
        // 无需实现
    }

    public static List<PopSkuAdditionBatchQueryReq.PopSkuAdditionQueryReq> toPopSkuAdditionQueryReqList(List<PopSkuAdditionQueryDto> popSkuAdditionQueryDtoList) {

        if (popSkuAdditionQueryDtoList == null) {
            return Lists.newArrayList();
        }
        List<PopSkuAdditionBatchQueryReq.PopSkuAdditionQueryReq> popSkuAdditionQueryReqList = new ArrayList<>();
        for (PopSkuAdditionQueryDto popSkuAdditionQueryDto : popSkuAdditionQueryDtoList) {
            popSkuAdditionQueryReqList.add(toPopSkuAdditionQueryReq(popSkuAdditionQueryDto));
        }
        return popSkuAdditionQueryReqList;
    }

    public static PopSkuAdditionBatchQueryReq.PopSkuAdditionQueryReq toPopSkuAdditionQueryReq(PopSkuAdditionQueryDto popSkuAdditionQueryDto) {
        if (popSkuAdditionQueryDto == null) {
            return null;
        }
        PopSkuAdditionBatchQueryReq.PopSkuAdditionQueryReq popSkuAdditionQueryReq = new PopSkuAdditionBatchQueryReq.PopSkuAdditionQueryReq();
        popSkuAdditionQueryReq.setWarehouseNo(popSkuAdditionQueryDto.getWarehouseNo());
        popSkuAdditionQueryReq.setSku(popSkuAdditionQueryDto.getSku());
        popSkuAdditionQueryReq.setQuantity(popSkuAdditionQueryDto.getQuantity());
        return popSkuAdditionQueryReq;
    }


    public static List<PopSkuAdditionInfoDTO> toPopSkuAdditionInfoDTOList(List<PopSkuAdditionResp> popSkuAdditionRespList) {
        if (popSkuAdditionRespList == null) {
            return Lists.newArrayList();
        }
        List<PopSkuAdditionInfoDTO> popSkuAdditionInfoDTOList = new ArrayList<>();
        for (PopSkuAdditionResp popSkuAdditionResp : popSkuAdditionRespList) {
            popSkuAdditionInfoDTOList.add(toPopSkuAdditionInfoDTO(popSkuAdditionResp));
        }
        return popSkuAdditionInfoDTOList;
    }

    public static PopSkuAdditionInfoDTO toPopSkuAdditionInfoDTO(PopSkuAdditionResp popSkuAdditionResp) {
        if (popSkuAdditionResp == null) {
            return null;
        }
        PopSkuAdditionInfoDTO popSkuAdditionInfoDTO = new PopSkuAdditionInfoDTO();
        PopSkuAdditionResp.SkuKickbackAddition skuKickbackAddition = popSkuAdditionResp.getTargetSkuKickbackAddition();
        PopSkuAdditionResp.SkuPackupAddition skuPackupAddition = popSkuAdditionResp.getSkuPackupAddition();
        popSkuAdditionInfoDTO.setSku(popSkuAdditionResp.getSku());
        if(skuKickbackAddition != null) {
            popSkuAdditionInfoDTO.setKickbackRatio(skuKickbackAddition.getKickbackRatio());
            popSkuAdditionInfoDTO.setQuantity(skuKickbackAddition.getQuantity());
        }
        if(skuPackupAddition != null) {
            popSkuAdditionInfoDTO.setWarehouseNo(skuPackupAddition.getWarehouseNo());
            popSkuAdditionInfoDTO.setSupplierId(skuPackupAddition.getSupplierId());
            popSkuAdditionInfoDTO.setSkuWeight(skuPackupAddition.getSkuWeight());
            popSkuAdditionInfoDTO.setSupplierWeightPrice(skuPackupAddition.getSupplierWeightPrice());
            popSkuAdditionInfoDTO.setTotalUnitCost(skuPackupAddition.getSupplierCost());
            popSkuAdditionInfoDTO.setQuoteType(skuPackupAddition.getQuoteType());
        }
        return popSkuAdditionInfoDTO;
    }

}
