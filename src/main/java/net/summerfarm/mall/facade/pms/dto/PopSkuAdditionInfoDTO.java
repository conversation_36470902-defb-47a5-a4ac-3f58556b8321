package net.summerfarm.mall.facade.pms.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description
 * @Date 2024/6/27 11:11
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PopSkuAdditionInfoDTO implements Serializable {

    private String sku;
    private Integer areaNo;

    /**
     * 数量
     */
    private Integer quantity;
    /**
     * 库存仓
     */
    private Long warehouseNo;
    /**
     * 供应商
     *
     */
    private Long supplierId;
    /**
     * 佣金比例
     */
    private BigDecimal kickbackRatio;

    /**
     * 供应商毛重成本
     */
    private BigDecimal supplierWeightPrice;

    /**
     * 毛重
     */
    private BigDecimal skuWeight;

    /**
     * 整件成本（单位：元）
     */
    private BigDecimal totalUnitCost;

    /**
     * 供应商报价类型
     */
    private Integer quoteType;

}
