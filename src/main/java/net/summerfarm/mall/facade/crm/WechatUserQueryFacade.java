package net.summerfarm.mall.facade.crm;


import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.client.dto.WechatUserInfoDTO;
import net.summerfarm.crm.client.enums.BdQrCodeQueryChannelEnum;
import net.summerfarm.crm.client.provider.WechatUserQueryProvider;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class WechatUserQueryFacade {

    @DubboReference
    private WechatUserQueryProvider wechatUserQueryProvider;

    public String queryBdQrCodeByMidAndChannel(Long mid, BdQrCodeQueryChannelEnum channel) {
        DubboResponse<String> response = wechatUserQueryProvider.queryBdQrCodeByMidAndChannel(mid, channel);
        if (response.isSuccess()) {
            return response.getData();
        }
        throw new ProviderException(response.getMsg());
    }

    public List<WechatUserInfoDTO> selectWechatUserInfoByUnionId(String unionId) {
        DubboResponse<List<WechatUserInfoDTO>> response = wechatUserQueryProvider.selectWechatUserInfoByUnionId(unionId);
        if (response.isSuccess()) {
            return response.getData();
        }
        throw new ProviderException(response.getMsg());
    }

}
