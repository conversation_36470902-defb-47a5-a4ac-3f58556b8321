package net.summerfarm.mall.facade.engine;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.common.util.PageInfoHelper;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.engine.client.provider.item.MarketQueryProvider;
import net.summerfarm.mall.engine.client.req.EsHomeProductQueryConditionReq;
import net.summerfarm.mall.engine.client.req.EsHomeProductQueryReq;
import net.summerfarm.mall.engine.client.resp.EsMarketItemInfoResp;
import net.summerfarm.mall.engine.client.resp.EsSuggestWordResp;
import net.summerfarm.mall.facade.engine.convert.EsSuggestWordConvert;
import net.summerfarm.mall.facade.engine.convert.MarketRespConvert;
import net.summerfarm.mall.facade.engine.model.EsMarketItemInfoDTO;
import net.summerfarm.mall.model.vo.EsProductVO;
import net.summerfarm.mall.model.vo.MerchantSubject;
import net.summerfarm.mall.model.vo.SearchConditionVO;
import net.summerfarm.mall.service.SkuQueryService;
import net.summerfarm.mall.service.search.ProductSearchDisperseBySpuService;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023-07-27
 * @description
 */
@Component
@Slf4j
public class MarketQueryProviderFacade {

    @DubboReference
    private MarketQueryProvider marketQueryProvider;
    @NacosValue(value = "${search.maxScore.percentage:70}", autoRefreshed = true)
    private Integer percentage;
    @Resource
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private SkuQueryService skuQueryService;

//    类目对应商品列表 /搜索
    public PageInfo<EsMarketItemInfoDTO> homeProductQuery(EsHomeProductQueryReq esHomeProductQueryReq, EsHomeProductQueryConditionReq conditionReq) {
        try {
            DubboResponse<PageInfo<EsMarketItemInfoResp>> dubboResponse = marketQueryProvider.homeProductQuery(esHomeProductQueryReq, conditionReq);
            return getEsMarketItemInfoDTOPageInfo(dubboResponse);
        } catch (Exception e) {
            log.error("homeProductQuery error:{}", e);
            return PageInfoHelper.createPageInfo(new ArrayList<>());
        }
    }
    public PageInfo<EsMarketItemInfoDTO> homeProductQueryWithCoreRpc(EsHomeProductQueryReq esHomeProductQueryReq, EsHomeProductQueryConditionReq conditionReq) {
        try {
            DubboResponse<PageInfo<EsMarketItemInfoResp>> dubboResponse = marketQueryProvider.homeProductQueryWithCore(esHomeProductQueryReq, conditionReq);
            PageInfo<EsMarketItemInfoDTO> esMarketItemInfoDTOPageInfo = getEsMarketItemInfoDTOPageInfo (dubboResponse);
            if (ObjectUtil.isNotNull (esHomeProductQueryReq.getSortBy ()) && esHomeProductQueryReq.getSortBy () == 2) {
                if(esHomeProductQueryReq.getSortDirection () == 2){
                    List<EsMarketItemInfoDTO> collect = esMarketItemInfoDTOPageInfo.getList()
                            .stream()
                            .sorted(Comparator.comparing(EsMarketItemInfoDTO::getPrice).reversed()) // 降序排序
                            .collect(Collectors.toList());
                    esMarketItemInfoDTOPageInfo.setList (collect);
                }else{
                    List<EsMarketItemInfoDTO> collect = esMarketItemInfoDTOPageInfo.getList ().stream ().sorted (Comparator.comparing (EsMarketItemInfoDTO::getPrice)).collect (Collectors.toList ());
                    esMarketItemInfoDTOPageInfo.setList (collect);
                }
            }
            return esMarketItemInfoDTOPageInfo;
        } catch (Exception e) {
            log.error("homeProductQuery error:{}", e);
            return PageInfoHelper.createPageInfo(new ArrayList<>());
        }
    }
//    搜索优化
    public PageInfo<EsMarketItemInfoDTO> homeProductQueryWithCore(EsHomeProductQueryReq esHomeProductQueryReq, EsHomeProductQueryConditionReq conditionReq) {
        int pageSize = esHomeProductQueryReq.getPageSize();
        int pageIndex = esHomeProductQueryReq.getPageIndex();
        try {
            esHomeProductQueryReq.setPageSize(200 + pageSize * pageIndex);// 先查询200条+pageSize
            esHomeProductQueryReq.setPageIndex(1); // 固定从page 1开始
            PageInfo<EsMarketItemInfoDTO> esPageInfo = homeProductQueryWithCoreRpc(esHomeProductQueryReq,conditionReq);

            List<EsMarketItemInfoDTO> all = esPageInfo.getList ();
            if(CollectionUtil.isEmpty (all)){
                return PageInfoHelper.createPageInfo(new ArrayList<>());
            }

            EsMarketItemInfoDTO esMarketItemInfoDTO = esPageInfo.getList ().get (0);
            float maxScore = esMarketItemInfoDTO.getMaxScore () * percentage / 100;
            List<EsMarketItemInfoDTO> esMarketItemInfoDTOs = esPageInfo.getList().stream().filter (o -> o.getScore ()>maxScore).collect(Collectors.toList());

            skuQueryService.fillCondition(esHomeProductQueryReq,esMarketItemInfoDTOs);

            // 对排序完的esMarketItemInfoDTOs根据pageSize，pageIndex做分页
            int start = Math.max(0, (pageIndex - 1) * pageSize);
            if (start >= esMarketItemInfoDTOs.size()) {
                log.warn("分页参数错误，start:{}, size:{}", start, esMarketItemInfoDTOs.size());
                esPageInfo.setList(Collections.EMPTY_LIST);
            }
            int end = Math.min(start + pageSize, esMarketItemInfoDTOs.size());
            esPageInfo.setList(esMarketItemInfoDTOs.subList(start, end));
            esPageInfo.setTotal (esMarketItemInfoDTOs.size());
            return esPageInfo;
        } catch (Exception e) {
            log.error("homeProductQueryWithCore error:{}", e);
            return PageInfoHelper.createPageInfo(new ArrayList<>());
        }
    }


    public List<EsProductVO> querySuggestWord(EsHomeProductQueryReq esHomeProductQueryReq, EsHomeProductQueryConditionReq conditionReq) {
        try {
            DubboResponse<List<EsSuggestWordResp>> dubboResponse = marketQueryProvider.querySuggestWord(esHomeProductQueryReq, conditionReq);
            if (dubboResponse.isSuccess()) {
                return EsSuggestWordConvert.toEsProductVOList(dubboResponse.getData());
            }
        } catch (Exception e) {
            log.error("querySuggestWord error:{}", e);
        }
        return Lists.newArrayList();
    }


//    商品推荐查询接口
    public PageInfo<EsMarketItemInfoDTO> getRecommendMarketItemEsInfoEntity(EsHomeProductQueryReq esHomeProductQueryReq, EsHomeProductQueryConditionReq conditionReq) {
        try {
            DubboResponse<PageInfo<EsMarketItemInfoResp>> dubboResponse = marketQueryProvider.getRecommendMarketItemEsInfoEntity(esHomeProductQueryReq, conditionReq);
            return getEsMarketItemInfoDTOPageInfo(dubboResponse);
        } catch (Exception e) {
            log.error("getRecommendMarketItemEsInfoEntity error:{}", e);
            return PageInfoHelper.createPageInfo(new ArrayList<>());
        }
    }

    public PageInfo<EsMarketItemInfoDTO> getCouponMarketItemEsInfoEntity(EsHomeProductQueryReq esHomeProductQueryReq, EsHomeProductQueryConditionReq conditionReq) {
        try {
            DubboResponse<PageInfo<EsMarketItemInfoResp>> dubboResponse = marketQueryProvider.getCouponMarketItemEsInfoEntity(esHomeProductQueryReq, conditionReq);
            return getEsMarketItemInfoDTOPageInfo(dubboResponse);
        } catch (Exception e) {
            log.error("getCouponMarketItemEsInfoEntity error:{}", e);
            return PageInfo.emptyPageInfo();
        }
    }

    public PageInfo<EsMarketItemInfoDTO> getHomeCommonRecommendMarketItemEsInfoEntity(EsHomeProductQueryReq esHomeProductQueryReq, EsHomeProductQueryConditionReq conditionReq) {
        try {
            DubboResponse<PageInfo<EsMarketItemInfoResp>> dubboResponse = marketQueryProvider.getHomeCommonRecommendMarketItemEsInfoEntity(esHomeProductQueryReq, conditionReq);
            return getEsMarketItemInfoDTOPageInfo(dubboResponse);
        } catch (Exception e) {
            log.error("getHomeCommonRecommendMarketItemEsInfoEntity error:{}", e);
            return PageInfo.emptyPageInfo();
        }
    }

    private PageInfo<EsMarketItemInfoDTO> getEsMarketItemInfoDTOPageInfo(DubboResponse<PageInfo<EsMarketItemInfoResp>> dubboResponse) {
        if (dubboResponse.isSuccess()) {
            PageInfo<EsMarketItemInfoResp> pageInfoResp = dubboResponse.getData();
            if (!CollectionUtil.isEmpty(pageInfoResp.getList())) {
                PageInfo pageInfo = new PageInfo();
                pageInfo.setPageNum(pageInfoResp.getPageNum());
                pageInfo.setPageSize(pageInfoResp.getPageSize());
                pageInfo.setSize(pageInfoResp.getSize());
                pageInfo.setStartRow(pageInfoResp.getStartRow());
                pageInfo.setEndRow(pageInfoResp.getEndRow());
                pageInfo.setPages(pageInfoResp.getPages());
                pageInfo.setPrePage(pageInfoResp.getPrePage());
                pageInfo.setNextPage(pageInfoResp.getNextPage());
                pageInfo.setIsFirstPage(pageInfoResp.isIsFirstPage());
                pageInfo.setIsLastPage(pageInfoResp.isIsLastPage());
                pageInfo.setHasPreviousPage(pageInfoResp.isHasPreviousPage());
                pageInfo.setHasNextPage(pageInfoResp.isHasNextPage());
                pageInfo.setNavigatePages(pageInfoResp.getNavigatePages());
                pageInfo.setNavigatepageNums(pageInfoResp.getNavigatepageNums());
                pageInfo.setNavigateFirstPage(pageInfoResp.getNavigateFirstPage());
                pageInfo.setNavigateLastPage(pageInfoResp.getNavigateLastPage());
                pageInfo.setTotal(pageInfoResp.getTotal());
                List<EsMarketItemInfoDTO> esMarketItemInfoDTOList = MarketRespConvert.toEsMarketItemInfoDTOList(dubboResponse.getData().getList());
                pageInfo.setList(esMarketItemInfoDTOList);
                pageInfo.setIsLastPage(dubboResponse.getData().isIsLastPage());
                return pageInfo;
            }
        }
        return PageInfo.emptyPageInfo();
    }
}
