package net.summerfarm.mall.facade.engine.convert;

import net.summerfarm.mall.engine.client.resp.EsSuggestWordResp;
import net.summerfarm.mall.model.vo.EsProductVO;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023-07-31
 * @description
 */
public class EsSuggestWordConvert {

    private EsSuggestWordConvert() {
        // 无需实现
    }

    public static List<EsProductVO> toEsProductVOList(List<EsSuggestWordResp> esSuggestWordRespList) {
        if (esSuggestWordRespList == null) {
            return Collections.emptyList();
        }
        List<EsProductVO> esProductVOList = new ArrayList<>();
        for (EsSuggestWordResp esSuggestWordResp : esSuggestWordRespList) {
            esProductVOList.add(toEsProductVO(esSuggestWordResp));
        }
        return esProductVOList;
    }

    public static EsProductVO toEsProductVO(EsSuggestWordResp esSuggestWordResp) {
        if (esSuggestWordResp == null) {
            return null;
        }
        EsProductVO esProductVO = new EsProductVO();
        esProductVO.setPdId(esSuggestWordResp.getOutId());
        esProductVO.setPdName(esSuggestWordResp.getTitle());
        esProductVO.setScore(esSuggestWordResp.getScore());
        return esProductVO;
    }

    public static List<EsSuggestWordResp> toEsSuggestWordRespList(List<EsProductVO> esProductVOList) {
        if (esProductVOList == null) {
            return Collections.emptyList();
        }
        List<EsSuggestWordResp> esSuggestWordRespList = new ArrayList<>();
        for (EsProductVO esProductVO : esProductVOList) {
            esSuggestWordRespList.add(toEsSuggestWordResp(esProductVO));
        }
        return esSuggestWordRespList;
    }

    public static EsSuggestWordResp toEsSuggestWordResp(EsProductVO esProductVO) {
        if (esProductVO == null) {
            return null;
        }
        EsSuggestWordResp esSuggestWordResp = new EsSuggestWordResp();
        esSuggestWordResp.setScore(esProductVO.getScore());
// Not mapped TO fields:
// outId
// title
// Not mapped FROM fields:
// pdId
// pdName
        return esSuggestWordResp;
    }
}
