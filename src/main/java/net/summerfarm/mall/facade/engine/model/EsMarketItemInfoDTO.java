package net.summerfarm.mall.facade.engine.model;

import lombok.Data;
import net.summerfarm.mall.model.vo.ProductInfoVO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023-07-31
 * @description
 */
@Data
public class EsMarketItemInfoDTO {
    private String id;
    /**
     * =====================market===================
     */
    private Long marketId;
    private String title;
    private String titleExt;
    private String titlePure;
    private String subTitle;
    private Long categoryId;
    /**
     * 商品状态 0 删除 1 使用
     */
    private Integer deleteFlag;
    private String mainPicture;
    /**
     * pd_id
     */
    private Long outId;
    /**
     * products.other_slogan
     * 图文描述
     */
    private String slogan;

    /**
     * =====================marketItem===================
     */
    private Long marketItemId;
    /**
     * 规格
     */
    private String specification;
    /**
     * 规格单位
     */
    private String specificationUnit;
    /**
     * inventory  sku
     */
    private String itemCode;

    private String marketItemTitle;
    /**
     * sku pic
     */
    private String marketItemMainPicture;
    /**
     * 最小起售  inventory.baseSaleQuantity
     */
    private Integer miniOrderQuantity;

    private Integer marketItemDeleteFlag;
    /**
     * sku性质(扩展类型)：0、常规 2、临保 3、拆包 4、不卖 5、破袋
     */
    private Integer extType;

    /**
     * 商品二级性质，1 自营-代销不入仓、2 自营-代销入仓、3 自营-经销、4 代仓-代仓
     */
    private Integer subType;

    private Integer baseSaleUnit;
    /**
     * 买手ID
     */
    private Long buyerId;

    /**
     * 买手名称
     */
    private String buyerName;

    /**
     * 净重
     */
    private BigDecimal netWeightNum;
    /**
     * 毛重
     */
    private BigDecimal weightNum;

    /**
     * 净重单位
     */
    private String netWeightUnit;

    /**
     * 视频链接
     */
    private String videoUrl;
    /**
     * 售后规则详情
     */
    private String afterSaleRuleDetail;
    /**
     * =====================onsale===================
     */
    /**
     * area_sku  area_no
     */
    private Integer targetId;
    /**
     * 是否大客户专享
     */
    private Integer mType;
    /**
     * 商城是否展示
     */
    private Integer show;
    /**
     * 是否上架
     */
    private Integer onSale;
    /**
     * 上架策略
     */
    private Integer onSaleStrategyType;
    /**
     * ===============================products相关============================================
     */
    /**
     * 保质期时常
     */
    private Integer qualityTime;
    /**
     * 保质期单位
     */
    private String qualityTimeUnit;

    /**
     * ===============================front_category相关============================================
     */
    /**
     * 前台类目
     */
    private List<Map<String, String>> category;

    /**
     * ===============================products_properties相关============================================
     */
    /**
     * 销售属性值（口味）
     */
    private List<String> propertyValues;
    /**
     * 关键属性
     */
    private List<String> keyProperty;

    private String brandName;

    /**
     * ===============================AREA_SKU相关============================================
     */
    /**
     * 销售模式
     */
    private Integer salesMode;
    /**
     * 限购数量
     */
    private Integer limitedQuantity;
    /**
     * 排序
     */
    private Integer priority;
    private Integer fixFlag;
    private Integer fixNum;
    private BigDecimal price;
    /**
     * ===============================interestRate相关============================================
     */
    /**
     * 毛利率
     */
    private BigDecimal interestRate;
    /**
     * ===============================仓库相关============================================
     */
    /**
     * 核心用户是否售罄
     */
    private Integer coreSaleOut;
    /**
     * 普通用户是否售罄
     */
    private Integer saleOut;
    /**
     * 普通用户库存
     */
    private Integer storeQuantity;
    /**
     * 核心用户库存
     */
    private Integer coreStoreQuantity;
    /**
     * 库存仓编号
     */
    private Integer warehouseNo;

    /**
     * es score
     */
    private float score;

    /**
     * es maxScore
     */
    private float maxScore;

    public static ProductInfoVO convertToProductInfoVO(EsMarketItemInfoDTO esInfo, boolean isCoreCustomer) {
        ProductInfoVO productInfoVO = new ProductInfoVO();
        productInfoVO.setBaseSaleUnit(esInfo.getBaseSaleUnit());
        productInfoVO.setBaseSaleQuantity(esInfo.getMiniOrderQuantity());
        productInfoVO.setSku(esInfo.getItemCode());
        productInfoVO.setSkuName(esInfo.getMarketItemTitle());
        productInfoVO.setSalesMode(esInfo.getSalesMode());
        if(esInfo.getLimitedQuantity() != null){
            productInfoVO.setLimitedQuantity(esInfo.getLimitedQuantity());
        }
        productInfoVO.setPdName(esInfo.getTitle());
        productInfoVO.setCategoryId(esInfo.getCategoryId().intValue());
        productInfoVO.setPddetail(esInfo.getSubTitle());
        productInfoVO.setUnit(esInfo.getSpecificationUnit());
        productInfoVO.setWeight(esInfo.getSpecification());
        productInfoVO.setPicturePath(esInfo.getMainPicture());
        productInfoVO.setPdId(esInfo.getOutId());
//        productInfoVO.setInfo(esAreaSkuInfo.getInfo());
//        productInfoVO.setSlogan(esAreaSkuInfo.getSlogan());
        productInfoVO.setOtherSlogan(esInfo.getSlogan());
//        productInfoVO.setShowAdvance(esAreaSkuInfo.getShowAdvance() == 1);
//        productInfoVO.setAdvance(esAreaSkuInfo.getAdvance());
        productInfoVO.setSkuPic(esInfo.getMarketItemMainPicture());
        productInfoVO.setFixNum(esInfo.getFixNum());
        productInfoVO.setQualityTime(esInfo.getQualityTime());
        productInfoVO.setQualityTimeUnit(esInfo.getQualityTimeUnit());
        productInfoVO.setExtType(esInfo.getExtType());
        productInfoVO.setSubType(esInfo.getSubType());
        if(isCoreCustomer){
            if(esInfo.getCoreStoreQuantity() == null){
                productInfoVO.setQuantity(0);
                productInfoVO.setOnlineQuantity(0);
            }else{
                productInfoVO.setQuantity(esInfo.getCoreStoreQuantity());
                productInfoVO.setOnlineQuantity(esInfo.getCoreStoreQuantity());
            }
        }else{
            if(esInfo.getStoreQuantity() == null){
                productInfoVO.setQuantity(0);
                productInfoVO.setOnlineQuantity(0);
            }else{
                productInfoVO.setQuantity(esInfo.getStoreQuantity());
                productInfoVO.setOnlineQuantity(esInfo.getStoreQuantity());
            }

        }
        productInfoVO.setBuyerId(esInfo.getBuyerId ());
        productInfoVO.setBuyerName(esInfo.getBuyerName ());
        productInfoVO.setNetWeightNum(esInfo.getNetWeightNum ());
        productInfoVO.setWeightNum(esInfo.getWeightNum ());
        productInfoVO.setVideoUrl(esInfo.getVideoUrl ());
        productInfoVO.setNetWeightUnit (esInfo.getNetWeightUnit ());
        productInfoVO.setAfterSaleRuleDetail(esInfo.getAfterSaleRuleDetail ());
        productInfoVO.setEsScore (esInfo.getScore ());
        productInfoVO.setMaxScore (esInfo.getMaxScore ());
        return productInfoVO;
    }
}
