package net.summerfarm.mall.facade.engine.convert;

import net.summerfarm.mall.engine.client.resp.EsMarketItemInfoResp;
import net.summerfarm.mall.facade.engine.model.EsMarketItemInfoDTO;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023-07-31
 * @description
 */
public class MarketRespConvert {

    private MarketRespConvert() {
        // 无需实现
    }

    public static List<EsMarketItemInfoDTO> toEsMarketItemInfoDTOList(List<EsMarketItemInfoResp> esMarketItemInfoRespList) {
        if (esMarketItemInfoRespList == null) {
            return Collections.emptyList();
        }
        List<EsMarketItemInfoDTO> esMarketItemInfoDTOList = new ArrayList<>();
        for (EsMarketItemInfoResp esMarketItemInfoResp : esMarketItemInfoRespList) {
            esMarketItemInfoDTOList.add(toEsMarketItemInfoDTO(esMarketItemInfoResp));
        }
        return esMarketItemInfoDTOList;
    }

    public static EsMarketItemInfoDTO toEsMarketItemInfoDTO(EsMarketItemInfoResp esMarketItemInfoResp) {
        if (esMarketItemInfoResp == null) {
            return null;
        }
        EsMarketItemInfoDTO esMarketItemInfoDTO = new EsMarketItemInfoDTO();
        esMarketItemInfoDTO.setId(esMarketItemInfoResp.getId());
        esMarketItemInfoDTO.setMarketId(esMarketItemInfoResp.getMarketId());
        esMarketItemInfoDTO.setTitle(esMarketItemInfoResp.getTitle());
        esMarketItemInfoDTO.setTitleExt(esMarketItemInfoResp.getTitleExt());
        esMarketItemInfoDTO.setTitlePure(esMarketItemInfoResp.getTitlePure());
        esMarketItemInfoDTO.setSubTitle(esMarketItemInfoResp.getSubTitle());
        esMarketItemInfoDTO.setCategoryId(esMarketItemInfoResp.getCategoryId());
        esMarketItemInfoDTO.setDeleteFlag(esMarketItemInfoResp.getDeleteFlag());
        esMarketItemInfoDTO.setMainPicture(esMarketItemInfoResp.getMainPicture());
        esMarketItemInfoDTO.setOutId(esMarketItemInfoResp.getOutId());
        esMarketItemInfoDTO.setSlogan(esMarketItemInfoResp.getSlogan());
        esMarketItemInfoDTO.setMarketItemId(esMarketItemInfoResp.getMarketItemId());
        esMarketItemInfoDTO.setSpecification(esMarketItemInfoResp.getSpecification());
        esMarketItemInfoDTO.setSpecificationUnit(esMarketItemInfoResp.getSpecificationUnit());
        esMarketItemInfoDTO.setItemCode(esMarketItemInfoResp.getItemCode());
        esMarketItemInfoDTO.setMarketItemTitle(esMarketItemInfoResp.getMarketItemTitle());
        esMarketItemInfoDTO.setMarketItemMainPicture(esMarketItemInfoResp.getMarketItemMainPicture());
        esMarketItemInfoDTO.setMiniOrderQuantity(esMarketItemInfoResp.getMiniOrderQuantity());
        esMarketItemInfoDTO.setMarketItemDeleteFlag(esMarketItemInfoResp.getMarketItemDeleteFlag());
        esMarketItemInfoDTO.setExtType(esMarketItemInfoResp.getExtType());
        esMarketItemInfoDTO.setSubType(esMarketItemInfoResp.getSubType());
        esMarketItemInfoDTO.setBaseSaleUnit(esMarketItemInfoResp.getBaseSaleUnit());
        esMarketItemInfoDTO.setTargetId(esMarketItemInfoResp.getTargetId());
        esMarketItemInfoDTO.setMType(esMarketItemInfoResp.getMType());
        esMarketItemInfoDTO.setShow(esMarketItemInfoResp.getShow());
        esMarketItemInfoDTO.setOnSale(esMarketItemInfoResp.getOnSale());
        esMarketItemInfoDTO.setOnSaleStrategyType(esMarketItemInfoResp.getOnSaleStrategyType());
        esMarketItemInfoDTO.setQualityTime(esMarketItemInfoResp.getQualityTime());
        esMarketItemInfoDTO.setQualityTimeUnit(esMarketItemInfoResp.getQualityTimeUnit());
        esMarketItemInfoDTO.setCategory(esMarketItemInfoResp.getCategory());
        esMarketItemInfoDTO.setPropertyValues(esMarketItemInfoResp.getPropertyValues());
        esMarketItemInfoDTO.setKeyProperty(esMarketItemInfoResp.getKeyProperty());
        esMarketItemInfoDTO.setBrandName(esMarketItemInfoResp.getBrandName());
        esMarketItemInfoDTO.setSalesMode(esMarketItemInfoResp.getSalesMode());
        esMarketItemInfoDTO.setLimitedQuantity(esMarketItemInfoResp.getLimitedQuantity());
        esMarketItemInfoDTO.setPriority(esMarketItemInfoResp.getPriority());
        esMarketItemInfoDTO.setFixFlag(esMarketItemInfoResp.getFixFlag());
        esMarketItemInfoDTO.setFixNum(esMarketItemInfoResp.getFixNum());
        esMarketItemInfoDTO.setInterestRate(esMarketItemInfoResp.getInterestRate());
        esMarketItemInfoDTO.setCoreSaleOut(esMarketItemInfoResp.getCoreSaleOut());
        esMarketItemInfoDTO.setSaleOut(esMarketItemInfoResp.getSaleOut());
        esMarketItemInfoDTO.setStoreQuantity(esMarketItemInfoResp.getStoreQuantity());
        esMarketItemInfoDTO.setCoreStoreQuantity(esMarketItemInfoResp.getCoreStoreQuantity());
        esMarketItemInfoDTO.setWarehouseNo(esMarketItemInfoResp.getWarehouseNo());
        esMarketItemInfoDTO.setBuyerId(esMarketItemInfoResp.getBuyerId ());
        esMarketItemInfoDTO.setBuyerName(esMarketItemInfoResp.getBuyerName ());
        esMarketItemInfoDTO.setNetWeightNum(esMarketItemInfoResp.getNetWeightNum ());
        esMarketItemInfoDTO.setWeightNum(esMarketItemInfoResp.getWeightNum ());
        esMarketItemInfoDTO.setNetWeightUnit(esMarketItemInfoResp.getNetWeightUnit ());
        esMarketItemInfoDTO.setVideoUrl(esMarketItemInfoResp.getVideoUrl ());
        esMarketItemInfoDTO.setAfterSaleRuleDetail(esMarketItemInfoResp.getAfterSaleRuleDetail ());
        esMarketItemInfoDTO.setScore (esMarketItemInfoResp.getScore ());
        esMarketItemInfoDTO.setMaxScore (esMarketItemInfoResp.getMaxScore ());
        esMarketItemInfoDTO.setPrice (esMarketItemInfoResp.getPrice ());
        return esMarketItemInfoDTO;
    }

    public static List<EsMarketItemInfoResp> toEsMarketItemInfoRespList(List<EsMarketItemInfoDTO> esMarketItemInfoDTOList) {
        if (esMarketItemInfoDTOList == null) {
            return Collections.emptyList();
        }
        List<EsMarketItemInfoResp> esMarketItemInfoRespList = new ArrayList<>();
        for (EsMarketItemInfoDTO esMarketItemInfoDTO : esMarketItemInfoDTOList) {
            esMarketItemInfoRespList.add(toEsMarketItemInfoResp(esMarketItemInfoDTO));
        }
        return esMarketItemInfoRespList;
    }

    public static EsMarketItemInfoResp toEsMarketItemInfoResp(EsMarketItemInfoDTO esMarketItemInfoDTO) {
        if (esMarketItemInfoDTO == null) {
            return null;
        }
        EsMarketItemInfoResp esMarketItemInfoResp = new EsMarketItemInfoResp();
        esMarketItemInfoResp.setId(esMarketItemInfoDTO.getId());
        esMarketItemInfoResp.setMarketId(esMarketItemInfoDTO.getMarketId());
        esMarketItemInfoResp.setTitle(esMarketItemInfoDTO.getTitle());
        esMarketItemInfoResp.setTitleExt(esMarketItemInfoDTO.getTitleExt());
        esMarketItemInfoResp.setTitlePure(esMarketItemInfoDTO.getTitlePure());
        esMarketItemInfoResp.setSubTitle(esMarketItemInfoDTO.getSubTitle());
        esMarketItemInfoResp.setCategoryId(esMarketItemInfoDTO.getCategoryId());
        esMarketItemInfoResp.setDeleteFlag(esMarketItemInfoDTO.getDeleteFlag());
        esMarketItemInfoResp.setMainPicture(esMarketItemInfoDTO.getMainPicture());
        esMarketItemInfoResp.setOutId(esMarketItemInfoDTO.getOutId());
        esMarketItemInfoResp.setSlogan(esMarketItemInfoDTO.getSlogan());
        esMarketItemInfoResp.setMarketItemId(esMarketItemInfoDTO.getMarketItemId());
        esMarketItemInfoResp.setSpecification(esMarketItemInfoDTO.getSpecification());
        esMarketItemInfoResp.setSpecificationUnit(esMarketItemInfoDTO.getSpecificationUnit());
        esMarketItemInfoResp.setItemCode(esMarketItemInfoDTO.getItemCode());
        esMarketItemInfoResp.setMarketItemTitle(esMarketItemInfoDTO.getMarketItemTitle());
        esMarketItemInfoResp.setMarketItemMainPicture(esMarketItemInfoDTO.getMarketItemMainPicture());
        esMarketItemInfoResp.setMiniOrderQuantity(esMarketItemInfoDTO.getMiniOrderQuantity());
        esMarketItemInfoResp.setMarketItemDeleteFlag(esMarketItemInfoDTO.getMarketItemDeleteFlag());
        esMarketItemInfoResp.setExtType(esMarketItemInfoDTO.getExtType());
        esMarketItemInfoResp.setBaseSaleUnit(esMarketItemInfoDTO.getBaseSaleUnit());
        esMarketItemInfoResp.setTargetId(esMarketItemInfoDTO.getTargetId());
        esMarketItemInfoResp.setMType(esMarketItemInfoDTO.getMType());
        esMarketItemInfoResp.setShow(esMarketItemInfoDTO.getShow());
        esMarketItemInfoResp.setOnSale(esMarketItemInfoDTO.getOnSale());
        esMarketItemInfoResp.setOnSaleStrategyType(esMarketItemInfoDTO.getOnSaleStrategyType());
        esMarketItemInfoResp.setQualityTime(esMarketItemInfoDTO.getQualityTime());
        esMarketItemInfoResp.setQualityTimeUnit(esMarketItemInfoDTO.getQualityTimeUnit());
        esMarketItemInfoResp.setCategory(esMarketItemInfoDTO.getCategory());
        esMarketItemInfoResp.setPropertyValues(esMarketItemInfoDTO.getPropertyValues());
        esMarketItemInfoResp.setKeyProperty(esMarketItemInfoDTO.getKeyProperty());
        esMarketItemInfoResp.setBrandName(esMarketItemInfoDTO.getBrandName());
        esMarketItemInfoResp.setPrice (esMarketItemInfoDTO.getPrice ());
        esMarketItemInfoResp.setSalesMode(esMarketItemInfoDTO.getSalesMode());
        esMarketItemInfoResp.setLimitedQuantity(esMarketItemInfoDTO.getLimitedQuantity());
        esMarketItemInfoResp.setPriority(esMarketItemInfoDTO.getPriority());
        esMarketItemInfoResp.setFixFlag(esMarketItemInfoDTO.getFixFlag());
        esMarketItemInfoResp.setFixNum(esMarketItemInfoDTO.getFixNum());
        esMarketItemInfoResp.setInterestRate(esMarketItemInfoDTO.getInterestRate());
        esMarketItemInfoResp.setCoreSaleOut(esMarketItemInfoDTO.getCoreSaleOut());
        esMarketItemInfoResp.setSaleOut(esMarketItemInfoDTO.getSaleOut());
        esMarketItemInfoResp.setStoreQuantity(esMarketItemInfoDTO.getStoreQuantity());
        esMarketItemInfoResp.setCoreStoreQuantity(esMarketItemInfoDTO.getCoreStoreQuantity());
        esMarketItemInfoResp.setWarehouseNo(esMarketItemInfoDTO.getWarehouseNo());
        return esMarketItemInfoResp;
    }
}
