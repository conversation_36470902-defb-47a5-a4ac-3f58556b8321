package net.summerfarm.mall.facade.inventory;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.inventory.client.productcost.ProductCostQueryProvider;
import net.xianmu.inventory.client.productcost.dto.req.ProductCostQueryReq;
import net.xianmu.inventory.client.productcost.dto.res.ProductCostQueryResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @descripton
 * @date 2024/1/17 11:35
 */

@Slf4j
@Component
public class ProductCostQueryFacade {

    @DubboReference
    private ProductCostQueryProvider productCostQueryProvider;

    /**
     * 获取成本价
     * @param sku
     * @param warehouseNo
     * @return
     */
    public BigDecimal selectCycleCost(String sku, Integer warehouseNo){
        if(StringUtils.isBlank(sku) || warehouseNo == null) {
            log.warn("参数异常：sku:{}, warehouseNo:{}", sku, warehouseNo);
            return null;
        }
        ProductCostQueryReq req = new ProductCostQueryReq();
        req.setSku(sku);
        req.setWarehouseNo(warehouseNo);
        DubboResponse<ProductCostQueryResp> response = productCostQueryProvider.queryProductCost(req);
        if (response.isSuccess()) {
            ProductCostQueryResp data = response.getData();
            return data == null ? null : data.getCurrentCost();
        }
        throw new BizException(response.getMsg());
    }
}
