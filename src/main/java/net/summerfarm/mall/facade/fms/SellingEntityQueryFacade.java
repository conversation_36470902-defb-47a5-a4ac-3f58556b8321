package net.summerfarm.mall.facade.fms;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.common.config.biz.SellingEntityConfig;
import net.summerfarm.mall.model.vo.agreement.SellingEntityDetailVO;
import net.xianmu.bms.client.provider.sellingEntity.SellingEntityQueryProvider;
import net.xianmu.bms.client.resp.sellingEntity.SellingEntityDetailInfoResp;
import net.xianmu.bms.client.resp.sellingEntity.SellingEntityInfoResp;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.exception.error.code.ProviderErrorCode;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/3/14 18:43
 * @Version 1.0
 */
@Slf4j
@Component
public class SellingEntityQueryFacade {

    @DubboReference
    private SellingEntityQueryProvider sellingEntityQueryProvider;
    @Autowired
    private SellingEntityConfig sellingEntityConfig;


    /**
     * 根据mId获取销售实体
     * @param mId mId
     * @return 销售实体名称
     */
    public String querySellingEntityByMId(Long mId) {
        try {
            return sellingEntityQueryProvider.querySellingEntityByMId(mId).getData().getSellingEntityName();
        } catch (Exception e) {
            log.error("【获取销售主体失败！】mId:{}, >>> {}", mId, e.getMessage(), e);
            return sellingEntityConfig.getDefaultSellingEntityName();
        }
    }

    /**
     * 获取默认销售实体
     * @return 销售实体
     **/
    public String queryDefaultSellingEntity(){
        try {
            return sellingEntityQueryProvider.queryDefaultSellingEntity().getData().getSellingEntityName();
        } catch (Exception e) {
            log.error("【获取默认销售主体失败！】>>> {}", e.getMessage(), e);
            return sellingEntityConfig.getDefaultSellingEntityName();
        }
    }

    /**
     * 获取所有销售实体信息
     * @return 销售实体信息
     */
    public List<SellingEntityDetailVO> queryAllSellingEntityInfo(){
        try {
            DubboResponse<List<SellingEntityDetailInfoResp>> dubboResponse = sellingEntityQueryProvider.queryAllSellingEntityList();
            if (null == dubboResponse || !dubboResponse.isSuccess() || null == dubboResponse.getData()){
                throw new ProviderException(dubboResponse == null ? ProviderErrorCode.DEFAULT_CODE : dubboResponse.getCode());
            }
            List<SellingEntityDetailVO> resultList = new ArrayList<>();
            for (SellingEntityDetailInfoResp sellingEntityDetail : dubboResponse.getData()) {
                resultList.add(new SellingEntityDetailVO(sellingEntityDetail.getSellingEntityName(), sellingEntityDetail.getIsDefault(), sellingEntityDetail.getAreaNameList()));
            }
            return resultList;
        } catch (Exception e) {
            log.error("【获取销售主体信息失败！】>>> {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
}
