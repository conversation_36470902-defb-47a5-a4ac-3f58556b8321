package net.summerfarm.mall.facade.auth;

import com.aliyun.odps.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.model.vo.merchant.subaccount.MerchantSubAccountQuery;
import net.summerfarm.mall.model.vo.merchant.subaccount.SubAccountLoginQuery;
import net.xianmu.authentication.client.dto.AuthLoginDto;
import net.xianmu.authentication.client.dto.login.AuthQueryWechatInfoDTO;
import net.xianmu.authentication.client.enums.AuthTypeEnum;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.login.AuthClientMallLoginProviderInput;
import net.xianmu.authentication.client.input.login.AuthQueryWechatInfoInput;
import net.xianmu.authentication.client.input.user.AuthUserAuthQueryInput;
import net.xianmu.authentication.client.provider.AuthClientLoginProvider;
import net.xianmu.authentication.client.provider.AuthUserAuthProvider;
import net.xianmu.authentication.client.resp.AuthUserAuthResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

import static net.summerfarm.mall.constant.Constants.XIANMU_TENANT_ID;
import static net.summerfarm.mall.constant.MerchantConstants.XM_TENANT_ID;

/**
 * 查询auth的接口信息
 *
 * <AUTHOR>
 * @date 2023/11/14 14:10
 */
@Slf4j
@Component
public class AuthQueryFacade {
    @DubboReference
    private AuthUserAuthProvider authUserAuthProvider;
    @DubboReference
    private AuthClientLoginProvider authClientLoginProvider;

    public List<AuthUserAuthResp> queryAuthUserAuthByAuthId(MerchantSubAccountQuery merchantSubAccountQuery){
        AuthUserAuthQueryInput authUserAuthQueryInput = new AuthUserAuthQueryInput();
        authUserAuthQueryInput.setSystemOriginEnum(SystemOriginEnum.MALL);
        authUserAuthQueryInput.setTenantId(XM_TENANT_ID);
        authUserAuthQueryInput.setAuthType(AuthTypeEnum.OFFICIAL_WE_CHAT);
        if (!StringUtils.isEmpty(merchantSubAccountQuery.getMpOpenId())){
            authUserAuthQueryInput.setAuthType(AuthTypeEnum.WEI_CHAT);
        }
        authUserAuthQueryInput.setMpOpenId(merchantSubAccountQuery.getMpOpenId());
        authUserAuthQueryInput.setOpenId(merchantSubAccountQuery.getOpenId());
        authUserAuthQueryInput.setUnionid(merchantSubAccountQuery.getUnionId());
        DubboResponse<List<AuthUserAuthResp>> listDubboResponse = authUserAuthProvider.queryAuthUserAuthByAuthId(authUserAuthQueryInput);
        if (!listDubboResponse.isSuccess()){
            throw new BizException(listDubboResponse.getMsg());
        }
        return listDubboResponse.getData();
    }

    public AuthLoginDto authLogin(SubAccountLoginQuery accountLoginQuery){
        AuthClientMallLoginProviderInput input = new AuthClientMallLoginProviderInput();
        input.setLoginType(accountLoginQuery.getAuthTypeEnum());
        input.setTenantId(XIANMU_TENANT_ID);
        input.setSystemOriginEnum(net.xianmu.common.enums.base.auth.SystemOriginEnum.MALL);
        input.setOpenid(accountLoginQuery.getOpenid());
        input.setUnionid(accountLoginQuery.getUnionid());
        input.setBizId(accountLoginQuery.getAccountId());
        DubboResponse<AuthLoginDto> authLoginDtoDubboResponse = authClientLoginProvider.authLogin(input);
        if (authLoginDtoDubboResponse.isSuccess()){
            return authLoginDtoDubboResponse.getData();
        }
        throw new BizException(authLoginDtoDubboResponse.getMsg());
    }

    public AuthQueryWechatInfoDTO queryWxInfo(SubAccountLoginQuery accountLoginQuery){
        boolean popMerchant = accountLoginQuery.isPopMerchant();
        AuthQueryWechatInfoInput input = new AuthQueryWechatInfoInput();
        input.setType(accountLoginQuery.getAuthTypeEnum());
        input.setCode(accountLoginQuery.getCode());
        DubboResponse<AuthQueryWechatInfoDTO> authLoginDtoDubboResponse;
        if(popMerchant) {
            authLoginDtoDubboResponse = authClientLoginProvider.authPopMallQueryWechatInfo(input);
        } else {
            authLoginDtoDubboResponse = authClientLoginProvider.authMallQueryWechatInfo(input);
        }
        if (authLoginDtoDubboResponse.isSuccess()){
            return authLoginDtoDubboResponse.getData();
        }
        throw new BizException(authLoginDtoDubboResponse.getMsg());
    }

}
