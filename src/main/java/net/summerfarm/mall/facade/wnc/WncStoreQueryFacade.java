package net.summerfarm.mall.facade.wnc;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.client.provider.warehouse.WarehouseLogisticsQueryProvider;
import net.summerfarm.wnc.client.req.WarehouseLogisticsQueryReq;
import net.summerfarm.wnc.client.resp.WarehousLogisticsCenterResp;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * wnc城配仓查询服务
 * <AUTHOR>
 * @date 2025/03/31 10:04
 */
@Slf4j
@Component
public class WncStoreQueryFacade {

    @DubboReference
    private WarehouseLogisticsQueryProvider warehouseLogisticsQueryProvider;


    /**
     * 根据城配仓号查询城配仓的履约类型
     * @param storeNo 城配仓号
     * @return 城配仓的履约类型
     */
    public Integer getFulfillmentTypeByStoreNo(Integer storeNo){
        if (null == storeNo){
            log.error("\n 查询城配仓的履约类型失败，城配仓号为空 \n");
            return null;
        }
        WarehouseLogisticsQueryReq warehouseLogisticsQueryReq = new WarehouseLogisticsQueryReq();
        warehouseLogisticsQueryReq.setStoreNo(storeNo);
        DubboResponse<List<WarehousLogisticsCenterResp>> dubboResponse = warehouseLogisticsQueryProvider.queryWarehouseLogisticsList(warehouseLogisticsQueryReq);
        if (null == dubboResponse || !dubboResponse.isSuccess()
                || CollectionUtils.isEmpty(dubboResponse.getData()) || null == dubboResponse.getData().get(0)){
            log.error("\n 查询城配仓的履约类型失败 dubboResponse >>> {} \n", JSON.toJSONString(dubboResponse));
            return null;
        }
        return dubboResponse.getData().get(0).getFulfillmentType();
    }

    /**
     * 根据城配仓号查询城配仓的履约类型
     * @param storeNoList 城配仓号List
     * @return 城配仓的履约类型
     */
    public Map<Integer,Integer> getFulfillmentTypeByStoreNoList(List<Integer> storeNoList){
        if (CollectionUtils.isEmpty(storeNoList)){
            return new HashMap<>();
        }
        WarehouseLogisticsQueryReq warehouseLogisticsQueryReq = new WarehouseLogisticsQueryReq();
        warehouseLogisticsQueryReq.setStoreNos(storeNoList.stream().distinct().collect(Collectors.toList()));
        DubboResponse<List<WarehousLogisticsCenterResp>> dubboResponse = warehouseLogisticsQueryProvider.queryWarehouseLogisticsList(warehouseLogisticsQueryReq);
        if (null == dubboResponse || !dubboResponse.isSuccess()
                || CollectionUtils.isEmpty(dubboResponse.getData()) || null == dubboResponse.getData().get(0)){
            log.error("\n 查询城配仓的履约类型失败 dubboResponse >>> {} \n", JSON.toJSONString(dubboResponse));
            return new HashMap<>();
        }
        Map<Integer,Integer> result = new HashMap<>();
        for (WarehousLogisticsCenterResp warehousLogisticsCenterResp : dubboResponse.getData()) {
            result.put(warehousLogisticsCenterResp.getStoreNo(),warehousLogisticsCenterResp.getFulfillmentType());
        }
        return result;
    }
}
