package net.summerfarm.mall.facade.wnc;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.mall.common.config.DynamicConfig;
import net.summerfarm.mall.constant.Constants;
import net.summerfarm.mall.service.facade.converter.WncDeliveryFenceConverter;
import net.summerfarm.mall.service.facade.dto.AreaQueryByContactReq;
import net.summerfarm.mall.service.facade.dto.AreaQueryRes;
import net.summerfarm.mall.service.facade.impl.WncDeliveryFenceQueryFacadeImpl;
import net.summerfarm.wnc.client.provider.fence.DeliveryFenceQueryProvider;
import net.summerfarm.wnc.client.provider.fence.PopFenceQueryProvider;
import net.summerfarm.wnc.client.provider.warehouse.WarehouseLogisticsQueryProvider;
import net.summerfarm.wnc.client.req.AreaQueryReq;
import net.summerfarm.wnc.client.req.fence.PopAddressAreaQueryReq;
import net.summerfarm.wnc.client.req.warehouse.PopWarehouseLogisticsQueryReq;
import net.summerfarm.wnc.client.resp.AreaQueryResp;
import net.summerfarm.wnc.client.resp.WarehousLogisticsCenterResp;
import net.summerfarm.wnc.client.resp.fence.PopAreaResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @descripton
 * @date 2024/6/19 15:31
 */
@Slf4j
@Component
public class FenceFacade {

    @DubboReference
    private WarehouseLogisticsQueryProvider warehouseLogisticsQueryProvider;
    @Resource
    private DynamicConfig dynamicConfig;
    @DubboReference
    private PopFenceQueryProvider popFenceQueryProvider;
    @DubboReference
    private DeliveryFenceQueryProvider deliveryFenceQueryProvider;

    /**
     * 获取pop商城的城配仓编号
     * @return
     */
    /**
     * 获取pop商城的城配仓编号
     * @return
     */
    public Integer getPopStoreNo(String city, String area, String poi, Long mid){
        PopWarehouseLogisticsQueryReq req = new PopWarehouseLogisticsQueryReq();
        req.setCity(city);
        req.setArea(area);
        req.setPoi(poi);
        req.setMerchantId(mid);
        req.setTenantId(Constants.XIANMU_TENANT_ID);
        DubboResponse<WarehousLogisticsCenterResp> dubboResponse = warehouseLogisticsQueryProvider.queryPopWarehouseLogistics(req);
        if (dubboResponse.isSuccess()) {
            WarehousLogisticsCenterResp data = dubboResponse.getData();
            return data == null ? null : data.getStoreNo();
        }
        throw new BizException(dubboResponse.getMsg());
    }


    public Integer getAreaNoByAddress(String city, String area, boolean isPopMerchant){
        log.info("查询门店运营区域：city:{}, area:{}, isPopMerchant:{}", city, area, isPopMerchant);
        Integer areaNo = null;
        if(isPopMerchant) {
            log.info("pop商城获取门店运营区域, city:{}, area:{}", city, area);
            areaNo = this.getPopAreaNoByAddress(city, area);
        } else {
            log.info("鲜沐商城获取门店运营区域, city:{}, area:{}", city, area);
            areaNo = this.getXmAreaByAddress(city, area);
        }
        if(Objects.isNull(areaNo)){
            throw new DefaultServiceException("该地址不在运营服务范围内");
        }
        return areaNo;
    }


    /**
     * pop查询运营区域,根据地址
     *
     * @return
     */
    public Integer getPopAreaNoByAddress(String city, String area){
        PopAddressAreaQueryReq areaQueryReq = new PopAddressAreaQueryReq();
        areaQueryReq.setCity(city);
        areaQueryReq.setArea(area);
        DubboResponse<PopAreaResp> dubboResponse = popFenceQueryProvider.queryPopAreaByAddress(areaQueryReq);
        if (null == dubboResponse || !dubboResponse.isSuccess()){
            log.warn("pop查询wnc获取区域信息失败：city:{}, area:{}, dubboResponse:{}", city, area, JSON.toJSONString(dubboResponse));
            throw new BizException("地址不在配送范围");
        }
        return dubboResponse.getData() == null ? null : dubboResponse.getData().getAreaNo();
    }


    public Integer getXmAreaByAddress(String city, String area) {
        AreaQueryReq areaQueryReq = new AreaQueryReq();
        areaQueryReq.setArea(area);
        areaQueryReq.setCity(city);
        DubboResponse<AreaQueryResp> dubboResponse = deliveryFenceQueryProvider.queryAreaByAddress(areaQueryReq);
        if (null == dubboResponse || !dubboResponse.isSuccess()){
            log.warn("xm查询wnc获取区域信息失败：city:{}, area:{}, dubboResponse:{}", city, area, JSON.toJSONString(dubboResponse));
            throw new BizException("地址不在配送范围");
        }
        return dubboResponse.getData() == null ? null : dubboResponse.getData().getAreaNo();
    }
}
