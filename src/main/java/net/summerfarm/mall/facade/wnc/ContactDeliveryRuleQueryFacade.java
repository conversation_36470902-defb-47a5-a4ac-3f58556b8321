package net.summerfarm.mall.facade.wnc;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.mall.common.config.DynamicConfig;
import net.summerfarm.mall.constant.Constants;
import net.summerfarm.wnc.client.provider.deliveryRule.ContactDeliveryRuleCommandProvider;
import net.summerfarm.wnc.client.provider.deliveryRule.ContactDeliveryRuleQueryProvider;
import net.summerfarm.wnc.client.provider.deliveryRuleConfig.ContactDeliveryRuleConfigCommandProvider;
import net.summerfarm.wnc.client.req.deliveryRuleConfig.ContactConfigCommandReq;
import net.summerfarm.wnc.client.req.deliveryRuleConfig.DeliveryRuleCommandReq;
import net.summerfarm.wnc.client.req.deliveryRuleConfig.DeliveryRuleConfigSaveOrUpdateCommandReq;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-manage
 * @description 地址配送周期
 * @date 2023/11/14 18:40:29
 */
@Slf4j
@Component
public class ContactDeliveryRuleQueryFacade {

    @DubboReference
    private ContactDeliveryRuleCommandProvider ruleCommandProvider;

    @DubboReference
    private ContactDeliveryRuleConfigCommandProvider configCommandProvider;

    @DubboReference
    private ContactDeliveryRuleQueryProvider ruleQueryProvider;

    @Resource
    private DynamicConfig dynamicConfig;



    /**
     * pop门店保存城配仓、配送周期
     */
    public void popDeliveryRuleConfigSaveOrUpdate(Long contactId, Integer storeNo) {
        String popDefaultDeliveryRule = dynamicConfig.getPopDefaultDeliveryRule();
        if (StringUtils.isBlank(popDefaultDeliveryRule) || storeNo == null || contactId == null) {
            log.error("配送周期：{}, 城配仓：{}， 客户地址id：{}", popDefaultDeliveryRule, storeNo, contactId);
            throw new BizException("pop客户设置城配仓失败!");
        }
        DeliveryRuleConfigSaveOrUpdateCommandReq req = new DeliveryRuleConfigSaveOrUpdateCommandReq();
        req.setTenantId(Constants.XIANMU_TENANT_ID);
        req.setContactId(contactId);
        ContactConfigCommandReq contactConfigCommandReq = new ContactConfigCommandReq();
        contactConfigCommandReq.setStoreNo(storeNo);
        req.setContactConfigCommandReq(contactConfigCommandReq);

        DeliveryRuleCommandReq deliveryRuleCommandReq = JSON.parseObject(popDefaultDeliveryRule, DeliveryRuleCommandReq.class);
        deliveryRuleCommandReq.setBeginCalculateDate(LocalDate.now());
        req.setDeliveryRuleCommandReq(deliveryRuleCommandReq);
        DubboResponse<Void> response = configCommandProvider.outerOrderDeliveryRuleConfigSaveOrUpdate(req);
        if (!response.isSuccess()) {
            throw new BizException("调整城配仓失败!");
        }
    }
}
