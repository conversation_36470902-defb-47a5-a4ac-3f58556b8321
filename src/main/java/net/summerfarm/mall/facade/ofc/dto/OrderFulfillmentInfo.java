package net.summerfarm.mall.facade.ofc.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/3/31 14:00
 * @Version 1.0
 */
@Data
@AllArgsConstructor
public class OrderFulfillmentInfo {

    /**
     * 订单配送方式： 0干配 1自提 2干线 3快递
     */
    private Integer orderFulfillmentType;

    /**
     * 物流信息
     */
    private List<LogisticsInfoDTO> logisticsInfoList;
}
