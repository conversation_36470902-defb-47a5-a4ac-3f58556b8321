package net.summerfarm.mall.facade.ofc;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.facade.ofc.dto.LogisticsInfoDTO;
import net.summerfarm.mall.facade.ofc.dto.OrderFulfillmentInfo;
import net.summerfarm.ofc.client.provider.FulfillmentOrderQueryProvider;
import net.summerfarm.ofc.client.resp.FulfillmentLogisticsDetailResp;
import net.summerfarm.ofc.client.resp.FulfillmentLogisticsResp;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * OFC 快递信息查询服务
 *
 * <AUTHOR>
 * @Date 2025/3/31 11:21
 * @Version 1.0
 */
@Slf4j
@Component
public class OfcLogisticsQueryFacade {

    @DubboReference
    private FulfillmentOrderQueryProvider fulfillmentOrderQueryProvider;

    /**
     * 根据订单号获取配送信息
     *
     * @param orderNo 订单号
     * @return OrderFulfillmentInfo
     */
    public OrderFulfillmentInfo queryNormalOrderLogisticsInfo(String orderNo) {
        if (StringUtils.isEmpty(orderNo)) {
            return null;
        }
        try {
            DubboResponse<List<FulfillmentLogisticsResp>> dubboResponse = fulfillmentOrderQueryProvider.queryOrderLogistics(Collections.singletonList(orderNo));
            if (null == dubboResponse || !dubboResponse.isSuccess()) {
                throw new ProviderException("服务提供方异常");
            }
            if (CollectionUtils.isEmpty(dubboResponse.getData())){
                return null;
            }
            return new OrderFulfillmentInfo(dubboResponse.getData().get(0).getFulfillmentWay(), buildLogisticsInfoDTOS(dubboResponse.getData().get(0).getDetailList()));
        } catch (Exception e) {
            log.error("查询订单的配送信息失败 >>> {} {}", orderNo, e.getMessage(), e);
            return null;
        }
    }


    /**
     * 根据订单号List获取履约方式
     *
     * @param orderNoList 订单号List
     * @return Map<String, Integer> key: orderNo value: fulfillmentWay
     */
    public Map<String, Integer> queryNormalOrderFulfillmentType(List<String> orderNoList) {
        if (CollectionUtils.isEmpty(orderNoList)) {
            return new HashMap<>();
        }
        try {
            DubboResponse<List<FulfillmentLogisticsResp>> dubboResponse = fulfillmentOrderQueryProvider.queryOrderLogistics(orderNoList);
            if (null == dubboResponse || !dubboResponse.isSuccess()) {
                throw new ProviderException("服务提供方异常");
            }
            if (CollectionUtils.isEmpty(dubboResponse.getData())){
                return new HashMap<>();
            }
            Map<String, Integer> orderFulfillmentTypeMap = new HashMap<>();
            for (FulfillmentLogisticsResp fulfillmentLogisticsResp : dubboResponse.getData()) {
                orderFulfillmentTypeMap.put(
                        fulfillmentLogisticsResp.getOrderNo(),
                        fulfillmentLogisticsResp.getFulfillmentWay()
                );
            }
            return orderFulfillmentTypeMap;
        } catch (Exception e) {
            log.error("查询订单的配送信息失败 >>> {} {}", orderNoList, e.getMessage(), e);
            return new HashMap<>();
        }
    }

    /**
     * 根据省心送订单号获取配送信息
     *
     * @param orderNo 订单号
     * @return Map<Integer, OrderFulfillmentInfo> key: deliveryPlanId
     */
    public Map<Integer, OrderFulfillmentInfo> queryTimingOrderLogisticsInfo(String orderNo) {
        if (StringUtils.isEmpty(orderNo)) {
            return new HashMap<>();
        }
        try {
            DubboResponse<List<FulfillmentLogisticsResp>> dubboResponse = fulfillmentOrderQueryProvider.queryOrderLogistics(Collections.singletonList(orderNo));
            if (null == dubboResponse || !dubboResponse.isSuccess()) {
                throw new ProviderException("服务提供方异常");
            }
            Map<Integer, OrderFulfillmentInfo> orderFulfillmentInfoMap = new HashMap<>();
            if (CollectionUtils.isEmpty(dubboResponse.getData())){
                return orderFulfillmentInfoMap;
            }
            for (FulfillmentLogisticsResp fulfillmentLogisticsResp : dubboResponse.getData()) {
                if (StringUtils.isEmpty(fulfillmentLogisticsResp.getSubOrderNo())) {
                    log.error("\n订单号:{} 的配送信息子订单号为空 \n", orderNo);
                    continue;
                }
                List<LogisticsInfoDTO> logisticsInfoDTOList = buildLogisticsInfoDTOS(fulfillmentLogisticsResp.getDetailList());
                orderFulfillmentInfoMap.put(
                        Integer.valueOf(fulfillmentLogisticsResp.getSubOrderNo()),
                        new OrderFulfillmentInfo(fulfillmentLogisticsResp.getFulfillmentWay(), logisticsInfoDTOList)
                );
            }
            return orderFulfillmentInfoMap;
        } catch (Exception e) {
            log.error("查询订单的配送信息失败 >>> {} {}", orderNo, e.getMessage(), e);
            return new HashMap<>();
        }
    }

    private static List<LogisticsInfoDTO> buildLogisticsInfoDTOS(List<FulfillmentLogisticsDetailResp> ofcLogisticsDetailList) {
        List<LogisticsInfoDTO> logisticsInfoDTOList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(ofcLogisticsDetailList)) {
            return logisticsInfoDTOList;
        }
        for (FulfillmentLogisticsDetailResp fulfillmentLogisticsDetailResp : ofcLogisticsDetailList) {
            logisticsInfoDTOList.add(new LogisticsInfoDTO(fulfillmentLogisticsDetailResp.getLogisticsCompany(), fulfillmentLogisticsDetailResp.getLogisticsNo()));
        }
        return logisticsInfoDTOList;
    }
}
