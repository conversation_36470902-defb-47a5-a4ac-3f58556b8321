package net.summerfarm.mall.facade.wms;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.model.dto.wms.Convert.WmsConvertor;
import net.summerfarm.mall.model.dto.wms.SkuBatchCodeTraceBatchDTO;
import net.summerfarm.wms.skucodetrace.SkuBatchCodeTraceQueryProvider;
import net.summerfarm.wms.skucodetrace.req.BatchTraceCodeOrderNoReq;
import net.summerfarm.wms.skucodetrace.resp.SkuBatchCodeTraceBatchResp;
import net.summerfarm.wms.skucodetrace.resp.SkuBatchCodeTraceResp;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: George
 * @date: 2024-08-15
 **/
@Slf4j
@Component
public class SkuBatchCodeTraceQueryFacade {

    @DubboReference
    private SkuBatchCodeTraceQueryProvider skuBatchCodeTraceQueryProvider;

    /**
     * 根据订单号查询溯源信息
     */
    public List<SkuBatchCodeTraceBatchDTO> queryByOrderNos(Long contactId, LocalDate deliveryDate, List<String> orderNos) {
        BatchTraceCodeOrderNoReq req = new BatchTraceCodeOrderNoReq();
        req.setContactId(contactId);
        req.setOrderNos(orderNos);
        req.setDeliveryDate(deliveryDate);
        DubboResponse<SkuBatchCodeTraceBatchResp> skuBatchCodeTraceBatchRespDubboResponse = skuBatchCodeTraceQueryProvider.queryByOrderNos(req);
        List<SkuBatchCodeTraceResp> skuBatchCodeTraceList = getSkuBatchCodeTraceResps(skuBatchCodeTraceBatchRespDubboResponse);
        return skuBatchCodeTraceList.stream().map(WmsConvertor::convertSkuBatchCodeTraceBatchDTO).collect(Collectors.toList());
    }

    private static List<SkuBatchCodeTraceResp> getSkuBatchCodeTraceResps(DubboResponse<SkuBatchCodeTraceBatchResp> skuBatchCodeTraceBatchRespDubboResponse) {
        if (!skuBatchCodeTraceBatchRespDubboResponse.isSuccess()) {
            throw new ProviderException("获取溯源信息失败");
        }
        SkuBatchCodeTraceBatchResp data = skuBatchCodeTraceBatchRespDubboResponse.getData();
        if (data == null) {
            log.info("获取溯源信息为空！");
            return Collections.emptyList();
        }

        //pop t+2 去掉当前校验 理由：以前T+1是根据订单提前生成所有的码，这期T+2是配送完成的时候将码绑定到订单，所以缺货的时候有的sku就没有那么多码
        List<SkuBatchCodeTraceResp> skuBatchCodeTraceList = data.getSkuBatchCodeTraceList();
        if (CollectionUtils.isEmpty(skuBatchCodeTraceList)) {
            log.info("获取溯源信息为空！");
            return Collections.emptyList();
        }
        return data.getSkuBatchCodeTraceList();
    }

}
