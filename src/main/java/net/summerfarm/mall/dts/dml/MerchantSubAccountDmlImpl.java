package net.summerfarm.mall.dts.dml;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.dts.DtsConstant;
import net.summerfarm.mall.dts.dml.AbstractDbTableDml;
import net.summerfarm.mall.dts.dto.DtsModel;
import net.summerfarm.mall.dts.dto.XmPair;
import net.summerfarm.mall.enums.DtsModelTypeEnum;
import net.summerfarm.mall.service.impl.MerchantServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/4/22 15:08
 */
@Slf4j
@Service
public class MerchantSubAccountDmlImpl extends AbstractDbTableDml {

    @Resource
    private MerchantServiceImpl merchantService;

    @Override
    public void doService(XmPair<Map<String, String>, Map<String, String>> pair, DtsModel dtsModel) {
        if (Objects.equals(DtsModelTypeEnum.UPDATE.name(), dtsModel.getType())) {
            Map<String, String> dataMap = pair.getKey();
            Map<String, String> oldMap = pair.getValue();

            if (isRefresh(oldMap)) {
                Long mid = Long.valueOf(dataMap.get("m_id"));
                Long accountId = Long.valueOf(dataMap.get("account_id"));

                log.info("开始刷新accountId:{}的缓存.", accountId);
                 merchantService.refreshLoginMerchantCache(mid, accountId);
            }

        }
    }


    private boolean isRefresh(Map<String, String> oldMap) {
        if (null == oldMap) {
            log.warn("old区域数据为空！");
            return false;
        }
        Set<String> refreshCacheFieldSet = DtsConstant.MERCHANT_SUB_ACCOUNT_REFRESH_CACHE_FIELD_SET;
        for (Map.Entry<String, String> entry : oldMap.entrySet()) {
            String changeFieldName = entry.getKey();
            if (refreshCacheFieldSet.contains(changeFieldName)) {
                return Boolean.TRUE;
            }
        }
        return false;
    }
}
