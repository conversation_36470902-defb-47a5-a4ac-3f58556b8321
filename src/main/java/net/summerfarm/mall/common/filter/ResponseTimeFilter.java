package net.summerfarm.mall.common.filter;

import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

import javax.servlet.*;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Component
@Order(100)
@Slf4j
public class ResponseTimeFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        long startTime = System.currentTimeMillis();
        try {
            chain.doFilter(request, response);
        } finally {
            long endTime = System.currentTimeMillis();
            long elapsedTime = endTime - startTime;
            log.info("total_time_spend_of_this_call(ms):{}", elapsedTime);
            HttpServletResponse httpResponse = (HttpServletResponse) response;
            httpResponse.setHeader("x-server-rt", String.valueOf(elapsedTime));
        }
    }
}
