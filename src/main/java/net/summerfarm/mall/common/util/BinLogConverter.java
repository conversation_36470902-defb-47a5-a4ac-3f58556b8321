package net.summerfarm.mall.common.util;



import net.summerfarm.mall.dts.dto.DtsModel;
import net.summerfarm.mall.dts.dto.XmPair;
import net.summerfarm.mall.enums.DtsModelTypeEnum;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/6/26 18:23
 */
public class BinLogConverter {


    /**
     * 获取DtsModel对齐的数据
     * @return pair集合（pair左边是data，右边是old）
     */
    public static List<XmPair<Map<String, String>, Map<String, String>>> getAlignedData(DtsModel dtsModelEvent){
        List<XmPair<Map<String, String>, Map<String, String>>> pairList = new ArrayList<>();
        String type = dtsModelEvent.getType();
        if (Objects.equals(DtsModelTypeEnum.INSERT.name(), type)) {
            List<Map<String, String>> dataList = dtsModelEvent.getData();
            dataList.forEach(data ->pairList.add(new XmPair<>(data, null)));
        } else if (Objects.equals(DtsModelTypeEnum.UPDATE.name(), type)) {
            int size = dtsModelEvent.getData() == null ? 0 : dtsModelEvent.getData().size();
            int oldSize = dtsModelEvent.getOld() == null ? 0 : dtsModelEvent.getOld().size();
            if (size != oldSize) {
                return pairList;
            }
            for (int i = 0; i < size; i++) {
                pairList.add(new XmPair<>(dtsModelEvent.getData().get(i), dtsModelEvent.getOld().get(i)));
            }
        } else if (Objects.equals(DtsModelTypeEnum.DELETE.name(), type)) {
            List<Map<String, String>> oldList= dtsModelEvent.getOld();
            oldList.forEach(old ->pairList.add(new XmPair<>(null, old)));
        }
        return pairList;
    }
}
