package net.summerfarm.mall.common.util;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.common.config.DynamicConfig;
import net.summerfarm.mall.model.domain.Merchant;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

@Slf4j
@Component
public class ProvinceConverterV2 {

    @Resource
    private DynamicConfig dynamicConfig;

    public void convert(Merchant merchant, String tag) {
        try {
            Map<String, String> mapping = dynamicConfig.getProvinceConvertMap();
            if (mapping.containsKey(merchant.getProvince())) {
                log.warn("出现特殊省名: province:{}, tag:{} ", merchant.getProvince(), tag);
                merchant.setProvince(mapping.get(merchant.getProvince()));
            }
        } catch (Exception e) {
            log.warn("省份转换失败", e);
        }
    }
}
