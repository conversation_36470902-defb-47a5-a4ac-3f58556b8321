package net.summerfarm.mall.common.util;

import java.util.List;
import lombok.Builder;
import lombok.Data;

import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @date 2023/12/20  14:29
 */
@Data
@Builder
public class RedisCachePipelineCMD<T> {
    /**
     * redis key
     */
    private String key;
    /**
     * 缓存value
     */
    private Supplier<T> value;

    /**
     * 缓存value
     */
    private Supplier<List<T>> valueList;
}
