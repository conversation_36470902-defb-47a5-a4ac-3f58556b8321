package net.summerfarm.mall.common.util;

import cn.hutool.json.JSON;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.schedulerx.shade.org.apache.commons.lang.StringUtils;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.model.dto.merchant.TXAddressResp;
import net.summerfarm.mall.wechat.utils.HttpUtil;
import net.summerfarm.mall.wechat.utils.MD5Util;
import org.apache.http.client.utils.URLEncodedUtils;
import org.springframework.web.bind.annotation.RequestMethod;

import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.Objects;

/**
 * 请求高德的接口
 */
@Slf4j
public class TXAddressUtils {
    private static final String ADJUST_ADDRESS_URL = "https://apis.map.qq.com/ws/geocoder/v1/?key=%s&address=%s";
    private static final String SIG_URL = "/ws/geocoder/v1/?address=%s&key=%s";

    private static final Integer OK_STATUS = 0;
    private static final String KEY = "KOJBZ-36TWI-BJMG5-UUXTR-I3VGF-WFBWD";
    private static final String SECRET_KEY = "qmFvDDzQwjnOkJFaxQJ0UZO1q3BItQzV";

    //qmFvDDzQwjnOkJFaxQJ0UZO1q3BItQzV
    public static TXAddressResp txAddressResp(String address) {
        try {
            log.info("即将转换地址:{}", address);
            String sigUrl = String.format(SIG_URL,address,KEY) + SECRET_KEY;
            String sig = MD5Util.getMD5(sigUrl.getBytes());
            String url = String.format(ADJUST_ADDRESS_URL, KEY, URLEncoder.encode(address, "UTF-8"))
                    + "&sig=" + sig;
            String result = HttpUtil.sendHttps(url, RequestMethod.GET);
            if (StringUtils.isEmpty(result)) {
                log.error("调用地址解析详情失败 address {} message {}", address, result);
                return null;
            }
            JSONObject jsonObject = JSONUtil.parseObj(result);
            Object status = jsonObject.get("status");
            if (!Objects.equals(status, OK_STATUS)) {
                log.error("调用地址解析详情失败 address {} message {}", address, result);
                return null;
            }
            Object byPath = jsonObject.getByPath("result.address_components");
            return JSONUtil.toBean((JSONObject) byPath, TXAddressResp.class);
        } catch (Exception e) {
            log.error("调用地址解析详情失败 address {}", address, e);
            return null;
        }
    }

}
