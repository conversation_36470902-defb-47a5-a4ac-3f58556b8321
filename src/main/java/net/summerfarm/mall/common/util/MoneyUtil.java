package net.summerfarm.mall.common.util;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * <AUTHOR>
 * @Description 金额倒减工具类
 */
@Slf4j
public class MoneyUtil {
    /**
     * 金额倒减计算，保留两位小数，多余金额随机分配到某一项上
     *
     * @param weightMap 计算map k、唯一key v、分配权重
     * @param total     总计优惠
     * @return 金额
     */
    public static Map<String, BigDecimal> moneyDownUtil(Map<String, BigDecimal> weightMap, BigDecimal total) {
        if (total == null || total.compareTo(BigDecimal.ZERO) <= 0) {
            return Collections.EMPTY_MAP;
        }
        if (CollectionUtil.isEmpty(weightMap)) {
            log.info("优惠券信息权重为空, couponMoney:{}", total);
            return Collections.EMPTY_MAP;
        }

        BigDecimal weightTotal = BigDecimal.ZERO;
        for (BigDecimal value : weightMap.values()) {
            weightTotal = weightTotal.add(value);
        }

        if (weightTotal.compareTo(total) < 0) {
            log.error("计算金额小于优惠券金额不进行均摊, couponMoney:{}, weightTotal:{}", total, weightTotal);
            return Collections.EMPTY_MAP;
        }

        //如果去除小数后有差额，将差额摊给额度最大的那一项
        String maxKey = null;
        Map<String, BigDecimal> resultMap = new HashMap<>(16);
        for (Map.Entry<String, BigDecimal> entry : weightMap.entrySet()) {
            BigDecimal down = total.multiply(entry.getValue()).divide(weightTotal, 2, RoundingMode.FLOOR);
            resultMap.put(entry.getKey(), down);

            if (maxKey == null || down.compareTo(resultMap.get(maxKey)) > 0) {
                maxKey = entry.getKey();
            }
        }

        //计算差额，把剩余的金额随机分配
        BigDecimal diff = total;
        for (BigDecimal value : resultMap.values()) {
            diff = diff.subtract(value);
        }

        if (diff.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal addDiff = resultMap.get(maxKey).add(diff);
            resultMap.put(maxKey, addDiff);
        }

        return resultMap;
    }
}
