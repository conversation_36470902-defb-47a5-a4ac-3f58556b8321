package net.summerfarm.mall.common.util;


import com.alibaba.fastjson.JSONObject;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @Date 2025/3/4 15:17
 * @Version 1.0
 */
public class JsonEqualityChecker {
    /**
     * 判断两个 JSON 字符串表示的对象是否相等
     * @param jsonStr1 第一个 JSON 字符串
     * @param jsonStr2 第二个 JSON 字符串
     * @return 如果两个 JSON 对象相等返回 true，否则返回 false
     */
    public static boolean areJsonObjectsEqual(String jsonStr1, String jsonStr2) {
        if (StringUtils.isEmpty(jsonStr1) && StringUtils.isEmpty(jsonStr2)){
            return true;
        }
        try {
            // 将 JSON 字符串解析为 JSONObject 对象
            JSONObject jsonObject1 = JSONObject.parseObject(jsonStr1);
            JSONObject jsonObject2 = JSONObject.parseObject(jsonStr2);

            // 使用 equals 方法比较两个 JSONObject 对象
            return jsonObject1.equals(jsonObject2);
        } catch (Exception e) {
            // 如果解析失败（例如 JSON 格式无效），返回 false
            return false;
        }
    }

    // 测试代码
    public static void main(String[] args) {
        // 示例 JSON 字符串
        String json1 = "{\"customRemark\":\"\",\"baseRemark\":[]}";
        String json2 = "{\"baseRemark\":[],\"customRemark\":\"\"}";

        // 调用方法判断是否相等
        boolean areEqual = areJsonObjectsEqual(json1, json2);
        System.out.println("两个 JSON 对象是否相等？ " + areEqual);
    }
}
