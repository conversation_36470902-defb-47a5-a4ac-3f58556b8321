package net.summerfarm.mall.common.util;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.payments.common.enums.DinPaymentEnum;
import net.summerfarm.mall.payments.common.pojo.din.response.DinResponseDTO;
import net.summerfarm.mall.payments.common.utils.SM2Utils;
import net.xianmu.common.exception.ProviderException;

import java.security.PublicKey;
import java.util.Objects;
import java.util.Optional;

/**
 * @description: 智付工具类
 * @author: George
 * @date: 2025-02-11
 **/
@Slf4j
public class DinUtils {

    public static <T> DinResponseDTO<T> executeRequest(String url, String requestBody, Class<T> responseType, PublicKey publicKey) {
        String response = HttpRequest.post(url)
                .header("Content-Type", "application/json")
                .body(requestBody)
                .timeout(30000)
                .execute()
                .body();
        log.info("din request:{}, ding response:{}", requestBody, response);

        // 解析响应
        DinResponseDTO<String> tempResult = JSON.parseObject(response, new TypeReference<DinResponseDTO<String>>() {});

        // 验签
        if (Objects.nonNull(tempResult.getData()) && !SM2Utils.verify(publicKey, tempResult.getData(), tempResult.getSign())) {
            throw new ProviderException("Din response sign verify failed");
        }

        // 解析 data 字段
        T realData = tempResult.getData() != null ? JSON.parseObject(tempResult.getData(), responseType) : null;

        // 返回解析后的对象
        return new DinResponseDTO<>(tempResult.getCode(), tempResult.getMsg(), realData,
                tempResult.getSignatureMethod(), tempResult.getSign());
    }
}
