package net.summerfarm.mall.common.util;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.util.Map;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SessionCallback;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

@Component
@Slf4j(topic = "redisutil")
public class RedisCacheUtil {

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    private static void logRedisCacheKeyIfNeeded(String key) {
        if (log.isDebugEnabled()) {
            log.debug("redisCacheKey:{}", key);
        }
    }

    public <T> T getDataWithCache(String key, long cacheSeconds, Class<T> clazz, Supplier<T> value) {
        logRedisCacheKeyIfNeeded(key);
        String str = redisTemplate.opsForValue().get(key);
        if (StringUtils.isNotBlank(str)) {
            return JSONObject.parseObject(str, clazz);
        } else {
            Long ttl = redisTemplate.getExpire(key);
            //过期key，重新查询数据库
            if (ttl < 0L) {
                T val = value.get();
                if (val != null) {
                    redisTemplate.opsForValue().set(key, JSONObject.toJSONString(val), cacheSeconds, TimeUnit.SECONDS);
                    return val;
                } else {
                    redisTemplate.opsForValue().set(key, "", cacheSeconds, TimeUnit.SECONDS);
                }
            }  //其他线程写入值，需要再判断一次
            else if (cacheSeconds - ttl < 1L) {
                str = redisTemplate.opsForValue().get(key);
                if (StringUtils.isNotBlank(str)) {
                    return JSONObject.parseObject(str, clazz);
                }
            }
        }
        return null;
    }

    public <T> List<T> getDataWithCacheList(String key, long cacheSeconds, Class<T> clazz, Supplier<List<T>> value) {
        logRedisCacheKeyIfNeeded(key);
        String str = redisTemplate.opsForValue().get(key);
        if (StringUtils.isNotBlank(str)) {
            return JSONObject.parseArray(str, clazz);
        } else {
            Long ttl = redisTemplate.getExpire(key);
            //过期key，重新查询数据库
            if (ttl < 0L) {
                List<T> val = value.get();
                if (val != null) {
                    redisTemplate.opsForValue().set(key, JSONObject.toJSONString(val), cacheSeconds, TimeUnit.SECONDS);
                    return val;
                } else {
                    redisTemplate.opsForValue().set(key, "", cacheSeconds, TimeUnit.SECONDS);
                }
            }  //其他线程写入值，需要再判断一次
            else if (cacheSeconds - ttl < 1L) {
                str = redisTemplate.opsForValue().get(key);
                if (StringUtils.isNotBlank(str)) {
                    return JSONObject.parseArray(str, clazz);
                }
            }
        }
        return null;
    }

    /**
     * 使用redis管道批量获取数据
     *
     * @param cacheSeconds 缓存时间
     * @param cmdSupplier  redis命令集合
     * @return List：需要根据传入参数自行转换
     */
    public <T> List<T> getDataWithCachePipelined(long cacheSeconds, Class<T> clazz, Supplier<List<RedisCachePipelineCMD<T>>> cmdSupplier) {
        List<RedisCachePipelineCMD<T>> cmdList = cmdSupplier.get();
        //批量查询key
        List<Object> pipelinedRes = redisTemplate.executePipelined(new SessionCallback<Object>() {
            @Override
            public <K, V> Object execute(@NonNull RedisOperations<K, V> redisOperations) throws DataAccessException {
                cmdList.forEach(el -> redisOperations.opsForValue().get(el.getKey()));
                return null;
            }
        });

        //为空的key再查一次库，并复制到结果里
        List<T> res = new ArrayList<>();
        for (int i = 0; i < pipelinedRes.size(); i++) {
            RedisCachePipelineCMD<T> cmd = cmdList.get(i);
            if (pipelinedRes.get(i) != null) {
                res.add(JSONObject.parseObject(pipelinedRes.get(i).toString(), clazz));
                continue;
            }

            T cacheData = getDataWithCache(cmd.getKey(), cacheSeconds, clazz, cmd.getValue());
            res.add(cacheData);
        }

        return res;
    }


    /**
     * 使用redis管道批量获取数据
     *
     * @param cacheSeconds 缓存时间
     * @param cmdSupplier  redis命令集合
     * @return List：需要根据传入参数自行转换
     */
    public <T> Map<String, T> getDataMapWithCachePipelined(long cacheSeconds, Class<T> clazz, Supplier<List<RedisCachePipelineCMD<T>>> cmdSupplier) {
        List<RedisCachePipelineCMD<T>> cmdList = cmdSupplier.get();
        //批量查询key
        List<Object> pipelinedRes = redisTemplate.executePipelined(new SessionCallback<Object>() {
            @Override
            public <K, V> Object execute(@NonNull RedisOperations<K, V> redisOperations) throws DataAccessException {
                cmdList.forEach(el -> redisOperations.opsForValue().get(el.getKey()));
                return null;
            }
        });

        //为空的key再查一次库，并复制到结果里
        Map<String, T> res = Maps.newHashMap();
        for (int i = 0; i < pipelinedRes.size(); i++) {
            RedisCachePipelineCMD<T> cmd = cmdList.get(i);
            if (pipelinedRes.get(i) != null) {
                res.put(cmd.getKey(), JSONObject.parseObject(pipelinedRes.get(i).toString(), clazz));
                continue;
            }

            T cacheData = getDataWithCache(cmd.getKey(), cacheSeconds, clazz, cmd.getValue());
            res.put(cmd.getKey(), cacheData);
        }

        return res;
    }


    /**
     * 使用redis管道批量获取数据,map的key即为传入的redis的key值
     * @param cacheSeconds
     * @param clazz
     * @param cmdSupplier
     * @return
     * @param <T>
     */
    public <T> Map<String, List<T>> getDataWithCacheMapPipelined(long cacheSeconds, Class<T> clazz, Supplier<List<RedisCachePipelineCMD<T>>> cmdSupplier) {
        List<RedisCachePipelineCMD<T>> cmdList = cmdSupplier.get();
        //批量查询key
        List<Object> pipelinedRes = redisTemplate.executePipelined(new SessionCallback<Object>() {
            @Override
            public <K, V> Object execute(@NonNull RedisOperations<K, V> redisOperations) throws DataAccessException {
                cmdList.forEach(el -> redisOperations.opsForValue().get(el.getKey()));
                return null;
            }
        });

        //为空的key再查一次库，并复制到结果里
        Map<String, List<T>> listMap = Maps.newHashMap();
        for (int i = 0; i < pipelinedRes.size(); i++) {
            RedisCachePipelineCMD<T> cmd = cmdList.get(i);
            if (pipelinedRes.get(i) != null) {
                listMap.put(cmd.getKey(), JSONObject.parseArray(pipelinedRes.get(i).toString(), clazz));
                continue;
            }

            List<T> cacheData = getDataWithCacheList(cmd.getKey(), cacheSeconds, clazz, cmd.getValueList());
            listMap.put(cmd.getKey(), cacheData);
        }

        return listMap;
    }
}

