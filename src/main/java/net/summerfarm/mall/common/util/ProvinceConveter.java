package net.summerfarm.mall.common.util;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.model.domain.Merchant;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public class ProvinceConveter {
    private static Map<String, String> mapping = new HashMap<>();

    static {
        mapping.put("湖北省", "湖北");
        mapping.put("江西省", "江西");
        mapping.put("杭州市", "杭州");
        mapping.put("重庆市", "重庆");
        mapping.put("安徽省", "安徽");
        mapping.put("湖南省", "湖南");
        mapping.put("上海市", "上海");
        mapping.put("福建省", "福建");
        mapping.put("四川省", "四川");
        mapping.put("江苏省", "江苏");
        mapping.put("浙江省", "浙江");
        mapping.put("广东省", "广东");
    }


    public static void convert(Merchant merchant, String tag) {
        try {
            if (mapping.containsKey(merchant.getProvince())) {
                log.warn("出现特殊省名: province:{}, tag:{} ", merchant.getProvince(), tag);
                merchant.setProvince(mapping.get(merchant.getProvince()));
            }
        } catch (Exception e) {

        }
    }
}
