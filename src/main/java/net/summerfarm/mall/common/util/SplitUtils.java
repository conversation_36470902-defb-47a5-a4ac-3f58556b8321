package net.summerfarm.mall.common.util;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2021/6/10  12:02
 */
public class SplitUtils {

    /**
     * list 分组 每组 size 个
     */
    public static<T> List<List<T>> fixedSplit(List<T> list, int size){
        //校验
        if(list== null || list.size()==0){
            return null;
        }
        // 获得数据总量
        int count = list.size();
        // 计算出要分成几个批次
        int pageCount = (count / size) + (count % size == 0 ? 0 : 1);
        List<List<T>> temp = new ArrayList<>(pageCount);
        for (int i = 0, from, to; i < pageCount; i++) {
            from = i * size;
            to = from + size;
            // 如果超过总数量，则取到最后一个数的位置
            to = to > count ? count : to;
            // 对list 进行拆分
            List<T> subList = list.subList(from, to);
            // 将拆分后的list放入大List返回
            temp.add(subList);
            // 也可以改造本方法，直接在此处做操作
        }
        return temp;
    }

    /**
     * list 拆分为n个数组
     */
    public static<T> List<List<T>> avgSplit(List<T> list,Integer n){
        //校验
        if(list== null || list.size()==0){
            return null;
        }
        int size = list.size();
        //每组多少个元素
        int number = size / n ;
        //按组分后 剩余元素个数  每组一个
        int lastLength = size % n;
        //偏移量
        int offset = 0;
        List<List<T>> temp = new ArrayList<>(n);
        for (int i = 0; i < n; i++) {
            List<T> value;
            if (lastLength > offset) {
                value = list.subList(i * number + offset, (i + 1) * number + offset + 1);
                offset++;
            } else {
                value = list.subList(i * number + offset, (i + 1) * number + offset);
            }

            temp.add(value);

        }

        return temp;

    }

}
