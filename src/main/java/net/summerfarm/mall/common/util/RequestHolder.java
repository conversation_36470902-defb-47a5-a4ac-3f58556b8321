package net.summerfarm.mall.common.util;

import static net.summerfarm.mall.contexts.SpringContextUtil.isProduct;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONValidator;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.mall.common.redis.KeyConstant;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.contexts.SpringContextUtil;
import net.summerfarm.mall.model.domain.Area;
import net.summerfarm.mall.model.dto.login.AbExpDTO;
import net.summerfarm.mall.model.dto.login.LoginMerchantCacheDTO;
import net.summerfarm.mall.model.vo.MemberVO;
import net.summerfarm.mall.model.vo.MerchantSubject;
import net.summerfarm.mall.service.AreaService;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @Package: net.summerfarm.common.util
 * @Description:
 * @author: <EMAIL>
 * @Date: 2018/7/25
 */
@Slf4j
public class RequestHolder {

    /**
     * ab实验header
     */
    private static String AB_EXP_HEADER = "xm-ab-exp";

    private static final ThreadLocal<LoginMerchantCacheDTO> USER_INFO_THREAD_LOCAL = new ThreadLocal<>();

    public static HttpServletRequest getRequest() {
        return  ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();
    }

    public static HttpServletResponse getResponse() {
        return  ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getResponse();
    }

    public static MerchantSubject getMerchantSubject() {
        LoginMerchantCacheDTO loginMerchantCacheDTO = USER_INFO_THREAD_LOCAL.get();
        if(loginMerchantCacheDTO != null){
            return loginMerchantCacheDTO.getMerchantSubject();
        }
        return null;
    }

    public static MerchantSubject getMerchantSubject(String token, HttpServletRequest request) {
        if(StringUtils.isEmpty(token)){
            token = getToken(request);
            if(StringUtils.isEmpty(token)){
                return null;
            }
        }
        RedisTemplate<String, String> redisTemplate = SpringContextUtil.getBean("redisTemplate", RedisTemplate.class);
        String userInfoString = redisTemplate.opsForValue().get(KeyConstant.MALL_TOKEN_PREFIX + token);
        if(StringUtils.isEmpty(userInfoString)){
            return null;
        }
        LoginMerchantCacheDTO loginMerchantCacheDTO = JSON.parseObject(userInfoString, LoginMerchantCacheDTO.class);
        if(loginMerchantCacheDTO == null){
            return null;
        }

        //获取AB实验信息
        try {
            String abExpHeader = request.getHeader(AB_EXP_HEADER);
            if (StringUtils.isNotBlank(abExpHeader) && JSONValidator.Type.Array.equals(JSONValidator.from(abExpHeader).getType())) {
                List<AbExpDTO> abExpDTOS = JSON.parseArray(abExpHeader, AbExpDTO.class);
                loginMerchantCacheDTO.setAbExpDTOSet(new HashSet<>(abExpDTOS));
            }
        } catch (Exception e) {
            log.error("获取AB实验信息失败", e);
        }

        USER_INFO_THREAD_LOCAL.set(loginMerchantCacheDTO);
        return loginMerchantCacheDTO.getMerchantSubject();
    }

    public static Area getMerchantArea() {
        MerchantSubject merchantSubject = getMerchantSubject();
        if(merchantSubject==null){
            return new Area(1001,"{}");
        }
        return merchantSubject.getArea();
    }

    public static boolean isPopMerchant() {
        MerchantSubject merchantSubject = getMerchantSubject();
        if(merchantSubject==null){
            return false;
        }
        return merchantSubject.isPopMerchant();
    }

    public static Integer getMerchantAreaNo() {
        MerchantSubject merchantSubject = getMerchantSubject();
        Integer areaNo = 1001;
        if (merchantSubject != null) {
            areaNo = merchantSubject.getArea().getAreaNo();
        } else {
            // 用户处理一些用户还未登录时默认信息
            if (isPopMall()) {
                // 如果是POP商城，则返回一个POP商城的固定area NO:
                int devPopAreaNo = 29453;
                int prodPopAreaNo = 44240;
                if (isProduct()) {
                    log.warn("用户未登录，且是POP商城，返回默认的POP商城area NO:{}", prodPopAreaNo);
                    return prodPopAreaNo;
                } else {
                    log.warn("用户未登录，且是POP商城(非生产)，返回默认的POP商城area NO:{}", devPopAreaNo);
                    return devPopAreaNo;
                }
            }
            log.warn("未获取到当前用户的areaNo, 将使用默认:{}", areaNo);
        }
        return areaNo;
    }

    public static Integer getAdminId() {
        MerchantSubject merchantSubject = getMerchantSubject();
        Integer adminId=null;
        if (merchantSubject != null) {
            adminId = merchantSubject.getAdminId();
        }
        return adminId;
    }

    public static Integer getBusinessLine() {
        MerchantSubject merchantSubject = getMerchantSubject();
        return merchantSubject == null ? null : merchantSubject.getBusinessLine();
    }

    public static Integer getGrayscale(){
        MerchantSubject merchantSubject = getMerchantSubject();
        if (merchantSubject == null){
            return 0;
        }
        return merchantSubject.getGrayscale();
    }

    public static List<MemberVO> getMemberVOS(){
        MerchantSubject merchantSubject = getMerchantSubject();
        if (merchantSubject != null){
            return merchantSubject.getMemberVOS();
        }
        return new ArrayList<>();
    }

    public static Integer getDirect() {
        MerchantSubject merchantSubject = getMerchantSubject();
        Integer direct=null;
        if (merchantSubject != null) {
            direct = merchantSubject.getDirect();
        }
        return direct;
    }

    public static Integer getSkuShow() {
        MerchantSubject merchantSubject = getMerchantSubject();
        Integer skuShow=null;
        if (merchantSubject != null) {
            skuShow = merchantSubject.getSkuShow();
        }
        return skuShow;
    }

    public static String getName() {
        return Optional.ofNullable(getMerchantSubject()).map(MerchantSubject::getName).orElse(null);
    }

    public static boolean isMajor(){
        MerchantSubject merchantSubject = getMerchantSubject();
        return merchantSubject != null && "大客户".equals(merchantSubject.getSize());
    }

    public static boolean isMajorDirect(){
        MerchantSubject merchantSubject = getMerchantSubject();
        return merchantSubject != null && "大客户".equals(merchantSubject.getSize()) && merchantSubject.getDirect() == 1;
    }

    public static Boolean isServered(){
        MerchantSubject merchantSubject = getMerchantSubject();
        return merchantSubject != null && merchantSubject.getServer() == 1;
    }


    public static Long getMId() {
        LoginMerchantCacheDTO loginMerchantCacheDTO = USER_INFO_THREAD_LOCAL.get();
        if (Objects.isNull(loginMerchantCacheDTO)) {
            return null;
        }
        return loginMerchantCacheDTO.getMerchantId();
    }

    public static String getOpenId() {
        LoginMerchantCacheDTO loginMerchantCacheDTO = USER_INFO_THREAD_LOCAL.get();
        if(loginMerchantCacheDTO == null){
           return null;
        }
        return loginMerchantCacheDTO.getOpenId();
    }

    public static String getUnionid() {
        return USER_INFO_THREAD_LOCAL.get().getUnionid();
    }

    public static String getMpOpenId() {
        return USER_INFO_THREAD_LOCAL.get().getMpOpenId();
    }

    public static Long getAccountId(){
        return USER_INFO_THREAD_LOCAL.get().getAccountId();
    }

    public static boolean inChange(){
        AreaService areaService = SpringContextUtil.getApplicationContext().getBean(AreaService.class);
        return areaService.inChange(RequestHolder.getMerchantAreaNo(), RequestHolder.getMId());
    }

    public static String getSize() {
        MerchantSubject subject = getMerchantSubject();
        return subject == null ? "单店" : subject.getSize();
    }

    public static boolean isMiniProgramLogin() {
        LoginMerchantCacheDTO dto = USER_INFO_THREAD_LOCAL.get();
        if (dto == null) {
            return false;
        }
        String loginWay = dto.getLoginWay();

        //公众号每次进入都会重新判断登录信息，以此判断会更准确
        return !Global.WECHAT_GZH.equals(loginWay);
    }

    @Deprecated
    // 可能会导致空指针
    public static Long getMerchantId() {
        return Optional.ofNullable(USER_INFO_THREAD_LOCAL.get()).map(LoginMerchantCacheDTO::getMerchantId).orElse(null);
    }

    public static void clearCurrentUserInfo(){
        USER_INFO_THREAD_LOCAL.remove();
    }

    public static HttpSession getSession() {
        return getRequest().getSession();
    }

    public static LoginMerchantCacheDTO getLoginMerchantCache(){
        LoginMerchantCacheDTO loginMerchantCacheDTO = USER_INFO_THREAD_LOCAL.get();
        if(loginMerchantCacheDTO == null){
            getMerchantSubject(getToken(getRequest()), null);//NOSONAR
        }
        return USER_INFO_THREAD_LOCAL.get();
    }

    public static void setLoginMerchantCache(String token, LoginMerchantCacheDTO loginMerchantCacheDTO){
        RedisTemplate<String, String> redisTemplate = SpringContextUtil.getBean("redisTemplate", RedisTemplate.class);
        redisTemplate.opsForValue().set(KeyConstant.MALL_TOKEN_PREFIX + token, JSON.toJSONString(loginMerchantCacheDTO),
                240L, TimeUnit.MINUTES);
        USER_INFO_THREAD_LOCAL.set(loginMerchantCacheDTO);
    }

    public static String getToken(HttpServletRequest request) {
        if(request == null){
            request = getRequest();
        }
        return request.getHeader("token");
    }

    public static Long getNewMerchantId(){
        Long mId;
        try {
             mId = USER_INFO_THREAD_LOCAL.get().getMerchantId();
        } catch (Exception e) {
            return null;
        }
        return mId;
    }

    /**
     * @return 获取用户在某个实验的分组
     */
    public static String getAbExpVariant(String experimentId) {
        LoginMerchantCacheDTO loginMerchantCacheDTO = USER_INFO_THREAD_LOCAL.get();
        if (loginMerchantCacheDTO == null || loginMerchantCacheDTO.getAbExpDTOSet() == null) {
            return null;
        }
        for (AbExpDTO abExpDTO : loginMerchantCacheDTO.getAbExpDTOSet()) {
            if (Objects.equals(abExpDTO.getExperimentId(), experimentId)) {
                return abExpDTO.getVariantId();
            }
        }
        return null;
    }

    /**
     * 是否为POP商城，目前POP商城所用的域名是  *.cosfo.cn
     * @return
     */
    public static boolean isPopMall() {
        try {
            HttpServletRequest request = getRequest();
            String host = request.getHeader("Host");
            return org.apache.commons.lang3.StringUtils.isNotBlank(host) && host.indexOf("cosfo.cn") >= 0;
        } catch (Exception e) {
            log.warn("接收到dubbo请求,无需获取pop标记");
        }
        return false;
    }
}
