package net.summerfarm.mall.common.util;

import lombok.extern.slf4j.Slf4j;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2019-07-03
 * @description
 */
@Slf4j
public class EnvUtil {

    public static Boolean IS_PROD = Boolean.FALSE;
    public static Boolean IS_TEST = Boolean.FALSE;
    public static Boolean IS_LOCAL = Boolean.FALSE;

    static {
        InetAddress inetAddress;
        String serverIp = null;
        try {
            inetAddress = InetAddress.getLocalHost();
            serverIp = inetAddress.getHostAddress();
            log.info("服务器地址为：" + serverIp);
        } catch (UnknownHostException e) {
            log.error("未知异常", e);
        }
        if ("*************".equals(serverIp) || "*************".equals(serverIp)) {
            log.info("--------使用正式配置--------");
            IS_PROD = Boolean.TRUE;
        } else if ("*************".equals(serverIp)) {
            log.info("--------测试环境--------");
            IS_TEST = Boolean.TRUE;
        } else if ("*************".equals(serverIp)) {
            log.info("--------测试环境--------");
            IS_TEST = Boolean.TRUE;
        } else {
            log.info("--------本地环境--------");
            IS_LOCAL = Boolean.TRUE;
        }
    }
}
