package net.summerfarm.mall.common.util;

import net.summerfarm.common.util.BaseDateUtils;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.model.domain.StoreAllocation;
import net.summerfarm.warehouse.model.domain.WarehouseLogisticsConfig;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Package: com.manageSystem.common.utils
 * @Description: 日期工具类
 * @author: <EMAIL>
 * @Date: 2016/8/30
 */
public class DateUtils extends BaseDateUtils {

    private static String PATTERN = "yyyy-MM-dd HH:mm:ss";

    /**
     * 截单时间
     *
     * @param localDate
     * @return
     */
    public static LocalDateTime localDate2closingTime(LocalDate localDate) {
        return LocalDateTime.of(localDate, Global.CLOSING_ORDER_TIME);
    }

    /**
     * 查询下个周期的时间
     *
     * @param configList
     */
    public static LocalDate allocationDate(List<WarehouseLogisticsConfig> configList, Integer closeOrderType) {

        if (CollectionUtils.isEmpty(configList)) {
            return null;
        }
        //获取调拨类型
        Integer type = configList.get(0).getCycleType();
        List<Integer> allocation = configList.stream()
                .map(WarehouseLogisticsConfig::getLogisticsTime)
                .sorted().distinct().collect(Collectors.toList());
        //去重排序
        List<Integer> allocationList = allocation.stream()
                .distinct()
                .sorted()
                .collect(Collectors.toList());

        int size = allocationList.size();
        boolean nextWeek = false;
        //设为周期最小值
        int nextDay = allocationList.get(0);
        //过22点为第二天
        int hour = LocalDateTime.now().getHour();
        Integer checkoutHour = Objects.equals(closeOrderType, Global.TYPE_TWENTY_THREE) ? 23 : 22;
        checkoutHour = Objects.equals(closeOrderType, Global.TYPE_NINE) ? checkoutHour : 21;
        LocalDate nowDate = hour >= checkoutHour ? LocalDate.now().plusDays(1) : LocalDate.now();
        LocalDate resultDate = nowDate;
        //根据类型计算初始值
        int now = Objects.equals(type, StoreAllocation.ALLOCATION_TYPE_MONTH) ? nowDate.getDayOfMonth() : nowDate.getDayOfWeek().getValue();
        //获取下个周期的日
        for (int i = 0; i < size; i++) {
            int day = allocationList.get(i);
            //正常情况 截止日期为本个周期的下一日期
            if (now < day) {
                nextDay = allocationList.get(i);
                break;
                //截止时间为下个周期的第一日期
            } else if (Objects.equals(i, size - 1) && now >= day) {
                nextWeek = true;
                break;
            }

        }
        //月单位
        if (Objects.equals(type, 2)) {
            LocalDate firstCalculateDate = LocalDate.of(nowDate.getYear(), nowDate.getMonthValue(), nextDay);
            //是否是在本周期
            resultDate = nextWeek ? firstCalculateDate.plusMonths(1) : firstCalculateDate;
        }
        //周单位
        if (Objects.equals(type, 1)) {
            //获取下个周期时间在本周的时间
            LocalDate firstCalculateDate = nowDate.with(DayOfWeek.MONDAY).plusDays(nextDay - 1);
            resultDate = nextWeek ? firstCalculateDate.plusWeeks(1) : firstCalculateDate;
        }
        return resultDate;
    }

    /**
     * 获得当天剩余秒数
     *
     * @param currentDate 当天的日期
     * @return 剩余的秒数
     */
    public static Integer getRemainSecondsOneDay(Date currentDate) {
        LocalDateTime midnight = LocalDateTime.ofInstant(currentDate.toInstant(),
                ZoneId.systemDefault()).plusDays(1).withHour(0).withMinute(0)
                .withSecond(0).withNano(0);
        LocalDateTime currentDateTime = LocalDateTime.ofInstant(currentDate.toInstant(),
                ZoneId.systemDefault());
        long seconds = ChronoUnit.SECONDS.between(currentDateTime, midnight);
        return (int) seconds;
    }

    /**
     * 计算日期
     *
     * @param deliveryFrequent    配送周期
     * @param nextDeliveryDate    下个首配日
     * @param defaultDeliveryDate 配送日
     * @return
     */

    public static LocalDate calcDeliveryDate(Integer[] deliveryFrequent, LocalDate nextDeliveryDate, LocalDate defaultDeliveryDate) {
        if (!Objects.equals(0, deliveryFrequent[0])) {
            //存在下次配送时间 & 在当前默认时间之后，采用下次配送时间计算
            if (nextDeliveryDate != null && nextDeliveryDate.isAfter(defaultDeliveryDate)) {
                defaultDeliveryDate = nextDeliveryDate;
            }

            int dayOfWeek = defaultDeliveryDate.getDayOfWeek().getValue();
            boolean flag = true;
            for (Integer deliveryDay : deliveryFrequent) {
                if (dayOfWeek <= deliveryDay) {
                    defaultDeliveryDate = defaultDeliveryDate.plusDays(deliveryDay - dayOfWeek);
                    flag = false;
                    break;
                }
            }
            if (flag) {
                defaultDeliveryDate = defaultDeliveryDate.plusDays(7 - dayOfWeek + deliveryFrequent[0]);
            }
        } //每天配送 & 在当前默认时间之后，配送时间为下次配送时间
        else if (nextDeliveryDate != null && nextDeliveryDate.isAfter(defaultDeliveryDate)) {
            defaultDeliveryDate = nextDeliveryDate;
        }
        return defaultDeliveryDate;
    }


    /**
     * 获得当天hour时零分零秒
     *
     * @param hour 第几个小时
     * @return
     */
    public static LocalDateTime getDateByHour(int hour) {
        if (hour < 0 || hour > 23) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, hour);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return LocalDateTime.ofInstant(calendar.getTime().toInstant(), ZoneId.systemDefault());
    }


    /**
     * 获得昨天hour时零分零秒
     *
     * @param hour 第几个小时
     * @return
     */
    public static LocalDateTime getYesterdayDateByHour(int hour) {
        if (hour < 0 || hour > 23) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DATE, calendar.get(Calendar.DAY_OF_MONTH) - 1);
        calendar.set(Calendar.HOUR_OF_DAY, hour);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return LocalDateTime.ofInstant(calendar.getTime().toInstant(), ZoneId.systemDefault());
    }

    public static LocalDateTime stampToLocalDate(long timestamp) {
        Instant instant = Instant.ofEpochMilli(timestamp);
        return LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
    }

    /**
     * 时间加 减
     *
     * @param date  日期
     * @param field Calendar.XXX
     * @param num   变化数值
     * @return
     */
    final public static Date dateAdd(Date date, final int field, int num) {
        if (date == null)
            return null;
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(field, num);
        return cal.getTime();
    }

    /**
     * 判断日期是否在某个范围
     * @param begin
     * @param end
     * @param now
     * @return
     */
    public static final Boolean isEffectiveDate(LocalDateTime begin,LocalDateTime end,LocalDateTime now){
        if(now.isAfter(begin) && now.isBefore(end)){
            return true;
        }else{
            return false;
        }
    }


    public static String localDateTimeToString(LocalDateTime localDateTime) {
        if (Objects.isNull(localDateTime)) {
            return null;
        }
        return DateTimeFormatter.ofPattern(PATTERN).format(localDateTime);
    }

    /**
     * @description: 获取当前时间的时间戳
     * @author: lzh
     * @date: 2023/3/17 13:54
     * @param: [localDateTime]
     * @return: java.lang.Long
     **/
    public static Long localDateTimeToLong(LocalDateTime localDateTime) {
        if (Objects.isNull(localDateTime)) {
            return null;
        }
        return Timestamp.valueOf(localDateTime).getTime();
    }

    /**
     * @description: 时间和日期进行拼凑
     * @author: lzh
     * @date: 2023/3/17 13:53
     * @param: [localDate, localTime]
     * @return: java.time.LocalDateTime
     **/
    public static LocalDateTime localTimeToLocalDateTime(LocalDate localDate, LocalTime localTime) {
        return LocalDateTime.of(localDate, localTime);
    }

    /**
     * 时间戳转为LocalDateTime
     * @param strTimestamp
     * @return
     */
    public static LocalDateTime timestampToLocalDateTime(String strTimestamp) {
        LocalDateTime localDateTime = Instant.ofEpochMilli(Long.parseLong(strTimestamp)).atZone(ZoneOffset.ofHours(8)).toLocalDateTime();
        return localDateTime;
    }
    /**
     * 将 如2018-06-23 14:22:22 格式解析成 LocalDateTime
     *
     * @param localDateTime
     * @return
     */
    public static LocalDateTime parseLocalDateTime(String localDateTime) {
        return LocalDateTime.parse(localDateTime, DateTimeFormatter.ofPattern(PATTERN));
    }

    public static String dateToString(Date date) {
        if (Objects.isNull(date)) {
            return null;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(PATTERN);
        return simpleDateFormat.format(date);
    }

}
