package net.summerfarm.mall.common.util;

import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.lianok.core.config.Md5Config;
import com.lianok.core.emuns.EnvEnum;
import com.lianok.core.entity.AbstractDockingRequest;
import com.lianok.core.entity.DockingResponseBase;
import com.lianok.core.entity.ResponseResultBase;
import com.lianok.docking.LianokService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;

import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;

/**
 * @description: 火脸相关工具类
 * @author: George
 * @date: 2025-01-14
 **/
@Slf4j
public class FireFaceUtils {

    /**
     * 构建服务对象
     *
     * @param authCode 授权码
     * @param secret   密钥
     * @return LianokService 服务对象
     */
    public static LianokService buildLianokService(String authCode, String secret) {
        EnvEnum env = EnvEnum.PUBLISH;
        Md5Config config = new Md5Config.Builder()
                .config(env, authCode, secret)
                .build();
        return new LianokService.Builder()
                .config(config)
                .build();
    }


    public static <T extends AbstractDockingRequest, R extends DockingResponseBase> ResponseResultBase<R> executeRequest(String authCode, String secret, T request, Class<R> responseType) {
        return executeRequest(buildLianokService(authCode, secret), request, responseType);
    }


    /**
     * 执行火脸支付接口请求
     *
     * @param service      服务对象
     * @param request      请求参数
     * @param responseType 响应类型类对象
     * @param <T>          请求类型
     * @param <R>          响应类型
     * @return 响应结果
     */
    public static <T extends AbstractDockingRequest, R extends DockingResponseBase> ResponseResultBase<R> executeRequest(
            LianokService service, T request, Class<R> responseType) {
        try {
            log.info("火脸请求参数: {}", JSON.toJSONString(request));
            ResponseResultBase<?> response = service.execute(request);
            log.info("火脸响应参数: {}", JSON.toJSONString(response));
            if (response == null) {
                log.error("火脸支付请求异常: 返回结果为空");
                return null;
            }
            // 类型检查：确保返回的数据类型与期望类型一致
            if (response.getData() != null && !responseType.isInstance(response.getData())) {
                throw new ClassCastException("返回的数据类型与期望的类型不匹配");
            }
            // 强制类型转换
            return (ResponseResultBase<R>) response;
        } catch (Exception e) {
            log.error("火脸支付请求异常: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 验证回调签名
     *
     * @param obj 回调参数
     * @param salt   签名密钥
     * @param sign   回调中的签名
     * @return 是否验签成功
     */
    public static boolean validateSignature(Object obj, String salt, String sign) {
        // 解析 JSON
        JSONObject jsonObject = (JSONObject) JSON.toJSON(obj);
        jsonObject.remove("code");
        jsonObject.remove("message");
        jsonObject.remove("sign");

        // 使用 TreeMap 进行按 ASCII 排序
        Map<String, Object> sortedMap = new TreeMap<>();
        sortedMap.putAll(jsonObject);

        // 构造字符串
        StringBuilder str = new StringBuilder();
        for (Map.Entry<String, Object> entry : sortedMap.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue() == null ? "" : entry.getValue().toString();
            // 拼接查询字符串
            if (str.length() > 0) {
                str.append("&");
            }
            str.append(key).append("=").append(value);
        }

        String data = str.toString().toLowerCase();
        data = data + "&" + salt;
        String calSign = SecureUtil.md5(data);
        return Objects.equals(calSign, sign);
    }
}
