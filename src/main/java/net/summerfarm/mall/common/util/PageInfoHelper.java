package net.summerfarm.mall.common.util;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;

import static java.util.stream.Collectors.toList;

/**
 * @Package: com.manageSystem.common.utils
 * @Description:
 * @author: <EMAIL>
 * @Date: 2016/8/25
 */
public class PageInfoHelper {

    public static PageInfo createPageInfo(List data) {
        return (data != null && !data.isEmpty()) ? new PageInfo(data) : null;
    }

    /**
     * pagehelper   手动分页
     *
     * @param currentPage 当前页
     * @param pageSize
     * @param list
     * @param <T>
     * @return
     */
    public static <T> PageInfo<T> manualPage(int currentPage, int pageSize, List<T> list) {
        int total = list.size();
        if (total > pageSize) {
            int toIndex = pageSize * currentPage;
            if (toIndex > total) {
                toIndex = total;
            }
            int fromIndex = pageSize * (currentPage - 1);

            if (fromIndex <= toIndex) {
                list = list.subList(fromIndex, toIndex);
            } else {
                list = null;
            }
        }
        Page<T> page = new Page<>(currentPage, pageSize);
        if (!CollectionUtils.isEmpty(list)) {
            page.addAll(list);
        }
        page.setPages((total + pageSize - 1) / pageSize);
        page.setTotal(total);

        return new PageInfo<>(page);
    }

    public static <T> PageInfo<T> copyPageInfo(PageInfo pageInfoResp, List<T> newList) {
        if (Objects.isNull(pageInfoResp) || CollectionUtil.isEmpty(newList)) {
            return null;
        }
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(pageInfoResp.getPageNum());
        pageInfo.setPageSize(pageInfoResp.getPageSize());
        pageInfo.setSize(pageInfoResp.getSize());
        pageInfo.setStartRow(pageInfoResp.getStartRow());
        pageInfo.setEndRow(pageInfoResp.getEndRow());
        pageInfo.setPages(pageInfoResp.getPages());
        pageInfo.setPrePage(pageInfoResp.getPrePage());
        pageInfo.setNextPage(pageInfoResp.getNextPage());
        pageInfo.setIsFirstPage(pageInfoResp.isIsFirstPage());
        pageInfo.setIsLastPage(pageInfoResp.isIsLastPage());
        pageInfo.setHasPreviousPage(pageInfoResp.isHasPreviousPage());
        pageInfo.setHasNextPage(pageInfoResp.isHasNextPage());
        pageInfo.setNavigatePages(pageInfoResp.getNavigatePages());
        pageInfo.setNavigatepageNums(pageInfoResp.getNavigatepageNums());
        pageInfo.setNavigateFirstPage(pageInfoResp.getNavigateFirstPage());
        pageInfo.setNavigateLastPage(pageInfoResp.getNavigateLastPage());
        pageInfo.setTotal(pageInfoResp.getTotal());
        pageInfo.setList(newList);
        return pageInfo;
    }


    public static <T> PageInfo<T> convert2PageInfo(Integer pageNum, Integer pageSize, Integer total, Boolean isLastPage, List<T> list) {
        PageInfo<T> pageInfo = new PageInfo<>(list);
        pageInfo.setPageNum(pageNum);
        pageInfo.setPageSize(pageSize);
        pageInfo.setTotal(total);
        pageInfo.setIsFirstPage(1 == pageNum);
        pageInfo.setIsLastPage(isLastPage);
        pageInfo.setNextPage(isLastPage ? 0 : pageNum + 1);
        return pageInfo;
    }


    public static <T> PageInfo<T> createPageInfoV2(List<T> data) {
        return (data != null && !data.isEmpty()) ? new PageInfo<T>(data) : new PageInfo<T>(new ArrayList<>());
    }


    public static <T, R> PageInfo<R> toPageResp(PageInfo<T> page, Function<? super T, ? extends R> function) {
        PageInfo<R> resp = new PageInfo<R>();
        resp.setPageNum(page.getPageNum());
        resp.setPageSize(page.getPageSize());
        resp.setTotal(page.getTotal());
        resp.setSize(page.getSize());
        resp.setStartRow(page.getStartRow());
        resp.setEndRow(page.getEndRow());
        resp.setPages(page.getPages());
        resp.setPrePage(page.getPrePage());
        resp.setNextPage(page.getNextPage());
        resp.setIsFirstPage(page.isIsFirstPage());
        resp.setIsLastPage(page.isIsLastPage());
        resp.setHasNextPage(page.isHasNextPage());
        resp.setHasPreviousPage(page.isHasPreviousPage());
        resp.setNavigatePages(page.getNavigatePages());
        resp.setNavigatepageNums(page.getNavigatepageNums());
        resp.setNavigateFirstPage(page.getNavigateFirstPage());
        resp.setNavigateLastPage(page.getNavigateLastPage());
        List<R> collect = page.getList().stream().map(function).collect(toList());
        resp.setList(collect);
        return resp;
    }

}
