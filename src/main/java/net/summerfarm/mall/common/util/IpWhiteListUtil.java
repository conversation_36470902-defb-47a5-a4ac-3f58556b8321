package net.summerfarm.mall.common.util;

import cn.hutool.core.util.StrUtil;
import net.summerfarm.mall.Conf;
import net.summerfarm.mall.contexts.SpringContextUtil;

/**
 * @author: zjx
 * @email: <EMAIL>
 * @date: 2022/8/11 19:15
 */
public class IpWhiteListUtil {
    public static boolean check(String ip) {
        if(SpringContextUtil.isProduct()
                && (StrUtil.startWith(ip, Conf.ALI_K8S_POD_IP_PREFIX) || Conf.ALI_K8S_EXTERNAL_IP_SET.contains(ip))) {
            return true;
        } else if ((SpringContextUtil.isDev() || SpringContextUtil.isDev2() || SpringContextUtil.isDev3()|| SpringContextUtil.isDev4() || SpringContextUtil.isQa())
                && (StrUtil.startWith(ip, Conf.AZURE_K8S_POD_IP_PREFIX)|| Conf.AZURE_K8S_EXTERNAL_IP_SET.contains(ip))) {
            return true;
        }
        return false;
    }
}