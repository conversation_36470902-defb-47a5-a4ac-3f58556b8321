package net.summerfarm.mall.common.util;

import java.nio.charset.Charset;
import lombok.extern.slf4j.Slf4j;

import java.util.regex.Matcher;
import java.util.regex.Pattern;
/**
 * @Author: zhuyantao
 * @date: 2023/3/16 5:12 下午
 * @description
 */
@Slf4j
public class StringUtil {

    private final static Charset UTF8 = Charset.forName("UTF-8");

    /**
     * 过滤特殊字符
     * @param str
     * @return
     */
    public static String filterStr (String str){
        String regEx="[`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        return m.replaceAll("").trim();
    }

    /**
     * 过滤emoji表情，将表情替换成*
     * @param source
     * @return
     */
    public static String filterEmoji(String source) {
        if(source != null) {
            Pattern emoji = Pattern.compile("[\ud83c\udc00-\ud83c\udfff]|[\ud83d\udc00-\ud83d\udfff]|[\u2600-\u27ff]|" +
                            "[\ud83e\udd00-\ud83e\uddff]|[\u2300-\u23ff]|[\u2500-\u25ff]|[\u2100-\u21ff]|[\u00a0-\u0fff]|[\u2b00-\u2bff]|[\u2d06]|[\u3030]"
                    ,Pattern.UNICODE_CASE | Pattern.CASE_INSENSITIVE ) ;
            Matcher emojiMatcher = emoji.matcher(source);
            if (emojiMatcher.find()) {
                source = emojiMatcher.replaceAll("*");
                return source ;
            }
            return source;
        }
        return source;
    }

    /**
     * 过滤表情符号
     * @param content
     * @return
     */
    public static String filterEmoji2(String content) {
        StringBuilder sb = new StringBuilder();
        for (char ch : content.toCharArray()) {
            if (!Character.isHighSurrogate(ch) && !Character.isLowSurrogate(ch)) {
                sb.append(ch);
            }
        }
        if (sb.length() < content.length()) {
            log.info(content.length() + " 过滤掉 " + sb.length());
        }
        return sb.toString();
    }

    /**
     * 判断字符串中是否存在4字节字符
     *
     * @param input 输入字符串
     * @return 包含4字节返回true， 否则为false
     */
    public static boolean containsMb4Char(String input) {
        if (input == null) {
            return false;
        }
        byte[] bytes = input.getBytes(UTF8);
        for (int i = 0; i < bytes.length; i++) {
            byte b = bytes[i];
            //four bytes
            if ((b & 0XF0) == 0XF0) {
                return true;
            } else if ((b & 0XE0) == 0XE0) {
                //three bytes
                //forward 2 byte
                i += 2;
            } else if ((b & 0XC0) == 0XC0) {
                i += 1;
            }
        }
        return false;
    }

    public static boolean isNumericOrEmpty(String input) {
        // 正则表达式，表示纯数字或者空字符串
        String regex = "^[0-9]*$";

        // 编译正则表达式
        Pattern pattern = Pattern.compile(regex);

        // 创建 Matcher 对象
        Matcher matcher = pattern.matcher(input);

        // 判断是否匹配
        return matcher.matches();
    }

}
