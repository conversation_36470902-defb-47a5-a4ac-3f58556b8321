package net.summerfarm.mall.common.util;

import java.io.PrintWriter;
import java.io.StringWriter;

/**
 * 通用工具
 * @author: <EMAIL>
 * @create: 2022/6/30
 */
public class CommonToolUtil {

    /**
     * 收集异常堆栈信息
     */
    public static String collectExceptionStackMsg(Exception e) {
        StringWriter sw = new StringWriter();
        e.printStackTrace(new PrintWriter(sw, true));
        String strs = sw.toString();
        return strs;
    }

}
