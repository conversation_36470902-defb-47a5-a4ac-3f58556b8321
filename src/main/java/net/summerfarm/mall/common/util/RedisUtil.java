package net.summerfarm.mall.common.util;

import net.summerfarm.mall.contexts.SpringContextUtil;
import org.springframework.data.redis.connection.RedisStringCommands;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.data.redis.core.types.Expiration;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023-05-12
 * @description RedisUtil
 */
public class RedisUtil {

    private static RedisTemplate redisTemplate;

    private static RedisTemplate getRedisTemplate(){
        if(redisTemplate == null){
            redisTemplate = (RedisTemplate)  SpringContextUtil.getApplicationContext().getBean("redisTemplate");
        }
        return redisTemplate;
    }

    /**
     * get
     * @param key
     * @param <T>
     * @return
     */
    public static <T> T get(String key){
        return (T) getRedisTemplate().opsForValue().get(key);
    }

    /**
     * set
     * @param key
     * @param value
     * @param <T>
     */
    public static <T> void set(String key, T value){
        getRedisTemplate().opsForValue().set(key, value);
    }

    /**
     * mget
     *
     * @param keys
     * @return
     */
    public static <T> List mGet(List<String> keys){
        return getRedisTemplate().opsForValue().multiGet(keys);
    }

    /**
     * mset
     * @param map
     */
    public static <T> void mSet(Map<String, T> map){
        if(map != null && !map.isEmpty()){
            getRedisTemplate().opsForValue().multiSet(map);
        }
    }

    /**
     * setnx 方法 带超时时间
     * @param key
     * @param value
     * @param expireTime
     * @return
     */
    public static boolean setnxWithExpSecs(String key, long value, long expireTime){
        return setnxWithExpSecs(key, String.valueOf(value), expireTime);
    }

    /**
     * setnx 方法 带超时时间
     * @param key
     * @param value
     * @param expireTime
     * @return
     */
    public static boolean setnxWithExpSecs(String key, String value, long expireTime){
        return (boolean) getRedisTemplate().execute((RedisCallback) (connection) ->
                connection.set(key.getBytes(), value.getBytes(), Expiration.seconds(expireTime), RedisStringCommands.SetOption.SET_IF_ABSENT)
        );
    }

    /**
     * setnx 方法 带超时时间
     * @param key
     * @param value
     * @param expireTime
     * @return
     */
    public static boolean setnxWithExpMilSecs(String key, long value, long expireTime){
        return setnxWithExpMilSecs(key, String.valueOf(value), expireTime);
    }

    /**
     * setnx 方法 带超时时间
     * @param key
     * @param value
     * @param expireTime
     * @return
     */
    public static boolean setnxWithExpMilSecs(String key, String value, long expireTime){
        return (boolean) getRedisTemplate().execute((RedisCallback) (connection) ->
                connection.set(key.getBytes(), value.getBytes(), Expiration.milliseconds(expireTime), RedisStringCommands.SetOption.SET_IF_ABSENT)
        );
    }

    /** --------------------set相关操作-------------------------- */

    /**
     * set添加元素
     *
     * @param key
     * @param values
     * @return
     */
    public static Long sAdd(String key, String... values) {
        return getRedisTemplate().opsForSet().add(key, values);
    }
    /**
     * 获取集合所有元素
     *
     * @param key
     * @return
     */
    public static <T> Set<T> setMembers(String key) {
        return getRedisTemplate().opsForSet().members(key);
    }

    /**
     * 判断集合是否包含value
     *
     * @param key
     * @param value
     * @return
     */
    public static Boolean sIsMember(String key, Object value) {
        return getRedisTemplate().opsForSet().isMember(key, value);
    }

    /**
     * 获取两个集合的交集
     *
     * @param key
     * @param otherKey
     * @return
     */
    public static <T> Set<T> sIntersect(String key, String otherKey) {
        return getRedisTemplate().opsForSet().intersect(key, otherKey);
    }

    /**
     * 获取两个集合的并集
     *
     * @param key
     * @param otherKeys
     * @return
     */
    public static <T> Set<T> sUnion(String key, String otherKeys) {
        return getRedisTemplate().opsForSet().union(key, otherKeys);
    }

    /**------------------zSet相关操作--------------------------------*/

    /**
     * 添加元素,有序集合是按照元素的score值由小到大排列
     *
     * @param key
     * @param value
     * @param score
     * @return
     */
    public static Boolean zAdd(String key, String value, double score) {
        return getRedisTemplate().opsForZSet().add(key, value, score);
    }

    /**
     * 获取集合的元素, 从小到大排序
     *
     * @param key
     * @param start 开始位置
     * @param end   结束位置, -1查询所有
     * @return
     */
    public static <T> Set<T> zRange(String key, long start, long end) {
        return getRedisTemplate().opsForZSet().range(key, start, end);
    }
    /**
     * 获取集合的元素, 从大到小排序
     *
     * @param key
     * @param start
     * @param end
     * @return
     */
    public static <T> Set<T>  zReverseRange(String key, long start, long end) {
        return getRedisTemplate().opsForZSet().reverseRange(key, start, end);
    }
    /**
     * 获取集合元素, 并且把score值也获取
     *
     * @param key
     * @param start
     * @param end
     * @return
     */
    public static <T> Set<ZSetOperations.TypedTuple<T>> zRangeWithScores(String key, long start,
                                                                   long end) {
        return getRedisTemplate().opsForZSet().rangeWithScores(key, start, end);
    }
    /**
     * 删除key
     *
     * @param key
     */
    public static void delete(String key) {
        getRedisTemplate().delete(key);
    }


    public static Boolean expire(String key, long time, TimeUnit timeUnit) {
        return getRedisTemplate().expire(key, time, timeUnit);
    }

    /** ------------------------list相关操作---------------------------- */

    /**
     * 通过索引获取列表中的元素
     *
     * @param key
     * @param index
     * @return
     */
    public static <T> T lIndex(String key, long index) {
        return (T) getRedisTemplate().opsForList().index(key, index);
    }

    /**
     * 获取列表指定范围内的元素
     *
     * @param key
     * @param start 开始位置, 0是开始位置
     * @param end   结束位置, -1返回所有
     * @return
     */
    public static <T> List<T> lRange(String key, long start, long end) {
        return getRedisTemplate().opsForList().range(key, start, end);
    }
    /**
     * @param key
     * @param value
     * @return
     */
    public static <T> Long rightPush(String key, T value) {
        return getRedisTemplate().opsForList().rightPush(key, value);
    }

    /**
     * @param key
     * @param values
     * @return
     */
    public static <T> Long rightPushAll(String key, List<T> values) {
        return getRedisTemplate().opsForList().rightPushAll(key, values);
    }
}
