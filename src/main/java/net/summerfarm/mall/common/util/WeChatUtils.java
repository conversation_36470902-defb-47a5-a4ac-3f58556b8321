package net.summerfarm.mall.common.util;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.mall.Conf;
import net.summerfarm.mall.common.config.DynamicConfig;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.contexts.SpringContextUtil;
import net.summerfarm.mall.model.dto.merchant.MerchantB2bRespDTO;
import net.summerfarm.mall.model.dto.merchant.WxB2BReqDTO;
import net.summerfarm.mall.model.vo.wechat.WeChatOpenIdUnionId;
import net.summerfarm.mall.service.facade.AuthWechatFacade;
import net.summerfarm.mall.wechat.api.MenuApi;
import net.summerfarm.mall.wechat.api.constant.ApiConsts;
import net.summerfarm.mall.wechat.enums.MenuType;
import net.summerfarm.mall.wechat.model.Menu;
import net.summerfarm.mall.wechat.model.MenuButton;
import net.summerfarm.mall.wechat.utils.HttpUtil;
import net.xianmu.common.enums.base.auth.WechatMiniProgramChannelEnum;
import net.xianmu.common.enums.base.auth.WxOfficialAccountsChannelEnum;
import net.xianmu.common.exception.BizException;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMethod;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

import static net.summerfarm.mall.wechat.api.constant.ApiConsts.WX_USER_LIST_URL;

/**
 * @Package: net.summerfarm.common.utils
 * @Description:
 * @author: <EMAIL>
 * @Date: 2016/10/8
 */
@Slf4j
public class WeChatUtils {



    /**
     * 获取openID
     *
     * @param code
     * @return
     */
    public static JSONObject getOpenId(String code) {
        String url = String.format(ApiConsts.WX_USER_BASE_INFO, Conf.APP_Id, Conf.APP_Secret, code);
        String result = HttpUtil.sendHttps(url, RequestMethod.GET, null, 2000);
        if (StringUtils.isBlank(result)) {
            log.error("静默获取openid失败");
            return null;
        }
        log.info(result);
        JSONObject userInfo = JSONObject.parseObject(result);
        if(StringUtils.isNotBlank(userInfo.getString("errcode"))){
            log.error("获取openid错误");
            return null;
        }
        return userInfo;
    }

    /**
     * 获取openID
     *
     * @param code
     * @return
     */
    public static JSONObject getMpOpenId(String code) {
        String url = String.format(ApiConsts.WX_MP_OPENID, Conf.MP_APP_ID, Conf.MP_APP_SECRET, code);
        String result = HttpUtil.sendHttps(url, RequestMethod.GET, null, 2000);
        return JSONObject.parseObject(result);
    }

    public static boolean onLoadMenu(Menu menu, String accessToken){
        if (menu == null||CollectionUtils.isEmpty(menu.getButton())) {
            log.error("菜单为空", new RuntimeException("菜单为空，创建菜单失败"));
            return false;
        }
        for (MenuButton menuButton : menu.getButton()) {
            updateMenuUrl(menuButton);
        }

        return createMenu(menu, accessToken);
    }

    /**
     * 递归更新菜单按钮的URL，用于拼接微信授权链接。
     * 主要针对VIEW类型的菜单，将原始URL替换为包含微信授权的URL。
     *
     * @param menuButton 待更新的菜单按钮。如果为null，则直接返回。
     */
    private static void updateMenuUrl(MenuButton menuButton) {
        if (menuButton == null) {
            return;
        }

        // 2. 检查是否需要更新URL：
        //    - 菜单按钮的URL不为空，并且
        //    - 菜单按钮的类型是VIEW（即跳转网页）。
        if (org.apache.commons.lang3.StringUtils.isNotBlank(menuButton.getUrl())) {
            // 3. 更新URL：调用getWeChatCode方法，将原始URL转换为带有微信授权的URL。
            //    - getWeChatCode方法负责拼接微信授权链接，确保用户在点击菜单时能获得授权。
            // 如果URL是https://开头的，说明不需要拼接微信授权链接
            if (!menuButton.getUrl().startsWith("https://")) {
                menuButton.setUrl(getWeChatCode(menuButton.getUrl()));
                menuButton.setType(MenuType.VIEW.toString());
            } else {
                log.warn("URL已经是https:", menuButton.getUrl());
            }
        } else {
            menuButton.setType(null);
        }

        // 4. 递归处理子菜单：如果菜单按钮包含子菜单，则递归调用本方法，更新所有子菜单的URL。
        if (!CollectionUtils.isEmpty(menuButton.getSubButton())) {
            // 5. 遍历子菜单列表。
            for (MenuButton subButton : menuButton.getSubButton()) {
                // 6. 递归调用：对每个子菜单都执行updateMenuUrl方法，确保所有层级的菜单URL都被正确更新。
                updateMenuUrl(subButton);
            }
        }
    }

    private static boolean createMenu(Menu menu, String accessToken) {
        MenuApi menuApi = new MenuApi();
        String message = menuApi.createMenu(menu, accessToken);
        log.info("菜单创建结果:{}", message);

        try {
            JSONObject jsonObject = JSONObject.parseObject(message);
            if (jsonObject != null) {
                Integer errcode = jsonObject.getInteger("errcode");
                String errmsg = jsonObject.getString("errmsg");
                if (errcode != null && errcode == 0 && "ok".equals(errmsg)) {
                    log.info("菜单创建成功");
                    return true;
                } else {
                    log.error("菜单创建失败，错误码：{}，错误信息：{}", errcode, errmsg, new BizException("菜单创建失败"));
                }
            } else {
                log.error("创建菜单后返回结果为空");
            }
        } catch (Exception e) {
            log.error("解析创建菜单返回结果失败", e);
        }
        return false;
    }

    public static String getWeChatCode(String url) {
        return getWeChatCode(url, null);
    }

    /**
     *  获取授权code
     *  switch (scope)
     *  {
     *  case snsapi_base : 静默授权; break;
     *  case snsapi_userinfo : 主动授权; break;
     *  }
     *  强制主动授权
     * @param url
     * @param scope
     * @return
     * @throws UnsupportedEncodingException
     */
    public static String getWeChatCode(String url, String scope) {
        String code = null;
        url = url.contains(Global.DOMAIN_NAME) ? url : Global.DOMAIN_NAME + url;
        try {
            code = "https://open.weixin.qq.com/connect/oauth2/authorize?"
                    + "appid=" + URLEncoder.encode(Conf.APP_Id, "UTF-8")
                    + "&redirect_uri=" + URLEncoder.encode(url, "UTF-8")
                    + "&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect";
        } catch (UnsupportedEncodingException e) {
            log.error("获取微信授权码失败", e);
        }
        return code;
    }


    /**
     *  获取授权code
     *  switch (scope)
     *  {
     *  case snsapi_base : 静默授权; break;
     *  case snsapi_userinfo : 主动授权; break;
     *  }
     *  强制主动授权
     * @param url
     * @param scope
     * @return
     * @throws UnsupportedEncodingException
     */
    public static String getPopWeChatCode(String url, String scope) {
        String code = null;
        DynamicConfig dynamicConfig = SpringContextUtil.getBean("dynamicConfig", DynamicConfig.class);
        String popDomain = dynamicConfig.getPopMallDomain();
        log.info("当前域名：popDomain:{}", popDomain);
        url = url.contains(popDomain) ? url : popDomain + url;
        try {
            code = "https://open.weixin.qq.com/connect/oauth2/authorize?"
                    + "appid="+ URLEncoder.encode(Conf.POP_APP_Id,"UTF-8")
                    +"&redirect_uri="+URLEncoder.encode(url,"UTF-8")
                    + "&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect";
        } catch (UnsupportedEncodingException e) {
            log.error("获取POP微信授权码失败", e);
        }
        return code;
    }


    /**
     * 返回信息说明
     * subscribe	用户是否订阅该公众号标识，值为0时，代表此用户没有关注该公众号，拉取不到其余信息。
     * openid	用户的标识，对当前公众号唯一
     * nickname	用户的昵称
     * sex	用户的性别，值为1时是男性，值为2时是女性，值为0时是未知
     * city	用户所在城市
     * country	用户所在国家
     * province	用户所在省份
     * language	用户的语言，简体中文为zh_CN
     * headimgurl	用户头像，最后一个数值代表正方形头像大小（有0、46、64、96、132数值可选，0代表640*640正方形头像），用户没有头像时该项为空。若用户更换头像，原有头像URL将失效。
     * subscribe_time	用户关注时间，为时间戳。如果用户曾多次关注，则取最后关注时间
     * unionid	只有在用户将公众号绑定到微信开放平台帐号后，才会出现该字段。
     * remark	公众号运营者对粉丝的备注，公众号运营者可在微信公众平台用户管理界面对粉丝添加备注
     * groupid	用户所在的分组ID（兼容旧的用户分组接口）
     * tagid_list	用户被打上的标签ID列表
     * subscribe_scene	返回用户关注的渠道来源，ADD_SCENE_SEARCH 公众号搜索，ADD_SCENE_ACCOUNT_MIGRATION 公众号迁移，ADD_SCENE_PROFILE_CARD 名片分享，ADD_SCENE_QR_CODE 扫描二维码，ADD_SCENEPROFILE LINK 图文页内名称点击，ADD_SCENE_PROFILE_ITEM 图文页右上角菜单，ADD_SCENE_PAID 支付后关注，ADD_SCENE_OTHERS 其他
     * qr_scene	二维码扫码场景（开发者自定义）
     * qr_scene_str	二维码扫码场景描述（开发者自定义）
     *
     * @param openid openid
     * @return {@link <a>https://mp.weixin.qq.com/wiki?t=resource/res_main&id=mp1421140839<a/>}
     */
    /**
     * 返回信息说明
     * subscribe	用户是否订阅该公众号标识，值为0时，代表此用户没有关注该公众号，拉取不到其余信息。
     * openid	用户的标识，对当前公众号唯一
     * nickname	用户的昵称
     * sex	用户的性别，值为1时是男性，值为2时是女性，值为0时是未知
     * city	用户所在城市
     * country	用户所在国家
     * province	用户所在省份
     * language	用户的语言，简体中文为zh_CN
     * headimgurl	用户头像，最后一个数值代表正方形头像大小（有0、46、64、96、132数值可选，0代表640*640正方形头像），用户没有头像时该项为空。若用户更换头像，原有头像URL将失效。
     * subscribe_time	用户关注时间，为时间戳。如果用户曾多次关注，则取最后关注时间
     * unionid	只有在用户将公众号绑定到微信开放平台帐号后，才会出现该字段。
     * remark	公众号运营者对粉丝的备注，公众号运营者可在微信公众平台用户管理界面对粉丝添加备注
     * groupid	用户所在的分组ID（兼容旧的用户分组接口）
     * tagid_list	用户被打上的标签ID列表
     * subscribe_scene	返回用户关注的渠道来源，ADD_SCENE_SEARCH 公众号搜索，ADD_SCENE_ACCOUNT_MIGRATION 公众号迁移，ADD_SCENE_PROFILE_CARD 名片分享，ADD_SCENE_QR_CODE 扫描二维码，ADD_SCENEPROFILE LINK 图文页内名称点击，ADD_SCENE_PROFILE_ITEM 图文页右上角菜单，ADD_SCENE_PAID 支付后关注，ADD_SCENE_OTHERS 其他
     * qr_scene	二维码扫码场景（开发者自定义）
     * qr_scene_str	二维码扫码场景描述（开发者自定义）
     *
     * @param openid openid
     * @return {@link <a>https://mp.weixin.qq.com/wiki?t=resource/res_main&id=mp1421140839<a/>}
     */
    public static JSONObject getUserInfo(String openid, String channelCode) {
        AuthWechatFacade authWechatFacade = SpringContextUtil.getBean("authWechatFacadeImpl", AuthWechatFacade.class);
        //String redisAccessToken = (String) redisTemplate.opsForValue().get(WechatConstant.MALL_WECHAT_ACCESS_TOKEN);
        String redisAccessToken = authWechatFacade.queryChannelToken(channelCode);
        String url = String.format(ApiConsts.WX_UNIONID_API, redisAccessToken, openid);
        log.info("请求url：" + url);
        String result = HttpUtil.sendHttps(url, RequestMethod.GET);
        if (StringUtils.isBlank(result)) {
            return null;
        }

        log.info("union 返回值：" + result);
        JSONObject jsonObject = JSONObject.parseObject(result);
        if (jsonObject.get("errcode") != null) {
            log.error("获取用户微信信息错误");
            return null;
        }

        return jsonObject;
    }

    /**
     * mobile_phone	手机号
     * retail_type	一级门店类型
     * sub_retail_type	二级门店类型
     * retail_address	门店地址
     * retail_name	门店名称
     * identification 营业执照注册号
     * principal 企业名称
     * legal_person_name	法人姓名
     * openid	门店负责人openid
     * status	认证状态	1：已完成认证
     * auth_mode	认证方式	0：营业执照认证；
     * 1：预录入且不在店主包内；
     * 2：快速认证（即无预录入，需上传门头照分支）;
     * 3：预录入且在店主包内
     * auth_time	认证时间	认证完成那一刻的时间戳
     * grant_time	授权时间	授权给小程序那一刻的时间戳
     */
    public static MerchantB2bRespDTO getB2BUserInfo(String openid) {
        try {
            AuthWechatFacade authWechatFacade = SpringContextUtil.getBean("authWechatFacadeImpl", AuthWechatFacade.class);
            String redisAccessToken = authWechatFacade.queryChannelToken(WechatMiniProgramChannelEnum.XM_MALL_MP.channelCode);
            String url = String.format(ApiConsts.WX_MP_B2B_AUTHORIZATION_API, redisAccessToken);
            log.info("b2b获取名店详情获取 openid{} url{} ",openid, url);
            Map<String,String>  param= new HashMap<>();
            param.put("openid",openid);
            String result = HttpUtil.sendHttps(url, RequestMethod.POST, JSONUtil.toJsonStr(param));
            log.info("b2b获取名店详情获取返回 openId{} result{}", openid, result);

            if (StringUtils.isBlank(result)) {
                return null;
            }
            MerchantB2bRespDTO merchantB2bRespDTO = JSONUtil.toBean(result, MerchantB2bRespDTO.class);
            return merchantB2bRespDTO;
        }catch (Exception e){
            log.error("b2b获取名店详情调用异常 openId{}", openid, e);

            return null;
        }
    }

    /**
     *
     * @param wxB2BReqDTO
     * @return
     */
    public static cn.hutool.json.JSONObject foreEntering(WxB2BReqDTO wxB2BReqDTO) {
        AuthWechatFacade authWechatFacade = SpringContextUtil.getBean("authWechatFacadeImpl", AuthWechatFacade.class);
        String redisAccessToken = authWechatFacade.queryChannelToken(WechatMiniProgramChannelEnum.XM_MALL_MP.channelCode);
        String url = String.format(ApiConsts.WX_MP_B2B_FOREENTERING, redisAccessToken);
        Map<String,List<WxB2BReqDTO>> map = new HashMap<>();
        map.put("retail_info_list",Arrays.asList(wxB2BReqDTO));
        String param = JSONUtil.toJsonStr(map);
        log.info("b2b预录入接入：" + url +"param:"+ JSONUtil.toJsonStr(param));
        String result = HttpUtil.sendHttps(url, RequestMethod.POST, param);
        if (StringUtils.isBlank(result)) {
            log.error("微信b2b门店调用异常 result{}",result);
            return null;
        }
        log.info("获取b2b请求url 返回值：" + result);
        cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(result);
        if (!Objects.equals(jsonObject.get("errcode"), 0)) {
            log.error("b2b预录入调用接口错误",result );
            return null;
        }
        return jsonObject;
    }


    public static List<WeChatOpenIdUnionId> getUnionidsByOpenList(List<String> openId) {
        if (CollectionUtils.isEmpty(openId)){
            return new ArrayList<>();
        }
        AuthWechatFacade authWechatFacade = SpringContextUtil.getBean("authWechatFacadeImpl", AuthWechatFacade.class);
        String redisAccessToken = authWechatFacade.queryChannelToken(WxOfficialAccountsChannelEnum.XM_MALL.channelCode);
        String url = String.format(WX_USER_LIST_URL, redisAccessToken);

        Map<String, Object> body = new HashMap<>();
        List<Map<String, Object>> bodyDetail = new ArrayList<>(openId.size());
        openId.forEach(
                it -> {
                    Map<String, Object> vo = new HashMap<>();
                    vo.put("openid", it);
                    vo.put("lang", "zh_CN");
                    bodyDetail.add(vo);
                }
        );
        body.put("user_list", bodyDetail);
        String resp = cn.hutool.http.HttpUtil.post(url, JSONUtil.toJsonStr(body), 2000);
        log.info("access_token请求 user_list 接口 body {}",JSONUtil.toJsonStr(body));
        if (org.springframework.util.StringUtils.isEmpty(resp)) {
            log.error("微信调用接口失败", new RuntimeException(resp));
            return new ArrayList<>();
        }
        cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(resp);
        if (jsonObject.get("user_info_list") == null) {
            log.warn("微信调用接口失败", new RuntimeException(resp));
            return new ArrayList<>();
        }
        return cn.hutool.json.JSONUtil.toList(jsonObject.getJSONArray("user_info_list"), WeChatOpenIdUnionId.class)
                .stream().filter(it -> !org.springframework.util.StringUtils.isEmpty(it.getUnionid())).collect(Collectors.toList());
    }

    private static final String CODE2SESSION_URL = "https://api.weixin.qq.com/sns/jscode2session";

    /**
     * 调用微信 auth.code2Session 接口
     *
     * @param appId    小程序的 AppID
     * @param appSecret 小程序的 AppSecret
     * @param jsCode   登录时获取的 code
     * @return 返回 JSON 对象包含 openid、session_key 等信息
     */
    public static JSONObject code2Session(String appId, String appSecret, String jsCode) {
        // 构建请求参数
        JSONObject params = new JSONObject();
        params.put("appid", appId);
        params.put("secret", appSecret);
        params.put("js_code", jsCode);
        params.put("grant_type", "authorization_code");

        // 发送 GET 请求
        String response = cn.hutool.http.HttpUtil.get(CODE2SESSION_URL, params);

        // 将响应结果转换为 JSON 对象返回
        JSONObject jsonObject = JSONObject.parseObject(response);
        Integer errcode = jsonObject.getInteger("errcode");
        if (errcode != null && errcode != 0) {
            log.error("调用微信接口失败，错误码：{}，错误信息：{}", errcode, jsonObject.getString("errmsg"));
            return null;
        }
        return jsonObject;
    }
}
