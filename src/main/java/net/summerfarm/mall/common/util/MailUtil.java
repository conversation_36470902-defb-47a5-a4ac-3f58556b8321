package net.summerfarm.mall.common.util;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.contexts.SpringContextUtil;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.annotation.Resource;
import javax.mail.Message;
import javax.mail.Multipart;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import javax.mail.internet.MimeUtility;
import javax.mail.util.ByteArrayDataSource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Map;

/**
 * Created by wjd on 2018/1/19.
 */
@Component
@Async
@Slf4j
public class MailUtil {


    @Resource
    private JavaMailSenderImpl mailSender;

    public  void sendMail(String subject,String text, String[] to, String[] cc){
        //非正式环境下不发送邮件
        if (!SpringContextUtil.isProduct()) {
            return;
        }
        subject = "【商城】" + subject;
        // 构建简单邮件对象，见名知意
        SimpleMailMessage smm = new SimpleMailMessage();
        // 设定邮件参数
        smm.setFrom(mailSender.getUsername());
        smm.setTo(to);
        smm.setCc(cc);
        smm.setSubject(new String(subject.getBytes(StandardCharsets.UTF_8)));
        smm.setText(new String(text.getBytes(StandardCharsets.UTF_8)));

        mailSender.send(smm);
    }

    /**
     * @description: 发送邮件并添加附件
     * @author: lzh
     * @date: 2023/6/8 16:21
     * @param: [subject, text, tos, ccs, excelParts]
     * @return: void
     **/
    public void sendMailAndExcel(String subject, String text, String[] tos, String[] ccs, Map<String, Workbook> excelParts) throws Exception {
        log.info("开始发送邮件subject:{}", subject);
        MimeMessage message = mailSender.createMimeMessage();
        message.setFrom(new InternetAddress(mailSender.getUsername()));
        for (String to: tos) {
            message.addRecipient(Message.RecipientType.TO, new InternetAddress(to));
        }
        if (ccs != null) {
            for (String cc: ccs) {
                message.addRecipients(Message.RecipientType.CC, cc);
            }
        }

        message.setSubject(new String(subject.getBytes(StandardCharsets.UTF_8)));
        message.addHeader("charset", "UTF-8");

        Multipart multipart = new MimeMultipart();
        if (!StringUtils.isEmpty(text)) {
            //文本内容
            log.info("文本内容:{}",text);
            MimeBodyPart contentPart = new MimeBodyPart();
            contentPart.setText(text, "UTF-8");
            contentPart.setHeader("Content-Type","text/html; charset=UTF-8");
            multipart.addBodyPart(contentPart);
        }

        // 附件内容 文件名 -> excel
        if (!CollectionUtils.isEmpty(excelParts)) {
            log.info("处理附件内容开始");
            excelParts.forEach((fileName,workbook) -> {
                MimeBodyPart excelPart = new MimeBodyPart();
                ByteArrayOutputStream baos = null;
                ByteArrayInputStream bais = null;
                try {
                    baos = new ByteArrayOutputStream();
                    workbook.write(baos);
                    byte[] bt = baos.toByteArray();
                    bais = new ByteArrayInputStream(bt, 0, bt.length);
                    DataSource source = new ByteArrayDataSource(bais, "application/msexcel");
                    excelPart.setDataHandler(new DataHandler(source));
                    excelPart.setFileName(MimeUtility.decodeText(fileName));
                    multipart.addBodyPart(excelPart);
                } catch (Exception e) {
                    log.error("MailUtil[]sendMail[]cause:{}", e.getMessage());
                } finally {
                    try {
                        baos.close();
                        bais.close();
                    } catch (IOException e) {
                        log.error("MailUtil[]sendMail[]cause:{}", e.getMessage());
                    }
                }
            });
        }
        log.info("执行发送邮件subject:{}", subject);
        message.setContent(multipart);
        message.setSentDate(new Date());
        message.saveChanges();
        mailSender.send(message);
    }
}
