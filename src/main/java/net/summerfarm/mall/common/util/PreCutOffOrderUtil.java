package net.summerfarm.mall.common.util;

import net.summerfarm.common.util.StringUtils;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.mapper.ConfigMapper;
import net.summerfarm.mall.model.domain.Config;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> ct
 * create at:  2021/3/8  15:41
 */
@Component
public class PreCutOffOrderUtil {

    @Resource
    private ConfigMapper configMapper;


    private static final String KEY = "pre_cut_order";

    public static final String TWENTY_THREE_KEY = "twenty_three_cut_order";

    public static final String NINE_THREE_KEY = "nine_cut_order";

    /** 是否含有特殊截单仓 */
    /*public  Boolean getIsPreCutOffStore(Integer storeNo){


        List<String> storeNoList = new ArrayList<>();

        Config config = configMapper.selectByKey(KEY);
        if(config != null && !StringUtils.isEmpty(config.getValue())){
            storeNoList.addAll(Arrays.asList(config.getValue().split(Global.SEPARATING_SYMBOL)));
        }
        Config twentyList = configMapper.selectByKey(TWENTY_THREE_KEY);
        if(twentyList != null && !StringUtils.isEmpty(twentyList.getValue())){
            storeNoList.addAll(Arrays.asList(twentyList.getValue().split(Global.SEPARATING_SYMBOL)));
        }
        Config nineList = configMapper.selectByKey(NINE_THREE_KEY);
        if(nineList != null && !StringUtils.isEmpty(nineList.getValue())){
            storeNoList.addAll(Arrays.asList(nineList.getValue().split(Global.SEPARATING_SYMBOL)));
        }

        return storeNoList.contains(String.valueOf(storeNo));

    }*/

    /**
    * 根据编号获取截单时间类型
    */
    /*public  Integer getIsPreCutOffTime(Integer storeNo){

        Integer type = Global.TYPE_NORMAL;
        Config config = configMapper.selectByKey(KEY);
        if(Objects.isNull(storeNo)){
            return type;
        }

        String value = config.getValue();
        if(!StringUtils.isEmpty(value)){
            type = Arrays.asList(value.split(Global.SEPARATING_SYMBOL)).contains(String.valueOf(storeNo)) ? Global.TYPE_TWENTY_TWO : type;
        }
        Config result = configMapper.selectByKey(TWENTY_THREE_KEY);
        if(!Objects.isNull(result) && !StringUtils.isEmpty(result.getValue())){
            type = Arrays.asList(result.getValue().split(Global.SEPARATING_SYMBOL))
                    .contains(String.valueOf(storeNo)) ? Global.TYPE_TWENTY_THREE : type;
        }
        Config nineResult = configMapper.selectByKey(NINE_THREE_KEY);
        if(!Objects.isNull(nineResult) && !StringUtils.isEmpty(nineResult.getValue())){
            type = Arrays.asList(nineResult.getValue().split(Global.SEPARATING_SYMBOL))
                    .contains(String.valueOf(storeNo)) ? Global.TYPE_NINE : type;
        }
        return type;

    }*/

    /**
     * 根据配送仓编号获取截单时间
     */
    /*public LocalDateTime getTimeByStoreNo(Integer storeNo){
        LocalDateTime closingTime = LocalDateTime.of(LocalDate.now(), Global.CLOSING_ORDER_TIME);
        Config config = configMapper.selectByKey(KEY);
        if(!Objects.isNull(config) && !StringUtils.isEmpty(config.getValue())){
            closingTime = Arrays.asList(config.getValue().split(Global.SEPARATING_SYMBOL)).contains(String.valueOf(storeNo)) ?
                    LocalDateTime.of(LocalDate.now(), Global.NJ_CLOSING_ORDER_TIME) : closingTime;
        }
        Config result = configMapper.selectByKey(TWENTY_THREE_KEY);
        if(!Objects.isNull(result) && !StringUtils.isEmpty(result.getValue())){
            closingTime =Arrays.asList(result.getValue().split(Global.SEPARATING_SYMBOL)).contains(String.valueOf(storeNo)) ?
                    LocalDateTime.of(LocalDate.now(), Global.TWENTY_THREE_CLOSING_TIME) : closingTime;
        }
        Config nineResult = configMapper.selectByKey(NINE_THREE_KEY);
        if(!Objects.isNull(nineResult) && !StringUtils.isEmpty(nineResult.getValue())){
            closingTime =Arrays.asList(nineResult.getValue().split(Global.SEPARATING_SYMBOL)).contains(String.valueOf(storeNo)) ?
                    LocalDateTime.of(LocalDate.now(), Global.NINE_CLOSING_TIME) : closingTime;
        }
        return closingTime;


    }*/

}
