package net.summerfarm.mall.common.util;

import java.math.BigDecimal;
import java.util.*;

/**
 * @Author: z<PERSON><PERSON>ta<PERSON>
 * @Date: 2023/03/16 11:53
 * @Description: price-center
 */
public class BaseUtil {

    /**
     * 是否为空
     *
     * @param arg
     * @return
     */
    public static final boolean isEmpty(Object arg) {
        return arg == null;
    }

    public static final boolean isNotEmpty(Object arg) {
        return !isEmpty(arg);
    }

    public static final boolean isNull(Object arg) {
        return arg == null;
    }

    public static final boolean isNotNull(Object arg) {
        return !isEmpty(arg);
    }

    public static final boolean isTrue(Boolean arg) {
        return arg != null && arg.equals(true);
    }


    /**
     * 是否为空
     *
     * @param arg
     * @return
     */
    public static final boolean isEmpty(Double arg) {
        return (arg == null);
    }

    public static final boolean isNotEmpty(Double arg) {
        return !isEmpty(arg);
    }

    /**
     * 是否为空
     *
     * @param arg
     * @return
     */
    public static final boolean isEmpty(String arg) {
        return (null == arg || "".equals(arg)) ? true : false;
    }

    public static final boolean isNotEmpty(String arg) {
        return !isEmpty(arg);
    }

    /**
     * 是否为空
     *
     * @param arg
     * @return
     */
    public static final boolean isEmpty(Collection arg) {
        return (arg == null || arg.size() == 0);//0不为空，不能返回true
    }

    public static final boolean isNotEmpty(Collection arg) {
        return !isEmpty(arg);
    }


    /**
     * 是否为空
     *
     * @param arg
     * @return
     */
    public static final boolean isEmpty(Map arg) {
        return (arg == null || arg.size() == 0);//0不为空，不能返回true
    }

    public static final boolean isNotEmpty(Map arg) {
        return !isEmpty(arg);
    }


    public static final boolean isEmpty(Integer arg) {
        return (arg == null);//0不为空，不能返回true
    }

    public static final boolean isNotEmpty(Integer arg) {
        return !isEmpty(arg);
    }

    /**
     * 计算商城库存 返回库存=11*实际库存+13*pdid+1021
     **/
    public static Integer cryptoInventory(Integer quantity, Long pdId) {
        if (pdId == null) {
            pdId = 0L;
        }

        return 11 * quantity + 13 * pdId.intValue() + 1021;
    }
}
