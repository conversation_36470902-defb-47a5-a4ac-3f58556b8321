package net.summerfarm.mall.common.thread;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import net.summerfarm.mall.contexts.SpringContextUtil;
import net.summerfarm.mall.contexts.WechatConstant;
import net.summerfarm.mall.Conf;
import net.summerfarm.mall.common.util.SplitUtils;
import net.summerfarm.mall.model.domain.Merchant;
import net.summerfarm.mall.service.facade.AuthWechatFacade;
import net.summerfarm.mall.wechat.api.constant.ApiConsts;
import net.summerfarm.mall.wechat.model.BaseResponse;
import net.summerfarm.mall.wechat.service.WeChatService;
import net.summerfarm.mall.wechat.token.AccessToken;
import net.summerfarm.mall.wechat.token.NetWorkHelper;
import net.summerfarm.mall.wechat.utils.HttpUtil;
import net.xianmu.common.enums.base.auth.WxOfficialAccountsChannelEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.RequestMethod;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ct
 * create at:  2021/6/10  12:58
 */
public class TagThread implements Runnable {

    private List<Merchant> merchants;

    private CountDownLatch countDownLatch;

    private AccessToken accessToken;

    private  Integer tagId;

    private  String tagManage;

    private static final Logger logger = LoggerFactory.getLogger(WeChatService.class);

    public TagThread(List<Merchant> merchants,CountDownLatch countDownLatch,
                     Integer tagId,String tagManage){
        this.merchants = merchants;
        this.countDownLatch = countDownLatch;
        this.tagId = tagId;
        this.tagManage = tagManage;
    }

    @Override
    public void run() {
        //开始执行操作
        //每50个执行一次操作
        /*List<List<Merchant>> split = SplitUtils.fixedSplit(merchants, 50);
        try {
            split.forEach(merchantList -> {
                AuthWechatFacade authWechatFacade = SpringContextUtil.getBean("authWechatFacadeImpl", AuthWechatFacade.class);
                //String redisAccessToken = (String) redisTemplate.opsForValue().get(WechatConstant.MALL_WECHAT_ACCESS_TOKEN);
                String redisAccessToken = authWechatFacade.queryChannelToken(WxOfficialAccountsChannelEnum.XM_MALL.channelCode);
                String signUrl = ApiConsts.SIGN_TAGS_API+ redisAccessToken;
                JSONArray openIdArray = new JSONArray();
                List<String> openIdList = merchantList.stream().map(Merchant::getOpenid).collect(Collectors.toList());
                openIdArray.addAll(openIdList);
                JSONObject signParam = new JSONObject();
                signParam.put("openid_list",openIdArray);
                signParam.put("tagid",tagId);
                String signResultStr = HttpUtil.sendHttps(signUrl, RequestMethod.POST,signParam.toJSONString());
                BaseResponse signResult = JSONObject.parseObject(signResultStr,BaseResponse.class);
                if (signResult.getErrcode() == 0){
                    // DO NOTHING
                }else if (signResult.getErrcode() == 40001){
                    //accessToken失效了
                    NetWorkHelper netHelper = new NetWorkHelper();
                    String Url = String.format(ApiConsts.ACCESS_TOKEN_API, Conf.APP_Id,Conf.APP_Secret);
                    String result = netHelper.getHttpsResponse(Url,"");
                    JSONObject json = JSONObject.parseObject(result);

                    AccessToken token = new AccessToken();
                    token.setErrcode(json.getIntValue("errcode"));
                    token.setErrmsg(json.getString("errmsg"));
                    token.setAccessToken(json.getString("access_token"));
                    token.setExpiresin(json.getInteger("expires_in"));
                    accessToken = token;
                }else if (signResult.getErrcode() == 45159){ // 标签被删除了
                    logger.info("公众号打标结束,标签:{},标签已被删除", tagManage);
                }else {
                    logger.info("用户{}添加标签失败,原因：{}" , signResult.getErrcode());
                }
            });
        } finally {
            countDownLatch.countDown();
        }
        logger.info("部分执行完成");*/

    }
}
