package net.summerfarm.mall.common.mq.ofc;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.common.mq.ofc.Input.DeliveryOrderMessage;
import net.summerfarm.mall.common.mq.ofc.Input.RefreshAfterSaleData;
import net.summerfarm.mall.contexts.OfcRocketMqConstant;
import net.summerfarm.mall.service.ofc.OfcDeliveryService;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> 刷新售后状态消息
 */
@Slf4j
@Component
@MqListener(
        topic = OfcRocketMqConstant.Topic.TOPIC_OFC_DELIVERY,
        consumerGroup = OfcRocketMqConstant.ConsumeGroup.GID_OFC_TO_MALL_REFRESH_AFTER_SALE,
        tag = OfcRocketMqConstant.Tag.REFRESH_AFTER_SALE,
        maxReconsumeTimes = 2)
public class RefreshAfterSaleMessageListener extends AbstractMqListener<RefreshAfterSaleData> {

    @Autowired
    OfcDeliveryService ofcDeliveryService;

    @Override
    public void process(RefreshAfterSaleData refreshAfterSaleData) {
        log.info("刷新售后状态消息：{}", JSONObject.toJSONString(refreshAfterSaleData));
        ofcDeliveryService.refreshAfterSale(refreshAfterSaleData.getAfterSaleList());
    }

}
