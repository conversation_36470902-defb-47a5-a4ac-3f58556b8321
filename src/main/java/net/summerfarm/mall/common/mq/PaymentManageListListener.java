package net.summerfarm.mall.common.mq;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.enums.RedissonLockKey;
import net.summerfarm.mall.contexts.MQTopicConstant;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.service.MasterPaymentService;
import net.summerfarm.mall.service.PaymentService;
import net.summerfarm.mall.service.RefundService;
import net.xianmu.log.helper.LogConfigHolder;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 支付、退款成功回调通知
 * @createTime 2021年12月23日 12:48:00
 * 2023:06:08更新消息格式，注意：目前相比之前取消了‘重试间隔时间’这个配置（因为目前是非顺序消费），顺序消息会有这个配置。
 */
@Slf4j
@Component
@MqListener(topic = MQTopicConstant.MALL_PAYMENT_LIST,
        tag = MQTopicConstant.SELECTOR_EXPRESSION,
        consumerGroup = MQTopicConstant.GID_MALL_PAYMENT,
        maxReconsumeTimes = 5,
        consumeThreadMax = 32)
public class PaymentManageListListener extends AbstractMqListener<MQData> {
    @Resource
    private PaymentService paymentService;
    @Resource
    private RefundService refundService;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private MasterPaymentService masterPaymentService;

    @Override
    public void process(MQData mqData) {
        try {
            log.info("支付分区顺序消息rocketmq receive：{}", JSONObject.toJSONString(mqData));
            String data = String.valueOf(mqData.getData());

            if (MType.PAYMENT_NOTIFY_SUCCESS.name().equals(mqData.getType())) {
                PayNotifySuccessData payNotifySuccessData = JSONObject.parseObject(data, PayNotifySuccessData.class);
                Payment payment = payNotifySuccessData.getPayment();
                Orders order = payNotifySuccessData.getOrder();

                //同一mId并发下单控制
                RLock redissonLock = redissonClient.getLock(RedissonLockKey.PAY_NOTIFY + ":order:" + order.getOrderNo());
                try {
                    boolean flag = redissonLock.tryLock(0L, 30L, TimeUnit.SECONDS);
                    if (!flag) {
                        log.error("支付回调信息正在处理中，orderNo:{}", order.getOrderNo());
                    }

                    paymentService.notifySuccess(payment, order);
                } catch (InterruptedException e) {
                    log.error("锁获取异常", e);
                    throw new DefaultServiceException("支付回调处理锁获取异常");
                } finally {
                    if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()){
                        redissonLock.unlock();
                    }
                }

            } else if (MType.REFUND_NOTIFY_SUCCESS.name().equals(mqData.getType())) {
                Refund refund = JSONObject.parseObject(data, Refund.class);
                refundService.notifySuccess(refund);
            } else if (MType.MASTER_PAYMENT_NOTIFY_SUCCESS.name().equals(mqData.getType())) {
                PayNotifySuccessDataV2 successDataV2 = JSONObject.parseObject(data, PayNotifySuccessDataV2.class);
                MasterPayment masterPayment = successDataV2.getMasterPayment();
                MasterOrder masterOrder = successDataV2.getMasterOrder();

                //同一mId并发下单控制
                RLock redissonLock = redissonClient.getLock(RedissonLockKey.PAY_NOTIFY + ":order:" + masterOrder.getMasterOrderNo());
                try {
                    boolean flag = redissonLock.tryLock(3L, 30L, TimeUnit.SECONDS);
                    if (!flag) {
                        log.error("支付回调信息正在处理中，orderNo:{}", masterOrder.getMasterOrderNo());
                    }

                    masterPaymentService.masterPaymentNotifyHandler(masterPayment);
                } catch (InterruptedException e) {
                    log.error("锁获取异常", e);
                    throw new DefaultServiceException("支付回调处理锁获取异常");
                } finally {
                    if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()){
                        redissonLock.unlock();
                    }
                }
            } else {
                log.error("支付分区顺序不认识的消息格式!");
            }
        } catch (Exception e) {
            boolean customException = e instanceof DefaultServiceException;
            if (!customException) {
                throw new DefaultServiceException("支付分区顺序消费失败", e);
            }
        }finally {
            LogConfigHolder.removeInboundFlag();
        }
    }
}
