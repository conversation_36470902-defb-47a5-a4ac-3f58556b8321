package net.summerfarm.mall.common.mq.ofc.Input;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class ShortOutItemEntityInput implements Serializable {
    /**
     * 外部履约物品ID 对应SKU
     */
    private String outItemId;

    /**
     * 履约单号
     */
    private Long fulfillmentNo;

    /**
     * 履约物品名称
     */
    private String itemName;

    /**
     * 履约物品状态标志: 0不缺货，1缺货
     */
    private Boolean statusTag;

    /**
     * 实际履约数量
     */
    private Integer actualAmount;

    /**
     * 商品规格
     */
    private String weight;

    /**
     * 履约数量
     */
    private Integer amount;

    /**
     * 图片
     */
    private String picture;

    /**
     * 商品属性: 10自营，11代仓
     */
    private Integer itemAttribute;

    /**
     * 拒收标志：0false不拒收，1true拒收
     */
    private Boolean refuseTag;

    /**
     * 库存仓
     */
    private Integer warehouseNo;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * pdId
     */
    private Long pdId;

    //以下为补充字段
    /**
     * 履约项类型
     */
    private String outerItemType;

    /**
     * 父品类名称
     */
    private String parentCategory;

    /**
     * 履约物品价格
     */
    private BigDecimal originalPrice;

    /**
     * 体积
     */
    private String volume;

    /**
     * 单位
     */
    private String unit;

    /**
     * sku类型
     */
    private Integer skuType;

    /**
     * product类型
     */
    private Integer productType;

    /**
     * 仓储区域
     */
    private Boolean storageLocation;

    /**
     * 商品规格-重量kg
     */
    private Boolean weightNum;

    /**
     * 缺货数量
     */
    private Integer shortCount;

    /**
     * 外部订单号 --orderNo
     */
    private String outOrderId;
}
