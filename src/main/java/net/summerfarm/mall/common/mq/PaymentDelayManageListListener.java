package net.summerfarm.mall.common.mq;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.mall.contexts.MQTopicConstant;
import net.summerfarm.mall.payments.common.delayqueue.PaymentDelayQueueItem;
import net.summerfarm.mall.payments.common.delayqueue.RefundDelayQueueItem;
import net.summerfarm.mall.payments.request.PaymentHandler;
import net.xianmu.common.exception.BizException;
import net.xianmu.log.helper.LogConfigHolder;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 支付、退款延时查询
 * @createTime 2021年12月23日 12:03:00
 * 2023:06:08更新消息格式，注意：目前相比之前取消了‘重试间隔时间’这个配置（因为目前是非顺序消费），顺序消息会有这个配置。
 */
@Slf4j
@Component
@MqListener(
        topic = MQTopicConstant.MALL_PAYMENT_DELAY_LIST,
        consumerGroup = MQTopicConstant.GID_MALL_COUNTER_CHECK,
        tag = MQTopicConstant.SELECTOR_EXPRESSION,
        maxReconsumeTimes = 5,
        consumeThreadMax = 32)
public class PaymentDelayManageListListener extends AbstractMqListener<MQData> {

    @Resource
    private PaymentHandler paymentHandler;

    @Override
    public void process(MQData mqData) {
        try {
            log.info("支付延时查询消息rocketmq receive：{}", JSONObject.toJSONString(mqData));
            String data = String.valueOf(mqData.getData());
            if (MType.PAYMENT_STATUS_QUERY.name().equals(mqData.getType())) {
                PaymentDelayQueueItem paymentDelayQueueItem = JSONObject.parseObject(data, PaymentDelayQueueItem.class);
                paymentHandler.syncPaymentResult(paymentDelayQueueItem.getPayOrderNo());
            } else if (MType.REFUND_STATUS_QUERY.name().equals(mqData.getType())) {
                RefundDelayQueueItem refundDelayQueueItem = JSONObject.parseObject(data, RefundDelayQueueItem.class);
                paymentHandler.queryRefund(refundDelayQueueItem.getRefundNo());
            } else if (MType.MASTER_PAYMENT_STATUS_QUERY.name().equals(mqData.getType())) {
                PaymentDelayQueueItem paymentDelayQueueItem = JSONObject.parseObject(data, PaymentDelayQueueItem.class);
                paymentHandler.syncPaymentResultV2(paymentDelayQueueItem.getPayOrderNo());
            } else {
                log.error("支付延时查询不认识的消息格式!");
            }
        } catch (Exception e) {
            boolean customException = e instanceof DefaultServiceException;
            if (!customException) {
                throw new BizException("支付延时查询消费失败", e);
            }
        }finally {
            LogConfigHolder.removeInboundFlag();
        }
    }

}
