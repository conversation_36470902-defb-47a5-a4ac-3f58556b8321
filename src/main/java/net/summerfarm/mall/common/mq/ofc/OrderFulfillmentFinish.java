package net.summerfarm.mall.common.mq.ofc;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.common.mq.ofc.Input.CommonFulfillmentFinishMessageDTO;
import net.summerfarm.mall.contexts.OfcRocketMqConstant;
import net.summerfarm.mall.service.ofc.OfcDeliveryService;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 配送完成
 */
@Slf4j
@Component
@MqListener(
        topic = OfcRocketMqConstant.Topic.TOPIC_OFC_COMMON_TASK_FINISH,
        consumerGroup = OfcRocketMqConstant.ConsumeGroup.GID_OFC_TO_MALL_ORDER_FULFILLMENT_FINISH,
        tag = OfcRocketMqConstant.Tag.FULFILLMENT_FINISH_ORDER + "||" + OfcRocketMqConstant.Tag.FULFILLMENT_FINISH_TIMING_ORDER,
        maxReconsumeTimes = 5)
public class OrderFulfillmentFinish extends AbstractMqListener<CommonFulfillmentFinishMessageDTO> {

    @Autowired
    OfcDeliveryService ofcDeliveryService;

    @Override
    public void process(CommonFulfillmentFinishMessageDTO commonFulfillmentFinishMessage) {
        log.info("履约配送完成信息：{}", JSONObject.toJSONString(commonFulfillmentFinishMessage));
        ofcDeliveryService.orderFulfillmentMessage(commonFulfillmentFinishMessage);
    }
}
