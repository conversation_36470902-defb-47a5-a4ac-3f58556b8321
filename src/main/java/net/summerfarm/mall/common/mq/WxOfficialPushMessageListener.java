package net.summerfarm.mall.common.mq;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.mall.model.dto.merchant.FeedBackMessageDTO;
import net.summerfarm.mall.wechat.service.WechatMsgService;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


import static net.summerfarm.mall.contexts.Global.*;


/**
 * 微信公众号推送
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@MqListener(
        topic = TOPIC_CRM_WX_OFFICIAL_PUSH,
        consumerGroup = GID_CRM_WX_FEEDBACK,     //group：不用和生产者group相同
        tag = TAG_CRM_WX_FEEDBACK,
        maxReconsumeTimes = 2, consumeThreadMax = 32)
public class WxOfficialPushMessageListener extends AbstractMqListener<FeedBackMessageDTO> {

    @Autowired
    private WechatMsgService wechatMsgService;

    @Override
    public void process(FeedBackMessageDTO feedBackMessageDTO) {
        log.info("接收到消息 {}",JSONUtil.toJsonStr(feedBackMessageDTO));
        try {
            wechatMsgService.subAccountFeedBack(feedBackMessageDTO.getAccountId(), feedBackMessageDTO.getFollowId(), feedBackMessageDTO.getTemplateId(), feedBackMessageDTO.getFeedTime());
        }catch (Exception e){
            log.warn("微信推送消息 warn {}", JSONUtil.toJsonStr(feedBackMessageDTO));
        }
    }

}