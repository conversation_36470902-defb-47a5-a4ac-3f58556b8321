package net.summerfarm.mall.common.mq.ofc.converter;

import net.summerfarm.mall.common.mq.ofc.Input.DeliveryPathShortSkuDTO;
import net.summerfarm.mall.model.domain.DeliveryPathShortSku;

/**
 * <AUTHOR>
 */
public class OfcMessageConverter {

    public static DeliveryPathShortSku converterDeliveryPathShortSku(DeliveryPathShortSkuDTO shortSkuDTO) {
        DeliveryPathShortSku shortSku = new DeliveryPathShortSku();
        shortSku.setAddTime(shortSkuDTO.getAddTime());
        shortSku.setDeliveryPathId(shortSkuDTO.getDeliveryPathId());
        shortSku.setId(shortSkuDTO.getId());
        shortSku.setRemark(shortSkuDTO.getRemark());
        shortSku.setShortCnt(shortSkuDTO.getShortCnt());
        shortSku.setSku(shortSkuDTO.getSku());
        shortSku.setType(shortSkuDTO.getType());

        return shortSku;
    }

}
