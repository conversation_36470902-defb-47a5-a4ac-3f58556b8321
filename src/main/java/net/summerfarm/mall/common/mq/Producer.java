//package net.summerfarm.mall.common.mq;
//
//import com.aliyun.openservices.ons.api.bean.ProducerBean;
//import net.summerfarm.mall.contexts.MQDelayConstant;
//import net.summerfarm.mall.contexts.MQTopicConstant;
//import net.summerfarm.mall.contexts.SpringContextUtil;
//import org.apache.rocketmq.spring.core.RocketMQTemplate;
//import org.springframework.messaging.Message;
//import org.springframework.messaging.support.MessageBuilder;
//import org.springframework.stereotype.Service;
//import javax.annotation.Resource;
//import java.nio.charset.StandardCharsets;
//
//
///**
// * @Package: com.manageSystem.mq
// * @Description:
// * @author: <EMAIL>
// * @Date: 2017/3/13
// */
//@Service
//public class Producer {
//
//    @Resource
//    private RocketMQTemplate rocketMQTemplate;
//
//    public void sendDataToQueue(String queueKey, String data) {
//        rocketMQTemplate.convertAndSend(queueKey, data);
//    }
//
//    public void sendDataToQueue(String queueKey, Object data) {
//        rocketMQTemplate.convertAndSend(queueKey, data);
//    }
//
//    /**
//     * 顺序消息
//     * @param queueKey
//     * @param data
//     * @param hashKey
//     */
//    public void sendOrderlyDataToQueue(String queueKey, String data, String hashKey) {
//        this.rocketMQTemplate.syncSendOrderly(queueKey, data, hashKey);
//    }
//
//    /**
//     * 发送延时消息
//     * @param queueKey
//     * @param data
//     * @param delayLevel
//     */
//    public void sendDelayDataToQueue(String queueKey, String data, int delayLevel) {
//        Message message = MessageBuilder.withPayload(data).build();
//        this.rocketMQTemplate.syncSend(queueKey, message, MQDelayConstant.TIMEOUT, delayLevel);
//    }
//
//    /**
//     * 发送延时消息
//     * @param queueKey
//     * @param data
//     * @param delayLevel
//     * @param startDeliverTime 延迟多久投递 单位ms
//     */
//    public void sendDelayDataToQueueByOnsClient(String queueKey, String data, int delayLevel, long startDeliverTime) {
//        if(SpringContextUtil.isProduct()){
//            // 此bean只在生产环境会被注册到spring容器中，不能直接使用字段注入方式，改为动态获取
//            ProducerBean onsProducer = SpringContextUtil.getApplicationContext().getBean(ProducerBean.class);
//            com.aliyun.openservices.ons.api.Message message =
//                    new com.aliyun.openservices.ons.api.Message(queueKey, "", data.getBytes(StandardCharsets.UTF_8));
//            message.setStartDeliverTime(startDeliverTime);
//            onsProducer.send(message);
//        }else{
//            Message message = MessageBuilder.withPayload(data).build();
//            this.rocketMQTemplate.syncSend(queueKey, message, MQDelayConstant.TIMEOUT, delayLevel);
//        }
//    }
//
//
//}
