package net.summerfarm.mall.common.mq.ofc.Input;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class DeliveryPathShortSkuDTO implements Serializable {

    private Integer id;

    /**
     *配送 信息id
     */
    private Integer deliveryPathId;

    private String sku;

    /**
     * 短缺数量
     */
    private Integer shortCnt;

    private LocalDateTime addTime;

    /**
     * 配送类型 0 配送 1 回收
     */
    private Integer type;
    /**
     *备注
     */
    private String remark;
}
