package net.summerfarm.mall.common.mq.ofc;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.common.mq.MQData;
import net.summerfarm.mall.common.mq.ofc.Input.DeliveryPathShortSkuDTO;
import net.summerfarm.mall.common.mq.ofc.Input.ShortSkuListData;
import net.summerfarm.mall.contexts.OfcRocketMqConstant;
import net.summerfarm.mall.service.ofc.OfcDeliveryService;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> 生成配送缺货sku
 */
@Slf4j
@Component
@MqListener(
        topic = OfcRocketMqConstant.Topic.TOPIC_OFC_DELIVERY,
        consumerGroup = OfcRocketMqConstant.ConsumeGroup.GID_OFC_TO_MALL_SHORT_SKU,
        tag = OfcRocketMqConstant.Tag.INIT_DELIVERY_PATH_SHORT_SKU,
        maxReconsumeTimes = 2)
public class InitDeliveryPathShortSku extends AbstractMqListener<ShortSkuListData> {

    @Autowired
    OfcDeliveryService ofcDeliveryService;

    @Override
    public void process(ShortSkuListData shortSkuListData) {
        log.info("生成配送缺货sku：{}", JSONObject.toJSONString(shortSkuListData));
        ofcDeliveryService.initDeliveryPathShortSku(shortSkuListData.getShortSkuList());
    }

}