package net.summerfarm.mall.common.mq;

/**
 * Created by wjd on 2017/12/22.
 */
public enum MType {
    flush, registerapproved, coupon, WECHAT_MENU, ADMIN_MSG, EMAIL, SMS,HELP_ORDER,AFTER_SALE_ORDER_NO,UPDATE_SIGN,AR<PERSON><PERSON><PERSON>_NOTICE,UN_<PERSON>LE_NOTICE,
    PURCHASES_CONFIG,AUTO_TRANSFER, REGISTER_AUDIT, DELIVERY_PLAN_CHANGE,UPDATE_AREA_STATUS,DING_TALK_PROCESS,DING_TALK_MSG,DING_TALK_ROBOT_MSG,
    DING_TALK_BIG_ORDER_MSG,DING_TALK_AT_ROBOT_MSG,DELIVERY_WECHANT_MSG,CONTACT_REGISTER_AUDIT, COUPON_SEND_MSG,SEND_RP_MSG,
    ORDER_JUDGEMENT,ORDER_UPDATE_STATUS,MERCHANT_MSG,ADD_FRUIT_SALES,REDUCE_FRUIT_SALES,<PERSON><PERSON><PERSON><PERSON><PERSON>_WARNING,
    MQ_PUSH_ONSALE_UPDATEPRICE,TIMING_LOCK_QUANTITY,TIMING_DELAY_TIME,PAYMENT_NOTIFY_SUCCESS,AFTER_SALE_TIMEOUT_CLOSE,BOC_PAY_STATUS_QUERY,ORDER_TIMEOUT_CLOSE,
    VIRTUAL_ORDER_TIMEOUT_CLOSE,PAYMENT_STATUS_QUERY,REFUND_STATUS_QUERY,REFUND_NOTIFY_SUCCESS,BATCH_UPDATE_DEVLIVERY_DATE,CANCEL_HEART_ORDER,ADVANCE_ORDER_TIMEOUT_CLOSE,INTERCEPT_ORDER,
    PARTNERSHIP_BUY_END,PARTNERSHIP_CONFIG_END,ORDER_ABNORMAL_HANDLE,REISSUE_WAREHOUSING,TIMING_ORDER_LOCK_TWO,MERCHANT_CANCEL,
    DING_TALK_MERCHANT_CANCEL,ORDER_BILL_EXPORT,MERCHANT_REPEATED_JUDGMENT,MASTER_PAYMENT_STATUS_QUERY,MASTER_PAYMENT_NOTIFY_SUCCESS,MASTER_ORDER_TIMEOUT_CLOSE,RISK_MERCHANT,AUTO_CONFIRM_COUPON,
    INVITE_COUPON, MERCHANT_URGE_AUDIT
}
