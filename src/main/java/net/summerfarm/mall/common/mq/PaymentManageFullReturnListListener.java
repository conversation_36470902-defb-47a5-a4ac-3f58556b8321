package net.summerfarm.mall.common.mq;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.mall.contexts.MQTopicConstant;
import net.summerfarm.mall.model.domain.MasterOrder;
import net.summerfarm.mall.service.MasterPaymentService;
import net.xianmu.common.exception.BizException;
import net.xianmu.log.helper.LogConfigHolder;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 支付成功回调仅处理参与满返订单
 * @createTime 2021年12月23日 12:48:00
 * 2023:06:08更新消息格式，注意：目前相比之前取消了‘重试间隔时间’这个配置（因为目前是非顺序消费），顺序消息会有这个配置。
 */
@Slf4j
@Component
@MqListener(topic = MQTopicConstant.MALL_PAYMENT_LIST,
        tag = MQTopicConstant.SELECTOR_EXPRESSION,
        consumerGroup = MQTopicConstant.GID_MALL_PAYMENT_FULL_RETURN,
        maxReconsumeTimes = 5)
public class PaymentManageFullReturnListListener extends AbstractMqListener<MQData> {

    @Resource
    private MasterPaymentService masterPaymentService;

    @Override
    public void process(MQData mqData) {
        try {
            log.info("支付分区顺序消息--普通订单发放满返劵，rocketmq receive：{}", JSONObject.toJSONString(mqData));
            String data = String.valueOf(mqData.getData());
            if (MType.MASTER_PAYMENT_NOTIFY_SUCCESS.name().equals(mqData.getType())) {
                PayNotifySuccessDataV2 successDataV2 = JSONObject.parseObject(data, PayNotifySuccessDataV2.class);
                MasterOrder masterOrder = successDataV2.getMasterOrder();
                masterPaymentService.sendFullReturn(masterOrder);
            }
        } catch (Exception e) {
            boolean customException = e instanceof DefaultServiceException;
            if (!customException) {
                throw new BizException("支付分区顺序--普通订单发放满返劵消费失败", e);
            }
        }finally {
            LogConfigHolder.removeInboundFlag();
        }
    }
}
