package net.summerfarm.mall.common.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;

import com.cosfo.message.client.resp.MsgSendLogResp;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.enums.SubAccountStatus;
import net.summerfarm.enums.coupon.CouponSenderSetupSenderTypeEnum;
import net.summerfarm.mall.common.delayqueue.AfterSaleProofItem;
import net.summerfarm.mall.common.sms.SMSSenderFactory;
import net.summerfarm.mall.common.util.DateUtils;
import net.summerfarm.mall.common.util.MailUtil;
import net.summerfarm.mall.common.util.WeChatUtils;
import net.summerfarm.mall.contexts.MQDelayConstant;
import net.summerfarm.mall.contexts.MQTopicConstant;
import net.summerfarm.mall.contexts.WechatConstant;
import net.summerfarm.mall.mapper.AfterSaleOrderMapper;
import net.summerfarm.mall.mapper.ArrivalNoticeMapper;
import net.summerfarm.mall.mapper.MerchantMapper;
import net.summerfarm.mall.mapper.MerchantSubAccountMapper;
import net.summerfarm.mall.model.DeliveryPlanChangeParam;
import net.summerfarm.mall.model.Email;
import net.summerfarm.mall.model.SMS;
import net.summerfarm.mall.model.bo.coupon.CouponSenderBO;
import net.summerfarm.mall.model.domain.ArrivalNotice;
import net.summerfarm.mall.model.domain.Coupon;
import net.summerfarm.mall.model.domain.MerchantSubAccount;
import net.summerfarm.mall.model.input.OrderBillExportReq;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import net.summerfarm.mall.model.vo.DeliveryPlanCancelWeChatVo;
import net.summerfarm.mall.model.vo.DeliveryPlanChangeWeChatVo;
import net.summerfarm.mall.model.vo.OrdersRevokeWeChatVo;
import net.summerfarm.mall.service.CategoryService;
import net.summerfarm.mall.service.MsgHistoryService;
import net.summerfarm.mall.service.OrderService;
import net.summerfarm.mall.service.facade.AuthWechatFacade;
import net.summerfarm.mall.service.strategy.coupon.CouponSenderContext;
import net.summerfarm.mall.wechat.model.Menu;
import net.summerfarm.mall.wechat.model.TagManage;
import net.summerfarm.mall.wechat.model.TemplateMsgResponse;
import net.summerfarm.mall.wechat.service.WeChatService;
import net.summerfarm.mall.wechat.service.WechatMsgService;
import net.summerfarm.mall.wechat.templatemessage.ArrivalNoticeMsg;
import net.summerfarm.mall.wechat.templatemessage.DeliveryPlanChangeMsg;
import net.summerfarm.mall.wechat.templatemessage.RefundApprovedMsg;
import net.summerfarm.mall.wechat.templatemessage.TemplateMsgSender;
import net.summerfarm.mall.wechat.templatemessage.msgTemplate.MsgTemplateFactory;
import net.xianmu.common.enums.base.auth.WxOfficialAccountsChannelEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.log.helper.LogConfigHolder;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-10-15
 * @description
 */
@Slf4j
@Component
@MqListener(topic = RocketMqMessageConstant.MANAGE_LIST, consumerGroup = RocketMqMessageConstant.GID_MALL)
public class ManageListListener extends AbstractMqListener<MQData> {
    @Resource
    private CategoryService categoryService;
    @Resource
    private WechatMsgService wechatMsgService;
    @Resource
    private MsgHistoryService msgHistoryService;
    @Resource
    private MailUtil mailUtil;
    @Resource
    private AfterSaleOrderMapper afterSaleOrderMapper;
    @Resource
    private WeChatService weChatService;
    @Resource
    private TemplateMsgSender templateMsgSender;
    @Resource
    private ArrivalNoticeMapper arrivalNoticeMapper;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private MerchantSubAccountMapper subAccountMapper;
    @Resource
    private SMSSenderFactory smsSenderFactory;
    @Resource
    CouponSenderContext couponSenderContext;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private  RedisTemplate redisTemplate;
    @Resource
    private OrderService orderService;
    @Resource
    private AuthWechatFacade authWechatFacade;

    @Override
    public void process(MQData mqData) {
        LogConfigHolder.putInboundFlag("Topic:"+ RocketMqMessageConstant.MANAGE_LIST + ":" + mqData.getType());
        log.info("rocketmq receive：{}", JSONObject.toJSONString(mqData));
        String dataStr;
        if (mqData.getData() instanceof String) {
            dataStr = String.valueOf(mqData.getData());
        } else {
            dataStr = JSONObject.toJSONString(mqData.getData());
        }

        if (MType.flush.name().equals(mqData.getType())) {
            categoryService.flushCategoryTree();
        } else if (MType.registerapproved.name().equals(mqData.getType())) {
            Long mId = Long.valueOf(dataStr);
            wechatMsgService.registerApprovedMsg(mId);
        } else if (MType.coupon.name().equals(mqData.getType())) {
            JSONObject jsonObject = JSONObject.parseObject(dataStr);
            String triggerTime = jsonObject.getString("triggerTime");
            Integer areaNo = jsonObject.getInteger("areaNo");
            Long mid = jsonObject.getLong("mId");
            String type = jsonObject.getString("type");
            String size = jsonObject.getString("size");
            Integer adminId = jsonObject.getInteger("adminId");
            CouponSenderBO couponSenderBO = new CouponSenderBO();
            couponSenderBO.setMId(mid);
            couponSenderBO.setSenderType(CouponSenderSetupSenderTypeEnum.REGISTRATION_SEND);
            couponSenderBO.setTriggerTime(DateUtils.stringToLocalDateTime(triggerTime));
            couponSenderBO.setAreaNo(areaNo);
            couponSenderBO.setSize(size);
            couponSenderBO.setAdminId(adminId);
            List<Coupon> coupons = couponSenderContext.sendCoupon(couponSenderBO);
            if (!CollectionUtils.isEmpty(coupons)) {
                String money = coupons.stream().map(Coupon::getMoney)
                        .reduce(BigDecimal.ZERO, BigDecimal::add).toString();
                msgHistoryService.msgSender(mid, type, null, money);
            }
        } else if (MType.INVITE_COUPON.name().equals(mqData.getType())) {
            JSONObject jsonObject = JSONObject.parseObject(dataStr);
            String triggerTime = jsonObject.getString("triggerTime");
            Integer areaNo = jsonObject.getInteger("areaNo");
            Long mid = jsonObject.getLong("mId");
            String type = jsonObject.getString("type");
            CouponSenderBO couponSenderBO = new CouponSenderBO();
            couponSenderBO.setMId(mid);
            couponSenderBO.setSenderType(CouponSenderSetupSenderTypeEnum.RECOMMENDED_ORDER);
            couponSenderBO.setTriggerTime(DateUtils.stringToLocalDateTime(triggerTime));
            couponSenderBO.setAreaNo(areaNo);
            couponSenderContext.sendCoupon(couponSenderBO);
        } else if (MType.WECHAT_MENU.name().equals(mqData.getType())) {
            log.error("MQ微信菜单更新失败：{}", dataStr, new BizException());
            // Menu menu = JSON.parseObject(dataStr, Menu.class);
            //String redisAccessToken = (String) redisTemplate.opsForValue().get(WechatConstant.MALL_WECHAT_ACCESS_TOKEN);
            // String redisAccessToken = authWechatFacade.queryChannelToken(WxOfficialAccountsChannelEnum.XM_MALL.channelCode);
            // WeChatUtils.createMenu(menu, redisAccessToken);
        } else if (MType.EMAIL.name().equals(mqData.getType())) {
            Email email = JSON.parseObject(dataStr, Email.class);
            mailUtil.sendMail(email.getSubject(), email.getText(), email.getTo(), email.getCc());
        } else if (MType.SMS.name().equals(mqData.getType())) {
            SMS sms = JSON.parseObject(dataStr, SMS.class);
            log.info("收到短信请求：{}",dataStr);
            smsSenderFactory.getSMSSender().sendSMS(sms);
        } else if (MType.AFTER_SALE_ORDER_NO.name().equals(mqData.getType())) {
            String afterSaleOrderNo = dataStr;
            log.info("取消售后申请：{}",afterSaleOrderNo);
            try {
                Thread.sleep(1000 * 5);
            } catch (InterruptedException e) {
                log.info("取消售后申请sleep fail：{}",e.getMessage());
            }
            AfterSaleOrderVO afterSaleOrderVO = afterSaleOrderMapper.selectByAfterSaleOrderNo(afterSaleOrderNo);
            if (afterSaleOrderVO == null){
                return;
            }
            if (afterSaleOrderVO.getStatus() == 4) {
                //添加事件到延迟队列
                try {
                    log.info("取消售后申请添加至延迟消息：{}",afterSaleOrderNo);
                    AfterSaleProofItem afterSaleProofItem = new AfterSaleProofItem("afterSaleOrderNo" + afterSaleOrderVO.getAfterSaleOrderNo(), afterSaleOrderVO.getUpdatetime(), 24 * 60 * 60 * 1000L, afterSaleOrderVO.getAfterSaleOrderNo());
                    DelayData delayData = new DelayData();
                    delayData.setType(MType.AFTER_SALE_TIMEOUT_CLOSE.name());
                    delayData.setData(JSONObject.toJSONString(afterSaleProofItem));
                    delayData.setDelayHours(MQDelayConstant.CANCEL_AFTER_SALES_DELAY_HOURS);
                    mqProducer.sendDelay(MQTopicConstant.MALL_DELAY_LIST, null, JSONObject.toJSONString(delayData), MQDelayConstant.CANCEL_AFTER_SALES_DELAY_HOURS_LONG);
                } catch (Exception e) {
                    throw new DefaultServiceException("添加延迟队列异常", e);
                }
            }
            String openId = merchantMapper.selectOpenId(Long.valueOf(afterSaleOrderVO.getmId()));
            String msg = RefundApprovedMsg.templateMessage(openId, afterSaleOrderVO);
            MsgSendLogResp sendResult = templateMsgSender.sendTemplateMsg(msg);
            log.info(JSON.toJSONString(sendResult));

        } else if (MType.UPDATE_SIGN.name().equals(mqData.getType())) {
            TagManage tagManage = JSON.parseObject(dataStr, TagManage.class);
            weChatService.updateTags(tagManage);
        } else if (MType.ARRIVAL_NOTICE.name().equals(mqData.getType())) {
            ArrivalNotice arrivalNotice = JSON.parseObject(dataStr, ArrivalNotice.class);
            if (arrivalNotice.getmId() == null) {
                log.warn("【到货通知】mId不存在，dataStr:{}", dataStr);
                return;
            }
            //更改状态 后 发消息
            if (arrivalNoticeMapper.updateById(arrivalNotice.getId()) == 1) {
                //发送店员到货提醒、为你上新
                List<MerchantSubAccount> accountList = subAccountMapper.selectByMId(arrivalNotice.getmId().longValue(), SubAccountStatus.AUDIT_SUC.ordinal());
                accountList.forEach(el -> {
                    arrivalNotice.setOpenid(el.getOpenid());
                    String subMsg = ArrivalNoticeMsg.templateMessage(arrivalNotice);
                    MsgSendLogResp subSendResult = templateMsgSender.sendTemplateMsg(subMsg);
                    log.info(JSON.toJSONString(subSendResult));
                });
            }
        } else if (MType.UN_SALE_NOTICE.name().equals(mqData.getType())) {
            ArrivalNotice arrivalNotice = JSON.parseObject(dataStr, ArrivalNotice.class);
            //更改状态 后 发消息
            if (arrivalNoticeMapper.updateById(arrivalNotice.getId()) == 1 && arrivalNotice.getType() == 0) {
                String msg = ArrivalNoticeMsg.unSaleMessage(arrivalNotice);
                MsgSendLogResp sendResult = templateMsgSender.sendTemplateMsg(msg);
                log.info(JSON.toJSONString(sendResult));
            }
        } else if (MType.DELIVERY_PLAN_CHANGE.name().equals(mqData.getType())) {
            DeliveryPlanChangeParam deliveryPlanChangeParam = JSON.parseObject(dataStr, DeliveryPlanChangeParam.class);
            String msg = DeliveryPlanChangeMsg.templateMessage(deliveryPlanChangeParam);
            MsgSendLogResp sendResult = templateMsgSender.sendTemplateMsg(msg);
            log.info(JSON.toJSONString(sendResult));
        } else if (MType.UPDATE_AREA_STATUS.name().equals(mqData.getType())) {
            JSONObject jsonObject = JSONObject.parseObject(dataStr);
            Integer areaNo = jsonObject.getInteger("areaNo");
            String notifyTitle = jsonObject.getString("notifyTitle");
            String notifyContent = jsonObject.getString("notifyContent");
            String notifyRemake = jsonObject.getString("notifyRemake");
            wechatMsgService.areaStatusNotice(areaNo, notifyTitle, notifyContent, notifyRemake);

        } else if (MType.DELIVERY_WECHANT_MSG.name().equals(mqData.getType())) {
            JSONObject jsonObject = JSONObject.parseObject(dataStr);
            String orderNo = jsonObject.getString("orderNo");
            Integer mId = jsonObject.getInteger("mId");
            String msg = jsonObject.getString("msg");
            String shortSku = jsonObject.getString("shortSku");
            String shortSkuMsg = jsonObject.getString("shortSkuMsg");
            String orderTime = jsonObject.getString("orderTime");
            if (StringUtils.isEmpty(shortSku)) {
                wechatMsgService.deliveryPlanFinish(mId, orderNo, msg);
            } else {
                wechatMsgService.deliveryPlanShortFinish(mId, orderNo, shortSku, shortSkuMsg, orderTime);
            }
        } else if(MType.TIMING_DELAY_TIME.name().equals(mqData.getType())){
            JSONObject jsonObject = JSONObject.parseObject(dataStr);
            Integer mId = jsonObject.getInteger("mId");
            String orderNo = jsonObject.getString("orderNo");
            String deliveryTime = jsonObject.getString("deliveryTime");
            String msg = jsonObject.getString("msg");
            wechatMsgService.templateDeliveryDelayMessage(mId,orderNo,deliveryTime,msg);
            //省心送 1<M<=2 发送冻结失败提醒
        } else if(MType.TIMING_LOCK_QUANTITY.name().equals(mqData.getType())){
            JSONObject jsonObject = JSONObject.parseObject(dataStr);
            Integer mId = jsonObject.getInteger("mId");
            String orderNo = jsonObject.getString("orderNo");
            String orderTime = jsonObject.getString("orderTime");
            String msg = jsonObject.getString("msg");
            wechatMsgService.templateDeliveryLockMessage(mId,orderNo,orderTime,msg);
        }else if (MType.BATCH_UPDATE_DEVLIVERY_DATE.name().equals(mqData.getType())) {
            DeliveryPlanChangeWeChatVo deliveryPlanChangeWeChatVo = JSON.parseObject(dataStr, DeliveryPlanChangeWeChatVo.class);
            String msg = DeliveryPlanChangeMsg.batchDeliveryPlanChange(deliveryPlanChangeWeChatVo);
            MsgSendLogResp sendResult = templateMsgSender.sendTemplateMsg(msg);
            log.info(JSON.toJSONString(sendResult));
        }else if (MType.CANCEL_HEART_ORDER.name().equals(mqData.getType())) {
            DeliveryPlanCancelWeChatVo deliveryPlanCancelWeChatVo = JSON.parseObject(dataStr, DeliveryPlanCancelWeChatVo.class);
            String msg = DeliveryPlanChangeMsg.batchDeliveryPlanCancel(deliveryPlanCancelWeChatVo);
            MsgSendLogResp sendResult = templateMsgSender.sendTemplateMsg(msg);
            log.info(JSON.toJSONString(sendResult));
        }else if (MType.ORDER_ABNORMAL_HANDLE.name().equals(mqData.getType())) {
            OrdersRevokeWeChatVo ordersRevokeWeChatVo = JSON.parseObject(dataStr, OrdersRevokeWeChatVo.class);
            String msg;
            if (Objects.equals(NumberUtils.INTEGER_ZERO,ordersRevokeWeChatVo.getType())){
                msg = DeliveryPlanChangeMsg.batchOrdersRevoke(ordersRevokeWeChatVo);
            }else {
                msg = DeliveryPlanChangeMsg.batchEasyDeliveryRevoke(ordersRevokeWeChatVo);
            }
            log.info("订单异常处理消息发送内容：{}",msg);
            MsgSendLogResp sendResult = templateMsgSender.sendTemplateMsg(msg);
            log.info(JSON.toJSONString(sendResult));
        } else if (MType.ORDER_BILL_EXPORT.name().equals(mqData.getType())) {
            OrderBillExportReq orderBillExportReq = JSON.parseObject(dataStr, OrderBillExportReq.class);
            orderService.orderBillExportAndSendMail(orderBillExportReq);
        } else if (MType.AUTO_CONFIRM_COUPON.name().equals(mqData.getType())) {
            JSONObject jsonObject = JSONObject.parseObject(dataStr);
            String openId = jsonObject.getString("openId");
            String orderNo = jsonObject.getString("orderNo");
            Date validDate = jsonObject.getDate("validDate");
            BigDecimal money = jsonObject.getBigDecimal("money");
            //根据type值，获取对应消息
            String msgJson = MsgTemplateFactory.createRuleMsg(openId, orderNo, money,
                    validDate);
            log.info(msgJson);
            MsgSendLogResp sendResult = templateMsgSender.sendTemplateMsg(msgJson);
            if (!ObjectUtils.isEmpty(sendResult) && sendResult.getSendStatus() == 0) {
                log.info("发送成功");
            } else {
                log.info("发送失败, msgJson:{}", msgJson);
            }
        } else {
            log.error("不认识的消息格式!");
        }
        LogConfigHolder.removeInboundFlag();
    }



}
