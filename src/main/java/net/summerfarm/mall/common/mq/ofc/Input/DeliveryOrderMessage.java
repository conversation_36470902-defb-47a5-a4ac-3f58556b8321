package net.summerfarm.mall.common.mq.ofc.Input;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class DeliveryOrderMessage implements Serializable {

    /**
     * 商品配送详情
     */
    private List<DeliveryItemMessage> deliveryItemMessages;

    /**
     * 委托单id
     */
    private Long distOrderId;

    /**
     * 来源
     */
    private Integer source;

    /**
     * 外部订单号 --orderNo
     */
    private String outOrderId;

    /**
     * 联系人
     */
    private String contactId;

    /**
     * 外部客户号 --mId
     */
    private String clientId;

    /**
     * 外部客户名 --mname
     */
    private String clientName;

    /**
     * 配送时间 --deliveryTime
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deliveryTime;

    /**
     * 详情地址
     */
    private String addressDetail;

    /**
     * 姓名 contactName
     */
    private String name;

    /**
     * 电话 contactPhone
     */
    private String phone;

    /**
     * 10未签收 20已签收 30异常签收
     */
    private Integer state;

    /**
     * 完成时间
     */
    private LocalDateTime finishTime;

    /**
     * 配送类型 0 配送 1 回收
     */
    private Integer type;

    /**
     * 拦截
     */
    private Integer interceptReasonCode;
    /**
     * 拦截原因
     */
    private String interceptReason;
}
