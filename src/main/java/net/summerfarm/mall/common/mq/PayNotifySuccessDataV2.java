package net.summerfarm.mall.common.mq;

import lombok.Data;
import net.summerfarm.mall.model.domain.MasterOrder;
import net.summerfarm.mall.model.domain.MasterPayment;
import net.summerfarm.mall.model.domain.Orders;
import net.summerfarm.mall.model.domain.Payment;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 支付消息
 * @createTime 2021年11月26日 14:45:00
 */
@Data
public class PayNotifySuccessDataV2 {

    /**
     * 付款信息
     */
    private MasterPayment masterPayment;

    /**
     * 订单信息
     */
    private MasterOrder masterOrder;
}
