package net.summerfarm.mall.common.mq.ofc.Input;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ShortOutOrderEntityInput implements Serializable {

    /**
     *     XM_MALL(200, "鲜沐商城"),
     *     XM_AFTER_SALE(201, "鲜沐售后"),
     *     XM_SAMPLE_APPLY(202, "鲜沐样品申请"),
     *     XM_MALL_TIMING(203, "鲜沐商城省心送订单"),
     *     SAAS_MALL(210, "Saas商城"),
     *     SAAS_AFTER_SALE(211, "Saas售后"),
     */
    private Integer orderSource;

    /**
     * 订单号信息
     */
    private String orderNo;

    /**
     * 用户id
     */
    private Integer mId;

    /**
     * 用户accountId
     */
    private Integer accountId;

    /**
     * 地址
     */
    private Long contactId;

    /**
     * 缺少sku信息
     */
    private String shortSkuMsg;

    /**
     * 缺少sku详情
     */
    private String shortSkuMsgDetail;


    /**
     * 订单时间
     */
    private LocalDateTime finishTime;

    /**
     * 缺货信息
     */
    private String shortMsg;

    /**
     * 发起售后的订单编号
     */
    private String afterSaleOrderNos;

    /**
     * 缺货sku信息
     */
    private String afterSaleSku;

    /**
     * 缺货商品名称
     */
    private String afterSalePdName;


    /**
     * 是否发起缺货
     */
    private Boolean skuShort;

    /**
     * 0配送 1回收
     */
    private Integer deliveryType;

    /**
     * 缺货订单item
     */
    private List<ShortOutItemEntityInput> orderItemList;
}
