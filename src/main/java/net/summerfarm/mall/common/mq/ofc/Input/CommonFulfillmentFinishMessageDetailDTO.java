package net.summerfarm.mall.common.mq.ofc.Input;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class CommonFulfillmentFinishMessageDetailDTO implements Serializable {

    private static final long serialVersionUID = -9164221685078548768L;

    /**
     * 外部商品id
     */
    private String itemId;

    /**
     * sku码
     */
    private String skuCode;

    /**
     * 订单计划履约数量
     */
    private Integer quantity;

    /**
     * 实际履约数量
     */
    private Integer actualQuantity;

    /**
     * 缺货数量
     */
    private Integer shortQuantity;

    /**
     * 状态 0:正常 1:异常
     */
    private Integer status;
}
