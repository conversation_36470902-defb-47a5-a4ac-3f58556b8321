package net.summerfarm.mall.common.mq.ofc;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.common.mq.ofc.Input.OrderDefectMessageDTO;
import net.summerfarm.mall.contexts.OfcRocketMqConstant;
import net.summerfarm.mall.service.ofc.OfcDeliveryService;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 */
@Slf4j
@Component
@MqListener(
        topic = OfcRocketMqConstant.Topic.TOPIC_OFC_DELIVERY,
        consumerGroup = OfcRocketMqConstant.ConsumeGroup.GID_OFC_TO_MALL_ORDER_DEFECT,
        tag = OfcRocketMqConstant.Tag.INIT_ORDER_DEFECT_MESSAGE,
        maxReconsumeTimes = 2)
public class InitOrderDefectListener extends AbstractMqListener<OrderDefectMessageDTO> {

    @Autowired
    OfcDeliveryService ofcDeliveryService;

    @Override
    public void process(OrderDefectMessageDTO orderDefectMessageDTO) {
        log.info("生成订单缺损信息：{}", JSONObject.toJSONString(orderDefectMessageDTO));
        ofcDeliveryService.initOrderDefectMessage(orderDefectMessageDTO);
    }

}