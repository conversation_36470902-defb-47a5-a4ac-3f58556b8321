package net.summerfarm.mall.common.mq.ofc;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.common.mq.MQData;
import net.summerfarm.mall.contexts.OfcRocketMqConstant;
import net.summerfarm.mall.service.ofc.OfcDeliveryService;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 完成配送商城微信消息推送
 */
@Slf4j
@Component
@MqListener(
        topic = OfcRocketMqConstant.Topic.TOPIC_OFC_NOTICE,
        consumerGroup = OfcRocketMqConstant.ConsumeGroup.GID_OFC_TO_MALL_WECHAT_MESSAGE,
        tag = OfcRocketMqConstant.Tag.PICK_STOCK_OUT_WECHAT_MESSAGE,
        maxReconsumeTimes = 2)
public class StockOutWechatMessageListener extends AbstractMqListener<MQData> {

    @Autowired
    private OfcDeliveryService ofcDeliveryService;

    @Override
    public void process(MQData mqData) {
        log.info("拣货缺货微信消息推送：{}", JSONObject.toJSONString(mqData));
        ofcDeliveryService.stockOutWeChatMessage(mqData);
    }

}