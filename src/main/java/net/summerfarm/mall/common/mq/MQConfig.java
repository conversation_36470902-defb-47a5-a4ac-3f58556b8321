package net.summerfarm.mall.common.mq;

import com.aliyun.openservices.ons.api.PropertyKeyConst;
import com.aliyun.openservices.ons.api.bean.ProducerBean;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

import java.util.Properties;

/**
 * 商业版RocketMQ客户端依赖配置
 */
@Data
@Configuration
public class MQConfig {

    @Value("${rocketmq.producer.access-key}")
    private String accessKey;

    @Value("${rocketmq.producer.secret-key}")
    private String secretKey;

    @Value("${rocketmq.name-server}")
    private String nameSrvAddr;

    public Properties getMqPropertie() {
        Properties properties = new Properties();
        properties.setProperty(PropertyKeyConst.AccessKey, this.accessKey);
        properties.setProperty(PropertyKeyConst.SecretKey, this.secretKey);
        properties.setProperty(PropertyKeyConst.NAMESRV_ADDR, this.nameSrvAddr);
        properties.setProperty(PropertyKeyConst.MsgTraceSwitch, "false");
        return properties;
    }

    @Profile(value = "pro")
    @Bean(name = "onsProducer", initMethod = "start", destroyMethod = "shutdown")
    public ProducerBean buildProducer() {
        ProducerBean producer = new ProducerBean();
        producer.setProperties(getMqPropertie());
        return producer;
    }

}
