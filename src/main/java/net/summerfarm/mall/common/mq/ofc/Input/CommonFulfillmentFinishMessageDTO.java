package net.summerfarm.mall.common.mq.ofc.Input;

import lombok.Data;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class CommonFulfillmentFinishMessageDTO  implements Serializable {

    private static final long serialVersionUID = 2927897556161964957L;


    /**
     * 单号（订单号/售后单号）
     */
    private String sourceOrderNo;

    /**
     * 配送日期(省心送需要这个字段)
     */
    private LocalDate deliveryDate;

    /**
     * contactId(省心送需要这个字段)
     */
    private String contactId;

    /**
     * 配送类型 0 配送(正向订单&售后补发)   1 回收（售后退货）
     */
    private Integer deliveryType;

    /**
     * 备注信息
     */
    private String message;

    /**
     * 履约明细
     */
    private List<CommonFulfillmentFinishMessageDetailDTO> itemList;

}
