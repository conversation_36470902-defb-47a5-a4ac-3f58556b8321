package net.summerfarm.mall.common.mq;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.mall.common.delayqueue.AfterSaleProofItem;
import net.summerfarm.mall.common.delayqueue.OrderCancelItem;
import net.summerfarm.mall.common.delayqueue.VirtualOrderCancelItem;
import net.summerfarm.mall.contexts.MQDelayConstant;
import net.summerfarm.mall.contexts.MQTopicConstant;
import net.summerfarm.mall.model.input.MerchantCancelReq;
import net.summerfarm.mall.service.*;
import net.xianmu.log.helper.LogConfigHolder;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 延时消息
 * @createTime 2021年11月26日 14:45:00
 */
@Slf4j
@Component
@MqListener(topic = MQTopicConstant.MALL_DELAY_LIST, consumerGroup = MQTopicConstant.GID_MALL_DELAY)
public class DelayManageListListener extends AbstractMqListener<DelayData> {

    @Resource
    private AfterSaleOrderService afterSaleOrderService;

    @Resource
    private MqProducer mqProducer;

    @Resource
    private OrderService orderService;
    @Resource
    private MerchantCancelService merchantCancelService;
    @Resource
    private MasterOrderService masterOrderService;

    @Override
    public void process(DelayData delayData) {
        LogConfigHolder.putInboundFlag("Topic:"+ RocketMqMessageConstant.MALL_DELAY_LIST + ":" + delayData.getType());
        log.info("延时消息rocketmq receive：{}", JSONObject.toJSONString(delayData));

        if (MType.AFTER_SALE_TIMEOUT_CLOSE.name().equals(delayData.getType())) {
            AfterSaleProofItem afterSaleProofItem = JSONObject.parseObject(delayData.getData(), AfterSaleProofItem.class);
            afterSaleOrderService.closeProof(afterSaleProofItem);
        } else if (MType.ORDER_TIMEOUT_CLOSE.name().equals(delayData.getType())) {
            OrderCancelItem orderCancelItem = JSONObject.parseObject(delayData.getData(), OrderCancelItem.class);
            orderService.timeOutClose(orderCancelItem);
        } else if (MType.MASTER_ORDER_TIMEOUT_CLOSE.name().equals(delayData.getType())) {
            OrderCancelItem orderCancelItem = JSONObject.parseObject(delayData.getData(), OrderCancelItem.class);
            masterOrderService.timeOutCloseV2(orderCancelItem);
        } else if (MType.VIRTUAL_ORDER_TIMEOUT_CLOSE.name().equals(delayData.getType())) {
            VirtualOrderCancelItem virtualOrderCancelItem = JSONObject.parseObject(delayData.getData(), VirtualOrderCancelItem.class);
            orderService.virtualTimeOutClose(virtualOrderCancelItem);
        } else if(MType.MERCHANT_CANCEL.name().equals(delayData.getType())){
            MerchantCancelReq merchantCancelReq = JSONObject.parseObject(delayData.getData(), MerchantCancelReq.class);
            merchantCancelService.cancel(merchantCancelReq);
        } else {
            log.error("延时消息不认识的消息格式!");
        }
        LogConfigHolder.removeInboundFlag();
    }

}
