package net.summerfarm.mall.common.mq.ofc.Input;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class ShortOrderGenerateEntity implements Serializable {

    /**
     *     XM_MALL(200, "鲜沐商城"),
     *     XM_AFTER_SALE(201, "鲜沐售后"),
     *     XM_SAMPLE_APPLY(202, "鲜沐样品申请"),
     *     XM_MALL_TIMING(203, "鲜沐商城省心送订单"),
     *     SAAS_MALL(210, "Saas商城"),
     *     SAAS_AFTER_SALE(211, "Saas售后"),
     */
    private Integer orderSource;

    /**
     * 是否发起缺货
     */
    private Boolean skuShort;

    /**
     * 外部履约物品ID 对应SKU
     */
    private String outItemId;

    /**
     * 履约单号
     */
    private Long fulfillmentNo;

    /**
     * 履约物品名称
     */
    private String itemName;

    /**
     * 履约物品状态标志: 0不缺货，1缺货
     */
    private Boolean statusTag;

    /**
     * 实际履约数量
     */
    private Integer actualAmount;


    /**
     * 履约数量
     */
    private Integer amount;

    /**
     * 履约项类型
     */
    private String outerItemType;

    /**
     * 缺货数量
     */
    private Integer shortCount;

    /**
     * 补发数量
     */
    private Integer reissueCount;

    /**
     * 外部订单号 --orderNo
     */
    private String outOrderId;
}
