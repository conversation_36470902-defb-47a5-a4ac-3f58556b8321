package net.summerfarm.mall.common.mq.ofc.Input;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class DeliveryItemMessageDTO implements Serializable {

    /**
     * 配送单ID
     */
    private Long deliveryOrderId;

    /**
     * 委托单id
     */
    private Long distOrderId;

    /**
     * 外部订单号 --orderNo
     */
    private String outOrderId;

    /**
     * 外部条目id --sku
     */
    private String outItemId;

    /**
     * 计划签收数量
     */
    private Integer planReceiptCount;

    /**
     * 实际签收数量
     */
    private Integer realReceiptCount;

    /**
     * 缺货数量
     */
    private Integer shortCount;

    /**
     * 拦截数量
     */
    private Integer interceptCount;

    /**
     * 拒收数量
     */
    private Integer rejectCount;

    /**
     * 拒收原因
     */
    private String rejectRemark;

    /**
     * 扫码数量
     */
    private Integer scanCount;

    /**
     * 无码数量
     */
    private Integer noscanCount;

    /**
     * 无码原因
     */
    private String noscanReason;

    /**
     * 无码货物照片
     */
    private String noscanPics;
}
