package net.summerfarm.mall.common.mq.ofc;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.common.mq.MQData;
import net.summerfarm.mall.common.mq.ofc.Input.ReissueOrderData;
import net.summerfarm.mall.common.mq.ofc.Input.ShortOrderGenerateEntity;
import net.summerfarm.mall.contexts.OfcRocketMqConstant;
import net.summerfarm.mall.service.ofc.OfcDeliveryService;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> ofc发送生成补发单消息
 */
@Slf4j
@Component
@MqListener(
        topic = OfcRocketMqConstant.Topic.TOPIC_OFC_DELIVERY,
        consumerGroup = OfcRocketMqConstant.ConsumeGroup.GID_OFC_TO_MALL_REISSUE_MESSAGE,
        tag = OfcRocketMqConstant.Tag.REISSUE_ORDER_MESSAGE,
        maxReconsumeTimes = 2)
public class ReissueOrderMessageListener extends AbstractMqListener<ReissueOrderData> {

    @Lazy
    @Resource
    OfcDeliveryService ofcDeliveryService;

    @Override
    public void process(ReissueOrderData mqData) {
        log.info("ofc发送生成补发单消息：{}", JSONObject.toJSONString(mqData));
        ofcDeliveryService.reissueOrderMessage(mqData.getGenerateEntities());
    }

}