package net.summerfarm.mall.common.mq.ofc;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.common.mq.MQData;
import net.summerfarm.mall.common.mq.ofc.Input.CompleteWiringData;
import net.summerfarm.mall.common.mq.ofc.Input.DeliveryOrderMessage;
import net.summerfarm.mall.contexts.OfcRocketMqConstant;
import net.summerfarm.mall.service.ofc.OfcDeliveryService;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> 完成排线更新售后状态售后信息
 */
@Slf4j
@Component
@MqListener(
        topic = OfcRocketMqConstant.Topic.TOPIC_OFC_DELIVERY,
        consumerGroup = OfcRocketMqConstant.ConsumeGroup.GID_OFC_TO_MALL_COMPLETE_WIRING_MESSAGE,
        tag = OfcRocketMqConstant.Tag.COMPLETE_WIRING_MESSAGE,
        maxReconsumeTimes = 2)
public class CompleteWiringMessageListener extends AbstractMqListener<CompleteWiringData> {

    @Autowired
    OfcDeliveryService ofcDeliveryService;

    @Override
    public void process(CompleteWiringData completeWiringData) {
        log.info("完成排线更新售后状态售后信息：{}", JSONObject.toJSONString(completeWiringData));
        ofcDeliveryService.completeWiringMessage(completeWiringData.getOrderMessageInputs());
    }

}
