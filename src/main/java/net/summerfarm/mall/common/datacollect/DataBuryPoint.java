package net.summerfarm.mall.common.datacollect;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2019-11-07
 * @description
 */
public class DataBuryPoint {
    private static final Logger dbpLogger = LoggerFactory.getLogger("dbpLog");

    public static void logIntoDBP(String tab, String part, String url, String method, Long mId, Long accountId, String parameter, String result) {
        dbpLogger.info("{}●{}●{}●{}●{}●{}●{}●{}", tab, part, url, method, mId, accountId, parameter, result);
    }
}
