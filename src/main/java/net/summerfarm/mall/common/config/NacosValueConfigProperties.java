package net.summerfarm.mall.common.config;

import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @ClassName NacosValueConfigProperties
 * @Description
 * <AUTHOR>
 * @Date 13:38 2024/4/19
 * @Version 1.0
 **/
@Component
@NacosConfigurationProperties(autoRefreshed = true, dataId = "${spring.application.name}",
        prefix = "config", type = ConfigType.PROPERTIES)
public class NacosValueConfigProperties {

    /**
     * config配置表迁移到nacos配置中心 格式必须严格按照这种：config.map.ES_SWITCH=true
     */
    private Map<String, String> map;

    public Map<String, String> getMap() {
        return map;
    }

    public void setMap(Map<String, String> map) {
        this.map = map;
    }
}
