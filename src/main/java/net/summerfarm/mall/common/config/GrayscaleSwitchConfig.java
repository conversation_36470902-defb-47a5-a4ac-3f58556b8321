package net.summerfarm.mall.common.config;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

import java.util.Set;

/**
 * @author: monna.chen
 * @Date: 2023/11/23 13:57
 * @Description:
 */
@Configuration
@Slf4j
public class GrayscaleSwitchConfig {
    @NacosValue(value = "${itemGray.allSwitch:false}", autoRefreshed = true)
    private Boolean itemGrayAllSwitch;

    @NacosValue(value = "${itemGray.areaNos:}", autoRefreshed = true)
    private Set<Integer> itemGrayAreaNos;

    @NacosValue(value = "${itemGray.merchantIds:}", autoRefreshed = true)
    private Set<Long> itemGrayMerchantIds;

    public Boolean itemGraySwitch(Integer areaNo, Long merchantId) {
        if (itemGrayAllSwitch) {
            log.info("itemGraySwitch >>>> 灰度总开关已开启");
            return true;
        }

        if (null == areaNo || null == merchantId) {
            log.warn("itemGraySwitch >>>> 值非法，灰度未开启.areaNo:{},merchantId:{}", areaNo, merchantId);
            return false;
        }

        if (null == itemGrayAreaNos || null == itemGrayMerchantIds) {
            log.warn("itemGraySwitch >>>> 配置项值非法，灰度未开启.areaNos:{},merchantIds:{}", itemGrayAreaNos, itemGrayMerchantIds);
            return false;
        }

        Boolean graySwitch = itemGrayMerchantIds.contains(merchantId) || itemGrayAreaNos.contains(areaNo);
        if (graySwitch) {
            log.info("itemGraySwitch >>>> 灰度已开启.areaNo:{},merchantId:{}", areaNo, merchantId);
        } else {
            log.info("itemGraySwitch >>>> 灰度未开启.areaNo:{},merchantId:{}", areaNo, merchantId);
        }
        return graySwitch;
    }
}
