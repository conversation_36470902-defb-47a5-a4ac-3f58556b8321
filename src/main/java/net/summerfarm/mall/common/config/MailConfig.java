package net.summerfarm.mall.common.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.mail.javamail.JavaMailSenderImpl;

import java.util.Properties;

/**
 * @Package: com.manageSystem.common.config
 * @Description:
 * @author: <EMAIL>
 * @Date: 2019-12-25
 */
@Configuration
@Slf4j
public class MailConfig {

    @Value("${stmp.host}")
    private String host;
    @Value("${stmp.account}")
    private String account;
    @Value("${stmp.password}")
    private String password;
    @Value("${stmp.defaultEncoding}")
    private String defaultEncoding;
    @Value("${stmp.port}")
    private int port;
    @Value("${stmp.auth}")
    private String auth;
    @Value("${stmp.socketFactory.class}")
    private String socketFactoryClass;

    @Bean(name = "JavaMailSenderImpl")
    public JavaMailSenderImpl getMailSender() {
        log.info("生成邮件发送bean");
        JavaMailSenderImpl javaMailSender = new JavaMailSenderImpl();
        javaMailSender.setHost(host);
        javaMailSender.setUsername(account);
        javaMailSender.setPassword(password);
        javaMailSender.setPort(port);
        javaMailSender.setDefaultEncoding(defaultEncoding);
        Properties properties = new Properties();
        // 开启认证
        properties.setProperty("mail.smtp.auth", auth);
        // 设置ssl端口
        properties.setProperty("mail.smtp.socketFactory.port", String.valueOf(port));
        // SSL证书Socket工厂
        properties.setProperty("mail.smtp.socketFactory.class", socketFactoryClass);
        javaMailSender.setJavaMailProperties(properties);
        return javaMailSender;
    }

}
