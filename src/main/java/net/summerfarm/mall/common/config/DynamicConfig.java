package net.summerfarm.mall.common.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * @date 2023/5/16 11:07
 */
@Configuration
@Slf4j
public class DynamicConfig {


    /**
     * 成本价新老模型开关
     */
    @NacosValue(value = "${mall.new.cost.price.switch:false}", autoRefreshed = true)
    private Boolean mallNewCostPriceSwitch;


    /**
     * 年度账单灰度门店id。配置成[]全部不开放；配置指定门店id，则指定门店展示；配置[-1]则全部展示
     */
    @NacosValue(value = "${annual.statement.merchant.id.list:[-1]}", autoRefreshed = true)
    private String annualStatementMerchantIdList;


    /**
     * pop 默认运营区域
     */
    @NacosValue(value = "${pop.default.area.no:9999}", autoRefreshed = true)
    private Integer popDefaultAreaNo;


    /**
     * pop 默认挂属大客户id
     */
    @NacosValue(value = "${pop.default.admin.id:999999}", autoRefreshed = true)
    private Integer popDefaultAdminId;

    /**
     * 免审用户默认大客户id
     */
    @NacosValue(value = "${no.audit.admin.id:999998}", autoRefreshed = true)
    private Integer noAuditMerchantDefaultAdminId;

    /**
     * pop 默认默认配送周期
     */
    @NacosValue(value = "${pop.default.delivery.rule:}", autoRefreshed = true)
    private String popDefaultDeliveryRule;


    /**
     * 省份转换集合
     */
    @NacosValue(value = "${province.convert.map:}", autoRefreshed = true)
    private String provinceConvertMap;


    /**
     * 用户信息变更刷新redis缓存开关
     */
    @NacosValue(value = "${refresh.merchant.cache.switch:true}", autoRefreshed = true)
    private Boolean refreshMerchantCacheSwitch;


    /**
     * 任务灰度区域, -1为全部开放
     */
    @NacosValue(value = "${merchant.job.open.area.no:[]}", autoRefreshed = true)
    private String merchantJobOpenAreaNo;



    /**
     * 活动用免审线索池
     */
    @NacosValue(value = "${merchant.auto.audit.leads.id:-1}", autoRefreshed = true)
    private Long merchantAutoAuditLeadsId;


    /**
     * 单个门店下门店子账户最大账户数
     */
    @NacosValue(value = "${merchant.account.max.quantity:50}", autoRefreshed = true)
    private Integer merchantAccountMaxQuantity;


    /**
     * pop 需要加价的分销区域
     */
    @NacosValue(value = "${popMarkupPriceAreaNos:[]}")
    private String popMarkupPriceAreaNos;


    /**
     * POP商城域名url:https://supershunluda.cosfo.cn
     *
     */
    @NacosValue(value = "${dynamic.pop.mall.domain.url:https://supershunluda.cosfo.cn}", autoRefreshed = true)
    private String popMallDomain;

    /**
     * 核心品获取用户可用优惠券最大可循环次数
     */
    @NacosValue(value = "${coupon.max.limit.frequency:20}", autoRefreshed = true)
    private Integer couponMaxLimitFrequency;


    public Boolean getRefreshMerchantCacheSwitch() {
        return refreshMerchantCacheSwitch;
    }

    public Boolean getMallNewCostPriceSwitch() {
        return mallNewCostPriceSwitch;
    }


    public Integer getNoAuditMerchantDefaultAdminId() {
        return noAuditMerchantDefaultAdminId;
    }

    public List<Long> getAnnualStatementMerchantIdList() {
        try {
            return JSON.parseArray(annualStatementMerchantIdList, Long.class);
        } catch (Exception e){
            log.error("年度账单灰度门店id 动态配置解析失败", e);
            return Lists.newArrayList();
        }
    }

    public List<Integer> getMerchantJobOpenAreaNo() {
        try {
            return JSON.parseArray(merchantJobOpenAreaNo, Integer.class);
        } catch (Exception e){
            log.error("任务灰度区域 动态配置解析失败", e);
            return Lists.newArrayList();
        }
    }

    public Map<String, String> getProvinceConvertMap() {
        try {
            return JSON.parseObject(provinceConvertMap, Map.class);
        } catch (Exception e){
            log.error("省份映射 动态配置解析失败", e);
            return new HashMap<>();
        }
    }

    public Integer getPopDefaultAreaNo() {
        return popDefaultAreaNo;
    }


    public Integer getPopDefaultAdminId() {
        return popDefaultAdminId;
    }

    public String getPopDefaultDeliveryRule() {
        return popDefaultDeliveryRule;
    }

    public Long getMerchantAutoAuditLeadsId() {
        return merchantAutoAuditLeadsId;
    }

    public Integer getMerchantAccountMaxQuantity() {
        return merchantAccountMaxQuantity;
    }

    public List<Integer> getPopMarkupPriceAreaNos() {
        try {
            return JSON.parseArray(popMarkupPriceAreaNos, Integer.class);
        } catch (Exception e){
            log.error(" pop 需要加价的分销区域,动态配置解析失败", e);
            return Lists.newArrayList();
        }
    }

    public String getPopMallDomain() {
        return popMallDomain;
    }

    public Integer getCouponMaxLimitFrequency() {
        return couponMaxLimitFrequency;
    }
}
