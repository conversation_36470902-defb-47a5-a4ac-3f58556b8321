package net.summerfarm.mall.common.config.biz;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.Data;
import org.springframework.context.annotation.Configuration;

/**
 * 销售主体配置
 * @author: zach
 * @date: 2025-03-15
 **/
@Configuration
@Data
public class SellingEntityConfig {

    /**
     * 默认销售主体名称
     */
    @NacosValue(value = "${sellingEntity.default:杭州鲜沐科技有限公司}", autoRefreshed = true)
    private String defaultSellingEntityName;

    /**
     * 销售主体新协议弹窗 - 全局禁用开关（true：所有人都不会弹窗； false：按照正常需求逻辑弹窗）
     */
    @NacosValue(value = "${sellingEntity.popup.global.disable:false}", autoRefreshed = true)
    private Boolean globalDisablePopup;


}
