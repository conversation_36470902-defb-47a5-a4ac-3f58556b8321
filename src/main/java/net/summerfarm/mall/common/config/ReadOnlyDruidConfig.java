package net.summerfarm.mall.common.config;

import com.alibaba.druid.pool.DruidDataSource;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * <AUTHOR> ct
 * create at:  2022/4/12  17:38
 */
@Configuration
@Slf4j
@MapperScan(basePackages = "net.summerfarm.mall.mapper.offline", sqlSessionFactoryRef = "readOnlySqlSessionFactory")
public class ReadOnlyDruidConfig {

    @Value(value = "${mysql.dbType}")
    private String dbType;
    @Value(value = "${mysql.driverClassName:}")
    private String driverClassName;
    @Value(value = "${mysql.offline.url:}")
    private String url;
    @Value(value = "${mysql.offline.username:}")
    private String username;
    @Value(value = "${mysql.offline.password:}")
    private String password;
    @Value(value = "${mysql.minIdle:}")
    private int minIdle;
    @Value(value = "${mysql.initialSize:}")
    private int initialSize;
    @Value(value = "${mysql.maxActive:}")
    private int maxActive;
    @Value(value = "${mysql.maxWait:}")
    private int maxWait;
    @Value(value = "${mysql.asyncInit:}")
    private boolean asyncInit;
    @Value(value = "${mysql.testWhileIdle:}")
    private boolean testWhileIdle;


    @ConfigurationProperties(prefix = "spring.datasource.read.only")
    @Bean("readOnlyDruidDataSource")
    public DruidDataSource druidDataSource() {
        log.info("生成数据源readOnlyBean");
        DruidDataSource druidDataSource = new DruidDataSource();
        druidDataSource.setDbType("mysql");
        druidDataSource.setDriverClassName(driverClassName);
        druidDataSource.setUrl(url);
        druidDataSource.setUsername(username);
        druidDataSource.setPassword(password);
        druidDataSource.setInitialSize(8);
        druidDataSource.setMinIdle(8);
        druidDataSource.setMaxActive(40);
        druidDataSource.setMaxWait(maxWait);
        druidDataSource.setAsyncInit(asyncInit);
        druidDataSource.setTestWhileIdle(testWhileIdle);
        druidDataSource.setRemoveAbandoned(false);
        druidDataSource.setRemoveAbandonedTimeout(300);
        druidDataSource.setKeepAlive(true);
        druidDataSource.setKeepAliveBetweenTimeMillis(10 * 60 * 1000);
        return druidDataSource;
    }
    @Bean("readOnlySqlSessionFactory")
    public SqlSessionFactoryBean sqlSessionFactory(@Qualifier("readOnlyDruidDataSource") DruidDataSource druidDataSource) {
        SqlSessionFactoryBean sqlSessionFactoryBean = new SqlSessionFactoryBean();
        sqlSessionFactoryBean.setDataSource(druidDataSource);
        return sqlSessionFactoryBean;
    }
}
