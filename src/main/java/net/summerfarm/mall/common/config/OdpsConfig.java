package net.summerfarm.mall.common.config;

import com.aliyun.odps.Odps;
import com.aliyun.odps.account.Account;
import com.aliyun.odps.account.AliyunAccount;

/**
 * <AUTHOR>
 * @descripton
 * @date 2024/7/1 14:19
 */
public class OdpsConfig {

    /**
     * odbs连接信息
     */
    private static final String RESERVE_ACCESS_ID = "LTAIDLA1lWOJT7qh";
    private static final String RESERVE_ACCESS_KEY = "OeU8rsjMeFFijfzVscfKKM2wJuyzFw";
    private static final String END_POINT = "http://service.cn-hangzhou.maxcompute.aliyun.com/api";
    private static final String PROJECT = "summerfarm_ds";

    public static final Odps ODPS;

    static {
        Account account = new AliyunAccount(RESERVE_ACCESS_ID, RESERVE_ACCESS_KEY);
        Odps odps = new Odps(account);
        odps.setEndpoint(END_POINT);
        odps.setDefaultProject(PROJECT);
        ODPS = odps;
    }

}