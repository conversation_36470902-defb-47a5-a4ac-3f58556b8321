package net.summerfarm.mall.common.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.support.http.StatViewServlet;
import com.alibaba.druid.support.http.WebStatFilter;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.sql.SQLException;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * @Package: com.manageSystem.common.config
 * @Description:
 * @author: <EMAIL>
 * @Date: 2019-12-24
 */
@Slf4j
@Configuration
@MapperScan(basePackages = { "net.summerfarm.mall.mapper.*.xml", "net.summerfarm.warehouse.mapper",
        "net.summerfarm.mapper.*" })
public class DruidConfig {

    @Value(value = "${mysql.dbType}")
    private String dbType;
    @Value(value = "${mysql.driverClassName:}")
    private String driverClassName;
    @Value(value = "${mysql.url:}")
    private String url;
    @Value(value = "${mysql.username:}")
    private String username;
    @Value(value = "${mysql.password:}")
    private String password;
    @Value(value = "${mysql.minIdle:}")
    private int minIdle;
    @Value(value = "${mysql.initialSize:}")
    private int initialSize;
    @Value(value = "${mysql.maxActive:}")
    private int maxActive;
    @Value(value = "${mysql.maxWait:}")
    private int maxWait;
    @Value(value = "${mysql.asyncInit:}")
    private boolean asyncInit;
    @Value(value = "${mysql.testWhileIdle:}")
    private boolean testWhileIdle;

    @ConfigurationProperties(prefix = "spring.datasource")
    @Bean("manageDataSource")
    @Primary
    public DruidDataSource druidDataSource() {
        log.info("生成数据源bean");
        DruidDataSource druidDataSource = new DruidDataSource();
        druidDataSource.setDbType("mysql");
        druidDataSource.setDriverClassName(driverClassName);
        druidDataSource.setUrl(url);
        druidDataSource.setUsername(username);
        druidDataSource.setPassword(password);
        druidDataSource.setInitialSize(initialSize);
        druidDataSource.setMinIdle(minIdle);
        druidDataSource.setMaxActive(maxActive);
        druidDataSource.setMaxWait(maxWait);
        druidDataSource.setAsyncInit(asyncInit);
        druidDataSource.setTestWhileIdle(testWhileIdle);
        druidDataSource.setRemoveAbandoned(false);
        druidDataSource.setRemoveAbandonedTimeout(300);
        druidDataSource.setKeepAlive(true);
        druidDataSource.setKeepAliveBetweenTimeMillis(10 * 60 * 1000);
        try {
            druidDataSource.setFilters("stat,wall,slf4j");
            // 以下代码用于扩展group_concat可以返回的最大数据条数
            druidDataSource.setConnectionInitSqls(Collections.singletonList("SET SESSION group_concat_max_len = 102400;"));
        } catch (SQLException e) {
            log.error("生成数据源异常！异常信息{}", e.getMessage(), e);
        }
        return druidDataSource;
    }

    @Bean
    @Primary
    public ServletRegistrationBean druidServletRegistrationBean() {
        ServletRegistrationBean servletRegistrationBean = new ServletRegistrationBean();
        servletRegistrationBean.setServlet(new StatViewServlet());
        servletRegistrationBean.addUrlMappings("/druid/*");
        servletRegistrationBean.addInitParameter("allow", "");
        servletRegistrationBean.addInitParameter("deny", "");
        servletRegistrationBean.addInitParameter("loginUsername", "xianmu");
        servletRegistrationBean.addInitParameter("loginPassword", "xianmu619");
        return servletRegistrationBean;
    }

    /**
     * 注册DruidFilter拦截
     *
     * @return
     */
    @Bean
    public FilterRegistrationBean duridFilterRegistrationBean() {
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean();
        filterRegistrationBean.setFilter(new WebStatFilter());
        Map<String, String> initParams = new HashMap<>();
        // 设置忽略请求
        initParams.put("exclusions", "*.js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*");
        filterRegistrationBean.setInitParameters(initParams);
        filterRegistrationBean.addUrlPatterns("/*");
        return filterRegistrationBean;
    }

    @Bean("sqlSessionFactory")
    @Primary
    public SqlSessionFactoryBean sqlSessionFactory(@Qualifier("manageDataSource") DruidDataSource druidDataSource) {
        SqlSessionFactoryBean sqlSessionFactoryBean = new SqlSessionFactoryBean();
        sqlSessionFactoryBean.setDataSource(druidDataSource);
        return sqlSessionFactoryBean;
    }
}
