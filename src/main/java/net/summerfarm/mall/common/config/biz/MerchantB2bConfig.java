package net.summerfarm.mall.common.config.biz;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.Data;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Set;

/**
 * @description:
 * @author: George
 * @date: 2025-01-15
 **/
@Configuration
@Data
public class MerchantB2bConfig {

    @NacosValue(value = "${merchant.b2b.show.version:v1}", autoRefreshed = true)
    private String merchantB2bShowVersion;

    /**
     * B2B默认优惠券文案配置
     */
    @NacosValue(value = "${merchant.b2b.default.coupon.txt:好的，去开启}", autoRefreshed = true)
    private String merchantB2bDefaultCouponTxt;

    /**
     * B2B特殊优惠券文案配置
     * 一般用于未授权且有交易额的用户
     */
    @NacosValue(value = "${merchant.b2b.special.coupon.txt:去开启，领取5元无门槛红包}", autoRefreshed = true)
    private String merchantB2bSpecialCouponTxt;

    /**
     * B2B特殊优惠券文案客户ids
     */
    @NacosValue(value = "${merchant.b2b.special.coupon.txt.mids:}", autoRefreshed = true)
    private Set<Long> merchantB2bSpecialCouponTxtMIds;

    /**
     * B2B优惠券id
     */
    @NacosValue(value = "${merchant.b2b.coupon.id:}", autoRefreshed = true)
    private Integer merchantB2bCouponId;
}
