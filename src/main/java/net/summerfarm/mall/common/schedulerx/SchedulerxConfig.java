//package net.summerfarm.mall.common.schedulerx;
//
//import com.alibaba.schedulerx.worker.SchedulerxWorker;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
///**
// * <AUTHOR>
// * @version 1.0.0
// * @Description
// * @createTime 2021年12月07日 11:48:00
// */
//@Configuration
//@Slf4j
//public class SchedulerxConfig {
//
//    @Value(value = "${spring.schedulerx2.endpoint}")
//    private String endpoint;
//    @Value(value = "${spring.schedulerx2.namespace}")
//    private String namespace;
//    @Value(value = "${spring.schedulerx2.groupId}")
//    private String groupId;
//    @Value(value = "${spring.schedulerx2.appKey}")
//    private String appKey;
//
//    /**
//     * 初始化schedulerx
//     * @throws Exception
//     */
//    @Bean
//    public void initSchedulerxWorker() throws Exception {
//        SchedulerxWorker schedulerxWorker = new SchedulerxWorker();
//        schedulerxWorker.setEndpoint(endpoint);
//        schedulerxWorker.setNamespace(namespace);
//        schedulerxWorker.setGroupId(groupId);
//        schedulerxWorker.setAppKey(appKey);
//        schedulerxWorker.init();
//    }
//
//}
