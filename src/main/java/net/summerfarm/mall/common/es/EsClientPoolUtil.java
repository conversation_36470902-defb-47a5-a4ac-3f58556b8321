package net.summerfarm.mall.common.es;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.contexts.SpringContextUtil;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.elasticsearch.client.RestHighLevelClient;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2020-08-26
 * @description
 */
@Slf4j
public class EsClientPoolUtil {
    private static GenericObjectPool<RestHighLevelClient> clientPool;

    /**
     * 获得对象
     *
     * @return
     * @throws Exception
     */
    public static RestHighLevelClient getClient() throws Exception {
        if (clientPool == null) {
            instance();
        }
        return clientPool.borrowObject();
    }

    /**
     * 归还对象
     *
     * @param client
     */
    public static void returnClient(RestHighLevelClient client) {
        if (clientPool == null) {
            instance();
        }
        // 使用完毕之后，归还对象
        try {
            clientPool.returnObject(client);
        } catch (IllegalStateException e) {
            log.warn(e.getMessage());
        }
    }

    //初始化es连接池
    private static void instance() {
        EsClientPoolFactory esClientPoolFactory = SpringContextUtil.getBean("esClientPoolFactory", EsClientPoolFactory.class);
        GenericObjectPoolConfig<RestHighLevelClient> poolConfig = new GenericObjectPoolConfig<>();
        poolConfig.setMinIdle(10);
        poolConfig.setMaxIdle(10);
        poolConfig.setMaxTotal(10);
        poolConfig.setMaxWaitMillis(1000);
        clientPool = new GenericObjectPool<>(esClientPoolFactory, poolConfig);
    }
}
