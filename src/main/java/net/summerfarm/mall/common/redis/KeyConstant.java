package net.summerfarm.mall.common.redis;

/**
 * @Package: net.summerfarm.common.redis
 * @Description: redis key collection
 * @author: <EMAIL>
 * @Date: 2018/1/22
 */
public class KeyConstant {

    /**
     * 数据采集key
     */
    public static final String SCADA = "SCADA_";

    /**
     * 微信access_token
     */
//    public static final String ACCESS_TOKEN = "ACCESS_TOKEN";
//
//    public static final String JS_API_TICKET = "JS_API_TICKET";

    public static final String MALL_TOKEN_PREFIX = "login:mall:";

    public static final String MA_ID_2_TOKEN = "maId2Token";

    public static final String TEMPORARY_FRONTEND_DATA = "TEMPORARY_FRONTEND_DATA";

    public static final String MINI_RANK = "mini_rank";

    public static final String RELATED_QUERIES = "related_queries";
}
