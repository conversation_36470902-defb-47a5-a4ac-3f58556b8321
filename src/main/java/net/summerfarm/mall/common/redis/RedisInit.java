package net.summerfarm.mall.common.redis;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2019-10-11
 * @description
 */
@Component
public class RedisInit {
    @Resource
    private RedisTemplate<String,String> redisTemplate;

    private final Logger logger = LoggerFactory.getLogger(RedisInit.class);

    @PostConstruct
    public void clearMaId() throws UnknownHostException {
        String localIp = InetAddress.getLocalHost().getHostAddress();
        //本地环境重启不清空redis中token缓存
        if("*************".equals(localIp) || "*************".equals(localIp) || "*************".equals(localIp)){
            redisTemplate.delete(KeyConstant.MA_ID_2_TOKEN);
            logger.info("==========清空redis中缓存的账号token（key：maId2Taken）信息==========");
        }
    }
}
