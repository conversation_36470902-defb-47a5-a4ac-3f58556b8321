package net.summerfarm.mall.common.redis;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.google.common.collect.Iterators;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.mapper.MerchantCouponMapper;
import net.summerfarm.mall.service.CouponService;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.List;

@Component
@Slf4j
public class LocalCacheInit implements ApplicationRunner {
    @Resource
    private CouponService couponService;
    @NacosValue("${coupon.black.list.cache.init.days:300}")
    private int days;

    @Resource
    private MerchantCouponMapper merchantCouponMapper;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        int pageNum = 1;
        int pageSize = 2000;

        // 先把有效的coupon找出来，再一个一个的去初始化；
        // SELECT DISTINCT
        // `coupon_id`
        // FROM
        // `merchant_coupon`
        // WHERE
        // add_time >= DATE_ADD(NOW(), interval -300 day)
        // AND `used` = 0
        // AND `vaild_date` > NOW()

        long start = System.currentTimeMillis();
        String threadName = Thread.currentThread().getName();
        log.info("localCacheInit.threadName={},start={}, days:{}", threadName, start, days);
        List<Integer> inuseCouponIds = merchantCouponMapper.selectUnusedCouponIdsWithinDays(days);
        if (CollectionUtils.isEmpty(inuseCouponIds)) {
            log.error("inuseCouponIds is empty!");
            return;
        }

        Iterators.partition(inuseCouponIds.iterator(), 200).forEachRemaining(coupondIdSubSet -> {
            log.info("初始化黑白名单内存缓存,coupondIdSubSet={}", coupondIdSubSet);
            couponService.initCouponBlackOrWhiteListCache(coupondIdSubSet);
        });

        long end = System.currentTimeMillis();
        log.info("localCacheInit.threadName={},end={}", threadName, end);
        log.info("localCacheInit.threadName={},耗时={}ms", threadName, end - start);
    }
}
