package net.summerfarm.mall.common.sms;

import lombok.Setter;
import net.summerfarm.mall.mapper.ConfigMapper;
import net.summerfarm.mall.model.domain.Config;
import net.summerfarm.mall.service.ConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

@Component
@Setter
public class SMSSenderFactory {
    public static final String SMS_PLATFORM = "SmsPlatform";
    public static final String STOP_SEND_SMS = "StopSendSMS";
    public static final Logger logger = LoggerFactory.getLogger(SMSSenderFactory.class);

    @Resource
    private SMSSender aliSMSSender;
    @Resource
    private SMSSender chuangLanSMSSender;
    @Resource
    private SMSSender dingdongSMSSender;
    @Resource
    private SMSSender localSMSSender;

    @Resource
    private ConfigService configService;

    public SMSSender getSMSSender() {
        boolean stopSend = Optional.ofNullable(configService.getValue(STOP_SEND_SMS)).map(i -> "1".equals(i)).orElse(false);
        if (stopSend) {
            return localSMSSender;
        }

        String value = configService.getValue(SMS_PLATFORM);
        if (Objects.isNull(value)) {
            logger.error("配置缺失,fallback 阿里SMS, config={}", SMS_PLATFORM);
            return aliSMSSender;
        }
        int idx;
        try {
            idx = Integer.parseInt(value);
        } catch (NumberFormatException e) {
            logger.error("配置有误,fallback 阿里SMS, config={}", value);
            return aliSMSSender;
        }
        if (idx > SenderPlatform.values().length - 1 || idx < 0) {
            logger.error("配置有误,fallback 阿里SMS, config={}", value);
            return aliSMSSender;
        }

        SenderPlatform type = SenderPlatform.values()[idx];
        switch (type) {
            case ALI_SMS:
                return aliSMSSender;
            case CHUANGLAN_SMS:
                return chuangLanSMSSender;
            case DINGDONG_SMS:
                return dingdongSMSSender;
            case LOCAL_SMS:
                return localSMSSender;
            default:
                return aliSMSSender;
        }
    }

}
