package net.summerfarm.mall.common.sms.imp;

import net.summerfarm.mall.common.sms.SMSSender;
import net.summerfarm.mall.model.SMS;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 本地发送器，只打印日志不会实际发送短信
 */
@Component
public class LocalSMSSender implements SMSSender {
    private static final Logger logger = LoggerFactory.getLogger(LocalSMSSender.class);

    @Override
    public boolean sendSMS(SMS sms) {
        logger.info("sms:{}", sms);
        return true;
    }
}
