package net.summerfarm.mall.common.sms.imp;

import net.summerfarm.common.util.StringUtils;
import net.summerfarm.mall.common.sms.SMSSender;
import net.summerfarm.mall.common.sms.SenderPlatform;
import net.summerfarm.mall.mapper.SMSSceneMapper;
import net.summerfarm.mall.model.SMS;
import net.summerfarm.mall.model.domain.SMSScene;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.NameValuePair;
import org.apache.commons.httpclient.methods.PostMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.util.List;

/**
 * 叮咚短信发送
 */
@Component
public class DingdongSMSSender implements SMSSender {
    private static final Logger logger = LoggerFactory.getLogger(DingdongSMSSender.class);

    @Resource
    private SMSSceneMapper smsSceneMapper;

    @Override
    public boolean sendSMS(SMS sms) {
        // 设置您要发送的内容(内容必须和某个模板匹配。以下例子匹配的是系统提供的1号模板）
//        String smsContent = "【鲜沐农场】尊敬的用户，你的验证码是：" + code + "，请在10分钟内输入。请勿告诉其他人。";
        String content = StringUtils.isEmpty(sms.getContent()) ? renderTemp(sms.getSceneId(), sms.getArgs()) : sms.getContent();
        logger.info("sendSMS :{}, text:{}", sms, content);

        try {
            NameValuePair[] data = {new NameValuePair("apikey", URLEncoder.encode(Constant.API_KEY, Constant.ENCODING)),
                    new NameValuePair("mobile", URLEncoder.encode(sms.getPhone(), Constant.ENCODING)),
                    new NameValuePair("content", URLEncoder.encode(content, Constant.ENCODING))};
            return doPost(Constant.URL_SEND_YZM, data);
        } catch (Exception e) {
            logger.error("sendSMS meet error, sms:{}", sms, e);
            return false;
        }

    }

    /**
     * 基于HttpClient的post函数
     * 发送验证码的
     *
     * @param url  提交的URL
     * @param data 提交NameValuePair参数
     * @param data 发送的验证码
     * @return 提交响应
     */
    static boolean doPost(String url, NameValuePair[] data) {
        HttpClient client = new HttpClient();
        PostMethod method = new PostMethod(url);
        // method.setRequestHeader("ContentType",
        // "application/x-www-form-urlencoded;charset=UTF-8");
        method.setRequestBody(data);
        // client.getParams().setContentCharset("UTF-8");
        client.getParams().setConnectionManagerTimeout(10000);
        try {
            client.executeMethod(method);
            logger.info("短信返回结果：" + method.getResponseBodyAsString());
            return true;
        } catch (Exception e) {
            logger.info("短信发送异常：" + e);
            return false;
        }
    }


    /**
     * 渲染短信结果
     *
     * @param sceneId 场景id
     * @param args    参数
     * @return 短信内容
     */
    public String renderTemp(Long sceneId, List<String> args) {
        SMSScene smsScene = smsSceneMapper.selectByScenePlatform(sceneId, SenderPlatform.DINGDONG_SMS.ordinal());
        String t = smsScene.getTemplate();
        for (String arg : args) {
            t = t.replaceFirst("\\{s\\}", arg);
        }

        return t;
    }

}
