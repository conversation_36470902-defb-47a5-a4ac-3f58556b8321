package net.summerfarm.mall.common.sms.imp;

import com.google.gson.JsonObject;
import lombok.Setter;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.enums.SMSType;
import net.summerfarm.mall.common.sms.SMSSender;
import net.summerfarm.mall.common.sms.SenderPlatform;
import net.summerfarm.mall.enums.MerchantEnum;
import net.summerfarm.mall.mapper.ConfigMapper;
import net.summerfarm.mall.mapper.MerchantMapper;
import net.summerfarm.mall.mapper.SMSSceneMapper;
import net.summerfarm.mall.model.SMS;
import net.summerfarm.mall.model.domain.Merchant;
import net.summerfarm.mall.model.domain.SMSScene;
import net.summerfarm.mall.service.ConfigService;
import org.slf4j.Logger;
import org.springframework.http.HttpEntity;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.slf4j.LoggerFactory.getLogger;

/**
 * 创蓝短信发送
 */
@Component
public class ChuangLanSMSSender implements SMSSender {

    private static Logger logger = getLogger(ChuangLanSMSSender.class);

    public static final String ACCOUNT_KEY = "ChuangLanAccount";
    public static final String PASSWORD_KEY = "ChuangLanPassword";
    public static final String MARKET_ACCOUNT_KEY = "ChuangLanMarketAccount";
    public static final String MARKET_PASSWORD_KEY = "ChuangLanMarketPassword";
    public static final String FORMATTER = "yyyyMMddHHmm";
    public static final Long MERCHANT_SEND_COUPON_SCENE_ID = 3L;
    public static final Long POP_SEND_COUPON_SCENE_ID = 21L;

    @Setter
    @Resource
    private ConfigMapper configMapper;

    @Setter
    @Resource
    private SMSSceneMapper smsSceneMapper;

    @Resource
    private ConfigService configService;

    @Resource
    private MerchantMapper merchantMapper;

    @Override
    public boolean sendSMS(SMS sms) {
        String content = StringUtils.isEmpty(sms.getContent()) ? renderTemp(sms.getSceneId(), sms.getArgs(), sms.getPhone()) : sms.getContent();
        logger.info("sendSMS :{}, content:{}", sms, content);
        String account = getAccount(sms.getType());
        String password = getPassword(sms.getType());
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.getMessageConverters().set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8));
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("account", account);
        jsonObject.addProperty("password", password);
        jsonObject.addProperty("msg", content);
        jsonObject.addProperty("phone", sms.getPhone());
        String now = LocalDateTime.now().format(DateTimeFormatter.ofPattern(FORMATTER));
        jsonObject.addProperty("sendtime", now);

        HttpEntity<String> request = new HttpEntity<>(jsonObject.toString());
        logger.info("创蓝短信发送请求：{}", jsonObject.toString());
        Map<String, Object> result = restTemplate.postForObject(Constant.CHUANGLAN_SEND_URL, request, HashMap.class);
        logger.info("创蓝短信发送结果：{}", result);

        boolean success = result != null && "0".equals(result.get("code"));
        if (!success) {
            logger.warn("创蓝短信发送失败: result:{}, jsonObject:{}", result, jsonObject);
        }
        return success;
    }

    /**
     * 根据类型加载帐号，默认加载通知帐号
     *
     * @param type 短信类型
     * @return 密码
     */
    private String getPassword(SMSType type) {
        if (SMSType.NOTIFY == type || type == null) {
            String value = configService.getValue(PASSWORD_KEY);
            return value;
        }
        if (SMSType.MARKET == type) {
            String value = configService.getValue(MARKET_PASSWORD_KEY);
            return value;
        }
        logger.error("unknown SMSType");
        return "";
    }

    /**
     * 根据类型加载帐号，默认加载通知帐号
     *
     * @param type 短信类型
     * @return 帐号
     */
    private String getAccount(SMSType type) {
        if (SMSType.NOTIFY == type || type == null) {
            String value = configService.getValue(ACCOUNT_KEY);
            return value;
        }
        if (SMSType.MARKET == type) {
            String value = configService.getValue(MARKET_ACCOUNT_KEY);
            return value;
        }
        logger.error("unknown SMSType");
        return "";
    }

    /**
     * 渲染短信结果
     *
     * @param sceneId 场景id
     * @param args    参数
     * @param phone  发券手机号
     * @return 短信内容
     */
    public String renderTemp(Long sceneId, List<String> args, String phone) {
        if (sceneId == null) {
            return "";
        }

        //发券模版假如是pop客户需要特殊处理（短信签名不同）
        if (sceneId.equals(MERCHANT_SEND_COUPON_SCENE_ID)) {
            Merchant merchant = merchantMapper.selectIsUserPhone(phone);
            if (MerchantEnum.BusinessLineEnum.POP.getCode().equals(merchant.getBusinessLine())) {
                sceneId = POP_SEND_COUPON_SCENE_ID;
                logger.info("pop客户发券修改发放场景，sceneId:{}", sceneId);
            }
        }
        SMSScene smsScene = smsSceneMapper.selectByScenePlatform(sceneId, SenderPlatform.CHUANGLAN_SMS.ordinal());
        if (smsScene == null) {
            logger.info("短信模版不存在，sceneId:{}", sceneId);
            return "";
        }
        String t = smsScene.getTemplate();
        for (String arg : args) {
            t = t.replaceFirst("\\{s\\}", arg);
        }

        return t;
    }
}
