package net.summerfarm.mall.common.sms.imp;

/**
 * @Package: net.summerfarm.common.sms
 * @Description: 消息常量
 * @author: <EMAIL>
 * @Date: 2016/9/30
 */
class Constant {


   /**
    * 查账户信息的http地址
    */
   protected static final String URL_GET_USER_INFO = "https://api.dingdongcloud.com/v1/sms/userinfo";

   /**
    * 查询账户余额的http地址
    */
   protected static final String URL_GET_BALANCE = "https://api.dingdongcloud.com/v1/sms/querybalance";

   protected static final String URL_SEND_YYYZM = "https://api.dingdongcloud.com/v1/sms/sendyyyzm";

   /**
    * 通知短信发送接口的http地址
    */
   protected static final String URL_SEND_TZ = "https://api.dingdongcloud.com/v1/sms/sendtz";

   /**
    * 个性化通知短信发送
    */
   protected static final String MULTI_SEND = "https://api.dingdongcloud.com/v1/sms/notice/multi_send";

   /**
    * 营销短信发送接口的http地址
    */
   protected static final String URL_SEND_YX = "https://api.dingdongcloud.com/v1/sms/sendyx";

   /**
    * 验证码短信发送接口的http地址
    */
   protected static final String URL_SEND_YZM = "https://api.dingdongcloud.com/v1/sms/sendyzm";

   /**
    * 验证码短信发送接口的http地址
    */
   protected static final String ENCODING = "UTF-8";

   /**
    * apikey可在官网（https://www.dingdongcloud.com)登录后获取
    */
   protected static final String API_KEY = "df6edecd4e1f7879f3ad80860cde4b24";

   protected static final  String CHUANGLAN_SEND_URL = "http://smssh1.253.com/msg/v1/send/json";
}
