package net.summerfarm.mall.common.sms.imp;

import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsRequest;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import lombok.Setter;
import net.summerfarm.mall.common.sms.SMSSender;
import net.summerfarm.mall.common.sms.SenderPlatform;
import net.summerfarm.mall.mapper.SMSSceneMapper;
import net.summerfarm.mall.model.SMS;
import net.summerfarm.mall.model.domain.SMSScene;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 阿里短信发送
 */
@Component
@Deprecated
public class AliSMSSender implements SMSSender {
    public static final String[] NAMES = new String[]{"one", "two", "three", "four", "five", "six"};
    //产品名称:云通信短信API产品,开发者无需替换
    static final String product = "Dysmsapi";
    //产品域名,开发者无需替换
    static final String domain = "dysmsapi.aliyuncs.com";

    //  此处需要替换成开发者自己的AK(在阿里云访问控制台寻找)
    static final String accessKeyId = "LTAIfxUVIvEPOGUG";
    static final String accessKeySecret = "9rArRsylVzAF4oe8KF2ZwCsOhD1Gos";

    static final String smsHeadSign = "鲜沐农场";
    public static final String OK = "ok";

    Logger logger = LoggerFactory.getLogger(AliSMSSender.class);

    @Resource
    @Setter
    private SMSSceneMapper smsSceneMapper;

    @Override
    public boolean sendSMS(SMS sms) {

        logger.info("sendSMS :{}", sms);
        //可自助调整超时时间
        System.setProperty("sun.net.client.defaultConnectTimeout", "10000");
        System.setProperty("sun.net.client.defaultReadTimeout", "10000");

        try {
            //初始化acsClient,暂不支持region化
            IClientProfile profile = DefaultProfile.getProfile("cn-hangzhou", accessKeyId, accessKeySecret);
            DefaultProfile.addEndpoint("cn-hangzhou", "cn-hangzhou", product, domain);
            IAcsClient acsClient = new DefaultAcsClient(profile);

            //组装请求对象-具体描述见控制台-文档部分内容
            SendSmsRequest request = new SendSmsRequest();
            //必填:待发送手机号
            request.setPhoneNumbers(sms.getPhone());
            //必填:短信签名-可在短信控制台中找到
            request.setSignName(smsHeadSign);
            //必填:短信模板-可在短信控制台中找到
            SMSScene smsScene = smsSceneMapper.selectByScenePlatform(sms.getSceneId(), SenderPlatform.ALI_SMS.ordinal());
            request.setTemplateCode(smsScene.getTemplateId());
            //可选:模板中的变量替换JSON串,如模板内容为"亲爱的${name},您的验证码为${code}"时,此处的值为

            request.setTemplateParam(parseArgs(sms.getArgs()).toJSONString());

            //选填-上行短信扩展码(无特殊需求用户请忽略此字段)
            //request.setSmsUpExtendCode("90997");

            //可选:outId为提供给业务方扩展字段,最终在短信回执消息中将此值带回给调用者
            request.setOutId("summerfarm");

            //hint 此处可能会抛出异常，注意catch
            SendSmsResponse sendSmsResponse = null;
            sendSmsResponse = acsClient.getAcsResponse(request);
            logger.info("code:{}, msg:{},bizId:{},requestId:{}", sendSmsResponse.getCode(), sendSmsResponse.getMessage(), sendSmsResponse.getBizId(), sendSmsResponse.getRequestId());
            return OK.equalsIgnoreCase(sendSmsResponse.getCode());
        } catch (ClientException e) {
            logger.error("meet exception", e);
            return false;
        }
    }

    public static JSONObject parseArgs(List<String> args) {
        JSONObject jsonObject = new JSONObject();
        for (int i = 0; i < args.size(); i++) {
            jsonObject.put(NAMES[i], args.get(i));
        }
        return jsonObject;
    }
}
