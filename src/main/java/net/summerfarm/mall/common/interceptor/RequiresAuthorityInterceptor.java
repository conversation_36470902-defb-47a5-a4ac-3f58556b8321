package net.summerfarm.mall.common.interceptor;

import com.alibaba.fastjson.JSON;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.common.redis.KeyConstant;
import net.summerfarm.mall.common.util.IPUtil;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.contexts.SpringContextUtil;
import net.summerfarm.mall.model.vo.MerchantSubject;
import net.xianmu.authentication.client.dto.ShiroUser;
import org.apache.shiro.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.Objects;

/**
 * @Package: net.summerfarm.common.interceptor
 * @Description: 商户权限拦截器
 * @author: <EMAIL>
 * @Date: 2016/10/13
 */
@Component
public class RequiresAuthorityInterceptor extends HandlerInterceptorAdapter {

    final Logger logger = LoggerFactory.getLogger(getClass());

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        return requiresAuthorityHandler(request, response, handler);
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        RequestHolder.clearCurrentUserInfo();
    }

    /**
     * 商户权限校验
     * @param request
     * @param response
     * @param handler
     * @return
     */
    private boolean requiresAuthorityHandler(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        response.setContentType("application/json; charset=utf-8");
        if (!(handler instanceof HandlerMethod)){
            return true;
        }
        HandlerMethod handlerMethod=(HandlerMethod) handler;

        Class<?> clazz=handlerMethod.getBeanType();
        Method method=handlerMethod.getMethod();

        if (clazz!=null && method != null ) {
            boolean isClzAnnotation= clazz.isAnnotationPresent(RequiresAuthority.class);
            boolean isMethondAnnotation=method.isAnnotationPresent(RequiresAuthority.class);
            RequiresAuthority requiresAuthority = null;
            //如果方法和类声明中同时存在这个注解，那么方法中的会覆盖类中的设定。
            if(isMethondAnnotation){
                requiresAuthority = method.getAnnotation(RequiresAuthority.class);
            }else if(isClzAnnotation){
                requiresAuthority = clazz.getAnnotation(RequiresAuthority.class);
            }

            // 判断访问接口是否需要登录且用户是否已经登录登录成功
            String token = request.getHeader("token");
            if(null != requiresAuthority && StringUtils.isEmpty(token)){
                response.getWriter().println(JSON.toJSONString(AjaxResult.getError(ResultConstant.LOGIN_FIRST)));
                return false;
            }

            // 如果用户已经登录过，加载登录的用户信息
            MerchantSubject merchantSubject = null;
            if(StringUtils.isNotBlank(token)){
                if (token.contains("mall_")){
                    logger.info("new token:{}, IP：{}", token, IPUtil.getIpAddress(request));

                }
                merchantSubject = RequestHolder.getMerchantSubject(token, request);

            }

            if (merchantSubject != null && merchantSubject.getAccount() != null) {
                logger.info("token:{}, IP：{} 店铺：{} 账号：{} ", token, IPUtil.getIpAddress(request),
                        merchantSubject.getName(), merchantSubject.getAccount().getContact());

                //判断是否在切仓中（登录接口放行，避免session未更新）
                if (!(Objects.equals("net.summerfarm.mall.controller.MallController", clazz.getName())
                        && Objects.equals("getOpenid", handlerMethod.getMethod().getName()))
                        && RequestHolder.inChange()) {
                    logger.info("{}正在切仓，mId：{} 访问被拦截", merchantSubject.getArea().getAreaNo(), merchantSubject.getMerchantId());
                    RequestHolder.clearCurrentUserInfo();
                    response.getWriter().println(JSON.toJSONString(AjaxResult.getError(ResultConstant.IN_CHANGE)));
                    return false;
                }
            }else {
                //过滤心跳接口
                if (!Objects.equals("OK", handlerMethod.getMethod().getName())){
                    logger.info("token： {} 店铺访问了：{} 接口：{}", token,clazz.getName(),handlerMethod.getMethod().getName());
                }
            }

            if(null == requiresAuthority){
                //没有声明权限,放行
                return true;
            }
            //如果需要权限的
            if (merchantSubject == null) {
                logger.warn("用户无权限访问{}接口{}",clazz.getName(),handlerMethod.getMethod().getName());
                // 清理已经加载到内存中的用户信息
                RequestHolder.clearCurrentUserInfo();
                response.getWriter().println(JSON.toJSONString(AjaxResult.getError(ResultConstant.LOGIN_FIRST)));
                return false;
            }
            return true;
        }
        // 清理已经加载到内存中的用户信息
        RequestHolder.clearCurrentUserInfo();
        response.getWriter().println(JSON.toJSONString(AjaxResult.getError(ResultConstant.LOGIN_FIRST)));
        return false;
    }
}
