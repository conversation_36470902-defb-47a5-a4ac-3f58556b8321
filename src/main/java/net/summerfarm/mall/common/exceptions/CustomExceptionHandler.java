package net.summerfarm.mall.common.exceptions;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.contexts.ResultConstant;
import net.xianmu.common.exception.*;
import net.xianmu.common.exception.error.code.ProviderErrorCode;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.dubbo.support.constant.DubboCommonConstant;
import net.xianmu.redis.support.constant.RedisBizException;
import org.apache.catalina.connector.ClientAbortException;
import org.aspectj.lang.ProceedingJoinPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * @Package: com.manageSystem.common.exceptions
 * @Description: 自定义异常处理类
 * @author: <EMAIL>
 * @Date: 2016/7/27
 */
@RestControllerAdvice
public class CustomExceptionHandler implements net.xianmu.dubbo.support.handle.ExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(CustomExceptionHandler.class);

    @ExceptionHandler(Exception.class)
    public AjaxResult handle(Exception e) {
        AjaxResult result = AjaxResult.getError(ResultConstant.DEFAULT_FAILED);

        //客户端主动断开连接，不发邮件
        if (e instanceof ClientAbortException) {
            logger.info("【警告】message=[{}]", e.getMessage(), e);
        } else if (e instanceof ParamsException) {
            ParamsException exception = (ParamsException)e;
            logger.warn("调用方参数异常, 异常信息:{}", e.getMessage(), e);
            result = AjaxResult.getError(exception.getErrorCode().getCode(), exception.getMessage());
        } else if (e instanceof BizException) {
            BizException exception = (BizException) e;
            logger.warn("调用方异常, 异常信息:{}", e.getMessage(), e);
            result = AjaxResult.getError(exception.getErrorCode().getCode(), exception.getMessage());
        } else if (e instanceof CallerException) {
            CallerException exception = (CallerException)e;
            logger.warn("调用方异常, 异常信息:{}", e.getMessage(), e);
            result = AjaxResult.getError(exception.getErrorCode().getCode(), exception.getMessage());
        } else if (e instanceof RedisBizException) {
            RedisBizException exception = (RedisBizException)e;
            logger.warn("提供方异常, 异常信息:{}", e.getMessage(), e);
            result = AjaxResult.getError(exception.getErrorCode().getCode(), exception.getMessage());
        } else if (e instanceof ProviderException) {
            ProviderException exception = (ProviderException)e;
            logger.error("提供方异常, 异常信息:{}", e.getMessage(), e);
            result = AjaxResult.getError(exception.getErrorCode().getCode(), exception.getMessage());
        } else if (e instanceof MethodArgumentNotValidException) {
            MethodArgumentNotValidException ex = (MethodArgumentNotValidException) e;
            String message = ex.getBindingResult().getAllErrors().stream()
                    .map(DefaultMessageSourceResolvable::getDefaultMessage).findFirst().orElse("参数异常");
            logger.warn("调用方异常, 异常信息:{}", e.getMessage(), e);
            result = AjaxResult.getErrorWithMsg(message);
        } else if (e instanceof DefaultServiceException) {
            Object[] params = ((DefaultServiceException)e).getParams();
            if (((DefaultServiceException)e).getLevel() ==0 ) {
                logger.warn("调用方异常, 异常信息:{}", e.getMessage(), e);
            } else {
                logger.error("提供方异常, 异常信息:{}", e.getMessage(), e);
            }
            if (StringUtils.isBlank(params)) {
                //兼容模式如果e.getMessage()包含中文判断使用哪种构造函数
                //若e.getMessage()字节码长度等于本身长度则不包含中文
                if (StringUtils.isNotBlank(e.getMessage())&& e.getMessage().getBytes().length != e.getMessage().length()) {
                    result = AjaxResult.getErrorWithMsg(e.getMessage());
                }else{
                    result = AjaxResult.getError(e.getMessage());
                }
            } else {
                result = AjaxResult.getErrorWithParam(e.getMessage(), params);
            }
        } else {
            logger.error("提供方未知异常, 异常信息:{}", e.getMessage(), e);
        }
        return result;
    }


    @Override
    public DubboResponse processError(Throwable throwable, ProceedingJoinPoint joinPoint) {
        if (throwable instanceof ConsumerException) {
            ConsumerException exception = (ConsumerException)throwable;
            logger.warn("调用方异常, 异常信息:{}", throwable.getMessage(), throwable);
            return DubboResponse.getError(exception.getErrorCode().getCode(), exception.getMessage());
        } else if (throwable instanceof ParamsException) {
            ParamsException exception = (ParamsException)throwable;
            logger.warn("调用方参数异常, 异常信息:{}", throwable.getMessage(), throwable);
            return DubboResponse.getError(exception.getErrorCode().getCode(), exception.getMessage());
        } else if (throwable instanceof BizException) {
            BizException exception = (BizException)throwable;
            logger.warn("调用方异常, 异常信息:{}", throwable.getMessage(), throwable);
            return DubboResponse.getError(exception.getErrorCode().getCode(), exception.getMessage());
        } else if (throwable instanceof CallerException) {
            CallerException exception = (CallerException)throwable;
            logger.warn("调用方异常, 异常信息:{}", throwable.getMessage(), throwable);
            return DubboResponse.getError(exception.getErrorCode().getCode(), exception.getMessage());
        }  else if (throwable instanceof DefaultServiceException) {
            DefaultServiceException exception = (DefaultServiceException)throwable;
            if (exception.getLevel() == 0) {
                logger.info("调用方异常, 异常信息:{}", throwable.getMessage(), throwable);
            } else {
                logger.error("提供方异常, 异常信息:{}", throwable.getMessage(), throwable);
            }
            return DubboResponse.getError(exception.getCode(), exception.getMessage());
        }else if (throwable instanceof ProviderException) {
            ProviderException exception = (ProviderException)throwable;
            logger.error("提供方异常, 异常信息:{}", throwable.getMessage(), throwable);
            return DubboResponse.getError(exception.getErrorCode().getCode(), exception.getMessage());
        } else {
            logger.error("提供方未知异常, 异常信息:{}", throwable.getMessage(), throwable);
            ProviderErrorCode providerErrorCode = new ProviderErrorCode(DubboCommonConstant.UNDEFINED_EXCEPTION_CODE);
            return DubboResponse.getError(providerErrorCode.getCode(), throwable.getMessage());
        }
    }
}
