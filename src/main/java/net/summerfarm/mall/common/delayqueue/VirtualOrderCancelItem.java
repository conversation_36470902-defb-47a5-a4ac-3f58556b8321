package net.summerfarm.mall.common.delayqueue;

import lombok.Data;
import net.summerfarm.common.delayqueue.DelayQueueItem;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-09-13
 * @description
 */
@Data
public class VirtualOrderCancelItem extends DelayQueueItem {
    /**
     * 订单编号
     */
    private String orderNo;

    public VirtualOrderCancelItem() {
    }

    public VirtualOrderCancelItem(String id, LocalDateTime createTime, Long waitTime, String orderNo) {
        super(id, createTime, waitTime);
        this.orderNo = orderNo;
    }
}
