package net.summerfarm.mall.common.delayqueue;

import net.summerfarm.common.delayqueue.DelayQueueItem;

import java.time.LocalDateTime;

public class AfterSaleProofItem extends DelayQueueItem {

    private String  afterSaleOrderNo;

    public AfterSaleProofItem() {
    }

    public AfterSaleProofItem(String id) {
        super(id);
    }

    public AfterSaleProofItem(String id, LocalDateTime createTime, Long waitTime,String afterSaleOrderNo) {
        super(id, createTime, waitTime);
        this.afterSaleOrderNo=afterSaleOrderNo;
    }

    public String getAfterSaleOrderNo() {
        return afterSaleOrderNo;
    }

    public void setAfterSaleOrderNo(String afterSaleOrderNo) {
        this.afterSaleOrderNo = afterSaleOrderNo;
    }
}
