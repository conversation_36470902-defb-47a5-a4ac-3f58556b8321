package net.summerfarm.mall.common.delayqueue;

import net.summerfarm.common.delayqueue.DelayQueueItem;

import java.time.LocalDateTime;

/**
 * @Package: net.summerfarm.mall.model
 * @Description:
 * @author: <EMAIL>
 * @Date: 2018/8/28
 */
public class OrderCancelItem extends DelayQueueItem {
    private Long mId;

    private String orderNo;

    private String openid;

    private Integer areaNo;

    public OrderCancelItem() {
    }

    public OrderCancelItem(String id) {
        super(id);
    }

    public OrderCancelItem(String id, LocalDateTime createTime, Long waitTime, Long mId, String openid, Integer areaNo, String orderNo) {
        super(id, createTime, waitTime);
        this.mId = mId;
        this.orderNo = orderNo;
        this.openid = openid;
        this.areaNo = areaNo;
    }


    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public Integer getAreaNo() {
        return areaNo;
    }

    public void setAreaNo(Integer areaNo) {
        this.areaNo = areaNo;
    }

    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }
}
