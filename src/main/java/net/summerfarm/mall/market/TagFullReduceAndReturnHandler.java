package net.summerfarm.mall.market;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.common.util.RedisCacheUtil;
import net.summerfarm.mall.enums.market.MallTagEnum;
import net.summerfarm.mall.model.domain.MarketRule;
import net.summerfarm.mall.model.dto.market.malltag.SkuMallTagInfoDTO;
import net.summerfarm.mall.model.dto.merchant.MerchantBasicDTO;
import net.summerfarm.mall.service.MarketRuleService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 满减满返处理器
 *
 * @author: <EMAIL>
 * @create: 2023/5/4
 */
@Slf4j
@Service
public class TagFullReduceAndReturnHandler extends TagMarketHandler {

    @Resource
    private MarketRuleService marketRuleService;

    @Resource
    private RedisCacheUtil redisCacheUtil;

    private static final int FULL_RETURN_TYPE = 0, FULL_REDUCE_TYPE = 1;

    @Override
    public void handler(List<SkuMallTagInfoDTO> tagInfos, MerchantBasicDTO merchant) {
        if (merchant.getIsMajor()) { //NOSONAR
            log.info("商品标签--当前商户:{}为大客户,不处理满减满返活动标签", merchant.getMId());
            return;
        }
        Integer areaNo = merchant.getAreaNo();
        List<String> skuList = tagInfos.stream().map(SkuMallTagInfoDTO::getSku).collect(Collectors.toList());
        boolean hasFullReturn = false, hasFullReduce = false;
        Map<String, List<MarketRule>> skuMarketRulesMap = marketRuleService.selectSkuMarketRulesMapByAreaNo(areaNo, skuList);
        if (MapUtils.isEmpty(skuMarketRulesMap)) {
            log.info("这批SKU没有找到MarketRule:{}, areaNo:{}", skuList, areaNo);
            return;
        }
        log.info("这批SKU的MarketRules:{}, areaNo:{}", skuMarketRulesMap, areaNo);

        // 匹配满减满返规则
        for (SkuMallTagInfoDTO tagInfoDTO : tagInfos) {
            String sku = tagInfoDTO.getSku();
            // 满减满返规则展示
            if (!skuMarketRulesMap.containsKey(sku)) {
                log.warn("SKU:{}在运营服务区:{}无有效的满返或者满减MarketRule", sku, areaNo);
                continue;
            }
            List<MarketRule> marketRuleList = skuMarketRulesMap.get(sku);
            // 设置满返标签
            hasFullReturn = marketRuleList.stream().anyMatch(rule -> rule.getType() == FULL_RETURN_TYPE);
            if (hasFullReturn) {
                tagInfoDTO.getRemainTagEnums().add(MallTagEnum.FULL_RETURN);
            }
            // 设置满减标签
            hasFullReduce = marketRuleList.stream().anyMatch(rule -> rule.getType() == FULL_REDUCE_TYPE);
            if (hasFullReduce) {
                tagInfoDTO.getRemainTagEnums().add(MallTagEnum.FULL_REDUCE);
            }
            tagInfoDTO.setMarketRuleList(marketRuleList);
        }
    }
}
