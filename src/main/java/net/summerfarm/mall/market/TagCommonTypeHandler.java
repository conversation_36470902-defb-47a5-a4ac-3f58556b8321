package net.summerfarm.mall.market;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.enums.market.MallTagEnum;
import net.summerfarm.mall.model.domain.Inventory;
import net.summerfarm.mall.model.dto.market.malltag.SkuMallTagInfoDTO;
import net.summerfarm.mall.model.dto.merchant.MerchantBasicDTO;
import net.summerfarm.mall.service.InventoryService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 临保、破袋、拆包处理器
 *
 * @author: <EMAIL>
 * @create: 2023/5/5
 */
@Slf4j
@Service
public class TagCommonTypeHandler extends TagMarketHandler {

    @Resource
    private InventoryService inventoryService;

    @Override
    public void handler(List<SkuMallTagInfoDTO> tagInfos, MerchantBasicDTO merchant) {
        Set<String> skuList = tagInfos.stream().map(SkuMallTagInfoDTO::getSku).collect(Collectors.toSet());
        Map<String, Inventory> inventoryMap = inventoryService.selectInventoryWithCacheBatch(skuList);
        if (CollectionUtils.isEmpty(inventoryMap)) {
            log.error("商品标签--商品信息为空！skuList:{}", JSON.toJSONString(skuList));
            return;
        }

        for (SkuMallTagInfoDTO tagInfoDTO : tagInfos) {
            String sku = tagInfoDTO.getSku();
            Inventory inventory = inventoryMap.get(tagInfoDTO.getSku());
            if (inventory == null) {
                log.error("商品标签--商品信息为空！sku:{}", sku);
                continue;
            }
            Integer extType = inventory.getExtType();
            MallTagEnum tagEnum = getMallTagEnumByExt(extType);
            if (tagEnum != null) {
                tagInfoDTO.getRemainTagEnums().add(tagEnum);
            }
            tagInfoDTO.setExtType(extType);
        }
    }

    /**
     * extType 转换成 MallTagEnum
     *
     * @param extType
     * @return
     */
    private MallTagEnum getMallTagEnumByExt(Integer extType) {
        MallTagEnum tagEnum = null;
        switch (extType) {
            case 1:
                //活动
                tagEnum = MallTagEnum.EXT_ACTIVITY;
                break;
            case 2:
                //临保
                tagEnum = MallTagEnum.WILL_EXPIRE;
                break;
            case 3:
                //拆包
                tagEnum = MallTagEnum.UNPACK;
                break;
            case 5:
                //破袋
                tagEnum = MallTagEnum.BROKEN;
                break;
            default:
                break;
        }
        return tagEnum;
    }
}
