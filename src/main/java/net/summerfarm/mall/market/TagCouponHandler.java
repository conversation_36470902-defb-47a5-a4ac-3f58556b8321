package net.summerfarm.mall.market;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.enums.CouponActivityScopeEnum;
import net.summerfarm.mall.enums.market.MallTagEnum;
import net.summerfarm.mall.model.domain.Coupon;
import net.summerfarm.mall.model.dto.market.coupon.SkuCouponDTO;
import net.summerfarm.mall.model.dto.market.coupon.SkuCouponReqDTO;
import net.summerfarm.mall.model.dto.market.malltag.SkuMallTagInfoDTO;
import net.summerfarm.mall.model.dto.merchant.MerchantBasicDTO;
import net.summerfarm.mall.service.ConfigService;
import net.summerfarm.mall.service.CouponSenderService;
import net.xianmu.common.result.CommonResult;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 优惠券处理器
 *
 * @author: <EMAIL>
 * @create: 2023/5/4
 */
@Slf4j
@Service
public class TagCouponHandler extends TagMarketHandler {

    @Resource
    private CouponSenderService couponSenderService;

    @Resource
    private ConfigService configService;

    private static final String KEY = "AOL_LIMIT_PRICE_SKU";

    /** 释放卡劵-可以给安佳类产品使用 */
    public static final String releaseCard = "RELEASE_CARD";

    @Override
    public void handler(List<SkuMallTagInfoDTO> tagInfos, MerchantBasicDTO merchant) {
        if (merchant.getIsMajor()) { //NOSONAR
            log.info("商品标签--当前商户:{}为大客户,不处理优惠券标签", merchant.getMId());
            return;
        }
        List<String> skus = tagInfos.stream().map(x -> x.getSku()).collect(Collectors.toList());

        List<String> blackSkus = configService.getValuesWithCache(KEY, 1800L);
        SkuCouponReqDTO couponReqDTO = new SkuCouponReqDTO();
        couponReqDTO.setSkus(skus);
        couponReqDTO.setShowMerchantCoupon(Boolean.TRUE);

        //批量获取sku可用优惠券
        List<Coupon> coupons = couponSenderService.listUsableCouponByCache(merchant.getMId());
        CommonResult<List<SkuCouponDTO>> commonResult = couponSenderService.listAllValidCouponFromCacheV3(couponReqDTO, coupons, merchant.getMId());
        List<SkuCouponDTO> couponDTOList = commonResult.getData();
        if (CollectionUtil.isEmpty(couponDTOList)) {
            log.info("商品标签--当前客户可用优惠券为空");
            return;
        }

        //优惠券释放：售后补偿券、销售囤货券、销售现货券、销售品类券这四类分组的优惠券支持安佳可用
        List<String> releaseCardList = configService.getValuesByLocalCache(releaseCard);
        List<Integer> releaseCards = releaseCardList.stream().map(e -> Integer.valueOf(e)).collect(Collectors.toList());

        //需要剔除的优惠券类型
        List<String> values = configService.getValues(Global.HIDE_COUPON_GROUP);
        Set<Integer> hideCouponGrouping = values.stream().map(Integer::valueOf).collect(Collectors.toSet());
        Map<String, SkuCouponDTO> skuCouponDTOMap = couponDTOList.stream().collect(Collectors.toMap(SkuCouponDTO::getSku, Function.identity(), (o1, o2) -> o2));
        Set<String> usableCouponInfo = coupons.stream().map(coupon -> coupon.getName() + coupon.getId()).collect(Collectors.toSet());
        log.info("商品标签--用户可用优惠券ID列表:{}", JSON.toJSONString(usableCouponInfo));
        for (SkuMallTagInfoDTO tagInfoDTO : tagInfos) {
            SkuCouponDTO couponDTO = skuCouponDTOMap.get(tagInfoDTO.getSku());
            if(null== couponDTO){
                log.warn("SKU没有可用的优惠券:{}", tagInfoDTO.getSku());
                continue;
            }
            List<Coupon> couponList = couponDTO.getCouponList();
            List<Coupon> usableCouponList = new ArrayList<>();
            couponList.stream().forEach(couponVO -> {

                //获取标签所有商品屏蔽售后补偿券 需求名称：商城-预估到手价剔除售后补偿券 by[薄荷]
                if (!CollectionUtils.isEmpty(hideCouponGrouping) && hideCouponGrouping.contains(couponVO.getGrouping())) {
                    log.info("商品标签--预估到手价剔除售后补偿券, couponName:{}, couponId:{}", couponVO.getName(), couponVO.getId());
                    return;
                }

                //过滤安佳类产品不可用
                if (!CollectionUtils.isEmpty(blackSkus) && blackSkus.contains(tagInfoDTO.getSku())) {

                    //优惠券释放：售后补偿券、销售囤货券、销售现货券、销售品类券这四类分组的优惠券支持安佳可用
                    if (!CollectionUtils.isEmpty(releaseCards) && releaseCards.contains(couponVO.getGrouping())) {
                        usableCouponList.add(couponVO);
                    } else {
                        log.info("商品标签--安佳类产品:{}仅可用售后补偿券、销售囤货券、销售现货券、销售品类券这四类分组的优惠券, couponName:{}, couponId:{}", tagInfoDTO.getSku(), couponVO.getName(), couponVO.getId());
                    }
                } else {
                    usableCouponList.add(couponVO);
                }
            });

            //非省心送可用优惠券
            List<Coupon> commonCouponList = usableCouponList.stream()
                    .filter(x -> !Objects.equals(x.getActivityScope(),
                            CouponActivityScopeEnum.TIMING_DELIVERY.ordinal()))
                    .sorted(Comparator.comparing(Coupon::getMoney).reversed()).collect(
                            Collectors.toList());
            tagInfoDTO.setCouponList(commonCouponList);

            //全部、仅省心送可用优惠券
            List<Coupon> timingCouponList = usableCouponList.stream()
                    .filter(x -> Objects.equals(x.getActivityScope(),
                            CouponActivityScopeEnum.TIMING_DELIVERY.ordinal())
                            || Objects.equals(x.getActivityScope(),
                            CouponActivityScopeEnum.ALL.ordinal()))
                    .sorted(Comparator.comparing(Coupon::getMoney).reversed()).collect(
                            Collectors.toList());
            tagInfoDTO.setTimingCouponList(timingCouponList);
            if (CollectionUtil.isNotEmpty(tagInfoDTO.getCouponList()) || CollectionUtil.isNotEmpty(
                    tagInfoDTO.getTimingCouponList())) {
                tagInfoDTO.getRemainTagEnums().add(MallTagEnum.COUPON);
            }
        }
    }
}
