package net.summerfarm.mall.market;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.enums.market.MallTagEnum;
import net.summerfarm.mall.model.dto.market.malltag.SkuMallTagInfoDTO;
import net.summerfarm.mall.model.dto.merchant.MerchantBasicDTO;
import net.summerfarm.mall.model.vo.TimingRuleVO;
import net.summerfarm.mall.service.TimingRuleService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 省心送处理器
 *
 * @author: <EMAIL>
 * @create: 2023/5/4
 */
@Slf4j
@Service
public class TagTimingRuleHandler extends TagMarketHandler {

    @Resource
    private TimingRuleService timingRuleService;

    @Override
    public void handler(List<SkuMallTagInfoDTO> tagInfos, MerchantBasicDTO merchant) {
        if (merchant.getIsMajor()) { //NOSONAR
            log.info("商品标签--当前商户:{}为大客户,不处理省心送标签", merchant.getMId());
            return;
        }
        List<TimingRuleVO> timingRuleVOS = timingRuleService.getTimingInfoByAreaNoCache(merchant.getAreaNo());
        if (CollectionUtil.isEmpty(timingRuleVOS)) {
            log.info("商品标签--未获取到省心送规则");
            return;
        }
        Map<String, TimingRuleVO> ruleVOMap = timingRuleVOS.stream().collect(Collectors.
                toMap(TimingRuleVO::getTimingSku, Function.identity(), (v1, v2) -> v1));
        for (SkuMallTagInfoDTO tagInfoDTO : tagInfos) {
            if (ruleVOMap.containsKey(tagInfoDTO.getSku())) {
                tagInfoDTO.getRemainTagEnums().add(MallTagEnum.TIMING_DELIVER);
                tagInfoDTO.setTimingRule(ruleVOMap.get(tagInfoDTO.getSku()));
            }
        }
    }
}
