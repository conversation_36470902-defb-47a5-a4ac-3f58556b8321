package net.summerfarm.mall.market;

import java.util.List;
import net.summerfarm.mall.model.dto.market.malltag.SkuMallTagInfoDTO;
import net.summerfarm.mall.model.dto.merchant.MerchantBasicDTO;

/**
 * 标签处理器抽象类
 * @author: <EMAIL>
 * @create: 2023/5/4
 */
public abstract class TagMarketHandler {

    /**
     * 营销活动处理
     * @param tagInfos
     * @param merchant
     */
    public abstract void handler(List<SkuMallTagInfoDTO> tagInfos, MerchantBasicDTO merchant);

}
