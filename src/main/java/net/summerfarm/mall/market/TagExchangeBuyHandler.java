package net.summerfarm.mall.market;

import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.enums.market.MallTagEnum;
import net.summerfarm.mall.model.dto.market.exchange.ExchangeBuyDTO;
import net.summerfarm.mall.model.dto.market.malltag.SkuMallTagInfoDTO;
import net.summerfarm.mall.model.dto.merchant.MerchantBasicDTO;
import net.summerfarm.mall.service.ExchangeBuyService;
import net.xianmu.common.result.CommonResult;
import org.springframework.stereotype.Service;

/**
 * 换购处理器
 * @author: <EMAIL>
 * @create: 2023/5/10
 */
@Slf4j
@Service
public class TagExchangeBuyHandler extends TagMarketHandler{

    @Resource
    private ExchangeBuyService exchangeBuyService;

    @Override
    public void handler(List<SkuMallTagInfoDTO> tagInfos, MerchantBasicDTO merchant) {
        if (merchant.getIsMajor()) { //NOSONAR
            log.info("商品标签--当前商户:{}为大客户,不处理换购活动标签", merchant.getMId());
            return;
        }
        CommonResult<ExchangeBuyDTO> commonResult = exchangeBuyService.getExchangeBuyInfo();
        ExchangeBuyDTO buyDTO = commonResult.getData();
        if (buyDTO == null || buyDTO.getExchangeBaseInfo() == null) {
            log.info("商品标签--当前客户未获取到换购信息");
            return;
        }
        buyDTO.setExchangeScopeConfig(null);
        for (SkuMallTagInfoDTO tagInfoDTO : tagInfos) {
            tagInfoDTO.setExchangeBuyDTO(buyDTO);
            tagInfoDTO.getRemainTagEnums().add(MallTagEnum.EXCHANGE);
        }

    }
}
