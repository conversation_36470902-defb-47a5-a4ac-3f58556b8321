package net.summerfarm.mall.market;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.constant.Constants.RedisConstant;
import net.summerfarm.mall.enums.market.MallTagEnum;
import net.summerfarm.mall.mapper.AreaSkuRecordMapper;
import net.summerfarm.mall.model.domain.AreaSkuRecord;
import net.summerfarm.mall.model.dto.market.malltag.SkuMallTagInfoDTO;
import net.summerfarm.mall.model.dto.merchant.MerchantBasicDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

/**
 * 新品处理器
 *
 * @author: <EMAIL>
 * @create: 2023/5/19
 */
@Slf4j
@Service
public class TagNewProductHandler extends TagMarketHandler {

    @Resource
    private AreaSkuRecordMapper areaSkuRecordMapper;

    @Resource
    private RedisTemplate redisTemplate;

    public static final String NEW_PRODUCT = "new_product:";

    public static final Long EXPIRE_TIME = 3600L*12;

    @Override
    public void handler(List<SkuMallTagInfoDTO> tagInfos, MerchantBasicDTO merchant) {
        List<String> notFindSkus = Lists.newArrayList();
        Map<String, Integer> newSkuMap = Maps.newHashMap();
        for (SkuMallTagInfoDTO tagInfoDTO : tagInfos) {
            String sku = tagInfoDTO.getSku();
            String newProductKey = NEW_PRODUCT + ":" + sku + ":" + merchant.getAreaNo();
            String newProductValue = (String) redisTemplate.opsForValue().get(newProductKey);
            if (StringUtils.isNotBlank(newProductValue)) {
                if (Objects.equals(newProductValue, "0")) {
                    newSkuMap.put(sku, 0);
                }
                if (Objects.equals(newProductValue, "1")) {
                    newSkuMap.put(sku, 1);
                }
            } else {
                //缓存中未查到
                notFindSkus.add(sku);
            }
        }

        if (CollectionUtil.isNotEmpty(notFindSkus)) {
            //批量获取最早上架记录
            List<AreaSkuRecord> skuRecords = areaSkuRecordMapper.listFirstOnSaleBySkus(
                    merchant.getAreaNo(), notFindSkus);
            Map<String, LocalDateTime> timeMap = skuRecords.stream()
                    .collect(Collectors.toMap(x -> x.getSku(), t -> t.getAddtime()));

            for (String sku : notFindSkus) {
                LocalDateTime firstOnSaleTime = timeMap.get(sku);
                String newProductKey = NEW_PRODUCT + ":" + sku + ":" + merchant.getAreaNo();
                if (firstOnSaleTime != null && firstOnSaleTime.plusDays(30)
                        .isAfter(LocalDateTime.now())) {
                    redisTemplate.opsForValue()
                            .setIfAbsent(newProductKey, "1", EXPIRE_TIME, TimeUnit.SECONDS);
                    newSkuMap.put(sku, 1);
                } else {
                    redisTemplate.opsForValue()
                            .setIfAbsent(newProductKey, "0", EXPIRE_TIME, TimeUnit.SECONDS);
                    newSkuMap.put(sku, 0);
                }
            }
        }

        for (SkuMallTagInfoDTO tagInfoDTO : tagInfos) {
            String sku = tagInfoDTO.getSku();
            Integer value = newSkuMap.get(sku);
            if (Objects.equals(value, 1)) {
                tagInfoDTO.getRemainTagEnums().add(MallTagEnum.NEW_PRODUCT);
            }
        }

    }
}
