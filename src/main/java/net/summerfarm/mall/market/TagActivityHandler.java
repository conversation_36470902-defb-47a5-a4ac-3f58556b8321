package net.summerfarm.mall.market;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.enums.market.MallTagEnum;
import net.summerfarm.mall.model.dto.market.activity.ActivitySkuDTO;
import net.summerfarm.mall.model.dto.market.activity.ActivitySkuDetailDTO;
import net.summerfarm.mall.model.dto.market.malltag.SkuMallTagInfoDTO;
import net.summerfarm.mall.model.dto.merchant.MerchantBasicDTO;
import net.summerfarm.mall.service.ActivityService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 特价活动处理器
 * @author: <EMAIL>
 * @create: 2023/5/4
 */
@Slf4j
@Service
public class TagActivityHandler extends TagMarketHandler {

    @Resource
    private ActivityService activityService;

    @Override
    public void handler(List<SkuMallTagInfoDTO> tagInfos, MerchantBasicDTO merchant) {
        if (merchant.getIsMajor()) { //NOSONAR
            log.info("商品标签--当前商户:{}为大客户,不处理活动标签", merchant.getMId());
            return;
        }
        List<ActivitySkuDTO> activitySkuDTOS = new ArrayList<>(tagInfos.size());
        tagInfos.forEach(skuMallTagInfoDTO -> {
            if (skuMallTagInfoDTO.getSku() != null) {
                ActivitySkuDTO activitySkuDTO = new ActivitySkuDTO();
                activitySkuDTO.setSku(skuMallTagInfoDTO.getSku());
                activitySkuDTOS.add(activitySkuDTO);
            }
        });

        //批量获取sku活动信息
        Map<String, ActivitySkuDetailDTO> activitySkuDetailMap = activityService.listCacheByActivitySku(
                activitySkuDTOS, merchant.getAreaNo(), merchant.getMId(), Boolean.TRUE);
        for (SkuMallTagInfoDTO tagInfoDTO : tagInfos) {
            ActivitySkuDetailDTO activitySku = activitySkuDetailMap.get(tagInfoDTO.getSku());
            if (activitySku == null) {
                log.info("商品标签--当前sku:{}暂无活动信息", tagInfoDTO.getSku());
                continue;
            }
            Integer limitQuantity = activityService.getActivityLimitQuantity(activitySku);
            log.info("特价活动，限购数量：{}", limitQuantity);
            //没有超过限购就还是展示特价标签
            if (limitQuantity > 0) {
                tagInfoDTO.getRemainTagEnums().add(MallTagEnum.SPECIAL_OFFER);
            }
            tagInfoDTO.setActivityPrice(activitySku.getActivityPrice());
            tagInfoDTO.setPlatform(activitySku.getPlatform());
            //省心送特价有点区别，有该字段就展示特价标签，没有就看是否有阶梯价标签
            if (Objects.equals(activitySku.getIsSupportTiming(), 1)) {
                tagInfoDTO.getRemainTagEnums().add(MallTagEnum.TIMING_SPECIAL_OFFER);
            }
            tagInfoDTO.setDiscountLabel(activitySku.getDiscountLabel());
            tagInfoDTO.setActivityLadderPrices(activitySku.getLadderPrices());
        }

    }
}
