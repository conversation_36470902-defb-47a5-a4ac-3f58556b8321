package net.summerfarm.mall.market;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.enums.market.MallTagEnum;
import net.summerfarm.mall.model.domain.DiscountCard;
import net.summerfarm.mall.model.domain.DiscountCardAvailable;
import net.summerfarm.mall.model.domain.DiscountCardToMerchant;
import net.summerfarm.mall.model.dto.market.malltag.SkuMallTagInfoDTO;
import net.summerfarm.mall.model.dto.merchant.MerchantBasicDTO;
import net.summerfarm.mall.model.vo.DiscountCardToMerchantVO;
import net.summerfarm.mall.service.DiscountCardService;
import net.summerfarm.mall.service.convert.DiscountCardConverter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 优惠卡处理器
 * @author: <EMAIL>
 * @create: 2023/5/4
 */
@Slf4j
@Service
public class TagDiscountCardHandler extends TagMarketHandler {

    @Resource
    private DiscountCardService discountCardService;

    @Override
    public void handler(List<SkuMallTagInfoDTO> tagInfos, MerchantBasicDTO merchant) {
        if (merchant.getIsMajor()) { //NOSONAR
            log.info("商品标签--当前商户:{}为大客户,不处理优惠卡标签", merchant.getMId());
            return;
        }
        //获取所有可用优惠卡
        List<DiscountCardToMerchant> cardList = discountCardService.getMerchantCardByCache(merchant.getMId());
        if (CollectionUtil.isEmpty(cardList)) {
            log.info("商品标签--该客户暂无可用优惠卡");
            return;
        }
        Set<Integer> cardIds = cardList.stream().map(x -> x.getDiscountCardId())
                .collect(Collectors.toSet());

        //查出所有的优惠卡再做过滤
        List<DiscountCard> discountCards = discountCardService.selectAllDiscountCard();
        discountCards = discountCards.stream().filter(el -> cardIds.contains(el.getId())).collect(Collectors.toList());

        List<DiscountCard> validCards = Lists.newArrayList();
        //先过滤城市
        for (DiscountCard discountCard : discountCards) {
            if (StringUtils.isNotEmpty(discountCard.getAreaNos())) {
                String[] areaNos = discountCard.getAreaNos().split(",");
                List<String> resultList = new ArrayList<>(Arrays.asList(areaNos));
                if (resultList.contains(String.valueOf(merchant.getAreaNo()))) {
                    validCards.add(discountCard);
                }
            }
        }

        if (CollectionUtil.isEmpty(validCards)) {
            log.info("商品标签--该客户暂无有效优惠卡");
            return;
        }
        Map<Integer, DiscountCardToMerchant> discountCardMap = cardList.stream()
                .collect(Collectors.toMap(x -> x.getDiscountCardId(), Function.identity()));
        Set<Integer> validCardIds = validCards.stream().map(x -> x.getId()).collect(Collectors.toSet());
        List<String> skus = tagInfos.stream().map(x -> x.getSku()).collect(Collectors.toList());
        List<DiscountCardAvailable> cardAvailables = discountCardService.getAllDiscountCardAvailableByCache();
        cardAvailables = cardAvailables.stream().filter(el -> validCardIds.contains(el.getDiscountCardId())
                && skus.contains(el.getSku())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(cardAvailables)) {
            log.info("商品标签--该客户暂无有效优惠卡");
            return;
        }

        //匹配sku可用权益卡
        Map<Integer, DiscountCard> cardMap = discountCards.stream().collect(Collectors.toMap(x -> x.getId(), Function.identity()));
        for (SkuMallTagInfoDTO tagInfoDTO : tagInfos) {
            List<DiscountCardToMerchantVO> cards = Lists.newArrayList();
            for (DiscountCardAvailable cardAvailable : cardAvailables) {
                if (Objects.equals(cardAvailable.getSku(), tagInfoDTO.getSku())) {
                    DiscountCardToMerchant discountCardToMerchant = discountCardMap.get(
                            cardAvailable.getDiscountCardId());
                    DiscountCard discountCard = cardMap.get(cardAvailable.getDiscountCardId());
                    if (discountCardToMerchant == null || discountCard == null) {
                        log.error("优惠卡信息有误！cardAvailables:{}", JSON.toJSONString(cardAvailables));
                        continue;
                    }
                    DiscountCardToMerchantVO discountCardToMerchantVO = DiscountCardConverter.toDiscountCardMerchantVO(discountCardToMerchant);
                    discountCardToMerchantVO.setName(discountCard.getName());
                    discountCardToMerchantVO.setDiscount(discountCard.getDiscount());
                    cards.add(discountCardToMerchantVO);
                }
            }
            if (CollectionUtil.isNotEmpty(cards)) {
                tagInfoDTO.getRemainTagEnums().add(MallTagEnum.DISCOUNT_CARD);
                //从目前的业务来看，一个sku最多只会对应一张优惠卡，后台创建时有限制
                tagInfoDTO.setDiscountCard(cards);
            }
        }


    }
}
