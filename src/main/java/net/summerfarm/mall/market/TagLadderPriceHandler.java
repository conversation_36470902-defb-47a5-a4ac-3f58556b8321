package net.summerfarm.mall.market;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.mapper.AreaSkuMapper;
import net.summerfarm.mall.model.dto.market.malltag.SkuMallTagInfoDTO;
import net.summerfarm.mall.model.dto.merchant.MerchantBasicDTO;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 阶梯价处理器
 *
 * @author: <EMAIL>
 * @create: 2023/5/4
 */
@Slf4j
@Service
@Deprecated
public class TagLadderPriceHandler extends TagMarketHandler {

    @Resource
    private AreaSkuMapper areaSkuMapper;

    @Resource
    private RedisTemplate redisTemplate;

    public static final String LADDER_PRICE = "ladder_price:";

    @Override
    public void handler(List<SkuMallTagInfoDTO> tagInfos, MerchantBasicDTO merchant) {
        /*if (merchant.getIsMajor()) {
            return;
        }

        List<AreaSku> areaSkus = Lists.newArrayList();
        List<String> ladderSkus = Lists.newArrayList();
        List<String> notFindSkus = Lists.newArrayList();
        for (SkuMallTagInfoDTO tagInfoDTO : tagInfos) {
            String sku = tagInfoDTO.getSku();
            String ladderPriceKey = LADDER_PRICE + ":" + sku + ":" + merchant.getAreaNo();
            // 阶梯价
            String ladderPriceJson = (String) redisTemplate.opsForValue().get(ladderPriceKey);
            if (StringUtils.isNotBlank(ladderPriceJson)) {
                AreaSku areaSku = new AreaSku();
                areaSku.setSku(sku);
                areaSku.setLadderPrice(ladderPriceJson);
                areaSkus.add(areaSku);
                ladderSkus.add(sku);
            } else {
                notFindSkus.add(sku);
            }
        }

        if (CollectionUtil.isNotEmpty(notFindSkus)) {
            List<AreaSku> remainAreaSkus = areaSkuMapper.selectAreaSkuByAreaNoAndSkus(
                    merchant.getAreaNo(), notFindSkus);
            areaSkus.addAll(remainAreaSkus);
        }
        if (CollectionUtil.isEmpty(areaSkus)) {
            return;
        }
        Map<String, AreaSku> areaSkuMap = areaSkus.stream()
                .collect(Collectors.toMap(x -> x.getSku(), Function.identity(), (a, b) -> b));
        for (SkuMallTagInfoDTO tagInfoDTO : tagInfos) {
            String sku = tagInfoDTO.getSku();
            AreaSku areaSku = areaSkuMap.get(sku);
            if (areaSku != null) {
                // 阶梯价
                String ladderPriceJson = areaSku.getLadderPrice();
                if (ladderPriceJson == null) {
                    ladderPriceJson = "[]";
                }
                String ladderPriceKey = LADDER_PRICE + ":" + areaSku.getSku() + ":" + merchant.getAreaNo();
                redisTemplate.opsForValue()
                        .setIfAbsent(ladderPriceKey, ladderPriceJson, RedisConstant.EXPIRE_TIME, TimeUnit.SECONDS);
                if (StringUtils.isNotBlank(ladderPriceJson) && ObjectUtil.notEqual("[]", ladderPriceJson)) {
                    List<LadderPriceVO> ladderPriceObjs = JSONObject.parseArray(ladderPriceJson,
                            LadderPriceVO.class);
                    tagInfoDTO.getRemainTagEnums().add(MallTagEnum.LADDER_PRICE);
                    tagInfoDTO.setLadderPrices(ladderPriceObjs);
                }
            }
        }*/
    }
}
