package net.summerfarm.mall.after.template;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.enums.AfterSaleHandleType;
import net.summerfarm.enums.AfterSaleOrderStatus;
import net.summerfarm.enums.OrderTypeEnum;
import net.summerfarm.mall.after.template.impl.AuditAfterSolution;
import net.summerfarm.mall.contexts.AfterSaleWorkflowAction;
import net.summerfarm.mall.enums.AfterSaleDeliveryedEnum;
import net.summerfarm.mall.factory.AfterSaleSolutionOrderFactory;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022.06.30
 * 售后但审核审核
 */
@Component
@Slf4j
public class AfterSaleOrderHandleHandler {

    @Resource
    private AfterSaleSolutionOrderFactory afterSaleSolutionOrderFactory;
    @Resource
    private AfterSaleOrderMapper afterSaleOrderMapper;
    @Resource
    private AfterSaleProofMapper afterSaleProofMapper;
    @Resource
    protected InventoryMapper inventoryMapper;
    @Resource
    private AfterSaleWorkflowAction afterSaleWorkflowAction;
    @Resource
    private AfterSaleOrderCreatHandler afterSaleOrderCreatHandler;
    @Resource
    private AfterSaleDeliveryPathMapper afterSaleDeliveryPathMapper;
    /**
     * 基本校验，金额与数量
     * @return
     */
    public AjaxResult basicCheck(AfterSaleOrderVO afterSaleOrderVO,Orders orders,Integer skuType){
        //创建时的基本校验
        AjaxResult ajaxResult = afterSaleOrderCreatHandler.basicCheck(afterSaleOrderVO, orders, skuType);
        if (!AjaxResult.isSuccess(ajaxResult)){
            return ajaxResult;
        }
        if (!afterSaleOrderVO.getStatus().equals(AfterSaleOrderStatus.WAIT_HANDLE.getStatus())) {
            return AjaxResult.getErrorWithMsg("审核异常，该售后单不在待审核状态");
        }
        return AjaxResult.getOK();
    }


    /**
     * 审核售后单
     * @param afterSaleOrderVO
     * @param orders
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult handleAfterSaleOrder(BaseHandleAfterSaleSolution solution, AfterSaleOrderVO afterSaleOrderVO,Orders orders){
        //修改售后记录
        AfterSaleOrder updateKeys = new AfterSaleOrder();
        //-1为不通过,不用做校验
        if (afterSaleOrderVO.getHandleType() == -1){
            //修改状态
            updateKeys.setStatus(AfterSaleOrderStatus.FAIL.getStatus());
            afterSaleWorkflowAction.checkoutProof(afterSaleOrderVO);
            if (afterSaleOrderVO.getStatus().equals(AfterSaleOrderStatus.RE_COMMIT.getStatus())){
                updateKeys.setStatus(AfterSaleOrderStatus.RE_COMMIT.getStatus());
            }
            //将售后相关的配送信息取消
            AfterSaleDeliveryPath saleDeliveryPath = afterSaleDeliveryPathMapper.selectByAfterSaleOrderNo(afterSaleOrderVO.getAfterSaleOrderNo());
            if (saleDeliveryPath != null){
                saleDeliveryPath.setStatus(0);
                afterSaleDeliveryPathMapper.updateAfterSaleDeliveryPath(saleDeliveryPath);
            }

        }else {
            Inventory inventory = inventoryMapper.selectBySku(afterSaleOrderVO.getSku());
            //钩子判定
            if (hasBasicCheck(afterSaleOrderVO)){
                //基本校验
                AjaxResult basicCheck = basicCheck(afterSaleOrderVO, orders,inventory.getType());
                log.info("基本校验"+basicCheck);
                if(!AjaxResult.isSuccess(basicCheck)){
                    return basicCheck;
                }
            }
            //自定义校验
            AjaxResult customizeCheck = solution.customizeCheck(afterSaleOrderVO, orders);
            log.info("自定义校验"+customizeCheck);
            if(!AjaxResult.isSuccess(customizeCheck)){
                return customizeCheck;
            }
            //假如是不通过则状态为失败 （因为通过和不通过状态都是一样 所有只能根据不通过原因字段来区别是否通过）
            if (Objects.equals(afterSaleOrderVO.getHandleType(), AfterSaleHandleType.BLOCK_REFUNDS.getType())
                    && StringUtils.isNotBlank(afterSaleOrderVO.getHandleRemark())) {
                updateKeys.setStatus(AfterSaleOrderStatus.FAIL.getStatus());
                //这里失败的话设置ismanage字段，目的不让下面得代码进入审批流
                afterSaleOrderVO.setIsManage(Boolean.TRUE);
            } else {
                updateKeys.setStatus(AfterSaleOrderStatus.IN_HAND.getStatus());
            }

            //保存售后快照信息（平台、供应商定责比例）
            if (StringUtils.isNotBlank(afterSaleOrderVO.getSnapshot())) {
                updateKeys.setSnapshot(afterSaleOrderVO.getSnapshot());
            }
        }

        //只更改状态售后的
        updateKeys.setAfterSaleOrderNo(afterSaleOrderVO.getAfterSaleOrderNo());
        updateKeys.setView(0);
        afterSaleOrderMapper.updateByAfterSaleOrderNo(updateKeys);
        //获取最后一个
        List<AfterSaleProof> afterSaleProofs = afterSaleProofMapper.select(afterSaleOrderVO.getAfterSaleOrderNo());
        //根据id 修改proof 审核中
        AfterSaleProof updateProof = afterSaleWorkflowAction.conventAfterSaleProof(afterSaleOrderVO);
        updateProof.setStatus(updateKeys.getStatus());
        updateProof.setId(afterSaleProofs.get(afterSaleProofs.size() - 1).getId());
        updateProof.setAfterSaleType(afterSaleOrderVO.getAfterSaleType());
        updateProof.setRefundType(afterSaleOrderVO.getRefundType());
        updateProof.setHandler(afterSaleOrderVO.getHandler());
        updateProof.setRecoveryNum(afterSaleOrderVO.getRecoveryNum());
        updateProof.setHandleType(afterSaleOrderVO.getHandleType());
        updateProof.setHandleSecondaryRemark(afterSaleOrderVO.getHandleSecondaryRemark());
        //拒绝handler type不处理
        if (updateProof.getHandleType() == -1) {
            updateProof.setHandleType(null);
        }
        afterSaleProofMapper.updateById(updateProof);
        //审批工作流，根据isManage判断是否是独立审核的数据
        try {
            if (afterSaleOrderVO.getIsManage() == null){
                AjaxResult handleWork = handleWork(afterSaleOrderVO, orders);
                if (!AjaxResult.isSuccess(handleWork)){
                    //手动回滚
                    log.info("审核->审批通过异常：" + JSON.toJSONString(handleWork));
                    throw new RuntimeException("审核->审批通过异常");
                }
            }
        } catch (RuntimeException e) {
            log.info("审核->审批通过异常：{}",e);
            throw new RuntimeException("审核->审批通过异常");
        }

        return AjaxResult.getOK();
    }

    /**
     * 钩子方法
     * @return
     */
    private boolean hasBasicCheck(AfterSaleOrderVO afterSaleOrderVO) {
        if (afterSaleOrderVO.getHandleType().equals(AfterSaleHandleType.BLOCK_INCOMING_BILLS.getType())
                || afterSaleOrderVO.getHandleType().equals(AfterSaleHandleType.BLOCK_REFUNDS.getType())
                || afterSaleOrderVO.getHandleType().equals(AfterSaleHandleType.RETURN_SHIPPING.getType())
            || afterSaleOrderVO.getHandleType().equals(AfterSaleHandleType.RETURN_SHIPPING_BILL.getType())) {
            return false;
        }
        return true;
    }

    /**
     * 审批工作流
     * @param afterSaleOrderVO
     * @param orders
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult handleWork(AfterSaleOrderVO afterSaleOrderVO,Orders orders){
        Integer handleType = afterSaleOrderVO.getHandleType();
        Integer deliveryed = afterSaleOrderVO.getDeliveryed();
        //未到货退款(省心送订单不走),已到货返券,退运费,退运费录入账单,换货，补发 需要走自动审批
        //POP客户商城申请 已到货退款 -》待审核 自动审批
        boolean audit = ((deliveryed.equals(AfterSaleDeliveryedEnum.BROKEN.getType()) && Arrays.asList(new Integer[]{0 ,6,7,13,14}).contains(handleType) )
                || (deliveryed.equals(AfterSaleDeliveryedEnum.NOT_NEED.getType()) && Arrays.asList(new Integer[]{2, 3}).contains(handleType)&& !orders.getType().equals(OrderTypeEnum.TIMING.getId()))
                || (deliveryed.equals(AfterSaleDeliveryedEnum.NOT_NEED.getType()) && Arrays.asList(new Integer[]{11}).contains(handleType))
                || (deliveryed.equals(AfterSaleDeliveryedEnum.BROKEN.getType()) && Arrays.asList(new Integer[]{2}).contains(handleType) && orders.getType().equals(OrderTypeEnum.POP.getId())));
        if (audit){
            //审批
            //上古逻辑，status为1审核通过，0 审核不通过
            afterSaleOrderVO.setStatus(1);
            afterSaleOrderVO.setAuditer(afterSaleOrderVO.getApplyer());
            AuditAfterSolution auditAfterSolution = afterSaleSolutionOrderFactory.getAuditSolution(afterSaleOrderVO, orders);
            AfterSaleOrderAuditHandler afterSaleOrderAuditHandler = auditAfterSolution.getAfterSaleOrderAuditHandler();
            AjaxResult result = afterSaleOrderAuditHandler.auditAfterSaleOrder(auditAfterSolution, afterSaleOrderVO, orders);
            if (!AjaxResult.isSuccess(result)){
                return result;
            }
        }
        return AjaxResult.getOK();
    }
}
