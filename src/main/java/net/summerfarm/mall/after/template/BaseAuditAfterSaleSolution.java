package net.summerfarm.mall.after.template;


import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.mall.after.service.AfterSaleOrderHandleTypeSolution;
import net.summerfarm.mall.after.service.AfterSaleOrderSkuTypeSolution;
import net.summerfarm.mall.after.service.AfterSaleOrderTypeSolution;
import net.summerfarm.mall.model.domain.Orders;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;

/**
 * <AUTHOR>
 * @date 2022/10/9  14:30
 */
@Data
public class BaseAuditAfterSaleSolution {
    protected AfterSaleOrderHandleTypeSolution handleTypeSolution;
    protected AfterSaleOrderSkuTypeSolution skuTypeSolution;
    protected AfterSaleOrderTypeSolution orderTypeSolution;

    public AjaxResult customizeCheck(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {
        if (handleTypeSolution != null) {
            AjaxResult check = handleTypeSolution.auditByHandleTypeSolution(afterSaleOrderVO, orders);
            if (!AjaxResult.isSuccess(check)) {
                return check;
            }
        }
        if (skuTypeSolution != null) {
            AjaxResult check = skuTypeSolution.auditBySKUTypeSolution();
            if (!AjaxResult.isSuccess(check)) {
                return check;
            }
        }
        if (orderTypeSolution != null) {
            AjaxResult check = orderTypeSolution.auditByOrderTypeCheck();
            if (!AjaxResult.isSuccess(check)) {
                return check;
            }
        }
        return AjaxResult.getOK();
    }

    public AjaxResult successAfter(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {
        if (handleTypeSolution != null) {
            AjaxResult check = handleTypeSolution.auditSuccessfulOrderSolution(afterSaleOrderVO, orders);
            if (!AjaxResult.isSuccess(check)) {
                return check;
            }
        }
        return AjaxResult.getOK();
    }
}