package net.summerfarm.mall.after.template;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.enums.AfterSaleHandleType;
import net.summerfarm.enums.AfterSaleOrderStatus;
import net.summerfarm.mall.contexts.AfterSaleWorkflowAction;
import net.summerfarm.mall.enums.AfterSaleDeliveryedEnum;
import net.summerfarm.mall.enums.MerchantSizeEnum;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import net.summerfarm.mall.model.vo.InventoryVO;
import net.summerfarm.mall.service.ActivityService;
import net.summerfarm.mall.wechat.templatemessage.AfterSaleNoticeMag;
import net.summerfarm.mall.wechat.templatemessage.TemplateMsgSender;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022.06.30
 * 审批售后单
 */
@Component
@Slf4j
public class AfterSaleOrderAuditHandler {
    @Resource
    private AfterSaleOrderMapper afterSaleOrderMapper;
    @Resource
    private AfterSaleProofMapper afterSaleProofMapper;
    @Resource
    private AfterSaleWorkflowAction afterSaleWorkflowAction;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private AfterSaleOrderCreatHandler afterSaleOrderCreatHandler;
    @Resource
    private ActivityService activityService;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private TemplateMsgSender templateMsgSender;

    /**
     * 基础校验，金额数量等。
     */
    public AjaxResult basicCheck(AfterSaleOrderVO afterSaleOrderVO,Orders orders,Integer skuType){
        if (!Objects.equals(AfterSaleOrderStatus.IN_HAND.getStatus(),afterSaleOrderVO.getStatus())) {
            return AjaxResult.getErrorWithMsg("售后状态错误");
        }
        //创建时的基本校验
        AjaxResult ajaxResult = afterSaleOrderCreatHandler.basicCheck(afterSaleOrderVO, orders, skuType);
        if (!AjaxResult.isSuccess(ajaxResult)){
            return ajaxResult;
        }
        return AjaxResult.getOK();
    }

    /**
     * 钩子方法
     * @return
     */
    private boolean hasBasicCheck(AfterSaleOrderVO afterSaleOrderVO) {
        if (afterSaleOrderVO.getHandleType().equals(AfterSaleHandleType.BLOCK_INCOMING_BILLS.getType())
                || afterSaleOrderVO.getHandleType().equals(AfterSaleHandleType.BLOCK_REFUNDS.getType())
                || afterSaleOrderVO.getHandleType().equals(AfterSaleHandleType.RETURN_SHIPPING.getType())
                || afterSaleOrderVO.getHandleType().equals(AfterSaleHandleType.RETURN_SHIPPING_BILL.getType())) {
            return false;
        }
        return true;
    }

    /**
     * 审批售后单
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult auditAfterSaleOrder(BaseAuditAfterSaleSolution solution, AfterSaleOrderVO afterSaleOrderVO,Orders orders){

        Integer status = afterSaleOrderVO.getStatus();
        //获取最后一个
        List<AfterSaleProof> afterSaleProofs = afterSaleProofMapper.select(afterSaleOrderVO.getAfterSaleOrderNo());
        //根据id 修改proof
        AfterSaleProof updateProof = new AfterSaleProof();
        AfterSaleOrder updateAfterOrder = new AfterSaleOrder();
        updateProof.setAuditer(afterSaleOrderVO.getAuditer());
        updateProof.setId(afterSaleProofs.get(afterSaleProofs.size() - 1).getId());
        Integer isSuccess = AfterSaleOrderStatus.SUCCESS.getStatus();
        //审核不通过，不用做校验
        if (status == 0) {
            //更改审核订单列表
            isSuccess = AfterSaleOrderStatus.WAIT_HANDLE.getStatus();
            //更新凭证状态
            afterSaleWorkflowAction.notPass(afterSaleOrderVO);

            //审核通过
        }else if (status == 1){
            Inventory inventory = inventoryMapper.selectBySku(afterSaleOrderVO.getSku());
            //钩子判定
            if (hasBasicCheck(afterSaleOrderVO)){
                //基本校验
                AjaxResult basicCheck = basicCheck(afterSaleOrderVO, orders,inventory.getType());
                log.info("基本校验"+basicCheck);
                if(!AjaxResult.isSuccess(basicCheck)){
                    return basicCheck;
                }
            }
            //自定义校验
            AjaxResult customizeCheck = solution.customizeCheck(afterSaleOrderVO, orders);
            log.info("自定义校验"+customizeCheck);
            if(!AjaxResult.isSuccess(customizeCheck)){
                return customizeCheck;
            }

        }else {
            return AjaxResult.getErrorWithMsg("不正确的审核状态");
        }

        updateAfterOrder.setView(0);
        updateAfterOrder.setStatus(isSuccess);
        updateAfterOrder.setAfterSaleOrderNo(afterSaleOrderVO.getAfterSaleOrderNo());
        afterSaleOrderMapper.updateByAfterSaleOrderNo(updateAfterOrder);
        updateProof.setAuditer(null != afterSaleOrderVO.getAuditer() ? afterSaleOrderVO.getAuditer() : afterSaleOrderVO.getHandler());
        updateProof.setStatus(isSuccess);
        updateProof.setAuditeRemark(afterSaleOrderVO.getAuditeRemark());
        updateProof.setAuditetime(LocalDateTime.now());
        afterSaleProofMapper.updateById(updateProof);
        //审核通过执行逻辑，有些情况需要查询售后单号，所以在售后单号更新后再执行
        if (status == 1){
            //根据售后服务类型执行的后继操作
            AjaxResult ajaxResult = solution.successAfter(afterSaleOrderVO, orders);
            if (!AjaxResult.isSuccess(ajaxResult)){
                return ajaxResult;
            }

            //未到货活动商品，需要退活动库存 2024-08-21新增逻辑 省心送全部退款情况下才退活动库存
            if (afterSaleOrderVO.getDeliveryed().equals(AfterSaleDeliveryedEnum.NOT_NEED.getType())){
                activityService.afterSaleActivityQuantity(afterSaleOrderVO);
            }

            //退货退款、退款类型审核通过发送公众号消息提醒
            try {
                if (afterSaleOrderVO.getHandleType().equals(AfterSaleHandleType.REFUND.getType()) ||
                        afterSaleOrderVO.getHandleType().equals(AfterSaleHandleType.REFUND_GOODS.getType())){
                    Merchant merchant = merchantMapper.selectOneByMid(orders.getmId() == null ? afterSaleOrderVO.getmId() : Long.valueOf(orders.getmId()));
                    if (merchant != null && MerchantSizeEnum.SINGLE_SHOP.getValue().equals(merchant.getSize())) {
                        InventoryVO inventory = inventoryMapper.selectInventoryVOBySku(afterSaleOrderVO.getSku());
                        afterSaleOrderVO.setPdName(inventory.getPdName());
                        String message = AfterSaleNoticeMag.templateMessage(merchant.getOpenid(), afterSaleOrderVO, isSuccess, afterSaleProofs.get(0).getCreateTime());
                        templateMsgSender.sendTemplateMsg(message);
                    } else {
                        log.info("大客户类型，不发送售后单公众号消息提醒！");
                    }
                }
            } catch (Exception e) {
                log.warn("审核通过发送售后单公众号消息提醒失败！afterSaleOrderVO:{}, cause:{}", JSON.toJSONString(afterSaleOrderVO), e);
            }
        }

        return AjaxResult.getOK();
    }


}
