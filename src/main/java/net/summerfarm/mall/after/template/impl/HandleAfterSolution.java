package net.summerfarm.mall.after.template.impl;

import lombok.Data;
import net.summerfarm.mall.after.template.AfterSaleOrderHandleHandler;
import net.summerfarm.mall.after.template.BaseHandleAfterSaleSolution;


/**
 * <AUTHOR>
 * @date 2022.07.01
 * 审核售后单桥接类
 */
@Data
public class HandleAfterSolution extends BaseHandleAfterSaleSolution {
    private AfterSaleOrderHandleHandler afterSaleOrderHandleHandler;
}

