package net.summerfarm.mall.after.template;

import lombok.Data;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.mall.after.service.AfterSaleOrderHandleTypeSolution;
import net.summerfarm.mall.after.service.AfterSaleOrderSkuTypeSolution;
import net.summerfarm.mall.after.service.AfterSaleOrderTypeSolution;
import net.summerfarm.mall.model.domain.Orders;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;

/**
 * <AUTHOR>
 * @Description TODO
 * @date 2022/10/11 16:04
 * @Version 1.0
 */
@Data
public class BaseCreatAfterSaleSolution {
    protected AfterSaleOrderHandleTypeSolution handleTypeSolution;
    protected AfterSaleOrderSkuTypeSolution skuTypeSolution;
    protected AfterSaleOrderTypeSolution orderTypeSolution;

    public AjaxResult customizeCheck(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {
        if (handleTypeSolution != null) {
            AjaxResult check = handleTypeSolution.creatByHandleTypeSolution(afterSaleOrderVO, orders);
            if (!AjaxResult.isSuccess(check)) {
                return check;
            }
        }
        if (skuTypeSolution != null) {
            AjaxResult check = skuTypeSolution.createBySKUTypeSolution(afterSaleOrderVO, orders);
            if (!AjaxResult.isSuccess(check)) {
                return check;
            }
        }
        if (orderTypeSolution != null) {
            AjaxResult check = orderTypeSolution.creatByOrderTypeCheck(afterSaleOrderVO, orders);
            if (!AjaxResult.isSuccess(check)) {
                return check;
            }
        }
        return AjaxResult.getOK();
    }
}
