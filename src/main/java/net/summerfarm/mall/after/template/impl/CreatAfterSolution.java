package net.summerfarm.mall.after.template.impl;

import lombok.Data;
import net.summerfarm.mall.after.template.AfterSaleOrderCreatHandler;
import net.summerfarm.mall.after.template.BaseCreatAfterSaleSolution;

/**
 * <AUTHOR>
 * @date 2022.07.01
 * 生成售后单桥接类
 */
@Data
public class CreatAfterSolution extends BaseCreatAfterSaleSolution {
    private AfterSaleOrderCreatHandler afterSaleOrderCreatHandler;
}
