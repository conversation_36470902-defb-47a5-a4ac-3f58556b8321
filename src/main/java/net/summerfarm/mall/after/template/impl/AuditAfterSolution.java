package net.summerfarm.mall.after.template.impl;

import lombok.Data;
import net.summerfarm.mall.after.template.AfterSaleOrderAuditHandler;
import net.summerfarm.mall.after.template.BaseAuditAfterSaleSolution;

/**
 * <AUTHOR>
 * @date 2022.07.01
 * 审批售后单桥接类
 */
@Data
public class AuditAfterSolution extends BaseAuditAfterSaleSolution {
    private AfterSaleOrderAuditHandler afterSaleOrderAuditHandler;
}
