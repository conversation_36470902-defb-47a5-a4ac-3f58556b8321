package net.summerfarm.mall.after.service.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.enums.OrderTypeEnum;
import net.summerfarm.mall.after.service.AfterSaleOrderHandleTypeSolution;
import net.summerfarm.mall.common.mq.MQData;
import net.summerfarm.mall.common.mq.MType;
import net.summerfarm.mall.contexts.MQTopicConstant;
import net.summerfarm.mall.mapper.DeliveryPlanMapper;
import net.summerfarm.mall.model.domain.DeliveryPlan;
import net.summerfarm.mall.model.domain.Orders;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import net.summerfarm.mall.model.vo.DeliveryPlanVO;
import net.summerfarm.mall.service.AfterSaleOrderService;
import net.summerfarm.mall.service.helper.AfterHandleTypeExecuteHelper;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 补发
 * @date 2022/8/5 15:59
 * @Version 1.0
 */
@Component
@Slf4j
public class DeliveryGoodsHandleTypeAfterSolution implements AfterSaleOrderHandleTypeSolution {
    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;
    @Resource
    private AfterSaleOrderService afterSaleOrderService;
    @Resource
    private AfterHandleTypeExecuteHelper afterHandleTypeExecuteHelper;
    @Resource
    private MqProducer mqProducer;
    @Override
    public AjaxResult creatByHandleTypeSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {
        Integer deliveryId = afterSaleOrderVO.getDeliveryId();
        if (deliveryId == null && !orders.getType().equals(OrderTypeEnum.TIMING.getId())){
            List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanMapper.selectByOrderNo(orders.getOrderNo());
            if (deliveryPlanVOS.size() >1 || CollectionUtils.isEmpty(deliveryPlanVOS)){
                return AjaxResult.getErrorWithMsg("配送状态异常");
            }
            deliveryId = deliveryPlanVOS.get(0).getId();
        }
        AjaxResult result = afterSaleOrderService.checkStock(orders.getOrderNo(), afterSaleOrderVO.getSku(),deliveryId, afterSaleOrderVO.getQuantity(), afterSaleOrderVO.getmId());
        if (!AjaxResult.isSuccess(result)){
            return result;
        }
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult handleByHandleTypeSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {
        Integer deliveryId = afterSaleOrderVO.getDeliveryId();
        if (deliveryId == null && !orders.getType().equals(OrderTypeEnum.TIMING.getId())){
            List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanMapper.selectByOrderNo(orders.getOrderNo());
            if (deliveryPlanVOS.size() >1 || CollectionUtils.isEmpty(deliveryPlanVOS)){
                return AjaxResult.getErrorWithMsg("配送状态异常");
            }
            deliveryId = deliveryPlanVOS.get(0).getId();
        }
        AjaxResult result = afterSaleOrderService.checkStock(orders.getOrderNo(), afterSaleOrderVO.getSku(),deliveryId, afterSaleOrderVO.getQuantity(), afterSaleOrderVO.getmId());
        if (!AjaxResult.isSuccess(result)){
            return result;
        }
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult auditByHandleTypeSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {
        Integer deliveryId = afterSaleOrderVO.getDeliveryId();
        if (deliveryId == null && !orders.getType().equals(OrderTypeEnum.TIMING.getId())){
            List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanMapper.selectByOrderNo(orders.getOrderNo());
            if (deliveryPlanVOS.size() >1 || CollectionUtils.isEmpty(deliveryPlanVOS)){
                return AjaxResult.getErrorWithMsg("配送状态异常");
            }
            deliveryId = deliveryPlanVOS.get(0).getId();
        }
        AjaxResult result = afterSaleOrderService.checkStock(orders.getOrderNo(), afterSaleOrderVO.getSku(),deliveryId, afterSaleOrderVO.getQuantity(), afterSaleOrderVO.getmId());
        if (!AjaxResult.isSuccess(result)){
            return result;
        }
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult handleSuccessfulOrderSolution() {
        return AjaxResult.getOK();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED,rollbackFor=Exception.class)
    public AjaxResult auditSuccessfulOrderSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {

        AjaxResult result = afterHandleTypeExecuteHelper.backToStore(afterSaleOrderVO, orders);
        if (!AjaxResult.isSuccess(result)){
            return result;
        }
        //司机带货回来就要返库存，进行反库存操作
        HashMap<String, String> data = new HashMap<>(3);
        DeliveryPlan deliveryPlan = null;
        if (afterSaleOrderVO.getCarryingGoods() == 1){
            if (afterSaleOrderVO.getDeliveryId() != null){
                deliveryPlan = deliveryPlanMapper.selectById(afterSaleOrderVO.getDeliveryId());
            }else {
                List<DeliveryPlanVO> deliveryPlanVOList = deliveryPlanMapper.selectByOrderNo(afterSaleOrderVO.getOrderNo());
                if (deliveryPlanVOList == null || deliveryPlanVOList.size() >1){
                    return AjaxResult.getErrorWithMsg("配送计划错误");
                }
                deliveryPlan = deliveryPlanVOList.get(0);
            }
            // 注册事务同步处理
            if (TransactionSynchronizationManager.isActualTransactionActive()) {
                //存在事物
                DeliveryPlan finalDeliveryPlan = deliveryPlan;
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        sendReissueMq(afterSaleOrderVO, data, finalDeliveryPlan);
                    }
                });
            } else {
                sendReissueMq(afterSaleOrderVO, data, deliveryPlan);
            }
        }
        return AjaxResult.getOK();
    }

    private void sendReissueMq(AfterSaleOrderVO afterSaleOrderVO, HashMap<String, String> data, DeliveryPlan deliveryPlan) {
        MQData mqData = new MQData();
        data.put("orderStoreNo",deliveryPlan.getOrderStoreNo()+"");
        data.put("afterSaleOrderNo",afterSaleOrderVO.getAfterSaleOrderNo());
        data.put("orderNo",afterSaleOrderVO.getOrderNo());
        data.put("sku",afterSaleOrderVO.getSku());
        data.put("quantity",afterSaleOrderVO.getQuantity()+"");
        mqData.setData(data);
        mqData.setType(MType.REISSUE_WAREHOUSING.name());
        mqProducer.send(MQTopicConstant.MALL_LIST, null, JSON.toJSONString(mqData));
        log.info("补发生成入库任务，MQ消息发送："+JSON.toJSONString(mqData));
    }
}
