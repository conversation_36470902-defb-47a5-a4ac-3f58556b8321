package net.summerfarm.mall.after.service.impl;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.enums.AfterSaleHandleType;
import net.summerfarm.enums.OrderTypeEnum;
import net.summerfarm.mall.after.service.AfterSaleOrderTypeSolution;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.enums.OrderStatusEnum;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import net.summerfarm.mall.model.vo.DeliveryPlanVO;
import net.summerfarm.mall.service.helper.AfterSaleOrderHelper;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022.07.01
 * 普通订单类型售后单
 */
@Component
public class NormalOrderAfterSolution implements AfterSaleOrderTypeSolution {
    @Resource
    private AfterSaleOrderHelper afterSaleOrderHelper;
    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;
    @Resource
    private AfterSaleOrderMapper afterSaleOrderMapper;
    @Resource
    private OrderItemMapper orderItemMapper;

    @Override
    public AjaxResult getMayAfterSaleQuantity(AfterSaleOrderVO afterSaleOrderVO, Integer afterSaleQuantity, Orders orders) {
        //可售后的数量
        Integer quantity = 0;
        OrderItem orderItem = orderItemMapper.selectOrderItemByAfter(afterSaleOrderVO.getOrderNo(), afterSaleOrderVO.getSku(), afterSaleOrderVO.getSuitId(), afterSaleOrderVO.getProductType());
        if (Objects.isNull(orderItem)) {
            return AjaxResult.getErrorWithMsg("找不到订单项");
        }
        quantity = orderItem.getAmount();
        //数量先不管换货和补发的售后
        String orderNo = afterSaleOrderVO.getOrderNo();
        List<AfterSaleOrderVO> afterSaleOrderVOS = afterSaleOrderMapper.selectAfterQuantityByOrderNo(orderNo, afterSaleOrderVO.getSku(),afterSaleOrderVO.getDeliveryId(),afterSaleOrderVO.getSuitId(),afterSaleOrderVO.getAfterSaleOrderNo(),afterSaleOrderVO.getProductType());
        //判断返回数值类型,是SKU还是SKU最大售后数量
        Boolean calculation = afterSaleOrderHelper.calculation(afterSaleOrderVO.getDeliveryed(), afterSaleOrderVO.getHandleType());

        //售后支持小规格退货、回收需求改动 假如是 退货退款、退货录入账单 并且指定回收商品的话 按照最小售后单位进行售后
        if (AfterSaleHandleType.REFUND_GOODS.getType().equals(afterSaleOrderVO.getHandleType()) ||
                AfterSaleHandleType.REFUND_ENTRY_BILL.getType().equals(afterSaleOrderVO.getHandleType())) {
            if (!CollectionUtils.isEmpty(afterSaleOrderVO.getExchangeGoodList()) && !StringUtils.isEmpty(afterSaleOrderVO.getExchangeGoodList().get(0).getSku()) &&
                    !Objects.equals(afterSaleOrderVO.getSku(), afterSaleOrderVO.getExchangeGoodList().get(0).getSku())) {
                calculation = Boolean.FALSE;
            }
        }
        return afterSaleOrderHelper.maxQuantity(afterSaleOrderVOS, quantity, afterSaleQuantity, calculation, needRoundUpToAnInteger(afterSaleOrderVO, orders));
    }

    /**
     * 计算最大可售后数量时 是否需要向上取整
     * @param afterSaleOrderVO 售后单
     * @param orders 正向订单
     * @return 是否需要向上取整
     */
    private static boolean needRoundUpToAnInteger(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {
        boolean roundUpToAnInteger = false;
        if (OrderTypeEnum.POP.getId().equals(orders.getType())
                && AfterSaleHandleType.REFUND_GOODS.getType().equals(afterSaleOrderVO.getHandleType())){
            roundUpToAnInteger = true;
        }
        return roundUpToAnInteger;
    }

    @Override
    public AjaxResult creatByOrderTypeCheck(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {

        if (!afterSaleOrderVO.getHandleType().equals(AfterSaleHandleType.BLOCK_INCOMING_BILLS.getType()) && !afterSaleOrderVO.getHandleType().equals(AfterSaleHandleType.BLOCK_REFUNDS.getType())) {
            //未到货校验
            if (afterSaleOrderVO.getDeliveryed().equals(AfterSaleOrder.DELIVERY_NOT_RECEIVED)) {
                AjaxResult notArriveCheck = notArriveCheck(afterSaleOrderVO, orders);
                if (!AjaxResult.isSuccess(notArriveCheck)) {
                    return notArriveCheck;
                }
            } else {
                AjaxResult arriveCheck = arriveCheck(afterSaleOrderVO, orders);
                if (!AjaxResult.isSuccess(arriveCheck)) {
                    return arriveCheck;
                }
            }
        }
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult handleByOrderTypeCheck(AfterSaleOrderVO afterSaleOrderVO,Orders orders) {
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult auditByOrderTypeCheck() {
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult auditSuccessfulOrderSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {
        return AjaxResult.getOK();
    }

    /**
     * 已到货校验
     * @param afterSaleOrderVO
     * @param orders
     * @return
     */
    private AjaxResult arriveCheck(AfterSaleOrderVO afterSaleOrderVO, Orders orders){
        //配送计划校验
        List<DeliveryPlanVO> deliveryPlans = deliveryPlanMapper.selectByOrderNo(orders.getOrderNo());
        if (CollectionUtils.isEmpty(deliveryPlans) || deliveryPlans.size() > 1) {
            return AjaxResult.getErrorWithMsg("配送计划不正常");
        }

        //POP订单已到货售后需要校验配送状态是否已完成 -- 前台限制、后台不限制
        if (Objects.equals(orders.getType(), OrderTypeEnum.POP.getId()) && !deliveryPlans.get(0).getStatus().equals(OrderStatusEnum.RECEIVED.getId())
                && !afterSaleOrderVO.getIsManage()) {
            return AjaxResult.getErrorWithMsg("订单配送状态未完成不能申请已到货售后！");
        }
        return AjaxResult.getOK();
    }

    /**
     * 未到货校验
     * @param afterSaleOrderVO
     * @param orders
     * @return
     */
    private AjaxResult notArriveCheck(AfterSaleOrderVO afterSaleOrderVO, Orders orders){
        //配送计划校验
        List<DeliveryPlanVO> deliveryPlans = deliveryPlanMapper.selectByOrderNo(orders.getOrderNo());
        if (CollectionUtils.isEmpty(deliveryPlans) || deliveryPlans.size() > 1) {
            return AjaxResult.getErrorWithMsg("配送计划不正常");
        }
        DeliveryPlanVO deliveryPlanVO = deliveryPlans.get(0);
        Integer adminId = RequestHolder.getAdminId();

        //POP订单单独校验
        AjaxResult checkoutTime;
        if (Objects.equals(orders.getType(), OrderTypeEnum.POP.getId())) {
            //截单点校验
            checkoutTime = afterSaleOrderHelper.checkoutPOPTime(adminId, deliveryPlanVO, !afterSaleOrderVO.getIsManage(), orders);
        } else {
            //截单点校验
            checkoutTime = afterSaleOrderHelper.checkoutTime(adminId, deliveryPlanVO, !afterSaleOrderVO.getIsManage(), afterSaleOrderVO.getSku(), orders);
        }
        if(!AjaxResult.isSuccess(checkoutTime)){
            return checkoutTime;
        }
        return AjaxResult.getOK();
    }

}
