package net.summerfarm.mall.after.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.mall.model.domain.Orders;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022.07.01
 * 售后单 根据SKU状态校验
 */
@Component
public interface AfterSaleOrderSkuTypeSolution {

    /**
     * 创建是SKU校验
     * @param afterSaleOrderVO
     * @param orders
     * @return
     */
    AjaxResult createBySKUTypeSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders);

    /**
     * 审核售后单时校验
     * @return
     */
    AjaxResult handleBySKUTypeSolution();

    /**
     * 审批售后单时校验
     * @return
     */
    AjaxResult auditBySKUTypeSolution();
}
