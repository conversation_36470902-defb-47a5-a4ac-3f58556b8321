package net.summerfarm.mall.after.service.impl;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.mall.after.service.AfterSaleOrderHandleTypeSolution;
import net.summerfarm.mall.model.domain.Orders;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 拒收录入账单
 * @date 2022/8/5 16:00
 * @Version 1.0
 */
@Component
public class RefuseBillHandleTypeAfterSolution implements AfterSaleOrderHandleTypeSolution {


    @Override
    public AjaxResult creatByHandleTypeSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult handleByHandleTypeSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult auditByHandleTypeSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult handleSuccessfulOrderSolution() {
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult auditSuccessfulOrderSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {
        return AjaxResult.getOK();
    }
}
