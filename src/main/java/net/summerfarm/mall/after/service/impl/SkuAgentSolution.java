package net.summerfarm.mall.after.service.impl;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.mall.after.service.AfterSaleOrderSkuTypeSolution;
import net.summerfarm.mall.mapper.InventoryMapper;
import net.summerfarm.mall.model.domain.Inventory;
import net.summerfarm.mall.model.domain.Orders;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022.07.03
 * 根据SKU商品校验
 */
@Component
public class SkuAgentSolution implements AfterSaleOrderSkuTypeSolution {

    @Resource
    private InventoryMapper inventoryMapper;

    @Override
    public AjaxResult createBySKUTypeSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult handleBySKUTypeSolution() {
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult auditBySKUTypeSolution() {
        return AjaxResult.getOK();
    }
}
