package net.summerfarm.mall.after.service.impl;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.enums.AfterSaleHandleType;
import net.summerfarm.enums.AfterSaleOrderStatus;
import net.summerfarm.mall.after.service.AfterSaleOrderTypeSolution;
import net.summerfarm.mall.enums.AfterSaleDeliveryedEnum;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import net.summerfarm.mall.model.vo.DeliveryPlanVO;
import net.summerfarm.mall.service.AfterSaleOrderService;
import net.summerfarm.mall.service.helper.AfterHandleTypeExecuteHelper;
import net.summerfarm.mall.service.helper.AfterSaleOrderHelper;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022.07.01
 * 省心送订单类型售后单
 */
@Component
public class TimingOrderAfterSolution implements AfterSaleOrderTypeSolution {

    @Resource
    private RefundMapper refundMapper;
    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;
    @Resource
    private AfterSaleOrderMapper afterSaleOrderMapper;
    @Resource
    private AfterSaleOrderHelper afterSaleOrderHelper;
    @Resource
    private OrderItemMapper orderItemMapper;
    @Resource
    private AfterSaleOrderService afterSaleOrderService;
    @Resource
    private AfterHandleTypeExecuteHelper afterHandleTypeExecuteHelper;

    @Override
    public AjaxResult getMayAfterSaleQuantity(AfterSaleOrderVO afterSaleOrderVO,Integer afterSaleQuantity, Orders orders) {

        //未到货
        if (afterSaleOrderVO.getDeliveryed().equals(AfterSaleDeliveryedEnum.NOT_NEED.getType())){
            OrderItem orderItem = orderItemMapper.selectOrderItemByAfter(afterSaleOrderVO.getOrderNo(), afterSaleOrderVO.getSku(), afterSaleOrderVO.getSuitId(), afterSaleOrderVO.getProductType());
            Integer amount = orderItem.getAmount();
            //已配送数量
            Integer integer = deliveryPlanMapper.selectDeliveredQuantity4Follow(afterSaleOrderVO.getOrderNo());
            //查询省心送定单已经未到货退款的数量
            Integer notDeliveryQuantity = 0;
            AfterSaleOrder selectData = new AfterSaleOrder();
            selectData.setOrderNo(afterSaleOrderVO.getOrderNo());
            List<AfterSaleOrderVO> selectList = afterSaleOrderMapper.selectAfterQuantityByOrderNo(afterSaleOrderVO.getOrderNo(), afterSaleOrderVO.getSku(),afterSaleOrderVO.getDeliveryId(),afterSaleOrderVO.getSuitId(),afterSaleOrderVO.getAfterSaleOrderNo(),afterSaleOrderVO.getProductType());
            if (!org.springframework.util.CollectionUtils.isEmpty(selectList)) {
                for (AfterSaleOrderVO afterSale : selectList) {
                    if (afterSale.getDeliveryed() == 0 ) {
                        notDeliveryQuantity = notDeliveryQuantity + afterSale.getQuantity();
                    }
                }
            }
            amount = amount - integer - notDeliveryQuantity;
            if (amount <= 0){
                return AjaxResult.getErrorWithMsg("可售后数量小于等于0，已不可发起该类型售后");
            }
            return AjaxResult.getOK(amount);
        }else {
            //已到货
            Integer deliveryId = afterSaleOrderVO.getDeliveryId();
            DeliveryPlan deliveryPlan = deliveryPlanMapper.selectById(deliveryId);
            //最大可售后数量
            Integer quantity = deliveryPlan.getQuantity();
            List<AfterSaleOrderVO> afterSaleOrderVOS = afterSaleOrderMapper.selectAfterQuantityByOrderNo(afterSaleOrderVO.getOrderNo(), null, deliveryId,afterSaleOrderVO.getSuitId(),afterSaleOrderVO.getAfterSaleOrderNo(),afterSaleOrderVO.getProductType());
            //判断返回数值类型,是SKU还是SKU最大售后数量
            Boolean calculation = afterSaleOrderHelper.calculation(afterSaleOrderVO.getDeliveryed(), afterSaleOrderVO.getHandleType());
            //售后支持小规格退货、回收需求改动 假如是 退货退款、退货录入账单 并且指定回收商品的话 按照最小售后单位进行售后
            if (AfterSaleHandleType.REFUND_GOODS.getType().equals(afterSaleOrderVO.getHandleType()) ||
                    AfterSaleHandleType.REFUND_ENTRY_BILL.getType().equals(afterSaleOrderVO.getHandleType())) {
                if (!CollectionUtils.isEmpty(afterSaleOrderVO.getExchangeGoodList()) && !StringUtils.isEmpty(afterSaleOrderVO.getExchangeGoodList().get(0).getSku()) &&
                        !Objects.equals(afterSaleOrderVO.getSku(), afterSaleOrderVO.getExchangeGoodList().get(0).getSku())) {
                    calculation = Boolean.FALSE;
                }
            }
            return afterSaleOrderHelper.maxQuantity(afterSaleOrderVOS, quantity, afterSaleQuantity, calculation, false);
        }
    }

    @Override
    public AjaxResult creatByOrderTypeCheck(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {

        //预售省心送校验,补发，换货不参与校验
        if (Objects.equals(orders.getOrderSaleType(),1) && !afterSaleOrderVO.getHandleType().equals(AfterSaleHandleType.DELIVERY_GOODS.getType())
                && !afterSaleOrderVO.getHandleType().equals(AfterSaleHandleType.EXCHANGE_GOODS.getType())){
            AjaxResult ajaxResult = checkAfterOrder(afterSaleOrderVO);
            if (!AjaxResult.isSuccess(ajaxResult)){
                return ajaxResult;
            }
        }

        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult handleByOrderTypeCheck(AfterSaleOrderVO afterSaleOrderVO,Orders orders) {

        List<AfterSaleOrderVO> afterSaleOrders = afterSaleOrderService.selectAfterSaleOrderVO(afterSaleOrderVO);
        Integer afterQuantity = 0;
        for (AfterSaleOrderVO check : afterSaleOrders) {
            if (check.getStatus() == AfterSaleOrderStatus.SUCCESS.getStatus()
                    && Objects.equals(check.getDeliveryId(), afterSaleOrderVO.getDeliveryId())) {
                afterQuantity = afterQuantity + check.getQuantity();
            }
        }
        //查询未配置的省心送数量
        Integer quantity  = timingAfterSaleOrder(afterSaleOrderVO.getDeliveryId(),afterSaleOrderVO.getOrderNo(),afterSaleOrderVO.getSku());

        if(afterSaleOrderVO.getDeliveryId() == null && !Objects.equals(0,quantity)){
            return AjaxResult.getError("TOTAL_REFUND","当前未设置数量发生变更，请重新发起售后");
        }

        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult auditByOrderTypeCheck() {
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult auditSuccessfulOrderSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {
        AjaxResult result = afterHandleTypeExecuteHelper.reviseTimingOrderStatus(orders,afterSaleOrderVO.getQuantity());
        if (!AjaxResult.isSuccess(result)){
            return result;
        }
        return AjaxResult.getOK();
    }

    /**
     * 校验是否有未完成的售后单及退款单
     * @param afterSaleOrderVO
     * @return
     */
    private AjaxResult checkAfterOrder(AfterSaleOrderVO afterSaleOrderVO) {

        List<AfterSaleOrder> afterSaleOrders = afterSaleOrderMapper.selectAfterOrder(afterSaleOrderVO.getOrderNo());
        if (CollectionUtils.isEmpty(afterSaleOrders)){
            return AjaxResult.getOK();
        }
        //未成功的售后单
        List<Integer> statusList = Arrays.asList(new Integer[]{AfterSaleOrderStatus.WAIT_HANDLE.getStatus(), AfterSaleOrderStatus.IN_HAND.getStatus(),
                AfterSaleOrderStatus.IN_PAYMENT.getStatus(), AfterSaleOrderStatus.MANUAL_HANDLE.getStatus()});

        for (AfterSaleOrder afterSaleOrder : afterSaleOrders) {
            if (statusList.contains(afterSaleOrder.getStatus())) {
                return AjaxResult.getErrorWithMsg("有处理中的售后单，请处理后再次尝试发起售后单");
            }
            //成功状态的售后单再进行下一步判断
            if (afterSaleOrder.getStatus().equals(AfterSaleOrderStatus.SUCCESS.getStatus())){
                List<Refund> refunds = refundMapper.selectByAllAfterSaleOrderNo(afterSaleOrder.getAfterSaleOrderNo());
                if (!CollectionUtils.isEmpty(refunds)){
                    for (Refund refund : refunds) {
                        if (refund.getStatus() == 0 || refund.getStatus() == 4){
                            return AjaxResult.getErrorWithMsg("有未退款的售后单，可能正在退款，请稍后重试");
                        }
                    }
                }
            }

        }
        return AjaxResult.getOK();
    }

    /**
     * 获取省心送未配置的数量
     * @param deliveryId
     * @param orderNo
     * @param sku
     * @return
     */
    private Integer timingAfterSaleOrder(Integer deliveryId,String orderNo ,String sku){
        List<OrderItem> orderItems = orderItemMapper.selectList(orderNo, sku, 0);
        Integer totalQuantity = 0;
        if(deliveryId != null){
            DeliveryPlan deliveryPlan = deliveryPlanMapper.selectById(deliveryId);
            totalQuantity = deliveryPlan.getQuantity();
        } else {
            List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanMapper.selectByOrderNo(orderNo);
            for (DeliveryPlanVO deliveryPlanVO : deliveryPlanVOS) {
                Integer quantity = deliveryPlanVO.getQuantity();
                totalQuantity = quantity == null ? totalQuantity : totalQuantity+quantity;
            }
            int notQuantity = afterSaleOrderMapper.selectNotQuantityByOrderNo(orderNo);
            totalQuantity = orderItems.get(0).getAmount() - totalQuantity - notQuantity;
        }

        return totalQuantity;
    }
}
