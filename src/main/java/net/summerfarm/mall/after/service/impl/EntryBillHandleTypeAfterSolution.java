package net.summerfarm.mall.after.service.impl;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.mall.after.service.AfterSaleOrderHandleTypeSolution;
import net.summerfarm.mall.enums.AfterSaleDeliveryedEnum;
import net.summerfarm.mall.model.domain.Orders;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import net.summerfarm.mall.service.AfterSaleOrderService;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description 录入账单
 * @date 2022/8/5 15:55
 * @Version 1.0
 */
@Component
public class EntryBillHandleTypeAfterSolution implements AfterSaleOrderHandleTypeSolution {

    @Resource
    private AfterSaleOrderService afterSaleOrderService;

    @Override
    public AjaxResult creatByHandleTypeSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {

        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult handleByHandleTypeSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult auditByHandleTypeSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult handleSuccessfulOrderSolution() {
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult auditSuccessfulOrderSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {

        if (afterSaleOrderVO.getDeliveryed().equals(AfterSaleDeliveryedEnum.NOT_NEED.getType())){
            afterSaleOrderService.afterSaleEntryBill(afterSaleOrderVO.getAfterSaleOrderNo());
        }
        return AjaxResult.getOK();
    }
}
