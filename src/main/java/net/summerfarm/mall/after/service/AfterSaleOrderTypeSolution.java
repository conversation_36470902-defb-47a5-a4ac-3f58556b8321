package net.summerfarm.mall.after.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.mall.model.domain.Orders;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;

/**
 * <AUTHOR>
 * @date 2022.07.01
 * 售后单 根据订单类型校验
 */
public interface AfterSaleOrderTypeSolution {

    /**
     * 获取可售后数量
     * @param afterSaleOrderVO
     * @param afterSaleQuantity
     * @return
     */
    AjaxResult getMayAfterSaleQuantity(AfterSaleOrderVO afterSaleOrderVO,Integer afterSaleQuantity, Orders orders);

    /**
     * 创建时校验
     * @param afterSaleOrderVO
     * @param orders
     * @return
     */
    AjaxResult creatByOrderTypeCheck(AfterSaleOrderVO afterSaleOrderVO, Orders orders);

    /**
     * 审核售后单时校验
     * @return
     */
    AjaxResult handleByOrderTypeCheck(AfterSaleOrderVO afterSaleOrderVO,Orders orders);

    /**
     * 审批售后单时校验
     * @return
     */
    AjaxResult auditByOrderTypeCheck();

    /**
     * 审批成功后订单维度处理
     * @param afterSaleOrderVO
     * @param orders
     * @return
     */
    AjaxResult auditSuccessfulOrderSolution(AfterSaleOrderVO afterSaleOrderVO,Orders orders);
}
