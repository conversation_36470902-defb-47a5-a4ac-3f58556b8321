package net.summerfarm.mall.after.service.impl;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.enums.OrderTypeEnum;
import net.summerfarm.mall.after.service.AfterSaleOrderHandleTypeSolution;
import net.summerfarm.mall.mapper.DeliveryPlanMapper;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import net.summerfarm.mall.model.vo.DeliveryPlanVO;
import net.summerfarm.mall.service.AfterSaleOrderService;
import net.summerfarm.mall.service.helper.AfterHandleTypeExecuteHelper;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Description  换货
 * @date 2022/8/5 15:58
 * @Version 1.0
 */
@Component
public class ExchangeGoodsHandleTypeAfterSolution implements AfterSaleOrderHandleTypeSolution {
    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;
    @Resource
    private AfterSaleOrderService afterSaleOrderService;
    @Resource
    private AfterHandleTypeExecuteHelper afterHandleTypeExecuteHelper;


    @Override
    @Transactional(propagation = Propagation.REQUIRED,rollbackFor=Exception.class)
    public AjaxResult creatByHandleTypeSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {
        Integer deliveryId = afterSaleOrderVO.getDeliveryId();
        if (deliveryId == null && !orders.getType().equals(OrderTypeEnum.TIMING.getId())){
            List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanMapper.selectByOrderNo(orders.getOrderNo());
            if (deliveryPlanVOS.size() >1 || CollectionUtils.isEmpty(deliveryPlanVOS)){
                return AjaxResult.getErrorWithMsg("配送状态异常");
            }
            deliveryId = deliveryPlanVOS.get(0).getId();
        }
        AjaxResult result = afterSaleOrderService.checkStock(orders.getOrderNo(), afterSaleOrderVO.getSku(),deliveryId, afterSaleOrderVO.getQuantity(), afterSaleOrderVO.getmId());
        if (!AjaxResult.isSuccess(result)){
            return result;
        }
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult handleByHandleTypeSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {
        Integer deliveryId = afterSaleOrderVO.getDeliveryId();
        if (deliveryId == null && !orders.getType().equals(OrderTypeEnum.TIMING.getId())){
            List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanMapper.selectByOrderNo(orders.getOrderNo());
            if (deliveryPlanVOS.size() >1 || CollectionUtils.isEmpty(deliveryPlanVOS)){
                return AjaxResult.getErrorWithMsg("配送状态异常");
            }
            deliveryId = deliveryPlanVOS.get(0).getId();
        }
        AjaxResult result = afterSaleOrderService.checkStock(orders.getOrderNo(), afterSaleOrderVO.getSku(),deliveryId, afterSaleOrderVO.getQuantity(),afterSaleOrderVO.getmId());
        if (!AjaxResult.isSuccess(result)){
            return result;
        }
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult auditByHandleTypeSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {
        Integer deliveryId = afterSaleOrderVO.getDeliveryId();
        if (deliveryId == null && !orders.getType().equals(OrderTypeEnum.TIMING.getId())){
            List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanMapper.selectByOrderNo(orders.getOrderNo());
            if (deliveryPlanVOS.size() >1 || CollectionUtils.isEmpty(deliveryPlanVOS)){
                return AjaxResult.getErrorWithMsg("配送状态异常");
            }
            deliveryId = deliveryPlanVOS.get(0).getId();
        }
        AjaxResult result = afterSaleOrderService.checkStock(orders.getOrderNo(), afterSaleOrderVO.getSku(),deliveryId, afterSaleOrderVO.getQuantity(), afterSaleOrderVO.getmId());
        if (!AjaxResult.isSuccess(result)){
            return result;
        }
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult handleSuccessfulOrderSolution() {
        return AjaxResult.getOK();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED,rollbackFor=Exception.class)
    public AjaxResult auditSuccessfulOrderSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {
        return afterHandleTypeExecuteHelper.backToStore(afterSaleOrderVO,orders);
    }



}
