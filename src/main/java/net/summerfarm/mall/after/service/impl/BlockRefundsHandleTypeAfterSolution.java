package net.summerfarm.mall.after.service.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.enums.AfterSaleHandleType;
import net.summerfarm.enums.AfterSaleOrderStatus;
import net.summerfarm.enums.OrderTypeEnum;
import net.summerfarm.mall.after.service.AfterSaleOrderHandleTypeSolution;
import net.summerfarm.mall.common.mq.MQData;
import net.summerfarm.mall.common.mq.MType;
import net.summerfarm.mall.enums.AfterSaleDeliveryedEnum;
import net.summerfarm.mall.enums.OrderStatusEnum;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import net.summerfarm.mall.model.vo.OrderVO;
import net.summerfarm.mall.service.helper.AfterHandleTypeExecuteHelper;
import net.summerfarm.mall.wechat.templatemessage.InterceptSuccessMsg;
import net.summerfarm.mall.wechat.templatemessage.TemplateMsgSender;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 拦截退款
 * @date 2022/8/5 16:01
 * @Version 1.0
 */
@Component
@Slf4j
public class BlockRefundsHandleTypeAfterSolution implements AfterSaleOrderHandleTypeSolution {
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private AfterHandleTypeExecuteHelper afterHandleTypeExecuteHelper;
    @Resource
    private AfterSaleProofMapper afterSaleProofMapper;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private TemplateMsgSender templateMsgSender;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;
    @Resource
    private AfterSaleOrderMapper afterSaleOrderMapper;

    @Override
    public AjaxResult creatByHandleTypeSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {
        if (!orders.getType().equals(OrderTypeEnum.TIMING.getId())){
            List<AfterSaleOrderVO> afterSaleOrders = afterSaleOrderMapper.selectAfterSaleOrderByOrderNo(afterSaleOrderVO.getOrderNo());
            if (!CollectionUtils.isEmpty(afterSaleOrders)){
                List<AfterSaleOrderVO> filterOrderList = afterSaleOrders.stream()
                        .filter(e -> !(e.getDeliveryed().equals(AfterSaleDeliveryedEnum.NOT_NEED.getType()) &&
                                e.getHandleType().equals(AfterSaleHandleType.REFUND.getType()) &&
                                e.getStatus().equals(AfterSaleOrderStatus.SUCCESS.getStatus()))).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(filterOrderList)){
                    log.warn("订单拦截已有售后，拦截失败，订单号："+afterSaleOrderVO.getOrderNo());
                    return AjaxResult.getErrorWithMsg("已有售后，不可再发起");
                }
            }
        }else {
            if (null != afterSaleOrderVO.getDeliveryId()){
                AfterSaleOrder timingAfterSaleOrder = new AfterSaleOrder();
                timingAfterSaleOrder.setOrderNo(afterSaleOrderVO.getOrderNo());
                timingAfterSaleOrder.setDeliveryId(afterSaleOrderVO.getDeliveryId());
                List<AfterSaleOrderVO> timingAfterSaleOrders = afterSaleOrderMapper.selectAfterSale(timingAfterSaleOrder);
                if (!CollectionUtils.isEmpty(timingAfterSaleOrders)){
                    log.warn("省心送订单拦截已有售后，拦截失败，订单号：{},配送计划ID：{}",afterSaleOrderVO.getOrderNo(),afterSaleOrderVO.getDeliveryId());
                    return AjaxResult.getErrorWithMsg("省心送计划已有售后，不可再发起");
                }
            }
        }

        Merchant merchant = merchantMapper.selectOneByMid(orders.getmId());
        //判断订单状态是否异常
        if (orders.getStatus() != OrderStatusEnum.WAIT_DELIVERY.getId() &&
                orders.getStatus() != OrderStatusEnum.DELIVERING.getId() &&
                orders.getStatus() != OrderStatusEnum.RECEIVED.getId()) {
            return AjaxResult.getErrorWithMsg("订单状态异常");
        }
        //用户切换单店/大客户不允许对原订单发起售后
        if (!Objects.equals(orders.getmSize(), merchant.getSize())) {
            return AjaxResult.getErrorWithMsg("已不可发起售后");
        }

        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult handleByHandleTypeSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {
        log.info("BlockRefundsHandleTypeAfterSolution[]handleByHandleTypeSolution[]afterSaleOrderVO:{},orders:{}", JSON.toJSONString(afterSaleOrderVO), JSON.toJSONString(orders));
        //省心送订单退款金额必须为0
        if (afterSaleOrderVO.getDeliveryId() != null){
            if (afterSaleOrderVO.getHandleNum().compareTo(new BigDecimal(0)) != 0){
                return AjaxResult.getErrorWithMsg("省心送退款金额不为0");
            }
        }
        BigDecimal totalPrice = orders.getTotalPrice();
        BigDecimal handleNum = afterSaleOrderVO.getHandleNum();
        BigDecimal recoveryNum = afterSaleOrderVO.getRecoveryNum();
        if (handleNum == null ){
            handleNum = new BigDecimal(0);
        }
        if (recoveryNum == null ){
            recoveryNum = new BigDecimal(0);
        }
        //假如不通过 则不检验金额 （因为通过和不通过状态都是一样 所有只能根据不通过原因字段来区别是否通过）
        if (Objects.equals(afterSaleOrderVO.getHandleType(), AfterSaleHandleType.BLOCK_REFUNDS.getType())
                && StringUtils.isNotBlank(afterSaleOrderVO.getHandleRemark())) {
            //暂不处理
        } else if (handleNum.compareTo(BigDecimal.ZERO) < 0){
            return AjaxResult.getErrorWithMsg("售后金额不可小于回收费");
        }

        if (handleNum.add(recoveryNum).compareTo(totalPrice) >0 ){
            return AjaxResult.getErrorWithMsg("售后金额不可大于订单支付金额");
        }
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult auditByHandleTypeSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {
        return AjaxResult.getOK();
    }


    @Override
    public AjaxResult handleSuccessfulOrderSolution() {
        return AjaxResult.getOK();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult auditSuccessfulOrderSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {

        //修改配送计划
        if (afterSaleOrderVO.getDeliveryId() != null ){
            DeliveryPlan plan = new DeliveryPlan();
            if (orders.getType().equals(OrderTypeEnum.TIMING.getId())) {
                plan.setId(afterSaleOrderVO.getDeliveryId());
                plan.setOrderNo(plan.getOrderNo());
                plan.setUpdateTime(LocalDateTime.now());
                plan.setStatus(11);
                deliveryPlanMapper.updateById(plan);
            }
        }else {
            //退奶油卡
            AjaxResult result = afterHandleTypeExecuteHelper.backCard(orders.getOrderNo());
            if (!AjaxResult.isSuccess(result)){
                return result;
            }
            //退款
            AjaxResult refund = afterHandleTypeExecuteHelper.refund(afterSaleOrderVO);
            if (!AjaxResult.isSuccess(refund)){
                return refund;
            }
        }

        notifySuccess(afterSaleOrderVO.getOrderNo());
        return AjaxResult.getOK();
    }

    private  void notifySuccess(String orderNo){
        OrderVO orderVO = ordersMapper.selectByOrderyNo(orderNo);
        List<AfterSaleProof> afterSaleProofs = afterSaleProofMapper.selectByOrderNo(orderNo, null);
        afterSaleProofs = afterSaleProofs.stream().filter(afterSaleProof -> afterSaleProof.getHandleType().equals(AfterSaleHandleType.BLOCK_REFUNDS.getType())).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(afterSaleProofs)){
            AfterSaleProof afterSaleProof = afterSaleProofs.get(0);
            //发送微信消息
            Merchant merchant = merchantMapper.selectByPrimaryKey(orderVO.getmId());
            String msg = InterceptSuccessMsg.templateMessage(merchant.getOpenid(), orderNo, afterSaleProof.getRefundType(), LocalDateTime.now());
            log.info("订单拦截成功微信消息发送"+msg);
            templateMsgSender.sendTemplateMsg(msg);

            //发送钉钉消息
            MQData mqData = new MQData();
            mqData.setType(MType.INTERCEPT_ORDER.name());
            mqData.setData(orderNo);
            mqProducer.send(RocketMqMessageConstant.MALL_LIST,null, JSON.toJSONString(mqData));
        }

    }


}
