package net.summerfarm.mall.after.service.impl;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.mall.after.service.AfterSaleOrderHandleTypeSolution;
import net.summerfarm.mall.model.domain.Orders;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import net.summerfarm.mall.service.helper.AfterHandleTypeExecuteHelper;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

/**
 * 退款售后服务类型处理
 * <AUTHOR>
 */
@Component
public class RefundHandleTypeAfterSolution implements AfterSaleOrderHandleTypeSolution {

    @Resource
    private AfterHandleTypeExecuteHelper afterHandleTypeExecuteHelper;

    @Override
    public AjaxResult creatByHandleTypeSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {

        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult handleByHandleTypeSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult auditByHandleTypeSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult handleSuccessfulOrderSolution() {
        return null;
    }

    @Override
    public AjaxResult auditSuccessfulOrderSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {
        //退款操作
        AjaxResult refund = afterHandleTypeExecuteHelper.refund(afterSaleOrderVO);
        if (!AjaxResult.isSuccess(refund)){
            return refund;
        }
        return AjaxResult.getOK();
    }

}
