package net.summerfarm.mall.after.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.mall.model.domain.Orders;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;

/**
 * <AUTHOR>
 * @date 2022.07.01
 * 售后单 根据售后服务类型校验
 */
public interface AfterSaleOrderHandleTypeSolution {

    /**
     * 创建售后单单时校验
     * @return
     */
    AjaxResult creatByHandleTypeSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders);

    /**
     * 审核售后单时校验
     * @return
     */
    AjaxResult handleByHandleTypeSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders);

    /**
     * 审批售后单时校验
     * @return
     */
    AjaxResult auditByHandleTypeSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders);

    /**
     * 审核通过后处理
     * @return
     */
    AjaxResult handleSuccessfulOrderSolution();

    /**
     * 审批通过后处理
     * @return
     */
    AjaxResult auditSuccessfulOrderSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders);

}
