package net.summerfarm.mall.after.service.impl;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.enums.OrderTypeEnum;
import net.summerfarm.mall.after.service.AfterSaleOrderHandleTypeSolution;
import net.summerfarm.mall.enums.OrderStatusEnum;
import net.summerfarm.mall.mapper.DeliveryPlanMapper;
import net.summerfarm.mall.mapper.MerchantMapper;
import net.summerfarm.mall.model.domain.DeliveryPlan;
import net.summerfarm.mall.model.domain.Merchant;
import net.summerfarm.mall.model.domain.Orders;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import net.summerfarm.mall.service.helper.AfterHandleTypeExecuteHelper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 拦截录入账单服务类型
 * @date 2022/8/5 16:01
 * @Version 1.0
 */
@Component
public class BlockIncomingBillsHandleTypeAfterSolution implements AfterSaleOrderHandleTypeSolution {
    @Resource
    private AfterHandleTypeExecuteHelper afterHandleTypeExecuteHelper;
    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;
    @Resource
    private MerchantMapper merchantMapper;
    @Override
    public AjaxResult creatByHandleTypeSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {
        Merchant merchant = merchantMapper.selectOneByMid(orders.getmId());
        //判断订单状态是否异常
        if (orders.getStatus() != OrderStatusEnum.WAIT_DELIVERY.getId() &&
                orders.getStatus() != OrderStatusEnum.DELIVERING.getId() &&
                orders.getStatus() != OrderStatusEnum.RECEIVED.getId()) {
            return AjaxResult.getErrorWithMsg("订单状态异常");
        }
        //用户切换单店/大客户不允许对原订单发起售后
        if (!Objects.equals(orders.getmSize(), merchant.getSize())) {
            return AjaxResult.getErrorWithMsg("已不可发起售后");
        }
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult handleByHandleTypeSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {
        BigDecimal totalPrice = orders.getTotalPrice();
        BigDecimal handleNum = afterSaleOrderVO.getHandleNum();
        BigDecimal recoveryNum = afterSaleOrderVO.getRecoveryNum();
        if (handleNum == null ){
            handleNum = new BigDecimal(0);
        }
        if (recoveryNum == null ){
            recoveryNum = new BigDecimal(0);
        }
        if (recoveryNum.compareTo(handleNum) > 0){
            return AjaxResult.getErrorWithMsg("运费不可大于售后金额");
        }
        if (handleNum.add(recoveryNum).compareTo(totalPrice) >0 ){
            return AjaxResult.getErrorWithMsg("售后金额不可大于订单支付金额");
        }
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult auditByHandleTypeSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult handleSuccessfulOrderSolution() {
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult auditSuccessfulOrderSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {

        //修改配送计划
        if (afterSaleOrderVO.getDeliveryId() != null ){
            DeliveryPlan plan = new DeliveryPlan();
            if (orders.getType().equals(OrderTypeEnum.TIMING.getId())) {
                plan.setId(afterSaleOrderVO.getDeliveryId());
                plan.setOrderNo(plan.getOrderNo());
                plan.setUpdateTime(LocalDateTime.now());
                plan.setStatus(11);
                deliveryPlanMapper.updateById(plan);
            }
        }
        return AjaxResult.getOK();
    }


}
