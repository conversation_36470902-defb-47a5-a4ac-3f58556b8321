package net.summerfarm.mall.after.service.impl;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.mall.after.service.AfterSaleOrderHandleTypeSolution;
import net.summerfarm.mall.enums.OrderStatusEnum;
import net.summerfarm.mall.enums.RefundFreightEnums;
import net.summerfarm.mall.mapper.AfterSaleOrderMapper;
import net.summerfarm.mall.mapper.MerchantMapper;
import net.summerfarm.mall.mapper.OrderDeliveryRecordMapper;
import net.summerfarm.mall.model.domain.Merchant;
import net.summerfarm.mall.model.domain.Orders;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import net.summerfarm.mall.service.helper.AfterHandleTypeExecuteHelper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 退运费录入账单
 * @date 2022/9/1 11:09
 * @Version 1.0
 */
@Component
public class ReturnShippingBillHandleTypeAfterSolution implements AfterSaleOrderHandleTypeSolution {
    @Resource
    private AfterHandleTypeExecuteHelper afterHandleTypeExecuteHelper;
    @Resource
    private AfterSaleOrderMapper afterSaleOrderMapper;
    @Resource
    private OrderDeliveryRecordMapper orderDeliveryRecordMapper;
    @Resource
    private MerchantMapper merchantMapper;
    @Override
    public AjaxResult creatByHandleTypeSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {
        Merchant merchant = merchantMapper.selectOneByMid(orders.getmId());
        //判断订单状态是否异常
        if (orders.getStatus() != OrderStatusEnum.WAIT_DELIVERY.getId() &&
                orders.getStatus() != OrderStatusEnum.DELIVERING.getId() &&
                orders.getStatus() != OrderStatusEnum.RECEIVED.getId()) {
            return AjaxResult.getErrorWithMsg("订单状态异常");
        }
        //用户切换单店/大客户不允许对原订单发起售后
        if (!Objects.equals(orders.getmSize(), merchant.getSize())) {
            return AjaxResult.getErrorWithMsg("已不可发起售后");
        }
        //减去运费券的钱
        BigDecimal deliveryFee = orders.getDeliveryFee();
        BigDecimal couponDeliveryFee = orderDeliveryRecordMapper.selectCouponDeliveryFeeByOrderNo(afterSaleOrderVO.getOrderNo());
        if (couponDeliveryFee != null && couponDeliveryFee.compareTo(BigDecimal.ZERO) != 0 ){
            deliveryFee = deliveryFee.subtract(couponDeliveryFee);
        }
        if (afterSaleOrderVO.getHandleNum().compareTo(deliveryFee)>0) {
            return AjaxResult.getErrorWithMsg("退款运费金额，不可超过订单实付运费金额");
        }
        //没有运费不允许退
        if (deliveryFee.compareTo(BigDecimal.ZERO) <= 0){
            return AjaxResult.getErrorWithMsg("该订单无运费，无法发起退运费服务");
        }
        //查询是否有对应退运费的操作
        Integer count = afterSaleOrderMapper.selectByDeliveryFeeCount(orders.getOrderNo(),afterSaleOrderVO.getAfterSaleOrderNo());
        if (count > 0){
            return AjaxResult.getErrorWithMsg("已有退运费操作，不可再次发起");
        }
        if (afterSaleOrderVO.getHandleNum().compareTo(BigDecimal.ZERO)<=0){
            return AjaxResult.getErrorWithMsg("售后金额不可小于等于0");
        }
        //设置无组合包
        afterSaleOrderVO.setSuitId(0);
        //退运费字段标记为已退运费
        afterSaleOrderVO.setRefundFreight(RefundFreightEnums.RETURNED.getStatus());
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult handleByHandleTypeSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {
        
        //减去运费券的钱
        BigDecimal deliveryFee = orders.getDeliveryFee();
        BigDecimal couponDeliveryFee = orderDeliveryRecordMapper.selectCouponDeliveryFeeByOrderNo(afterSaleOrderVO.getOrderNo());
        if (couponDeliveryFee != null && couponDeliveryFee.compareTo(BigDecimal.ZERO) != 0 ){
            deliveryFee = deliveryFee.subtract(couponDeliveryFee);
        }
        if (afterSaleOrderVO.getHandleNum().compareTo(deliveryFee)>0) {
            return AjaxResult.getErrorWithMsg("退款运费金额，不可超过订单实付运费金额");
        }
        //没有运费不允许退
        if (deliveryFee.compareTo(BigDecimal.ZERO) <= 0){
            return AjaxResult.getErrorWithMsg("该订单无运费，无法发起退运费服务");
        }
        //查询是否有对应退运费的操作
        Integer count = afterSaleOrderMapper.selectByDeliveryFeeCount(orders.getOrderNo(),afterSaleOrderVO.getAfterSaleOrderNo());
        if (count > 0){
            return AjaxResult.getErrorWithMsg("已有退运费操作，不可再次发起");
        }
        if (afterSaleOrderVO.getHandleNum().compareTo(BigDecimal.ZERO)<=0){
            return AjaxResult.getErrorWithMsg("售后金额不可小于等于0");
        }
        //退运费字段标记为已退运费
        afterSaleOrderVO.setRefundFreight(RefundFreightEnums.RETURNED.getStatus());
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult auditByHandleTypeSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult handleSuccessfulOrderSolution() {
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult auditSuccessfulOrderSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {
        return AjaxResult.getOK();
    }
}
