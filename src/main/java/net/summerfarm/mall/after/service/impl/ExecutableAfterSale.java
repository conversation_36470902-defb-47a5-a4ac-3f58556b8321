package net.summerfarm.mall.after.service.impl;

import lombok.Data;
import org.springframework.stereotype.Component;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022.07.26
 */
@Data
@Component
public class ExecutableAfterSale {

    /**
     * 0:未到货售后，1:已到货售后，2:都可进行 3:都不能发起并进行弹窗
     */
    private Integer delivery;

    /**
     * 可进行的售后服务类型
     */
    private Set<Integer> executeHandleType;
}
