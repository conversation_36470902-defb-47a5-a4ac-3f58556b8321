package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.TimingRule;
import net.summerfarm.mall.model.vo.TimingRuleVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TimingRuleMapper {

    TimingRuleVO selectByPrimaryKey(Integer id);

    /**
     * 根据sku及区域查询省心送规则
     * @param areaNo areaNo
     * @param sku sku
     * @param type 省心送类型
     * @return 省心送规则
     */
    TimingRule selectTimingRuleBySku(@Param("areaNo") Integer areaNo, @Param("sku") String sku,@Param("type") Integer type);

    List<TimingRuleVO> listBySkus(@Param("areaNo") Integer areaNo, @Param("skus") List<String> skus,@Param("type") Integer type);

    /**
     * 查询省心送sku的所有后台类目
     * @param areaNo
     * @return
     */
    List<Integer> listTimingCategory(@Param("areaNo") Integer areaNo);


    /**
     * 首页省心送排序查询
     * @param areaNo
     * @param skus
     * @param type
     * @return
     */
    List<TimingRule> listBySkusPriority(@Param("areaNo") Integer areaNo, @Param("skus") List<String> skus,@Param("type") Integer type);


    /**
     * 查询所有正常省心送配置
     * @return
     */
    List<TimingRule> listAllTimingRule();

    /**
     * 批量修改省心送配送周期
     * @return
     */
    int batchUpdateByPrimaryKey(@Param("list")List<Integer> ids);

    List<TimingRuleVO> getTimingInfoByAreaNo(@Param("areaNo") Integer areaNo);
}