package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.MerchantOrderRecord;

import java.util.List;

public interface MerchantOrderRecordMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(MerchantOrderRecord record);

    int insertSelective(MerchantOrderRecord record);

    MerchantOrderRecord selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(MerchantOrderRecord record);

    int updateByPrimaryKey(MerchantOrderRecord record);

    List<String> selectByMid(Long mId);
}