package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.MerchantVisit;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface MerchantVisitMapper {

    int deleteByPrimaryKey(Long id);

    int insert(MerchantVisit record);

    int insertSelective(MerchantVisit record);

    MerchantVisit selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MerchantVisit record);

    MerchantVisit getByMidAndType(@Param("mId") Long mId, @Param("type") Integer type);

}