package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.Action;
import org.springframework.stereotype.Repository;

@Repository
public interface ActionMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(Action record);

    int insertSelective(Action record);

    Action selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(Action record);

    int updateByPrimaryKey(Action record);

    Action selectByName(String actionName);
}