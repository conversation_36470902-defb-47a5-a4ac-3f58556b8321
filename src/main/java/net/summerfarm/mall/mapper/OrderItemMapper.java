package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.OrderItem;
import net.summerfarm.mall.model.vo.LimitedSaleVO;
import net.summerfarm.mall.model.vo.OrderItemVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Repository
public interface OrderItemMapper {

    List<OrderItem> selectOrderItem(String orderNo);

    List<OrderItem> selectOrderItemUsable(String orderNo);

    List<OrderItemVO> selectOrderItemSuitId(@Param("orderNo")String orderNo, @Param("sku")String sku , @Param("suitId")Integer suitId,@Param("productType") Integer productType);

    /**
     * 批量插入
     * @param orderItems
     * @return
     */
    int insertBatch(List<OrderItem> orderItems);

    /**
     * 查询已购买的日限购商品
     * @param mId
     * @param startTime
     * @param endTime
     * @return
     */
    List<LimitedSaleVO> selectDayLimiteds(@Param("mId") Long mId, @Param("startTime") Date startTime, @Param("endTime") Date endTime,
                                          @Param("areaNo") Integer areaNo);
    /**
     * 获取省心送订单购买数量
     * @param orderNo
     * @return
     */
    Integer selectTimingOrderQuantity(String orderNo);

    /**
     * 省心送订单商品
     * @param orderNo
     * @return
     */
    List<OrderItem> selectTimingItem(String orderNo);

    OrderItem selectOrderItemId(Long rderItemId);

    /**
     * 查询订单下对应商品购买记录
     * @param orderNo
     * @param sku
     * @return
     */
    OrderItem selectOne(@Param("orderNo") String orderNo, @Param("sku") String sku,@Param("suitId") Integer suitId,@Param("areaNo") Integer areaNo);


    List<OrderItem> selectOrderItemByArea(@Param("orderNo") String orderNo, @Param("areaNo") Integer areaNo);


    void updateStatusById(@Param("id") Long id, @Param("status") Integer status);

    void updateStatusByOrderNo(@Param("orderNo") String orderNo, @Param("status") Integer status);

    void updateStatusByOrderNoDelivery(@Param("orderNo") String orderNo, @Param("status") Integer status);

    List<OrderItemVO> selectOrderItemVO(String orderNo);
    /**
    * 订单件数 不包含精准送
    */
    Integer selectOrderItemActingUsable(@Param("orderNo")String orderNo,@Param("useCoupon")Integer useCoupon);
    /**
     * 已到货售后件数 不包含精准送
     */
    Integer selectOrderItemActing(@Param("orderNo")String orderNo,@Param("useCoupon")Integer useCoupon);

    List<OrderItem> selectList(@Param("orderNo") String orderNo, @Param("sku") String sku,@Param("suitId") Integer suitId);

    List<OrderItem>  selectMaxSku(@Param("mId") Long mId , @Param("areaNo") int areaNo, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 计算客单量
     * @param storeNo
     * @param sku
     * @param startTime
     * @param endTime
     * @return
     */
    Integer selectAvgAmount(@Param("storeNo") Integer storeNo, @Param("sku") String sku, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
    * 修改单价,修改是否使用优惠券
    */
    int updateOrderItem(OrderItem orderItem);

    /**
    * 茶百道插入
    */
    int insertBatchAOL(List<OrderItem> orderItems);

    BigDecimal selectActivityGmv(@Param("mId") Long mId,@Param("startTime") LocalDateTime startTime,@Param("endTime") LocalDateTime endTime);
    int insertSingle(OrderItem orderItem);

    void updateByOrderNoSelective(OrderItem orderItem);

    List<OrderItem> selectListByOrderNos(List<String> orderNos);

    List<OrderItem> selectByOrderNos(@Param("orderNos")Collection<String> orderNos);

    /**
     * 根据条件查询订单项
     * @param orderNo
     * @param sku
     * @param suitId
     * @param productType
     * @return
     */
    OrderItem selectOrderItemByAfter(@Param("orderNo")String orderNo,@Param("sku")String sku,@Param("suitId")Integer suitId,@Param("productType") Integer productType);

    /**
     * 查询用户当日已下单sku
     * @param mId
     * @return skuList
     */
    List<String> selectSkuListToday(@Param("mId") Long mId);

    /**
     * 查询订单是否有精准送
     * @return orderNo
     */
    int selectAccurateDelivery(@Param("orderNo")String orderNo);

    /**
     * 根据配送日期和delivery_plan表的status查询
     * @param orderNoList 订单号集合
     * @return sku 和 数量
     */
    List<OrderItem> listDataByDeliveryDateAndStatusList(@Param("orderNoList")List<String> orderNoList);

    /**
     * 查询订单项信息 ,不包含精准送sku
     */
    OrderItemVO selectByOrderNoNew(@Param("orderNo") String orderNo, @Param("sku") String sku,@Param("suitId") Integer suitId,@Param("areaNo") Integer areaNo);

    /**
     * 省心送订单商品
     * @param orderNo
     * @return
     */
    OrderItem selectTiming(String orderNo);

    /**
     * 普通订单
     * @param params
     * @return OrderItemVO
     */
    List<OrderItemVO> getCommonOrderExportList(Map<String, Object> params);

    /**
     * 省心送订单
     * @param params
     * @return OrderItemVO
     */
    List<OrderItemVO> getTimingOrderExportList(Map<String, Object> params);

    /**
     * 普通订单数量
     * @param params
     * @return int
     */
    int getCommonOrderExportListCount(Map<String, Object> params);

    /**
     * 省心送订单数量
     * @param params
     * @return int
     */
    int getTimingOrderExportListCount(Map<String, Object> params);

    /**
     * 查询订单项信息 ,不包含省心送sku
     */
    List<OrderItemVO> selectByOrderNo(String orderNo);

    OrderItemVO selectByEntity(OrderItem orderItem);

    /**
     * 查询订单指定sku列表
     * @param skuList sku集合
     * @param orderNo orderNo
     * @return list
     */
    List<OrderItem> getItemListBySkus(@Param("skuList")List<String> skuList,@Param("orderNo") String orderNo);

    /**
     * 批量查询订单项信息
     * @param orderNos orderNo
     */
    List<OrderItemVO> selectOrderItemVOForList(@Param("orderNos")List<String> orderNos);

    /**
     * 查询订单项信息 ,不包含精准送sku
     */
    List<OrderItemVO> selectByOrderNoNewPro(String orderNo);

    /**
     * 批量查询订单项信息
     * @param orderNos orderNo
     */
    List<OrderItemVO> selectOrderItemList(@Param("orderNos")List<String> orderNos);

    List<OrderItemVO> selectOrderItemVOByMaster(String orderNo);
}
