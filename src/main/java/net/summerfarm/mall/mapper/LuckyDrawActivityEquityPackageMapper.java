package net.summerfarm.mall.mapper;


import net.summerfarm.mall.model.domain.LuckyDrawActivityEquityPackage;

import java.util.List;


public interface LuckyDrawActivityEquityPackageMapper {

    int deleteByPrimaryKey(Long id);

    int insert(LuckyDrawActivityEquityPackage record);

    int insertSelective(LuckyDrawActivityEquityPackage record);

    LuckyDrawActivityEquityPackage selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(LuckyDrawActivityEquityPackage record);

    int updateByPrimaryKey(LuckyDrawActivityEquityPackage record);

    List<LuckyDrawActivityEquityPackage> selectByActivityId(Long activityId);

    int updateQuantity(Long id);
}