package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.CirclePeopleRuleAdmin;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CirclePeopleRuleAdminMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(CirclePeopleRuleAdmin record);

    int insertSelective(CirclePeopleRuleAdmin record);

    CirclePeopleRuleAdmin selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(CirclePeopleRuleAdmin record);

    int updateByPrimaryKey(CirclePeopleRuleAdmin record);

    /**
     * 根据typeId以及type及商户id查询圈人关系中此商户信息是否存在
     *
     * @param typeId 营销活动id
     * @param type   营销活动类型
     * @param mId    商户id
     * @return 商户信息
     */
    List<CirclePeopleRuleAdmin> selectByCircleRelation(@Param("typeId") Integer typeId,
                                                       @Param("type") Integer type,
                                                       @Param("mId") Long mId);
}