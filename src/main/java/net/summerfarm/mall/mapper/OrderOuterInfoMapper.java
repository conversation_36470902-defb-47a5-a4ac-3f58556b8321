package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.OrderOuterInfo;
import net.summerfarm.mall.model.domain.OrderOuterItem;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OrderOuterInfoMapper {

    /**
     * 根据外部订单号、外部平台id查询外部订单信息
     * @param orderNo
     * @param outerPlatformId
     * @return
     */
    OrderOuterInfo queryOrderOuterInfo(String orderNo,Integer outerPlatformId);

    /**
     * 根据外部订单号、外部平台id更新外部订单信息
     * @param orderOuterInfo
     * @return
     */
    int updateOrderOuterInfo(OrderOuterInfo orderOuterInfo);

    /**
     * 根据鲜沐订单号查询第三方订单信息
     */
    OrderOuterInfo queryInfoByXmOrderNo(String xmOrderNo);

    /**
     * 查询订单明细
     * @param orderNo
     * @return
     */
    List<OrderOuterItem> selectOrderOuterItem(String orderNo, Integer outerPlatformId);

}
