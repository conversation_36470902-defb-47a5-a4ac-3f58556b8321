package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.MerchantFreeDayLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 免配日日志mapper
 */
@Mapper
public interface MerchantFreeDayLogMapper {
    /**
     * 根据id删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 新增用户免配日日志信息
     * @param record 用户免配日日志信息
     * @return
     */
    int insert(MerchantFreeDayLog record);
    /**
     * 新增用户免配日日志信息
     * @param record 用户免配日日志信息
     * @return
     */
    int insertSelective(MerchantFreeDayLog record);
    /**
     * 根据id查看用户免配日日志信息
     * @param id
     * @return
     */
    MerchantFreeDayLog selectByPrimaryKey(Long id);
    /**
     * 更新用户免配日日志信息
     * @param record 用户免配日日志信息
     * @return
     */
    int updateByPrimaryKeySelective(MerchantFreeDayLog record);
    /**
     * 更新用户免配日日志信息
     * @param record 用户免配日日志信息
     * @return
     */
    int updateByPrimaryKey(MerchantFreeDayLog record);

    /**
     * 根据id取免配日志信息
     * @param mId 客户id
     * @return 免配信息
     */
    MerchantFreeDayLog selectByClickMid(@Param("mId") Long mId);
}