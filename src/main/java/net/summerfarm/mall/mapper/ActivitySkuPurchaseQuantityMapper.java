package net.summerfarm.mall.mapper;


import net.summerfarm.mall.model.domain.ActivitySkuPurchaseQuantity;
import net.summerfarm.mall.model.domain.OrderItemPreferential;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ActivitySkuPurchaseQuantityMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ActivitySkuPurchaseQuantity record);

    int insertSelective(ActivitySkuPurchaseQuantity record);

    ActivitySkuPurchaseQuantity selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ActivitySkuPurchaseQuantity record);

    int updateByPrimaryKey(ActivitySkuPurchaseQuantity record);

    List<ActivitySkuPurchaseQuantity> selectPurchaseQuantity(@Param("mId") Long mId,
                                                             @Param("newActivityId") Long newActivityId,
                                                             @Param("sku") String sku);

    /**
     * 获取当天的订单数据
     * @param mId
     * @param activityId
     * @param sku
     * @return
     */
    List<ActivitySkuPurchaseQuantity> selectPurchaseQuantityToday(@Param("mId") Long mId,
            @Param("newActivityId") Long newActivityId,
            @Param("sku") String sku);

    //批量插入信息
    int insertBatch(List<ActivitySkuPurchaseQuantity> list);

    ActivitySkuPurchaseQuantity selectByOrderNo(@Param("orderNo")String orderNo,@Param("sku") String sku);

    List<ActivitySkuPurchaseQuantity> selectPurchaseQuantityByActivityId (@Param("mId") Long mId,
                                                             @Param("newActivityId") Long newActivityId);

    /**
     * 获取当天的订单数据
     * @param mId
     * @param newActivityId
     * @return
     */
    List<ActivitySkuPurchaseQuantity> selectPurchaseQuantityTodayByActivityId (@Param("mId") Long mId,
                                                                  @Param("newActivityId") Long newActivityId);

    List<ActivitySkuPurchaseQuantity> batchSelectPurchaseQuantityToday(@Param("mId") Long mId,
                                                                  @Param("activityIds") List<Long> activityIds,
                                                                  @Param("skus") List<String> skus);

    List<ActivitySkuPurchaseQuantity> batchSelectPurchaseQuantity(@Param("mId") Long mId,
                                                             @Param("activityIds") List<Long> activityIds,
                                                             @Param("skus") List<String> skus);
}