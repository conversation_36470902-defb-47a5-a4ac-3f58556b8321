package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.MerchantOuterDO;
import org.springframework.stereotype.Repository;

@Repository
public interface MerchantOuterMapper {

    /**
     * 根据门店编号、外部平台id查询门店信息
     * @param outerNo
     * @param outerPlatformId
     * @return
     */
    MerchantOuterDO queryByOuterNo(String outerNo,Integer outerPlatformId);

    /**
     * 根据鲜沐门店mId查询该门店是否需要向外部对接推送发货通知
     * @param mId
     * @return
     */
    Integer selectByMidReceiptNotice(Integer mId);
}
