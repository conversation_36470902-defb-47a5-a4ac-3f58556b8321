package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.AdCodeMsg;
import net.summerfarm.mall.model.domain.Fence;
import net.summerfarm.mall.model.vo.FenceVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository
public interface FenceMapper {

    /**
    * 插入
     * @param
     * @return
    */
    int insertFence(Fence fence);

    /**
    * 修改
     * @param fence
     * @return
    */
    int updateFence(Fence fence);

    /**
    * 作废
     * @param id 围栏id
     * @return
    */
    int deleteFence(Integer id);

    /**
    * 列表查询
    */
    List<FenceVO> selectByFence(FenceVO fence);


    /**
    * 详细信息查询
     * @param id 围栏id
     * @return
    */
    FenceVO selectFenceById(Integer id);

    /**
    * 根据高德Id获取围栏信息
    */
    Fence selectFenceByGdId(String gdId);

    /**
    * 查询信息
    */
    List<Fence> selectFence(Fence fence);

    /**
     * 详细信息查询
     * @param fenceName 围栏id
     * @return
     */
    Fence selectFenceByName(String fenceName);


    /**
    * 获取城市围栏
    */
    List<Fence> selectFenceArea(Fence fence);

    /**
    * 获取打包id
    */
    Integer selectMaxPackId();

    List<Integer> selectStoreNoByPickId(Integer pickId);

    /**
    * 查询 信息 非同一个fenceId
    */
    List<AdCodeMsg> selectByCity(AdCodeMsg adCodeMsg);

    /**
     * @param area 城市
     * @param city 区
     * @return 围栏信息
     */
    FenceVO selectFenceVoByAreaCity(@Param("area") String area, @Param("city") String city);

    Fence selectOneByAreaNo(@Param("areaNo") Integer areaNo);


    List<Fence> getBatchFencesById(@Param("fenceIds") Collection<Integer> fenceIds);
}
