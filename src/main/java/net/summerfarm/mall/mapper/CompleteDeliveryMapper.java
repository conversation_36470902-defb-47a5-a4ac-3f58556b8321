package net.summerfarm.mall.mapper;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @description
 * @date 2022/7/15 17:11
 */
@Repository
public interface CompleteDeliveryMapper {

    /** 根据城市查询配送完成时间
     *
     * @param city
     * @param area
     * @return
     */
    String selectCompleteDeliveryTime(@Param("city") String city, @Param("area") String area);
}
