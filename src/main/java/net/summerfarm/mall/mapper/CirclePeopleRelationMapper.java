package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.CirclePeopleRelation;

public interface CirclePeopleRelationMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(CirclePeopleRelation record);

    int insertSelective(CirclePeopleRelation record);

    CirclePeopleRelation selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(CirclePeopleRelation record);

    int updateByPrimaryKey(CirclePeopleRelation record);
}