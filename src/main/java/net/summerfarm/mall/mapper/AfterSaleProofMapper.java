package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.AfterSaleOrder;
import net.summerfarm.mall.model.domain.AfterSaleProof;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AfterSaleProofMapper {

    List<AfterSaleProof> select(String afterSaleOrderNo);

    /**
     * 根据修改时间降序
     * @param afterSaleOrderNo
     * @return
     */
    List<AfterSaleProof> selectDesc(String afterSaleOrderNo);

    void updateById(AfterSaleProof afterSaleProof);


    void insert(AfterSaleProof afterSaleProof);

    /**
    * 查寻用券的sku已经售后的数量
    */
    Integer querySumQuantity(@Param("orderNo") String orderNo, @Param("useCoupon") Integer useCoupon,@Param("afterSaleOrderNo")String afterSaleOrderNo);

    List<AfterSaleProof> selectByOrderNo(AfterSaleOrder afterSaleOrder);


    List<AfterSaleProof> selectByOrderNo(@Param("orderNo") String orderNo,@Param("sku") String sku);

    void updateByAfterSale(AfterSaleProof afterSaleProof);

    int saveBatchAfterSaleProof(List<AfterSaleProof> afterSaleProofs);


}