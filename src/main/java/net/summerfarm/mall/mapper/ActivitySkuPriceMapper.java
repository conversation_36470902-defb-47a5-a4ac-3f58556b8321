package net.summerfarm.mall.mapper;

import java.util.List;
import net.summerfarm.mall.model.domain.market.ActivitySkuPrice;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface ActivitySkuPriceMapper {
    
    int deleteByPrimaryKey(Long id);

    
    int insert(ActivitySkuPrice record);

    
    int insertSelective(ActivitySkuPrice record);

    
    ActivitySkuPrice selectByPrimaryKey(Long id);

    
    int updateByPrimaryKeySelective(ActivitySkuPrice record);

    
    int updateByPrimaryKey(ActivitySkuPrice record);

    int updatePrice(ActivitySkuPrice activitySkuPrice);

    int insertBatch(@Param("list") List<ActivitySkuPrice> list);

    int deleteByBasicInfoId(Long basicInfoId);

    ActivitySkuPrice selectByDetailId(@Param("skuDetailId") Long skuDetailId, @Param("sku") String sku, @Param("areaNo") Integer areaNo);

    List<ActivitySkuPrice> listByDetailIds(@Param("skuDetailIds") List<Long> skuDetailIds, @Param("skus") List<String> skus, @Param("areaNo") Integer areaNo);

}