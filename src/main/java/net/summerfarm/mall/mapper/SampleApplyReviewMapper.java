package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.SampleApplyReview;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * @Entity net.summerfarm.model.domain.SampleApplyReview
 */
@Repository
public interface SampleApplyReviewMapper {

    /**
     * 查询样品审核记录
     * @param sampleApplyId 样品id
     * @param status 审核状态
     * @return 样品审核记录
     */
    SampleApplyReview isReview(@Param("sampleApplyId") Integer sampleApplyId, @Param("status") Integer status);
}




