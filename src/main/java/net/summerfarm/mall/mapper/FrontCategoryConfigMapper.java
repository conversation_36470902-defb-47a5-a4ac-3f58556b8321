package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.FrontCategoryConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface FrontCategoryConfigMapper {
    int deleteByPrimaryKey(Long id);

    int insert(FrontCategoryConfig record);

    int insertSelective(FrontCategoryConfig record);

    FrontCategoryConfig selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(FrontCategoryConfig record);

    int updateByPrimaryKey(FrontCategoryConfig record);

    /**
     * 查询大区和类目下的置顶sku
     * @param largeAreaNo 运营大区
     * @param frontCategoryId 前台类目ID
     * @return sku数量
     */
    List<String> selectSku(@Param("largeAreaNo") Integer largeAreaNo, @Param("frontCategoryId") Integer frontCategoryId);
}