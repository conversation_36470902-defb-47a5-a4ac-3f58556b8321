package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.FrontCategoryToCategory;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface FrontCategoryToCategoryMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(FrontCategoryToCategory record);

    int insertSelective(FrontCategoryToCategory record);

    FrontCategoryToCategory selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(FrontCategoryToCategory record);

    int updateByPrimaryKey(FrontCategoryToCategory record);

    /**
     * 查询一个前台二级类目对应的后台类目
     *
     * @param frontCategoryId 前台类目id
     * @return 后台类目id
     */
    List<Integer> selectCategoryId(@Param("frontCategoryId") Integer frontCategoryId);

    List<Integer> selectCategoryIdsByFrontCategoryIds(@Param("frontCategoryIds") List<Integer> frontCategoryIds);

    List<Integer> listBySecondFrontId(@Param("frontCategoryId") Integer frontCategoryId);
}