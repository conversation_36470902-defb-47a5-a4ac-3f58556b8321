package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.AfterSaleDeliveryDetail;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AfterSaleDeliveryDetailMapper {
    /**
    * 新增数据
    */
    int insertAfterSaleDeliveryDetail(AfterSaleDeliveryDetail afterSaleDeliveryDetail);

    int insertAfterSaleDeliveryDetailList(List<AfterSaleDeliveryDetail> afterSaleDeliveryDetail);

    /**
     * 售后配送信息ID
     */
    List<AfterSaleDeliveryDetail> selectDetail(Integer asDeliveryPathId);

    /**
     * 根据ids查询细节
     *
     * @param asDeliveryPathIds 配送路径id
     * @return {@link List}<{@link AfterSaleDeliveryDetail}>
     */
    List<AfterSaleDeliveryDetail> selectDetailByIds(@Param("asDeliveryPathIds") List<Integer> asDeliveryPathIds);
}
