package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.DiscountCardAvailable;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Set;

public interface DiscountCardAvailableMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(DiscountCardAvailable record);

    int insertSelective(DiscountCardAvailable record);

    DiscountCardAvailable selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(DiscountCardAvailable record);

    int updateByPrimaryKey(DiscountCardAvailable record);

    List<DiscountCardAvailable> selectByCardId(Integer id);

    Set<String> slelectSkuByCardId(@Param("cardId") Integer cardId, @Param("skuSet") Set<String> skuSet);

    List<DiscountCardAvailable> listByCardIdsAndSkus(@Param("cardIds") List<Integer> cardIds, @Param("skus") Collection<String> skus);

    Set<String> getSkusByCardId(Integer discountCardId);

    List<DiscountCardAvailable> getAllDiscountCardAvailable();
}