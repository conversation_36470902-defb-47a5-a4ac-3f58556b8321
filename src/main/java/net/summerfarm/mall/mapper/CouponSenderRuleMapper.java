package net.summerfarm.mall.mapper;


import net.summerfarm.mall.model.domain.CouponSenderRule;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CouponSenderRuleMapper {


    List<CouponSenderRule> getListBySendIds(@Param("list") List<Integer> couponSendIds, @Param("scopeType") Integer scopeType);

    List<CouponSenderRule> getListBySendId(@Param("couponSendId") Integer couponSendId, @Param("scopeType") Integer scopeType);

    List<CouponSenderRule> getListBySendIdsAndScopeTypeAndScopeId(@Param("list") List<Integer> sendIds,
                                                                  @Param("scopeType") Integer scopeType,
                                                                  @Param("scopeId") Long scopeId);
}