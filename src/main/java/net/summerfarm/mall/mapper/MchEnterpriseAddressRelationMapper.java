package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.MchEnterpriseAddressRelation;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * 2022-4-26
 */
@Repository
public interface MchEnterpriseAddressRelationMapper {

    /**
     * 删除关联
     * @param id
     * @return
     */
    int updateByKey(Long id);


    /**
     * 查询该地址是否已经被匹配
     * @param contactId
     * @return
     */
    MchEnterpriseAddressRelation selectById(Long contactId);

    /**
     * 修改
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(MchEnterpriseAddressRelation record);
}