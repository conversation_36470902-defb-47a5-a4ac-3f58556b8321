package net.summerfarm.mall.mapper;


import net.summerfarm.mall.model.domain.StockTaskItem;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface StockTaskItemMapper {
    /**
    * 查询自提订单的销售任务
    */
    StockTaskItem queryNumberByOrderNo(@Param("sku") String sku, @Param("orderNo") String orderNo);

    /**
     * 查询sku某天的销售出库
     */
    List<StockTaskItem> queryNumberBySku(@Param("sku") String sku, @Param("date") LocalDate date);
}
