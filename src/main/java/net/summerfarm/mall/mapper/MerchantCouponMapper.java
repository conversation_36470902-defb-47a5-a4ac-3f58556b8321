package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.bo.coupon.ReceiveIdCountBO;
import net.summerfarm.mall.model.domain.Coupon;
import net.summerfarm.mall.model.domain.MerchantCoupon;
import net.summerfarm.mall.model.vo.MerchantCouponVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Repository
public interface MerchantCouponMapper {

    int insertSelective(MerchantCoupon record);

    int updateByPrimaryKeySelective(MerchantCoupon record);

    int insertList(@Param("merchantCoupons") Collection<MerchantCoupon> merchantCoupons);

    int updateOrderNo(@Param("orderNo") String orderNo, @Param("mId") Long mId);

    MerchantCouponVO selectMerchantCouponVO(Integer merchantCouponId);

    /**
     * 查询未使用
     * 根据id查找
     *
     * @param merchantCouponId
     * @return
     */
    MerchantCouponVO selectUnusedById(Integer merchantCouponId);

    void updateViewByMId(Long mId);

    List<MerchantCouponVO> select(@Param("selectKeys") Map selectKeys, @Param("flag") Boolean flag);

    /**
     * 按商户id查询优惠券
     * @param status 优惠券状态
     * @param mId 商户id
     * @param type 优惠券类型
     * @return 优惠券列表
     */
    List<MerchantCouponVO> selectByMid(@Param("status") Integer status,@Param("mId") Long mId,@Param("type") Integer type);

    /**
     * 按商户id查询优惠券
     * @param status 优惠券状态
     * @param mId 商户id
     * @param type 优惠券类型
     * @return 优惠券列表
     */
    List<MerchantCouponVO> selectTimingByMid(@Param("status") Integer status,@Param("mId") Long mId,@Param("type") Integer type);

    List<MerchantCouponVO> selectByCoupon(@Param("selectKeys") Map selectKeys, @Param("flag") Boolean flag);

    Integer countUnused(@Param("mId") Long mId, @Param("date") LocalDateTime date);

    Integer countExpire(@Param("mId") Long mId, @Param("date") LocalDateTime date, @Param("view") Integer view);

    List<Coupon> selectCoupon(@Param("mId") Long mId, @Param("couponId") Integer couponId, @Param("vaildDate") LocalDate date);

    List<MerchantCouponVO> selectListMerchantCouponVO(List<Integer> merchantCouponIds);

    /**
     * 券信息
     */
    MerchantCouponVO selectCouponVO(Integer merchantCouponId);

    void updateByOrderNo(@Param("orderNo") String orderNo, @Param("mId") Long mId, @Param("type") Integer type);

    List<MerchantCouponVO> queryMerchantCoupon(@Param("mId") Long mId, @Param("dateTime") LocalDateTime dateTime);

    List<MerchantCoupon> queryMerchantCouponMsg(@Param("mId") Long mId, @Param("couponId") Integer couponId);

    List<MerchantCouponVO> selectUsableCoupon(@Param("type") Integer type, @Param("mId") Long mId, @Param("totalPrice") BigDecimal totalPrice);

    List<MerchantCouponVO> selectUsableCouponWithoutThreshold(@Param("type") Integer type, @Param("mId") Long mId);

    int selectUnusedByReceiveId(@Param("id") Integer id, @Param("mId") Long mId);

    List<ReceiveIdCountBO> selectUnusedByReceiveIdIn(@Param("ids") Collection<Integer> ids, @Param("mId") Long mId);

    /**
     * 批量使用优惠券
     * @param orderNo 订单编号
     * @param mcIdSet 使用优惠券
     * @return 影响行数
     */
    int batchUseCoupon(@Param("orderNo") String orderNo, @Param("mcIdSet") Set<Integer> mcIdSet);

    /**
     * 退还售后券
     * @param orderNo
     * @param mId
     * @param type
     */
    void updateByOrderNoAfter(@Param("orderNo") String orderNo, @Param("mId") Long mId, @Param("type") Integer type);

    /**
     * 根据条件查询多个劵信息
     * @param params
     */
    List<MerchantCouponVO> selectByMap(Map<String, Object> params);

    /**
     * 查询订单是否使用了某种券
     * @param orderNo orderNo
     * @param type type
     */
    List<MerchantCouponVO> selectDeliveryFeeCoupon(@Param("orderNo") String orderNo,@Param("type") Integer type);

    /**
     * 查询根据给定订单是否用到了运费券
     * @param orderNoList orderNoList
     */
    Boolean selectHasDeliveryFeeCouponByOrders(@Param("orderNos") List<String> orderNoList);

    /**
     * 根据条件获取用户领取次数
     * @param
     */
    List<ReceiveIdCountBO> getUserReceiveCount(@Param("couponIds") List<Integer> couponIds, @Param("mId") Long mId, @Param("receiveType") Integer receiveType);

    /**
     * 根据用户优惠记录查询卡券信息，不考虑状态
     * @param
     */
    MerchantCouponVO selectMerchantCouponWithoutStatus(Integer merchantCouponId);

    /**
     * 根据条件获取用户领取卡券信息
     * @param
     * @param relatedIds
     */
    List<MerchantCouponVO> getMerchantCoupon(@Param("mId") Long mId, @Param("couponIds") Set<Long> couponIds,
                                             @Param("receiveType") Integer receiveType, @Param("relatedIds") Set<Integer> relatedIds);

    /**
     * 根据条件获取用户领取次数
     * @param
     */
    List<ReceiveIdCountBO> getUserReceiveCountInfo(@Param("mId")  Long mId, @Param("receiveType")  Integer receiveType);

    List<MerchantCouponVO> selectUsableCouponByMid(@Param("mId") Long mId);

    /**
     * 查询指定天数内未使用的优惠券ID(仅用作缓存更新时使用)
     * @param days 天数
     * @return 优惠券ID列表
     */
    List<Integer> selectUnusedCouponIdsWithinDays(@Param("days") int days);
}
