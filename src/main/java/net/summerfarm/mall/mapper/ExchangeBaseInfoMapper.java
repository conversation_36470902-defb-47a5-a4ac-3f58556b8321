package net.summerfarm.mall.mapper;

import java.util.List;
import net.summerfarm.mall.model.domain.ExchangeBaseInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface ExchangeBaseInfoMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ExchangeBaseInfo record);

    int insertSelective(ExchangeBaseInfo record);

    ExchangeBaseInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ExchangeBaseInfo record);

    List<ExchangeBaseInfo> listAllValid();

    ExchangeBaseInfo selectById(@Param("id") Long id);

}