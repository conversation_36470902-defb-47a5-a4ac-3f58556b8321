package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.MerchantCard;
import net.summerfarm.mall.model.vo.MerchantCardVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface MerchantCardMapper {

    int insert(MerchantCard merchantCard);

    //查询有效的优惠卡
    List<MerchantCardVO> selectVaildCard(MerchantCardVO merchantCardVO);

    int countCards(@Param("mId") Long mId, @Param("date") LocalDateTime date);
}
