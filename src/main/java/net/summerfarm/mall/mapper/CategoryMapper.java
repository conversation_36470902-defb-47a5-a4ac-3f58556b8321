package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.Category;
import net.summerfarm.mall.model.dto.CategoryDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Set;

@Repository
public interface CategoryMapper {

    Category selectByPrimaryKey(Integer cId);

    List<Category> selectTreeNodes();

    List<Integer> selectChildrenId(Integer parentId);
    Set<Integer> selectChildrenIds(@Param("ids") Set<Integer> parentIds);

    List<Integer> selectChildrenIdByList(@Param("list") List<Integer> categoryIds);

    Set<Integer> selectDairy();

    Category selectOne(Integer id);

    List<Category> selectCategorys(@Param("areaNo") Integer areaNo,
                                   @Param("adminId") Integer adminId,
                                   @Param("skuList") List<String> skuList,
                                   @Param("skuShow") Integer skuShow,
                                   @Param("direct") Integer direct,
                                   @Param("msize") String msize,
                                   @Param("unShowCate") Set<Integer> unShowCate);

    /**
     * 根据SKU查询所属类目
     *
     * @param sku
     * @return
     */
    Category queryBySku(String sku);


    List<Category> selectByIds(@Param("list") Collection<Integer> categoryIds);

    Set<Integer> selectCategoryIdByName(@Param("name") String name);

    List<Category> getAllCategory();

    List<CategoryDto> getCategoryByIds (@Param("list") List<Integer> categoryIds, @Param("areaNo") Integer areaNo);
}
