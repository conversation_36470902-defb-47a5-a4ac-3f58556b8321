package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.AttrItem;
import org.springframework.stereotype.Repository;

@Repository
public interface AttrItemMapper {
    int deleteByPrimaryKey(Integer aitId);

    int insert(AttrItem record);

    int insertSelective(AttrItem record);

    AttrItem selectByPrimaryKey(Integer aitId);

    int updateByPrimaryKeySelective(AttrItem record);

    int updateByPrimaryKey(AttrItem record);
}