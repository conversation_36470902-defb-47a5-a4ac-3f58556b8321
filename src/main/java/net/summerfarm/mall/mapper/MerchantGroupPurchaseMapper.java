package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.MerchantGroupPurchase;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> ct
 * create at:  2020/3/14  15:22
 */
@Repository
public interface MerchantGroupPurchaseMapper {

    /**
    * 新增
     * @param groupPurchase
     * @return
    */
    int insertGroupPurchase(MerchantGroupPurchase groupPurchase);

    /**
     * 查询
     * @param mId
     * @return
     */
    MerchantGroupPurchase selectGroupPurchase(Long mId);
}
