package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.OrderDeliveryRecord;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;

@Repository
public interface OrderDeliveryRecordMapper {

    int insertRecord(OrderDeliveryRecord orderDeliveryRecord);

    OrderDeliveryRecord queryOrderRecord(String orderNo);

    /**
     * 根据订单号查询运费券金额
     * @param orderNo
     * @return
     */
    BigDecimal selectCouponDeliveryFeeByOrderNo(String orderNo);

    /**
     * 根据订单号查询运费实付
     * @param orderNo
     * @return
     */
    BigDecimal selectDeliveryFeeByOrderNo(String orderNo);
}
