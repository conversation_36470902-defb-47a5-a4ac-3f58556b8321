package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.Inventory;
import net.summerfarm.mall.model.domain.MajorPrice;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by wjd on 2018/7/13.
 */
@Repository
public interface MajorPriceMapper {

     /**
      * 筛选出处于生效中的sku
      * @param adminId
      * @param areaNo
      * @param direct
      * @return
      */
     List<String> selectSKu(@Param("adminId") Integer adminId, @Param("areaNo") Integer areaNo,@Param("direct") Integer direct, Integer mallShow);


     List<Inventory> selectSKuByAdminMsg(@Param("adminId") Integer adminId, @Param("areaNo") Integer areaNo,@Param("direct") Integer direct,@Param("pdId") Long pdId, Integer mallShow);


     List<String> selectSkuNotMallShow(@Param("adminId") Integer adminId, @Param("areaNo") Integer areaNo,@Param("direct") Integer direct, Integer mallShow);


    int selectByMid(@Param("mId") Long mId,@Param("now") LocalDateTime now,@Param("sku") String sku,@Param("areaNo") Integer areaNo);

    /**
     * 查询生效的报价单
     * @param adminId 大客户adminId
     * @param direct 合作方式
     * @param areaNo 城市编号
     * @param sku sku
     * @return 报价单信息
     */
    MajorPrice selectMajorPrice(Integer adminId, Integer direct, Integer areaNo, String sku);

    /**
     * 查询生效的报价单
     * @param adminId 大客户adminId
     * @param direct 合作方式
     * @param areaNo 城市编号
     * @param skus skus
     * @return 报价单信息
     */
    List<MajorPrice> selectMajorPriceList(Integer adminId, Integer direct, Integer areaNo, List<String> skus);


    /**
     * 查询生效的报价单
     * 与上一接口功能相同。但上一接口sql与参数名称不一致。skus / skuList
     * @param adminId 大客户adminId
     * @param direct 合作方式
     * @param areaNo 城市编号
     * @param skus skus
     * @return 报价单信息
     */
    List<MajorPrice> selectSkusMajorPriceList(Integer adminId, Integer direct, Integer areaNo, List<String> skus);

}
