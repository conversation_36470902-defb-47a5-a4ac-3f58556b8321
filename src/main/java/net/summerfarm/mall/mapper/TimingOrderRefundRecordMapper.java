package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.OrderItem;
import net.summerfarm.mall.model.domain.TimingOrderRefundRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TimingOrderRefundRecordMapper {
    int deleteByPrimaryKey(Long id);

    int insert(TimingOrderRefundRecord record);

    int insertSelective(TimingOrderRefundRecord record);

    TimingOrderRefundRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TimingOrderRefundRecord record);

    int updateByPrimaryKey(TimingOrderRefundRecord record);

    List<String> selectByOrderNoList(@Param("orderNoList") List<String> orderNoList,@Param("type") Integer type);

    /**
     * 批量插入
     * @param refundRecords
     * @return
     */
    int insertBatch(List<TimingOrderRefundRecord> refundRecords);
}