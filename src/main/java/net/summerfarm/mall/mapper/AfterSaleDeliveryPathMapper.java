package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.AfterSaleDeliveryDetail;
import net.summerfarm.mall.model.domain.AfterSaleDeliveryPath;
import net.summerfarm.mall.model.domain.ExchangeGoods;
import net.summerfarm.mall.model.dto.AfterSaleDeliveryPathDTO;
import net.summerfarm.mall.model.vo.ofc.AfterSaleOrderCompareVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface AfterSaleDeliveryPathMapper {

    /**
    * 新增售后详情
    */
    int insertAfterSaleDeliveryPath(AfterSaleDeliveryPath afterSaleDeliveryPath);

    int updateAfterSaleDeliveryPath(AfterSaleDeliveryPath afterSaleDeliveryPath);

    /**
     * 根据售后单号查询
     * @param afterSaleOrderNo
     * @return
     */
    AfterSaleDeliveryPath selectByAfterSaleOrderNo(String afterSaleOrderNo);

    /**
     * 根据id查询
     * @param id
     * @return
     */
    AfterSaleDeliveryPath selectById(Integer id);

    /**
     * 根据售后单号查询所有字段
     * @param afterSaleOrderNo 售后单号
     * @return 所有字段
     */
    AfterSaleDeliveryPath findByAfterSaleOrderNo(String afterSaleOrderNo);

    /**
     * 记数 - 根据配送日期
     *
     * @param deliveryTime 配送日期
     * @return int
     */
    int countByDeliveryDate(@Param("deliveryTime") LocalDate deliveryTime);

    /**
     * 列表 - 根据配送日期
     *
     * @param deliveryTime 配送日期
     * @param pageIndex    分页指数
     * @param pageSize     分页大小
     * @return {@link List}<{@link AfterSaleOrderCompareVO}>
     */
    List<AfterSaleOrderCompareVO> listByDeliveryDate(@Param("deliveryTime") LocalDate deliveryTime,
                                                     @Param("pageIndex") Integer pageIndex,
                                                     @Param("pageSize")Integer pageSize);

    /**
     * 查询详情
     * @param afterSaleDeliveryPath 配送时间
     * @return 详情
     *
     */
    int selectAfterSaleDetail(AfterSaleDeliveryPath afterSaleDeliveryPath);

    /**
     * 更新状态，取消的售后不更新状态
     */
    int updateDeliveryPathByPath(AfterSaleDeliveryPath afterSaleDeliveryPath);

    /**
     * 更新状态通过完成排线MQ
     */
    int updateDeliveryPathStatus(@Param("deliveryTime") LocalDate deliveryTime,@Param("contactId") Integer contactId);

    AfterSaleDeliveryPath selectPathByNo(String afterSaleNo);

    AfterSaleDeliveryDetail selectDetail(Integer deliveryPathId);

    int updateDeliveryPath(@Param("afterSaleNo") String afterSaleNo, @Param("status") Integer status);

    /**
     * 根据主键更新AfterSaleDeliveryPath
     */
    int updateById(AfterSaleDeliveryPath update);

    AfterSaleDeliveryPathDTO getPathDTOByAfterSaleOrderNo(@Param("afterSaleOrderNo") String afterSaleOrderNo);

    List<ExchangeGoods> selectExchangeGoods(@Param("afterSaleNo") String afterSaleOrderNo);
}
