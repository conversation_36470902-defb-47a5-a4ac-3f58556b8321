package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.MarketRule;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MarketRuleMapper {

    List<MarketRule> select(MarketRule selectKeys);

    MarketRule selectOne(Integer id);

    MarketRule selectTiming(@Param("status") Integer status, @Param("type") Integer type, @Param("sku") String sku, @Param("areaNo") Integer areaNo);

    List<MarketRule> selectMark(@Param("sku") String sku, @Param("areaNo") Integer areaNo);

    /**
     * @param type 类型 0、满返 1、满减
     * @param areaNo 城市编号
     * @return 生效中的market rule
     */
    List<MarketRule> selectValidRule(@Param("type") Integer type, @Param("areaNo") Integer areaNo);

    List<MarketRule> getInfoByIds(@Param("marketRuleIds") List<Integer> marketRuleIds);

    List<MarketRule> selectValidRuleByAreaNo(@Param("areaNo") Integer areaNo);
}