package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.LuckyDrawPrize;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface LuckyDrawPrizeMapper {

    LuckyDrawPrize selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(LuckyDrawPrize record);

    int updateByPrimaryKey(LuckyDrawPrize record);

    List<LuckyDrawPrize> selectByLuckyDrawId(Integer luckyDrawId);
}