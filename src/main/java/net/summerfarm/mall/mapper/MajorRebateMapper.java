package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.MajorRebate;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by wjd on 2018/7/17.
 */
@Repository
public interface MajorRebateMapper {

    MajorRebate selectOne(MajorRebate majorRebate);

    List<MajorRebate> selectValidSkuList(@Param("adminId") Integer adminId, @Param("sku") String sku);
}
