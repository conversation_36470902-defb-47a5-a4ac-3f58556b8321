package net.summerfarm.mall.mapper;


import net.summerfarm.mall.model.domain.LuckyDrawPrizeRecord;

import java.util.List;


public interface LuckyDrawPrizeRecordMapper {

    int deleteByPrimaryKey(Long id);

    int insert(LuckyDrawPrizeRecord record);

    int insertSelective(LuckyDrawPrizeRecord record);

    LuckyDrawPrizeRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(LuckyDrawPrizeRecord record);

    int updateByPrimaryKey(LuckyDrawPrizeRecord record);

    List<LuckyDrawPrizeRecord> selectByEntity(LuckyDrawPrizeRecord luckyDrawPrizeRecord);

    int batchInsert(List<LuckyDrawPrizeRecord> luckyDrawPrizeRecords);

    List<LuckyDrawPrizeRecord> getListByActivityId(Long activityId);
}