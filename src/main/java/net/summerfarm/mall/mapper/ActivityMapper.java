package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.Activity;
import net.summerfarm.mall.model.domain.ActivitySku;
import net.summerfarm.mall.model.vo.ActivitySkuVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by wjd on 2017/9/25.
 */
@Repository
public interface ActivityMapper {

    List<Activity> selectOpen(@Param("areaNo") Integer areaNo, @Param("type") Integer type);

    Activity selectById(Integer activityId);

    /**
     * 判断sku是否在某个城市参加活动
     * @param areaNo
     * @param sku
     * @param now
     * @return
     */
    boolean isOnActivity(@Param("areaNo") Integer areaNo, @Param("sku") String sku, @Param("now") LocalDateTime now);

    List<Activity> selectOpenByName (@Param("areaNo") Integer areaNo, @Param("name") String name);

    ActivitySku selectActivitySkuBySkuAndAreaNo(@Param("areaNo") Integer areaNo, @Param("sku") String sku, @Param("now") LocalDateTime now);

    ActivitySkuVO selectOpenActivitySkuBySkuAndAreaNo(@Param("areaNo") Integer areaNo, @Param("sku") String sku, @Param("now") LocalDateTime now);

    ActivitySkuVO selectOpenActivitySkuBySkuAndAreaNoAndTime(@Param("areaNo") Integer areaNo, @Param("sku") String sku, @Param("starTime") LocalDateTime starTime, @Param("endTime") LocalDateTime endTime);
}
