package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.FollowUpRelation;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;

@Repository
public interface FollowUpRelationMapper {

    FollowUpRelation selectByMId(Long mId);

    /**
     * 查询商户跟进信息
     * @param selectKeys 查询条件,mid,reassign,adminName
     * @return 商户跟进信息
     */
    FollowUpRelation selectOne(FollowUpRelation selectKeys);

    int deleteByMid(Long mId);

}