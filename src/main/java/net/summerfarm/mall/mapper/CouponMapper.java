package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.Coupon;
import net.summerfarm.mall.model.vo.MerchantAddCoupon;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository
public interface CouponMapper {

    Coupon selectByPrimaryKey(Integer id);

    List<Coupon> selectCoupon(Coupon key);

    Integer insertNewCoupon(Coupon coupon);

    Coupon selectByCode(String code);

    List<MerchantAddCoupon> selectMerchantRp();

    int selectIsSendRpWx(Long mId);

    List<Coupon> selectByIdIn(@Param("idCollection")Collection<Integer> idCollection);

    List<Coupon> batchSelectByIds(@Param("list") List<Long> couponIds);

    /**
     * 修改卡劵剩余数量
     * @param amount couponId
     * @return
     */
    int updateGrantAmount(@Param("amount") int amount, @Param("id") Integer couponId);
}