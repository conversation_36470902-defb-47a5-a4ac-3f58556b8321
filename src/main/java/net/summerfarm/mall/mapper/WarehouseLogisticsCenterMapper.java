package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.WarehouseLogisticsCenter;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository(value = "warehouseLogisticsMapper")
public interface WarehouseLogisticsCenterMapper {

    /**
     * 根据城配仓号查询
     * @param list
     * @return
     */
    List<WarehouseLogisticsCenter> listByStoreNoList(@Param("list") List<Integer> list);
}