package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.OrderPreferential;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OrderPreferentialMapper {
    //批量插入优惠信息
    int insertBatch(List<OrderPreferential> list);
    //查询活动
    List<OrderPreferential> selectPreferential(String orderNo);

    /**
     * 根据订单编号和优惠类型查询优惠信息
     * @param orderNo 订单编号
     * @param type 优惠类型
     * @return 优惠信息
     */
    List<OrderPreferential> selectSelective(@Param("orderNo") String orderNo, @Param("type") Integer type);

    //查询多个订单的优惠明细
    List<OrderPreferential> selectPreferentialByOrderList(@Param("orderNos")List<String> orderNos);
}
