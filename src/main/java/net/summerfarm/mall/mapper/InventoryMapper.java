package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.Inventory;
import net.summerfarm.mall.model.dto.product.ProductInventoryInfoDTO;
import net.summerfarm.mall.model.input.TimingDeliveryInput;
import net.summerfarm.mall.model.vo.*;
import net.summerfarm.mall.service.SalePriceTransService;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Set;

@Repository
public interface InventoryMapper {
    int count(String sku);

    /**
     * 查询selectTimingSku中，未迁移至商品中心的部分
     */
    List<ProductInventoryInfoDTO> selectProductInvInfo(@Param("skus") List<String> skuList, @Param("areaNo") Integer areaNo);

    /**
     * 大客户不参与 ，虚拟库存查询逻辑为单店
     */
    @Deprecated
    List<TimingProductVO> selectTimingSku(TimingDeliveryInput timingDeliveryInput);

    /**
     * 大客户不参与 ，虚拟库存查询逻辑为单店
     */
    ProductVO selectTimingSkuOne(@Param("sku") String sku, @Param("areaNo") Integer areaNo);

    /**
     * 大客户不参与 ，虚拟库存查询逻辑为单店
     */
    TimingProductVO selectTimingSkuDetail(@Param("sku") String sku, @Param("ruleId") Integer ruleId, @Param("areaNo") Integer areaNo);

    /**
     * 此方法查询出来的salePrice值为定价 非活动价，如果要使salePrice在活动时为活动价，
     * * 请参考使用 sql
     */
    List<ProductInfoVO> selectProductInfoByPdId(@Param("pdId") int pdId, @Param("areaNo") Integer areaNo, @Param(value = "adminId") Integer adminId,
                                                @Param(value = "skuList") List<String> skuList,
                                                @Param(value = "skuShow") Integer skuShow,
                                                @Param(value = "direct") Integer direct,
                                                @Param(value = "coreCustomer") Boolean coreCustomer);

    /**
     * 此方法查询出来的salePrice值为定价 非活动价，如果要使salePrice在活动时为活动价，
     * 请参考使用 改sql
     * {@link SalePriceTransService#selectHomeProductVO(Integer, Integer, List, Integer, List, Integer, Integer, Integer, String, String, Boolean, PageVo)}
     */
    @Deprecated
    List<ProductInfoVO> selectHomeProductVO(@Param(value = "categoryIds") List<Integer> categoryIds, @Param(value = "areaNo") Integer areaNo,
                                            @Param(value = "storeNo") Integer storeNo,
                                            @Param("esVOList") List<EsProductVO> esProductVOS, @Param(value = "adminId") Integer adminId, @Param(value = "skuList") List<String> skuList,
                                            @Param(value = "skuShow") Integer skuShow, @Param(value = "show") Integer show, @Param(value = "direct") Integer direct,
                                            @Param(value = "msize") String msize, @Param("queryStr") String queryStr,
                                            @Param(value = "coreCustomer") Boolean coreCustomer, Integer type, @Param("helpOrderFlag") Integer helpOrderFlag);

    Inventory selectBySku(String sku);

    List<Inventory> listBySkus(@Param(value = "skuList") Collection<String> skuList);

    /**
     * 大客户全量展示筛选sku用queryList sql
     */
    List<ProductInfoVO> selectCouponProductVO(@Param(value = "categoryIds") List<Integer> categoryIds, @Param(value = "areaNo") Integer areaNo, @Param(value = "pdName") String pdName, @Param(value = "adminId") Integer adminId, @Param(value = "skuList") List<String> skuList, @Param(value = "skuShow") Integer skuShow, @Param(value = "show") Integer show, @Param(value = "direct") Integer direct,
                                              @Param(value = "msize") String msize, @Param("queryList") List<String> queryList,
                                              @Param(value = "coreCustomer") Boolean coreCustomer, @Param(value = "blackSkus") List<String> blackSkus);

    ProductVO queryBySku(String sku);

    List<ProductInfoVO> selectRecommendProductVO(@Param("areaNo") Integer areaNo, @Param("storeNo") Integer storeNo, @Param("skuList") List<String> skuList);

    /**
     * 查询sku信息 根据pdid 排除大客户专享 下架 商品性质 非常规
     */
    List<String> selectBySpuList(@Param("spuList") List<String> spuList, @Param("areaNo") Integer areaNo);

    /**
     * 活动 根据类目id信息查询
     */
    List<ProductInfoVO> selectHomeProductVOByCateGoryId(@Param(value = "areaNo") Integer areaNo, @Param("categoryIdList") List<Integer> categoryIdList, @Param("invId") Integer invId);


    /**
     * 活动 根据sku信息查询
     */
    List<ProductInfoVO> selectHomeProductVOBySkus(@Param(value = "areaNo") Integer areaNo, @Param("skuList") List<String> skuList);

    /**
     * 区域编号以及商品编号以及是否展示查询商品数据
     *
     * @param areaNo 区域编号
     * @return 商品列表
     */
    List<ProductInfoVO> selectGroupBuyProductVO(@Param("areaNo") Integer areaNo);

    /**
     * 根据对应品牌的sku信息
     *
     * @param brandName
     * @param areaNo
     * @return
     */
    List<ProductInfoVO> selectHomeProductVOByBrandNames(@Param("brandName") String brandName, @Param(value = "areaNo") Integer areaNo);

    /**
     * 落地页配方下sku列表，根据要求排序； 改sql
     *
     * @param merchantAreaNo
     * @param formulaItemId
     * @return
     */
    List<ProductInfoVO> selectLandPageFormulaProductVO(@Param("areaNo") Integer merchantAreaNo,
                                                       @Param("formulaItemId") Long formulaItemId,
                                                       @Param(value = "coreCustomer") Boolean coreCustomer);

    /**
     * 落地页商品类型下sku列表，根据手动配置的sku排序；
     *
     * @param merchantAreaNo
     * @param productId
     * @return
     */
    List<ProductInfoVO> selectLandPageSkuProductVO(@Param("areaNo") Integer merchantAreaNo, @Param("productId") Long productId);

    /**
     * 落地页商品类型下sku列表， 根据销量sku自动排序 改sql
     *
     * @param merchantAreaNo
     * @param productId
     * @return
     */
    List<ProductInfoVO> selectLandPageSkuAutoSortProductVO(@Param("areaNo") Integer merchantAreaNo,
                                                           @Param("productId") Long productId,
                                                           @Param(value = "coreCustomer") Boolean coreCustomer);

    Boolean checkIsFruit(String sku);

    /**
     * 落地页商品类型下临保列表， 根据销量sku自动排序
     *
     * @param merchantAreaNo 用户区域
     * @param productId      land_page_item表ID 改sql
     * @return 临保商品
     */
    List<ProductInfoVO> selectExpiredSkuAutoSortProductVO(@Param("areaNo") Integer merchantAreaNo,
                                                          @Param(value = "coreCustomer") Boolean coreCustomer);

    /**
     * 取城市sku所在的库存仓
     *
     * @param sku    sku
     * @param areaNo 城市编号
     * @return sku所在库存仓编号
     */
    Integer selectWarehouseNo(String sku, Integer areaNo);

    /**
     * 查询sku周期成本价
     *
     * @param sku         sku
     * @param warehouseNo 库存总仓
     * @return
     */
    BigDecimal selectCycleCost(@Param("sku") String sku, @Param("warehouseNo") Integer warehouseNo);

    /**
     * 批量根据sku编号查询sku信息
     *
     * @param skuList
     * @return
     */
    List<InventoryVO> selectBySkuList(@Param("skuList") Collection<String> skuList);

    /**
     * 根据主键查询
     *
     * @param id
     * @return
     */
    Inventory selectByPrimaryKey(Long id);

    /**
     * 根据主键List查询
     *
     * @param ids    id
     * @param skuIds sku id
     * @return {@link List}<{@link InventoryVO}>
     */
    List<InventoryVO> selectByPrimaryKeyList(@Param("ids") List<Long> ids,
                                             @Param("skuIds") List<String> skuIds);

    /**
     * 查询非代仓的常规商品
     *
     * @param adminId
     * @param pdId
     * @param areaNo
     * @return
     */
    List<String> selectSkus(@Param("adminId") Integer adminId, @Param("pdId") Integer pdId, @Param("areaNo") Integer areaNo);

    List<String> selectByPdId(@Param("pdId") Long pdId, @Param("areaNo") Integer areaNo);

    InventoryVO selectBySkuAndAreaNo(@Param("sku") String sku, @Param("areaNo") Integer areaNo);

    InventoryVO selectSkuType(String sku);

    InventoryVO selectInventoryVOBySku(String sku);

    /**
     * 以sku为条件去查询其他spu下面的sku列表
     * @param skus
     * @return
     */
    List<String> querySkusBySameSpuSku(@Param("skus")List<String> skus);


    List<ProductInfoVO> selectRecommendProductVOForPOP(@Param("areaNo") Integer areaNo, @Param("storeNo") Integer storeNo, @Param("skuList") List<String> skuList);

    Set<Long> selectPdIdsSetBySkus(@Param("skus")Set<String> skus);
}
