package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.DiscountCardToMerchant;
import net.summerfarm.mall.model.vo.DiscountCardToMerchantVO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

public interface DiscountCardToMerchantMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(DiscountCardToMerchant record);

    int insertSelective(DiscountCardToMerchant record);

    DiscountCardToMerchant selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(DiscountCardToMerchant record);

    int updateByPrimaryKey(DiscountCardToMerchant record);

    DiscountCardToMerchant selectUsableDiscountCard(@Param("mId") Long mId, @Param("discountCardId") Integer discountCardId, @Param("now") LocalDate now);

    List<DiscountCardToMerchantVO> selectAllDiscountCard(@Param("mId") Long mId);

    /**
     * 根据条件查询商户优惠卡
     * @param mId 商户id
     * @param status 状态 0：失效 1：有效
     * @return 商户优惠卡列表
     */
    List<DiscountCardToMerchantVO> selectDiscountCardByCondition(@Param("mId") Long mId,@Param("status") Integer status);

    int countUsable(@Param("mId") Long mId, @Param("now") LocalDate now);

    List<DiscountCardToMerchant> selectCard( @Param("mId") Long mId);

    /**
     * 使用奶油卡（增加使用次数）
     * @param id id
     * @param addUsedTimes 使用次数
     */
    int increaseUsedTimes(@Param("id") Integer id, @Param("addUsedTimes") Integer addUsedTimes);
}