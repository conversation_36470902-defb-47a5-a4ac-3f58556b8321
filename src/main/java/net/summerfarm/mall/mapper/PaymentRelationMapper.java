package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.PaymentRelation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【payment_relation(主子单支付关联关系表)】的数据库操作Mapper
* @createDate 2023-10-11 14:11:15
* @Entity net.summerfarm.mall.model.domain.PaymentRelation
*/
public interface PaymentRelationMapper {

    int deleteByPrimaryKey(Long id);

    int insert(PaymentRelation record);

    int insertSelective(PaymentRelation record);

    PaymentRelation selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PaymentRelation record);

    int updateByPrimaryKey(PaymentRelation record);

    /**
     * 根据主单id查询子单id
     * @param masterPaymentId 主单id
     * @return List<Long>
     */
    List<Long> selectByMasterPaymentId(@Param("masterPaymentId") Long masterPaymentId);
}
