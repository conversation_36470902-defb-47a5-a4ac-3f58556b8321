package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.TimingOrderRefundTime;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

public interface TimingOrderRefundTimeMapper {
    int deleteByPrimaryKey(Long id);

    int insert(TimingOrderRefundTime record);

    int insertSelective(TimingOrderRefundTime record);

    TimingOrderRefundTime selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TimingOrderRefundTime record);

    int updateByPrimaryKey(TimingOrderRefundTime record);

    int deleteByOrderNo(@Param("orderNo")String orderNo);

    List<TimingOrderRefundTime> selectByRefundDate(@Param("refundDate") LocalDate refundDate);

    List<String> selectOrderNoByRefundDate(@Param("refundDate") LocalDate refundDate);

    TimingOrderRefundTime selectByOrderNo(@Param("orderNo") String orderNo);

    List<String> selectOrderNoForView(@Param("startDate") LocalDate startDate,@Param("endDate") LocalDate endDate,@Param("mId") Long mId);

    List<TimingOrderRefundTime> selectByOrderNoList(@Param("orderNos") List<String> orderNos);

    int batchInsert(@Param("records") List<TimingOrderRefundTime> records);
}