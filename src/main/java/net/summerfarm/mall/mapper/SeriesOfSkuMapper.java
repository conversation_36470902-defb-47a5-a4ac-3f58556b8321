package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.SeriesOfSku;
import net.summerfarm.mall.model.vo.SeriesOfSkuVO;

import java.util.List;

public interface SeriesOfSkuMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(SeriesOfSku record);

    int insertSelective(SeriesOfSku record);

    SeriesOfSku selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(SeriesOfSku record);

    int updateByPrimaryKey(SeriesOfSku record);

    List<SeriesOfSkuVO> selectByModel(SeriesOfSku query);
}