package net.summerfarm.mall.mapper;


import net.summerfarm.mall.model.domain.LuckyDrawActivity;

public interface LuckyDrawActivityMapper {

    int deleteByPrimaryKey(Long id);

    int insert(LuckyDrawActivity record);

    int insertSelective(LuckyDrawActivity record);

    LuckyDrawActivity selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(LuckyDrawActivity record);

    int updateByPrimaryKey(LuckyDrawActivity record);

    LuckyDrawActivity selectByEntity(LuckyDrawActivity luckyDrawActivity);
}