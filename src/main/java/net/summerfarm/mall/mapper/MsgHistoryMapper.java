package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.MsgHistory;
import org.springframework.stereotype.Repository;

@Repository
public interface MsgHistoryMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(MsgHistory record);

    int insertSelective(MsgHistory record);

    MsgHistory selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(MsgHistory record);

    int updateByPrimaryKeyWithBLOBs(MsgHistory record);

    int updateByPrimaryKey(MsgHistory record);
}