package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.MajorCategory;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2021/1/28  15:15
 */
@Repository
public interface MajorCategoryMapper {

    /**查询 展示 未删除的 类目信息*/
    List<String> selectSpu(@Param("adminId") Integer adminId, @Param("areaNo") Integer areaNo, @Param("direct") Integer direct, @Param("showType")Integer showType, @Param("pdId")Long pdId);

    /**
     * 查询是否存在生效的类目报价单
     * @param adminId 大客户adminId
     * @param direct 合作方式
     * @param areaNo 城市编号
     * @param categoryId categoryId
     * @return 报价单信息
     */
    Boolean selectMajorCategory(@Param("adminId") Integer adminId, @Param("areaNo") Integer areaNo, @Param("direct") Integer direct, @Param("categoryId") Integer categoryId);

    /**
     * 只返回了categoryid和type
     * @param adminId
     * @param areaNo
     * @param direct
     * @param categoryIds
     * @return
     */
    List<MajorCategory> selectMajorCategoryList(@Param("adminId") Integer adminId, @Param("areaNo") Integer areaNo, @Param("direct") Integer direct, @Param("categoryIds") List<Integer> categoryIds);

    List<Integer> selectCategoryNotMallShow(Integer adminId, Integer areaNo, Integer direct, Integer type);
}
