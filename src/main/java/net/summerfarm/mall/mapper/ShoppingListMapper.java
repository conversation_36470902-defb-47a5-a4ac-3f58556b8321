package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.MarketRule;
import net.summerfarm.mall.model.domain.ShoppingList;
import net.summerfarm.mall.model.domain.Trolley;
import net.summerfarm.mall.model.input.OrderPreferentialOutput;
import net.summerfarm.mall.model.vo.OrderItemVO;
import net.summerfarm.mall.model.vo.TrolleyVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> 2020/05/11
 */
public interface ShoppingListMapper {
    int deleteByPrimaryKey(ShoppingList key);

    int insert(ShoppingList record);

    int insertSelective(ShoppingList record);

    int updateByPrimaryKeySelective(ShoppingList record);

    int merge(TrolleyVO trolley);

    int cancelCheck(@Param("mId") Long mId, @Param("accountId") Long accountId, @Param("sku") String sku, @Param("suitId") int suitId);

    int updateQuantity(@Param("mId") Long mId,@Param("accountId") Long accountId, @Param("sku") String sku, @Param("suitId") int suitId, @Param("quantity") Integer quantity, @Param("productType") Integer productType);

    List<OrderItemVO> select4cart(@Param("mId") Long mId, @Param("accountId") Long accountId, @Param("areaNo") Integer areaNo, @Param("check") Integer check, @Param("adminId") Integer adminId, @Param("direct") Integer direct);

    List<OrderItemVO> select4cartOutTrolley(@Param("mId") Long mId, @Param("accountId") Long accountId, @Param("areaNo") Integer areaNo, @Param("suitId") Integer suitId, @Param("skuArr") List<String> skuArr, @Param("adminId") Integer adminId, @Param("direct") Integer direct);

    int updateSelectiveBatch(Trolley record);

    int updateChecked2Del(@Param("mId") Long mId, @Param("accountId") Long accountId);

    List<ShoppingList> select(ShoppingList select);

    BigDecimal selectActivityAmount(@Param("mId") Long mId, @Param("areaNo") Integer areaNo);

    List<OrderPreferentialOutput> selectActivity(@Param("mId") Long mId, @Param("areaNo") Integer areaNo);

    int deleteShop();

    List<MarketRule> selectAreaNo(@Param("areaNo") Integer areaNo);

    int selectActivityPrice(@Param("type") Integer type,@Param("mId") Long mId, @Param("areaNo") Integer areaNo);

    /**
     * 清空购物车赠品信息
     * @param mId mId
     * @param accountId accountId
     */
    void clearGiftItem(@Param("mId") Long mId, @Param("accountId") Long accountId);

    /**
     * 查询sku在购物车内数量
     * @param mId
     * @param accountId
     * @param areaNo
     * @return
     */
    ShoppingList selectSkuNum(@Param("mId") Long mId, @Param("accountId") Long accountId, @Param("areaNo") Integer areaNo,@Param("sku") String sku,@Param("suitId")Integer suitId);
}
