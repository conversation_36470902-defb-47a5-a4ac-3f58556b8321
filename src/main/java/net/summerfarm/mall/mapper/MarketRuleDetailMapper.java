package net.summerfarm.mall.mapper;

import java.util.Collection;
import java.util.List;
import java.util.Set;

import net.summerfarm.mall.model.domain.MarketRuleDetail;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface MarketRuleDetailMapper {
    /**
     * @param ruleId id
     * @param skuSet 查询sku，为空查询所有
     * @return rule detail sku
     */
    Set<String> selectRuleSku(@Param("ruleId") Integer ruleId, @Param("skuSet") Set<String> skuSet);

    /**
     * @param ruleId id
     * @param sku 查询sku，为空查询所有
     * @return rule detail sku
     */
    MarketRuleDetail selectRuleDeatilSku(@Param("ruleId") Integer ruleId, @Param("sku") String sku);

    List<MarketRuleDetail> selectRuleUsableSkusByRuleIds(@Param("ruleIds")  List<Integer> ruleIds);

    List<MarketRuleDetail> selectRuleDeatilByRuleId(@Param("ruleId") Integer ruleId);

    List<MarketRuleDetail> getAllEfficientRuleDetail(@Param("areaNo") Integer areaNo);

    List<MarketRuleDetail> selectRuleDetailBySkusAndRuleIds(@Param("skuList") Collection<String> skuList, @Param("ruleIds") List<Integer> ruleIds);
}
