package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.MerchantBusinessSwitch;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface MerchantBusinessSwitchMapper {
    int deleteByPrimaryKey(Long id);

    int insert(MerchantBusinessSwitch record);

    int insertSelective(MerchantBusinessSwitch record);

    MerchantBusinessSwitch selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MerchantBusinessSwitch record);

    int updateByPrimaryKey(MerchantBusinessSwitch record);

    /**
     * 获取用户业务开关
     * @param mId 用户ID
     * @param type 业务类型
     * @return 用户业务开关
     */
    MerchantBusinessSwitch selectByBusinessType(@Param("mId")Long mId, @Param("type")Integer type);
}