package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.EnterpriseInformation;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @title: EnterpriseInformationMapper
 * @date 2021/11/1715:49
 */
@Repository
public interface EnterpriseInformationMapper {

    int deleteByPrimaryKey(Long id);

    int insert(EnterpriseInformation record);

    /**
     * 新增企业信息
     * @param record 天眼查搜索到的企业信息
     * @return
     */
    int insertSelective(EnterpriseInformation record);

    /**
     * 公司信息表查询
     *
     * @param name       工商名称
     * @param creditCode 统一社会信用代码
     * @return 公司信息
     */
    EnterpriseInformation select(@Param("name") String name, @Param("creditCode") String creditCode);

    /**
     * 根据发票抬头，搜索数据库中企业的工商信息
     *
     * @param name 发票抬头
     * @return 企业信息
     */
    List<EnterpriseInformation> selectAll(String name);

    /**
     * 更新数据库信息
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(EnterpriseInformation record);

    int updateByPrimaryKey(EnterpriseInformation record);

    /**
     * 查询用户工商信息（含法人）
     * @param name
     * @param creditCode
     * @param legalPersonName
     * @return
     */
    EnterpriseInformation selectByMerchant(@Param("name") String name, @Param("creditCode") String creditCode, @Param("legalPersonName") String legalPersonName);
}