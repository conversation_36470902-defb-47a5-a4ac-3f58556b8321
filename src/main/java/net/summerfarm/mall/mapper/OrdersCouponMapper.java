package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.OrdersCoupon;
import net.summerfarm.mall.model.vo.MerchantCouponVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Repository
public interface OrdersCouponMapper {

    int insertSelective(OrdersCoupon ordersCoupon);

    List<MerchantCouponVO> select(Map selectkeys);

    int deleteByOrderNo(String orderNo);

    /**
     * 插入优惠券使用记录
     * @param orderNo 订单编号
     * @param mcIdSet 使用的优惠券
     * @return
     */
    int insertBatch(@Param("orderNo") String orderNo, @Param("mcIdSet") Set<Integer> mcIdSet);
}