package net.summerfarm.mall.mapper;


import net.summerfarm.mall.model.domain.DeliveryPath;
import net.summerfarm.mall.model.vo.DeliveryPathVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface DeliveryPathMapper {



    DeliveryPath selectOne(@Param("storeNo") Integer storeNo, @Param("deliveryTime") LocalDate deliveryTime, @Param("contactId") Long contactId);

    /**
     * 根据配送日期和路线、城配仓、状态查询配送路线
     * @param deliveryTime
     * @param path
     * @param storeNo
     * @param pathStatus
     * @return
     */
    List<DeliveryPath> selectDeliveryPath(@Param("deliveryTime") LocalDate deliveryTime,@Param("path") String path ,
                                          @Param("storeNo") Integer storeNo,@Param("pathStatus") Integer pathStatus);


}
