package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.SkuDiscount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SkuDiscountMapper {
    List<SkuDiscount> selectBySkus(@Param("list") List<String> skuList);

    /**
     * @param sku sku
     * @return discount
     */
    SkuDiscount selectBySku(String sku);
}