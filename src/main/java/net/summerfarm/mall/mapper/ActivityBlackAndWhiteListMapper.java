package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.ActivityBlackAndWhiteList;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ActivityBlackAndWhiteListMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ActivityBlackAndWhiteList record);

    int insertSelective(ActivityBlackAndWhiteList record);

    ActivityBlackAndWhiteList selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ActivityBlackAndWhiteList record);

    int updateByPrimaryKey(ActivityBlackAndWhiteList record);

    List<ActivityBlackAndWhiteList> selectByEntity(@Param("skus") List<String> skuCollect, @Param("areaNo") Integer areaNo);
}