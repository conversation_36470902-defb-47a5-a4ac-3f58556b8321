package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.MerchantExt;
import org.apache.ibatis.annotations.Param;

public interface MerchantExtMapper {
    /**
     * 根据id删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 新增用户免配日信息
     * @param record 免配日信息
     * @return
     */
    int insert(MerchantExt record);
    /**
     * 新增用户免配日信息
     * @param record 免配日信息
     * @return
     */
    int insertSelective(MerchantExt record);
    /**
     * 根据id查看用户免配日信息
     * @param id
     * @return
     */
    MerchantExt selectByPrimaryKey(Long id);
    /**
     * 更新用户免配日信息
     * @param record 用户免配日信息
     * @return
     */
    int updateByPrimaryKeySelective(MerchantExt record);
    /**
     * 更新用户免配日信息
     * @param record 用户免配日信息
     * @return
     */
    int updateByPrimaryKey(MerchantExt record);

    /**
     * 根据MID查询用户
     * @param mId
     * @return 用户扩展信息
     */
    MerchantExt selectByMid(@Param("mId") Long mId,@Param("clickFlag") Integer clickFlag);
}