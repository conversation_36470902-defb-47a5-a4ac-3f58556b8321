package net.summerfarm.mall.mapper;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import net.summerfarm.mall.model.domain.InterestRateConfig;
import net.summerfarm.mall.model.domain.TimingOrder;
import net.summerfarm.mall.model.vo.TimingOrderVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface TimingOrderMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(TimingOrder record);

    int insertSelective(TimingOrder record);

    TimingOrder selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(TimingOrder record);

    int updateByPrimaryKey(TimingOrder record);

    TimingOrder selectByOrderNo(String orderNo);

    int updateDeliveryTimes(@Param(value = "orderNo") String orderNo, @Param(value = "quantity") Integer quantity);

    TimingOrderVO selectDetail(String orderNo);

    List<Integer> listTimingOrders();

    /**
     * 批量修改省心送订单配送时间
     * @return
     */
    int batchUpdateByPrimaryKey(@Param("list")List<Integer> ids,@Param("deliveryEndTime") LocalDate deliveryEndTime);

    /**
     * 批量查询省心送订单配置
     * @return
     */
    List<TimingOrder> selectByOrderNoList(@Param("orderNoList")List<String> orderNoList);
}