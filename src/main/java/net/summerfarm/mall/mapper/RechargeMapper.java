package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.Recharge;
import net.summerfarm.mall.model.vo.RechargeVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2019-08-29
 * @description
 */
public interface RechargeMapper {
    RechargeVO selectVO(@Param("rechargeNo") String rechargeNo);

    int insert(Recharge record);

    /**
     * 查询收款流水认领的鲜沐卡金额
     * @param id
     * @return
     */
    BigDecimal selectWater(Long id);
}
