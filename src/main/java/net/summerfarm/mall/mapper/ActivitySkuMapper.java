package net.summerfarm.mall.mapper;


import net.summerfarm.mall.model.vo.ActivitySkuInfoVO;
import net.summerfarm.mall.model.vo.ActivitySkuVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by wjd on 2017/9/25.
 */
@Repository
public interface ActivitySkuMapper {

    List<String> selectByActivityId(Integer activityId);

    /**
     * @param areaNo 城市编号
     * @param sku sku
     * @return 生效的活动
     */
    ActivitySkuVO selectSkuValidActivity(@Param("areaNo") Integer areaNo, @Param("sku") String sku);

    /**
     * @param areaNo 城市编号
     * @return 生效活动的sku
     */
    List<ActivitySkuInfoVO> discountZoneSkuList(@Param("areaNo") Integer areaNo);

    /**
     * @param activityId 活动编号
     * @return 生效的活动
     */
    ActivitySkuVO selectSkuActivity(@Param("activityId") Integer activityId,@Param("sku") String sku);

    /**
     * 更新活动库存
     * @param id id
     */
    void updateActivityStock(@Param("id") Integer id, @Param("purchaseQuantity") Integer purchaseQuantity,
                             @Param(value = "updateTag") Boolean updateTag);
}
