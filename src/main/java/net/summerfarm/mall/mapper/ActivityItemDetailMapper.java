package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.market.ActivityItemDetail;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 */
@Mapper
public interface ActivityItemDetailMapper {
    
    int deleteByPrimaryKey(Long id);

    
    int insert(ActivityItemDetail record);

    
    int insertSelective(ActivityItemDetail record);

    
    ActivityItemDetail selectByPrimaryKey(Long id);

    
    int updateByPrimaryKeySelective(ActivityItemDetail record);

    
    int updateByPrimaryKey(ActivityItemDetail record);
}