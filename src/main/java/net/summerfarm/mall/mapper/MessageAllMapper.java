package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.MessageAll;
import org.springframework.stereotype.Repository;

@Repository
public interface MessageAllMapper {
    int deleteByPrimaryKey(Long smId);

    int insert(MessageAll record);

    int insertSelective(MessageAll record);

    MessageAll selectByPrimaryKey(Long smId);

    int updateByPrimaryKeySelective(MessageAll record);

    int updateByPrimaryKeyWithBLOBs(MessageAll record);

    int updateByPrimaryKey(MessageAll record);
}