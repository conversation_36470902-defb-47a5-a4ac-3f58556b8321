package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.OrderItemPreferential;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OrderItemPreferentialMapper {
    int deleteByPrimaryKey(Long id);

    int insert(OrderItemPreferential record);

    int insertSelective(OrderItemPreferential record);

    OrderItemPreferential selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(OrderItemPreferential record);

    int updateByPrimaryKey(OrderItemPreferential record);

    //批量插入优惠信息
    int insertBatch(List<OrderItemPreferential> list);

    List<OrderItemPreferential> queryByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 查询订单项优惠信息
     * @param condition 查询条件
     * @return 优惠信息
     */
    List<OrderItemPreferential> selectSelective(OrderItemPreferential condition);

    /**
     * 查询多个订单的item优惠明细
     * @param orderNos 查询条件
     * @param type
     * @return 优惠信息
     */
    List<OrderItemPreferential> queryByOrderNos(@Param("orderNos") List<String> orderNos, @Param("type") int type);
}