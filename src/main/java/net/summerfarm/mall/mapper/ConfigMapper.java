package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.Config;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by wjd on 2018/8/7.
 */
@Repository
public interface ConfigMapper {

    List<Config> selectAll();

    Config selectByKey(String key);

    /**
     * 根据value查询KEY
     * @param coupon
     * @return
     */
    List<String> selectByValue(String coupon);


    Config selectOne(@Param("key") String key);
}
