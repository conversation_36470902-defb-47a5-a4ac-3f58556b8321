package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.AreaSkuPriceMarkupConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2025-03-26 13:59:07
 * @version 1.0
 *
 */
@Mapper
public interface AreaSkuPriceMarkupConfigMapper{

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    AreaSkuPriceMarkupConfig selectById(@Param("id") Long id);

    /**
     * @Describe: 通过指定条件查询数据列表
     * @param param
     * @return
     */
    List<AreaSkuPriceMarkupConfig> selectBySkusAndAreaNo(@Param("skus")Collection<String> skus, @Param("areaNo")Integer areaNo);



}

