package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.MsgTemplate;
import org.springframework.stereotype.Repository;

@Repository
public interface MsgTemplateMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(MsgTemplate record);

    int insertSelective(MsgTemplate record);

    MsgTemplate selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(MsgTemplate record);

    int updateByPrimaryKey(MsgTemplate record);
}