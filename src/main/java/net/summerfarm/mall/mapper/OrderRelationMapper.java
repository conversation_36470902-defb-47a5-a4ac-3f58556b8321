package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.OrderRelation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【order_relation(主子单关联关系表)】的数据库操作Mapper
* @createDate 2023-10-11 14:10:17
* @Entity net.summerfarm.mall.model.domain.OrderRelation
*/
public interface OrderRelationMapper {

    int deleteByPrimaryKey(Long id);

    int insert(OrderRelation record);

    int insertSelective(OrderRelation record);

    OrderRelation selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(OrderRelation record);

    int updateByPrimaryKey(OrderRelation record);

    /**
     * 跟进主订单编号查询子订单编号
     * @param masterOrderNo 主订单编号
     * @return 子订单编号
     */
    List<String> selectOrderNoByMasterOrderNo(@Param("masterOrderNo") String masterOrderNo);

    /**
     * 根据子订单编号查询主订单编号
     * @param orderNoList 子订单编号
     * @return 主订单编号
     */
    List<OrderRelation> selectByOrderNoBatch(@Param("orderNoList") List<String> orderNoList);
}
