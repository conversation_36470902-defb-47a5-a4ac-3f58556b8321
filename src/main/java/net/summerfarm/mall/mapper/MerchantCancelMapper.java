package net.summerfarm.mall.mapper;


import net.summerfarm.mall.model.domain.MerchantCancel;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MerchantCancelMapper {

    int deleteByPrimaryKey(Long id);

    int insert(MerchantCancel record);

    int insertSelective(MerchantCancel record);

    MerchantCancel selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MerchantCancel record);

    int updateByPrimaryKey(MerchantCancel record);

    MerchantCancel selectInfoByMid(MerchantCancel merchantCancel);
}