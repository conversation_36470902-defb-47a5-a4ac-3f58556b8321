package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.SampleSku;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * <AUTHOR>
 */
@Repository
public interface SampleSkuMapper {

    /**
     * 根据样品申请id查询
     * @param sampleId
     * @return
     */
    List<SampleSku> selectBySampleId(Integer sampleId);

    List<SampleSku> selectBySampleIds(@Param("sampleIds") List<Integer> sampleIds);

}
