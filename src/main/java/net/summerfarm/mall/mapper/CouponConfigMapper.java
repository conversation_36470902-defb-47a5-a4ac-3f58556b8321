package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.CouponConfig;
import net.summerfarm.mall.model.vo.CouponConfigVO;

import java.util.List;

public interface CouponConfigMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(CouponConfig record);

    int insertSelective(CouponConfig record);

    CouponConfig selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(CouponConfig record);

    int updateByPrimaryKey(CouponConfig record);

    /**
     * 查询生效中的
     * @return
     */
    List<CouponConfigVO> selectValid();
}