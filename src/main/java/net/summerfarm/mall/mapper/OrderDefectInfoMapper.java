package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.OrderDefectInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OrderDefectInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(OrderDefectInfo record);

    int insertSelective(OrderDefectInfo record);

    OrderDefectInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(OrderDefectInfo record);

    int updateByPrimaryKey(OrderDefectInfo record);


    /**
     * 批量插入订单缺损明细
     *
     * @param orderDefectInfos 订单缺损明细
     * @return 变更记录数
     */
    int insertBatch(@Param("orderDefectInfos")  List<OrderDefectInfo> orderDefectInfos);
}