package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.vo.CollocationListVO;
import net.summerfarm.mall.model.vo.CollocationSku;
import net.summerfarm.mall.model.vo.SkuCollectionVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface CollocationMapper {

    /**
     * 批量判断sku是否在搭配购中
     * @param areaNo areaNo
     * @param skuList skuList
     * @return t、在搭配购中 f、不再搭配购里
     */
    Set<String> skuInValidCollectionForMap(@Param("areaNo") Integer areaNo, @Param("list") List<String> skuList);

}
