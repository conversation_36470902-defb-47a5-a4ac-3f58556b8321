package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.TmsStopDelivery;
import org.apache.ibatis.annotations.Mapper;

/**
 * 停运Mapper.
 */
@Mapper
public interface TmsStopDeliveryMapper {
    /**
     * 删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入
     * @param record
     * @return
     */
    int insert(TmsStopDelivery record);

    /**
     * 插入
     * @param record
     * @return
     */
    int insertSelective(TmsStopDelivery record);

    /**
     * 查询
     * @param id
     * @return
     */
    TmsStopDelivery selectByPrimaryKey(Long id);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(TmsStopDelivery record);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKey(TmsStopDelivery record);

    /**
     * 根据城配仓查看停运时间
     * @param storeNo 城配仓
     * @return 停运时间
     */
    TmsStopDelivery selectByStoreNo(Integer storeNo);

    /**
     * 根据storeNo取消
     * @param storeNo storeNo
     */
    void updateStatus(Integer storeNo);
}