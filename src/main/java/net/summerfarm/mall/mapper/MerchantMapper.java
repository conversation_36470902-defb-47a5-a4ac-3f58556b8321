package net.summerfarm.mall.mapper;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.mall.model.domain.Merchant;
import net.summerfarm.mall.model.dto.merchant.MerchantAndAccountDTO;
import net.summerfarm.mall.model.dto.merchant.merchantQueryDTO;
import net.summerfarm.mall.model.vo.BatchUpdateDeliveryDateVo;
import net.summerfarm.mall.model.vo.MerchantVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Repository
public interface MerchantMapper {

    Long insertSelective(Merchant record);

    int updateByPrimaryKeySelective(Merchant record);

    /**
     * 更新对应openId商户的信息
     * @param merchant
     * @return
     */
    int updateByOpenId(Merchant merchant);

    /**
     * 查询记录数
     * @param selectkeys
     * @return
     */
    int count(Map selectkeys);

    /**
     * 查询单个
     * @param selectKeys
     * @return
     */
    @Deprecated
    Merchant selectOne(Map<String, Object> selectKeys);

    /**
     * 查询openId
     * @param mId
     * @return
     */
    String selectOpenId(Long mId);

    /**
     * 查询渠道码
     * @param mId
     * @return
     */
    String selectChannelCode(Long mId);

    /**
     * 添加渠道码
     * @param mId
     * @param channelCode
     * @return
     */
    int updateChannelCode(@Param("mId") Long mId, @Param("channelCode") String channelCode);

    void updateMemberintegral();

    List<Merchant> selectMerchantList(List<Long> mIds);

    /**
     * 修改用户余额
     * @param amount
     * @param mId
     * @return
     */
    int updateRechargeAmount(@Param("amount") BigDecimal amount,@Param("mId") Long mId);

    /**
     * 修改店铺店长信息
     * @param merchant 店铺
     * @return
     */
    int updateManagerInfo(Merchant merchant);

    /**
     * 删除注册信息
     * @param mId 店铺id
     * @return
     */
    int deleteByPrimaryKey(Long mId);

    /**
    * 根据mid查询用户信息
    */
    Merchant selectOneByMid(Long mId);

    /**
     * 根据用户ID查询信息
     * @param mId
     * @return
     */
    Merchant selectByMid(Long mId);

    /**
     * 根据mid查询用户商城展示信息
     * @param mId 用户id
     * @return MerchantVO
     */
    MerchantVO selectMallByMid(Long mId);


    List<Merchant> selectMerchantByArea(Integer areaNo);


    /**
     * 用户名查询
     * @param phone
     * @return
     */
    Merchant selectIsUserPhone( String phone);

    /**
     * 增加用户账户余额
     * @param mId mId
     * @param totalPrice 充值金额
     */
    int inCreaseRechargeAmount(@Param("mId") Long mId, @Param("totalPrice") BigDecimal totalPrice);

    @RequiresDataPermission(originalField = "m.area_no")
    Merchant selectByPrimaryKey(Long mId);

    /**
     * 根据mid查询m name
     * @param mId
     * @return
     */
    merchantQueryDTO selectMNameByMid(Long mId);

    BatchUpdateDeliveryDateVo getMerchantInfoByCId(@Param("contactId") String contactId);

    Merchant selectByOpenId(@Param("openId") String openId);

    MerchantAndAccountDTO selectByAccountId(@Param("accountId") Long accountId);
}