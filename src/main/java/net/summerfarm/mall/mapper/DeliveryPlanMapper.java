package net.summerfarm.mall.mapper;

import net.summerfarm.mall.client.req.DeliveryPlanQueryReq;
import net.summerfarm.mall.client.resp.RecentDeliveryPlanResp;
import net.summerfarm.mall.model.domain.DeliveryNotice;
import net.summerfarm.mall.model.domain.DeliveryPlan;
import net.summerfarm.mall.model.dto.delivery.DeliveryPlanQueryDTO;
import net.summerfarm.mall.model.vo.DeliveryPlanVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Repository
public interface DeliveryPlanMapper {

    int insert(DeliveryPlan record);

    Integer count(String orderNo);

    List<DeliveryPlan> selectWaitByContact(@Param("contactId")Long contactId);

    List<DeliveryPlanVO> selectByOrderNo(String orderNo);

    List<DeliveryPlanVO> selectByOrderNoIntercept(String orderNo);

    List<DeliveryPlanVO> selectByOrderNoNoStatus(String orderNo);

    int countByDeliveryTime(@Param("mId") Long mId, @Param("contactId") Long contactId, @Param("deliveryDate") LocalDate deliveryDate);

    int insertBatch(@Param("list") List<DeliveryPlan> deliveryPlen);


    int selectDeliveredByOrderNo(String orderNo);

    Integer selectDeliveredQuantity4Follow(String orderNo);

    int updateStatus(DeliveryPlan updateDeliveryPlan);

    int updateTimingPlan(DeliveryPlan updateDeliveryPlan);

    int deliveryedById(int id);

    void updateById(DeliveryPlan plan);

    List<DeliveryPlanVO> selectAutoConfirm(String dateTime);

    List<DeliveryPlanVO> selectMonthly(@Param("mId") Long mId, @Param("date") Date date);

    List<DeliveryPlan> selectByMasterOrderNo(String masterOrderNo);

    int countWaitPay(String orderNo);

    List<DeliveryPlan> getUnLockOrder(@Param("deliveryDate") LocalDate deliveryDate, @Param("sku") String sku, @Param("storeNo") Integer storeNo);

    DeliveryPlan queryById(Integer id);

    List<DeliveryNotice> noticeList(@Param("delivertTime") LocalDate deliveryTime);

    List<DeliveryPlanVO> timingOrderPlans(String orderNo);

    int deleteById(Integer id);

    int updateQuantity(DeliveryPlan deliveryPlan);

    int updateStatusById(DeliveryPlan deliveryPlan);

    int updateContactIdById(DeliveryPlan deliveryPlan);

    /**
    * 排除大客户代仓的城市
    */
    List<DeliveryNotice> noticeLists(@Param("delivertTime") LocalDate deliveryTime);

    int countByMasterOrderNo(String masterOrderNo);

    DeliveryPlan selectById(Integer id);

    int updateTimingPlanLock(DeliveryPlan updateDeliveryPlan);

    DeliveryPlanVO selectTimingByOrderNoAndDateAndContactId(@Param("orderNo") String orderNo, @Param("deliveryTime") LocalDate deliveryTime,@Param("contactId") Long contactId);

    int updateDeliveryDate(@Param("id") Integer id, @Param("deliveryDate") LocalDate deliveryDate);


    List<DeliveryPlanVO> selectByMasterOrderNoVO(String orderNo);

    List<DeliveryPlanVO> selectByMasterOrderNoVOByNoStatus(String orderNo);


    List<DeliveryPlanVO> selectNoStatusByOrderNo(String orderNo);

    /**
     * 统计省心送配送计划待收货的数量
     *
     * @param orderNo
     * @return
     */
    int countByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 待收货省心送订单的配送计划
     * @param orderNo
     * @returnD
     */
    List<DeliveryPlanVO> selectByDeliveryPlan(@Param("orderNo") String orderNo);

    List<DeliveryPlanVO> selectByOrderNoAndContactId(String orderNo, Long contactId);

    /**
     * 根据deliveryTime，contactId，查询不同订单同一天的配送计划
     * @param deliveryTime
     * @param contactId
     * @param id
     * @return
     */
    List<DeliveryPlanVO> selectByDeliveryTimeAndContactId(LocalDate deliveryTime, Long contactId, Integer id);

    /**
     * 只根据订单号查询DeliveryPlan
     * @param orderNo 订单号
     * @return DeliveryPlan
     */
    List<DeliveryPlanVO> selectDeliveryPlanOnlyByOrderNo(String orderNo);

    /**
     * 列表数据 - 交货日期和状态
     *
     * @param deliveryTime 配送日期
     * @param statusList   状态列表
     * @param pageIndex    分页索引
     * @param pageSize     分页大小
     * @return {@link List}<{@link DeliveryPlanVO}>
     */
    List<DeliveryPlanVO> listDataByDeliveryDateAndStatusList(@Param("deliveryTime") LocalDate deliveryTime,
                                                             @Param("statusList") List<Integer> statusList,
                                                             @Param("pageIndex") Integer pageIndex,
                                                             @Param("pageSize")Integer pageSize);

    /**
     * 根据配送日期和状态列表 计数
     *
     * @param deliveryTime 配送时间
     * @param statusList   状态列表
     * @return int
     */
    int countDataByDeliveryDateAndStatusList(@Param("deliveryTime") LocalDate deliveryTime,
                                        @Param("statusList") List<Integer> statusList);

    List<DeliveryPlan> selectByDeliverTime(@Param("orderNo") String orderNo, @Param("deliveryTime") LocalDate deliveryTime,@Param("contactId") Long contactId);

    /**
     * 批量修改配送评价状态
     * @param updateDeliveryPlans
     * @return
     */
    int updateDeliveryEvalutionStatus(@Param("deliveryEvaluationStatus")Integer deliveryEvaluationStatus,@Param("updateDeliveryPlans")List<DeliveryPlan> updateDeliveryPlans);

    int countByDeliveryStatus(@Param("contactId") Long contactId);

    List<DeliveryPlanVO> listByOrderNos(@Param("orderNos") List<String> orderNos);

    /**
     * 订单自提
     * @param deliveryDate deliveryDate
     * @param orderNo orderNo
     * @param oldDeliveryDate oldDeliveryDate
     * @param contactId contactId
     * @return
     */
    int orderSelfPickup(@Param("deliveryDate") LocalDate deliveryDate,@Param("orderNo") String orderNo,@Param("oldDeliveryDate") LocalDate oldDeliveryDate,@Param("contactId") Long contactId);

    /**
     * 查询当前订单外是否有其他配送订单,只包含普通订单
     * @param orderNo 订单号
     * @return DeliveryPlan
     */
    List<DeliveryPlan> selectDeliveryPlanBeforeOrderNo(@Param("deliveryDate") LocalDate deliveryDate,@Param("orderNo") String orderNo,@Param("contactId") Long contactId);


    /**
     * 更新配送状态
     * @param deliveryPlanList 配送计划
     * @param status 状态
     */
    void updateStatusBatch(@Param("list") List<DeliveryPlanVO> deliveryPlanList, @Param("status") Integer status);

    List<DeliveryPlanVO> getSomeTimeUnLockPlan(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate, @Param("sku") String sku, @Param("storeNo") Integer storeNo, @Param("trustStoreNo") Integer trustStoreNo, @Param("status") Integer status);

    DeliveryPlan queryDeliveryTimeByOrderNo(String orderNo);

    /**
     * 查询当前点位评价订单信息
     * @param deliveryId deliveryId
     * @param deliveryDate deliveryDate
     * @param contactId contactId
     * @return DeliveryPlan
     */
    List<DeliveryPlan> selectDeliveryEvaluation(@Param("deliveryId") Integer deliveryId, @Param("deliveryDate") LocalDate deliveryDate, @Param("contactId") Long contactId);

    /**
     * 查询用户评价列表
     * @param deliveryDate deliveryDate
     * @param contactId contactId
     * @param deliveryEvaluationStatus deliveryEvaluationStatus
     * @return DeliveryPlan
     */
    List<DeliveryPlan> selectDeliveryEvaluationList(@Param("deliveryDate") LocalDate deliveryDate,@Param("deliveryEvaluationStatus") Integer deliveryEvaluationStatus,@Param("contactId") Long contactId,@Param("mId") Long mId,@Param("status") Integer status);

    /**
     * 配送完成查询配送计划
     * @param orderNo orderNo
     * @param deliveryTime deliveryTime
     * @param contactId contactId
     * @return DeliveryPlanVO
     */
    List<DeliveryPlanVO> selectDeliveryPlanForFulfillmentFinish(@Param("orderNo") String orderNo, @Param("deliveryTime") LocalDate deliveryTime,@Param("contactId") Long contactId);

    /**
     * 查询指定日期后的配送计划
     * @param req 查询条件
     * @return 配送计划
     */
    List<DeliveryPlanQueryDTO> selectListByMinDeliveryTime(DeliveryPlanQueryReq req);

    /**
     * 根据订单号更新配送计划
     * @param orderNoList 订单号
     * @param status 状态
     */
    void updateStatusBatchByOrderNo(@Param("orderNoList") List<String> orderNoList, @Param("status") Integer status);

    /**
     * 根据订单号查询配送计划
     * @param orderNoList 订单号
     * @return 配送计划
     */
    List<DeliveryPlan> selectByOrderNoBatch(@Param("orderNoList") List<String> orderNoList);

    /**
     * 查询门店近 2 次配送计划
     *
     * @param mId       门店 Id
     * @param orderTime 订单时间
     */
    List<RecentDeliveryPlanResp> queryRecentTwoDeliveryPlan(@Param("mId") Integer mId, @Param("orderTime") LocalDateTime orderTime);

    /**
     * 根据条件查询配送计划
     * @param select 订单号
     * @return 配送计划
     */
    DeliveryPlan selectOne(DeliveryPlan select);

    int updateDeliveryTimeAndOldDate(@Param("orderNo") String orderNo, @Param("deliveryTime") LocalDate deliveryTime, @Param("contactId") Long contactId,
                                      @Param("oldDeliveryTime")LocalDate oldDeliveryTime);

    int updateDeliveryTime(@Param("orderNo") String orderNo, @Param("deliveryTime") LocalDate deliveryTime, @Param("contactId") Long contactId);

    /**
     * 查询省心送订单已经设置的数量【如果给了id则代表是要修改的相关配送计划，会用前端给的数量进行计算，不会使用数据库中存下来的，所以这里排除掉】
     * @param orderNo 订单号
     * @return 配送计划
     */
    Integer selectDeliveredQuantityWithOutUpdate(String orderNo,List<Integer> ids);

    /**
     * 查询省心送订单已经设置的配送计划【修改配送计划需要比对数量和地址】
     * @param orderNo 订单号
     * @param planIds 修改的配送计划id
     * @return 配送计划列表
     */
    List<DeliveryPlanVO> selectByDeliveryPlanIds(@Param("orderNo") String orderNo,@Param("planIds") List<Integer> planIds);

    List<DeliveryPlanVO> getDeliveryPlanByMIds(@Param("mIdList") List<Long> mIdList, @Param("deliveryTime") LocalDate deliveryTime);

    List<DeliveryPlanVO> getAllDeliveryPlanByMIdAndContactId(@Param("mId") Long mId, @Param("contactId") Long contactId);
}
