package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.ExpandActivityQuantity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ExpandActivityQuantityMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ExpandActivityQuantity record);

    int insertSelective(ExpandActivityQuantity record);

    ExpandActivityQuantity selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ExpandActivityQuantity record);

    int updateByPrimaryKey(ExpandActivityQuantity record);

    List<ExpandActivityQuantity> selectPurchaseQuantity(@Param("mId") Long mId,
                                                             @Param("expandId") Long expandId,
                                                             @Param("sku") String sku);


    //批量插入信息
    int insertBatch(List<ExpandActivityQuantity> list);
}