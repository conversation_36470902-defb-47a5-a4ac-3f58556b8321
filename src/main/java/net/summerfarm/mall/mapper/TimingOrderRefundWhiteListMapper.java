package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.TimingOrderRefundWhiteList;
import org.apache.ibatis.annotations.Param;

public interface TimingOrderRefundWhiteListMapper {
    int deleteByPrimaryKey(Long id);

    int insert(TimingOrderRefundWhiteList record);

    int insertSelective(TimingOrderRefundWhiteList record);

    TimingOrderRefundWhiteList selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TimingOrderRefundWhiteList record);

    int updateByPrimaryKey(TimingOrderRefundWhiteList record);

    Boolean selectByOrderNo(@Param("orderNo") String orderNo);
}