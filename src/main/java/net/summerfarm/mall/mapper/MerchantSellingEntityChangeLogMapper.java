package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.sellingEntity.MerchantSellingEntityChangeLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 *
 * <AUTHOR>
 * @date 2025-03-17 10:33:51
 * @version 1.0
 *
 */
@Mapper
public interface MerchantSellingEntityChangeLogMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(MerchantSellingEntityChangeLog record);

    /**
     * @Describe: 通过mId查询最新一条数据
     * @param mId
     * @return
     */
    MerchantSellingEntityChangeLog selectLatestByMId(@Param("mId") Long mId);


    int updateByPrimaryKeySelective(@Param("id") Long id, @Param("agree") Integer agree);
}

