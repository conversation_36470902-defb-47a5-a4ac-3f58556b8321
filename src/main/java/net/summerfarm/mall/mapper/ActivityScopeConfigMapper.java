package net.summerfarm.mall.mapper;

import java.util.List;
import net.summerfarm.mall.model.domain.market.ActivityBasicInfo;
import net.summerfarm.mall.model.domain.market.ActivityScopeConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface ActivityScopeConfigMapper {

    int deleteByPrimaryKey(Long id);


    int insert(ActivityScopeConfig record);


    int insertSelective(ActivityScopeConfig record);


    ActivityScopeConfig selectByPrimaryKey(Long id);


    int updateByPrimaryKeySelective(ActivityScopeConfig record);


    int updateByPrimaryKey(ActivityScopeConfig record);

    int updateDelFlag(@Param("basicInfoId") Long basicInfoId, @Param("adminId") Integer adminId);

    int insertBatch(@Param("list") List<ActivityScopeConfig> list);

    List<ActivityScopeConfig> selectByInfoId(@Param("basicInfoId") Long basicInfoId, @Param("limit") Integer limit);

    int countByBasicInfoId(@Param("basicInfoId") Long basicInfoId);

    ActivityScopeConfig selectByScopeId(@Param("basicInfoId") Long basicInfoId, @Param("scopeId") Long scopeId);

    ActivityBasicInfo selectExpiredActivity(@Param("scopeId") Long ScopeId);

    /**
     * 获取所有临保活动的城市
     * @return
     */
    List<ActivityScopeConfig> selectAllExpiredScope();

}
