package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.FinanceAccountingPeriodOrder;

public interface FinanceAccountingPeriodOrderMapper {

    int deleteByPrimaryKey(Long id);

    int insert(FinanceAccountingPeriodOrder record);

    int insertSelective(FinanceAccountingPeriodOrder record);

    FinanceAccountingPeriodOrder selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(FinanceAccountingPeriodOrder record);

    int updateByPrimaryKey(FinanceAccountingPeriodOrder record);

    Integer getUnFinishedCount(Long mId);
}