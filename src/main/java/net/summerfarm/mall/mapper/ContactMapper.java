package net.summerfarm.mall.mapper;
import java.util.Collection;

import net.summerfarm.mall.model.domain.Contact;
import net.summerfarm.mall.model.vo.ContactVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ContactMapper {
    int insertSelective(Contact record);

    Contact selectByPrimaryKey(Long contactId);

    int updateByMid(Contact contact);

    int updateByPrimaryKeySelective(Contact record);

    List<Contact> selectByMid(@Param(value = "mId") Long mId, @Param(value = "status") Integer status);

    boolean isRepeat(Contact contact);

    int deleteByMId(Long mId);

    /**
     * 检查是否是默认地址
     * @param contactId
     * @return
     */
    Contact selectIsDefault(Long contactId);


    /**
    * 获取配送地址
    */
    List<Contact> selectByMidOrderByDefault(@Param(value = "mId") Long mId, @Param(value = "status") Integer status);

    /**
     * 取默认地址
     * @param mId 用户ID
     * @return 默认地址
     */
    Contact selectIsDefaultByMid(@Param("mId") Long mId);

    List<Contact> selectByContactIdIn(@Param("contactIds")Collection<Long> contactIds);

    /**
     * 查询用户地址信息，以及关联情况
     * @param mId
     * @return
     */
    List<ContactVO> selectByEnterpriseInformation(@Param("mId") Long mId);


}
