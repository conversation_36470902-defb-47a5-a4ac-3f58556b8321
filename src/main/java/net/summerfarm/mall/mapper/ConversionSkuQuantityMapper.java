package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.ConversionSkuQuantity;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2021/10/22  14:47
 */
@Repository
public interface ConversionSkuQuantityMapper {

    /**
     *  查询信息
     * @Author: ct
     * @param skuQuantity 信息
     * @return
     **/
    ConversionSkuQuantity selectDetail(ConversionSkuQuantity skuQuantity);
}
