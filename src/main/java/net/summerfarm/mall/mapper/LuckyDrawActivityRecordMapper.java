package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.LuckyDrawActivityRecord;
import net.summerfarm.mall.model.domain.LuckyDrawPrizeRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface LuckyDrawActivityRecordMapper {

    int deleteByPrimaryKey(Long id);

    int insert(LuckyDrawActivityRecord record);

    int insertSelective(LuckyDrawActivityRecord record);


    LuckyDrawActivityRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(LuckyDrawActivityRecord record);

    int updateByPrimaryKey(LuckyDrawActivityRecord record);

    LuckyDrawActivityRecord selectByEntity(LuckyDrawActivityRecord luckyDrawActivityRecord);
}