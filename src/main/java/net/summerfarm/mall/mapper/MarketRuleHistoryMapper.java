package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.MarketRuleHistory;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by wjd on 2018/9/19.
 */

@Repository
public interface MarketRuleHistoryMapper {

    void insert(MarketRuleHistory marketRuleHistory);


    List<MarketRuleHistory> select(@Param("orderNo") String orderNo, @Param("type") Integer type);

    int updateSendStatus(@Param("id") Integer id, @Param("sendStatus") Integer sendStatus);
}
