package net.summerfarm.mall.mapper;


import net.summerfarm.mall.model.domain.CoreProductBasePrice;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * 底价控制mapper
 *
 * <AUTHOR>
 * @Date 2023/3/2 17:36
 */
@Repository
public interface CoreProductBasePriceMapper {


    List<CoreProductBasePrice> listBasePriceByLargeAreaNo(@Param("largeAreaNo") Integer largeAreaNo);

    List<CoreProductBasePrice> listBasePriceBySkus(@Param("skus") Collection<String> skus, @Param("largeAreaNo") Integer largeAreaNo);
}
