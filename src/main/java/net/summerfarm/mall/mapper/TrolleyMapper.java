package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.MarketRule;
import net.summerfarm.mall.model.domain.Trolley;
import net.summerfarm.mall.model.input.OrderPreferentialOutput;
import net.summerfarm.mall.model.vo.OrderItemVO;
import net.summerfarm.mall.model.vo.TrolleyVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

@Repository
public interface TrolleyMapper {
    int deleteByPrimaryKey(Trolley key);

    int insert(Trolley record);

    int insertSelective(Trolley record);

    int updateByPrimaryKeySelective(Trolley record);

    int updateToMain(Trolley record);

    int merge(TrolleyVO trolley);

    int cancelCheck(@Param("mId") Long mId, @Param("accountId") Long accountId, @Param("sku") String sku, @Param("suitId") int suitId);

    /**
     * 取消商品的勾选
     * @param mId
     * @param accountId
     * @param sku
     * @param parentSku
     * @return
     */
    int cancelChecked(@Param("mId") Long mId, @Param("accountId") Long accountId, @Param("sku") String sku, @Param("parentSku") String parentSku);

    /**
     * 取消主商品下的所有副商品的勾选
     * @param mId
     * @param accountId
     * @param parentSku
     * @return
     */
    int cancelCheckedSub(@Param("mId") Long mId, @Param("accountId") Long accountId,@Param("parentSku") String parentSku);


    int updateQuantity(@Param("mId") Long mId,@Param("accountId") Long accountId, @Param("sku") String sku, @Param("suitId") int suitId, @Param("quantity") Integer quantity, @Param("productType") Integer productType);

    int updateQuantityByParentSku(@Param("mId") Long mId,@Param("accountId") Long accountId, @Param("sku") String sku, @Param("parentSku") String parentSku, @Param("quantity") Integer quantity, @Param("productType") Integer productType);

    /**
     * 查询客户购物车中所有sku
     *
     * @param mId
     * @param accountId
     * @param check
     * @param adminId
     * @param direct
     * @return
     */
    List<Trolley> selectTrolleyAllSku(@Param("mId") Long mId, @Param("accountId") Long accountId,@Param("check") Integer check);

    /**
     * 获取购物车所有商品不包含搭配购,换购
     * @param mId
     * @param accountId
     * @return
     */
    List<Trolley> selectTrolleyAllSkuNoParent(@Param("mId") Long mId, @Param("accountId") Long accountId);

    List<OrderItemVO> select4cartOutTrolley(@Param("mId") Long mId, @Param("accountId") Long accountId, @Param("areaNo") Integer areaNo, @Param("suitId") Integer suitId, @Param("skuArr") List<String> skuArr, @Param("adminId") Integer adminId, @Param("direct") Integer direct);

    int updateSelectiveBatch(Trolley record);

    int updateChecked2Del(@Param("mId") Long mId, @Param("accountId") Long accountId);

    List<Trolley> select(Trolley select);

    List<Trolley> selectRepeatSku(Trolley select);

    @Deprecated
    BigDecimal selectActivityAmount(@Param("mId") Long mId, @Param("accountId") Long accountId ,@Param("areaNo") Integer areaNo);

    @Deprecated
    List<OrderPreferentialOutput> selectActivity(@Param("mId") Long mId,@Param("accountId") Long accountId, @Param("areaNo") Integer areaNo);

    List<OrderPreferentialOutput> selectActivityOrderNow(@Param("areaNo") Integer areaNo, @Param("sku") String sku);

    /**
     * 清空购物车赠品信息
     * @param mId mId
     * @param accountId accountId
     */
    void clearGiftItem(@Param("mId") Long mId, @Param("accountId") Long accountId);

    List<MarketRule> selectAreaNo(@Param("areaNo") Integer areaNo);

    int selectActivityPrice(@Param("type") Integer type,@Param("mId") Long mId, @Param("areaNo") Integer areaNo);

    /**
     * 查询sku在购物车内数量
     * @param mId
     * @param accountId
     * @param areaNo
     * @return
     */
    Trolley selectSkuNum(@Param("mId") Long mId, @Param("accountId") Long accountId, @Param("areaNo") Integer areaNo,@Param("sku") String sku,@Param("parentSku") String parentSku,@Param("suitId")Integer suitId);

    List<OrderItemVO> selectChildCollocationItem(@Param("mId") Long mId, @Param("accountId") Long accountId, @Param("sku") String sku, @Param("areaNo") Integer areaNo);

    /**
     * 查询用户购物车选中数据
     * @param mId mId
     * @param accountId accountId
     * @return 购物车数据
     */
    List<Trolley> selectCheckList(@Param("mId") Long mId, @Param("accountId") Long accountId);

    /**
     * 取消子商品和主商品的绑定
     *
     * @param mId
     * @param accountId
     * @param sku
     * @param parentSku
     * @return
     */
    int cancelCollocationChildSkuBindParentSku(@Param("mId") Long mId, @Param("accountId") Long accountId, @Param("sku") String sku,@Param("parentSku") String parentSku,@Param("productType")Integer productType);

    /**
     * 清理购物车中无效商品
     *
     * @param select
     * @return
     */
    int clearFailureSkuByTrolley(Trolley select);

    int clearSku(Trolley trolley);

    List<String> listAllSku(@Param("mId") Long mId, @Param("accountId") Long accountId);
}