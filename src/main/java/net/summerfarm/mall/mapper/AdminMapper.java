package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.bo.admin.AdminBriefInfoBO;
import net.summerfarm.mall.model.domain.Admin;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AdminMapper {

    Admin selectByPrimaryKey(Integer adminId);

    /**
     * 查询所有BD
     * @param kaAdminId 大客户id
     * @return
     */
    List<Admin> selectBdListByKaAdminId(Integer kaAdminId);

    /**
     * 根据Id查询信息
     * @param adminId
     * @return
     */
    Admin selectAdminInfoByadminId(Integer adminId);

    /**
     * 根据adminId查询品牌名称和是否独立拣货字段
     * @param adminId adminId
     * @return  品牌名称
     */
    AdminBriefInfoBO selectAdminBasicInfoByAdminId(Integer adminId);
    AdminBriefInfoBO selectAdminBasicInfoByMId(@Param("mId") Long mId);

    /**
     * mId查询基本信息
     * @param mId mId
     * @return  Admin
     */
    Admin selectAdminDetails(Long mId);

    /**
     * 通过id列表查询admin realname
     *
     * @param ids id
     * @return {@link List}<{@link Admin}>
     */
    List<Admin> listAdminByIds(@Param("ids") List<Integer> ids);


}