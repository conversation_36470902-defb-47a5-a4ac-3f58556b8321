package net.summerfarm.mall.mapper;

import net.summerfarm.warehouse.model.domain.WarehouseInventoryMapping;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository(value = "warehouseInventoryMapper")
public interface WarehouseInventoryMapper {
    /**
     * 列表通过商店号和sku
     *
     * @param storeNo 城配仓号
     * @param skuList sku列表
     * @return {@link List}<{@link WarehouseInventoryMapping}>
     */
    List<WarehouseInventoryMapping> listByStoreNoAndSku(@Param("storeNo") Integer storeNo,
                                                        @Param("skuList") List<String> skuList);
}
