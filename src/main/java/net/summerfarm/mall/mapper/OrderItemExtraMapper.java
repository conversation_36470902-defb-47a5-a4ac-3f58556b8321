package net.summerfarm.mall.mapper;


import net.summerfarm.mall.model.domain.OrderItemExtra;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-06-27 14:30:21
 * @version 1.0
 *
 */
@Mapper
public interface OrderItemExtraMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(OrderItemExtra record);

    int insertBatch(@Param("list") List<OrderItemExtra> list);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(OrderItemExtra record);

    /**
     * @Describe: 通过主键删除
     * @param
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    OrderItemExtra selectById(@Param("id") Long id);

    /**
     * 根据订单明细id查询订单明细扩展信息
     * @param orderItemIds
     * @return
     */
    List<OrderItemExtra> selectByOrderItemIds(@Param("orderItemIds") List<Long> orderItemIds);

    /**
     * 根据orderItemId更新
     * @param orderItemExtra
     * @return
     */
    int updateSelectiveByOrderItemId(OrderItemExtra orderItemExtra);
}

