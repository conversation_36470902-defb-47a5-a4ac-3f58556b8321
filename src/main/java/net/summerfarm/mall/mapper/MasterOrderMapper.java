package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.MasterOrder;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【master_order(主订单表)】的数据库操作Mapper
* @createDate 2023-10-11 14:03:10
* @Entity net.summerfarm.mall.model.MasterOrder
*/
public interface MasterOrderMapper {

    int deleteByPrimaryKey(Long id);

    int insert(MasterOrder record);

    int insertSelective(MasterOrder record);

    MasterOrder selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MasterOrder record);

    int updateByPrimaryKey(MasterOrder record);

    /**
     * 根据主订单编号查询主订单
     * @param masterOrderNo 主订单编号
     * @return 主订单
     */
    MasterOrder selectByMasterOrderNo(@Param("masterOrderNo") String masterOrderNo);

    /**
     * 根据主订单号更新主订单
     * @param masterOrderUpdate 主订单
     */
    void updateByMasterOrderSelective(MasterOrder masterOrderUpdate);

    /**
     * 更新主订单状态
     * @param masterOrderNo 主订单编号
     * @param oldStatus 旧状态
     * @param newStatus 新状态
     * @return 更新结果
     */
    int updateMasterOrderStatus(@Param("masterOrderNo") String masterOrderNo, @Param("oldStatus") Integer oldStatus, @Param("newStatus") Integer newStatus);
}
