package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.CouponSenderRelation;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface CouponSenderRelationMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(CouponSenderRelation record);

    int insertSelective(CouponSenderRelation record);

    CouponSenderRelation selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(CouponSenderRelation record);

    int updateByPrimaryKey(CouponSenderRelation record);

    /**
     * 根据卡券发放设置id查询对应的卡券id数据
     *
     * @param couponSenderId 卡券发放设置id
     * @return 卡券id数据
     */
    List<CouponSenderRelation> selectByCouponSenderId(@Param("couponSenderId") Integer couponSenderId);

    /**
     * 根据卡券发放设置id查询对应的卡券id数据
     *
     * @param couponSenderIds 卡券发放设置id数据集合
     * @return 卡券id数据
     */
    List<CouponSenderRelation> selectByCouponSenderIdIn(@Param("couponSenderIds") Collection<Integer> couponSenderIds);
}