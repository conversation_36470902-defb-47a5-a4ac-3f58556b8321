package net.summerfarm.mall.mapper;

import java.util.Date;
import net.summerfarm.mall.model.domain.Area;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AreaMapper {

    Area selectByPrimaryKey(Integer id);

    Area selectByAreaNo(Integer areaNo);

    Area selectByMId(Long mId);

    List<Area> selectSecond();

    List<Area> select();

    /**
     * 根据城配仓获取加单城市
     * @param storeNo
     * @return
     */
    List<Area> selectSupportAddOrder(Integer storeNo);

    List<Area> listByAreaNoList(@Param("areaNoList") List<Integer> areaNoList);

    List<Area> getListByStatus(Integer status);

    List<Area> selectByLargeAreaNos(@Param("largeIds") List<Long> largeIds);

    List<Area> getUpdatedAreaList(@Param("updatedInPassedTime") Date updatedInPassedTime);
}