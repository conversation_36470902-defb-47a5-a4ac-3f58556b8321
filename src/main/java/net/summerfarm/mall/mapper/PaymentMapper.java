package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.Payment;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Repository
public interface PaymentMapper {
    int deleteByOrderNo(String orderNo);

    int insertSelective(Payment record);

    Payment selectOne(Payment selectKeys);

    /**
     * 根据支付单号查询支付单
     * @param paymentNo
     * @return
     */
    Payment selectByNo(String paymentNo);

    /**
     * 查询预售尾款订单的情况
     * @param orderNo
     * @return
     */
    List<Payment> selectByAdvanceSuccess(@Param("orderNo") String orderNo);

    int updateByPrimaryKeySelective(Payment record);

    int updateByPrimaryKey(Payment record);

    Map<String,Object> selectRefund(String orderno);

    int countSuccessByOrderNo(String orderno);

    int updateStatusById(@Param("id") Long paymentId, @Param("status") int status);

    int countSuccess(@Param("payType") String payType, @Param("startDate") LocalDate now, @Param("bocPayType") String bocPayType);

    void updateByOrderNo(Payment payment);

    /**
     * 支付单Id查询支付单
     * @param paymentIdList 支付id
     * @return List<Payment>
     */
    List<Payment> selectByPaymentIdList(@Param("paymentIdList") List<Long> paymentIdList);

    /**
     * 根据订单号查询支付时间
     * @param orderNo
     * @return
     */
    LocalDateTime selectPayTimeByOrderNo(@Param("orderNo")String orderNo);
}