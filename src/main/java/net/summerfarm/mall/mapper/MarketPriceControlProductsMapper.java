package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.MarketPriceControlProducts;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface MarketPriceControlProductsMapper {
    int deleteByPrimaryKey(Long id);

    int insert(MarketPriceControlProducts record);

    int insertSelective(MarketPriceControlProducts record);

    /**
     * 根据pdId查询信息
     * @param pdId
     * @return
     */
    MarketPriceControlProducts selectByPdId(Long pdId);

    /**
     * 查询所有控价品pdId
     * @return
     */
    List<MarketPriceControlProducts> selectAllControlProducts();

    int updateByPrimaryKeySelective(MarketPriceControlProducts record);

    int updateByPrimaryKey(MarketPriceControlProducts record);

    /**
     * 根据ID查询需要隐藏实付价的控价品
     * @return
     */
    List<MarketPriceControlProducts> selectBySkuIds(@Param("skus") List<String> skus, @Param("priceHide") Integer priceHide);

    /**
     * 根据sku查询信息
     * @param sku
     * @return
     */
    MarketPriceControlProducts selectBySku(String sku);
}