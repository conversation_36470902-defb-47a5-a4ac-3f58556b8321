package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.StoreRecord;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface storeRecordMallMapper {

    /**
     * 查询仓库剩余批次信息
     * @param storeNo 仓库编号
     * @param sku sku
     * @return
     */
    List<StoreRecord> selectQuantity(@Param("storeNo") Integer storeNo, @Param("sku") String sku);

    List<StoreRecord> selectQuantityBySku(String sku);

    StoreRecord selectQualityDate(@Param("sku") String sku, @Param("warehouseNo") Integer warehouseNo);
}