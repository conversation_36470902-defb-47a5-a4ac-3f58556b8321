package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.SkuPriceFluctuation;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * @date 2022.04.22
 * <AUTHOR>
 * @description  关注的商品价格变动记录。
 */

@Repository
public interface SkuPriceFluctuationMapper {

    /**
     * 批量插入
     * @param skuPriceFluctuations
     * @return
     */
    int insertBatch(List<SkuPriceFluctuation> skuPriceFluctuations);

    /**
     * 根据sku+areaNo+time 查询
     * @param sku
     * @param addTime
     * @param areaNo
     * @return
     */
    List<SkuPriceFluctuation> selectBySkuAndAreaAndAddTime(String sku, LocalDateTime addTime,Integer areaNo);
}
