package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.RefundHandleEvent;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RefundHandleEventMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(RefundHandleEvent record);

    int insertSelective(RefundHandleEvent record);

    RefundHandleEvent selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(RefundHandleEvent record);

    int updateByPrimaryKey(RefundHandleEvent record);

    int updateStatusByRefundNo(@Param("updatedStatus")Integer updatedStatus,@Param("refundNo")String refundNo);



    List<RefundHandleEvent> selectAllByStatus(List<Integer> statusList);


}