package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.WechatScheme;

public interface WechatSchemeMapper {
    int deleteByPrimaryKey(Long id);

    int insert(WechatScheme record);

    int insertSelective(WechatScheme record);

    WechatScheme selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(WechatScheme record);

    int updateByPrimaryKey(WechatScheme record);

    WechatScheme selectByIp(String ip);

}