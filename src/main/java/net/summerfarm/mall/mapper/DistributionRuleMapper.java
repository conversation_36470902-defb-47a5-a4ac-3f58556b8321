package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.DistributionRule;
import net.summerfarm.mall.model.domain.RuleDistributionFree;
import net.summerfarm.mall.model.vo.DistributionRuleVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
* @Author:ct
* @Date:2:32 PM 2019/7/8
*/
@Repository
public interface DistributionRuleMapper {

    /**
    *  根据大客户ID 和城市查询配送费规则
    */
    DistributionRuleVO queryByAdminId(@Param("adminId") Integer adminId , @Param("areaNo") Integer areaNo);

    /**
    * 查询大客户配送费规则是否是适应全部城市
    */
    DistributionRule queryAreaNO(Integer adminId);

    /**
     * 查询生效运费策略
     * @param adminId 大客户ID
     * @param areaNo  城市编号
     * @return  运费策略
     */
    DistributionRule selectValid(@Param("adminId") Integer adminId, @Param("areaNo") Integer areaNo);

    /**
     * 查询rule
     * @param distributionId id
     * @return rule
     */
    List<RuleDistributionFree> selectValidRule(@Param("distributionId") Integer distributionId);
}
