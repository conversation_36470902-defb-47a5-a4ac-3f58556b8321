package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.PrepayInventory;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PrepayInventoryMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(PrepayInventory record);

    int insertSelective(PrepayInventory record);

    PrepayInventory selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(PrepayInventory record);

    int updateByPrimaryKey(PrepayInventory record);

    List<PrepayInventory> selectUsableList(@Param("adminId") Integer adminId, @Param("sku") String sku);

    /**
     * 查可用的预付商品
     * @param adminId 大客户ID
     * @param sku     sku
     * @param direct  1、账期 2、现结
     * @return 预付
     */
    List<PrepayInventory> selectUsableListByType(@Param("adminId") Integer adminId, @Param("sku") String sku, @Param("direct") Integer direct);
}