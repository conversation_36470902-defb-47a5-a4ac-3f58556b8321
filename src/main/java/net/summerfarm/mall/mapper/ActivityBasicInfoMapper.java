package net.summerfarm.mall.mapper;

import java.util.List;
import net.summerfarm.mall.model.domain.market.ActivityBasicInfo;
import net.summerfarm.mall.model.domain.market.ActivityItemConfig;
import net.summerfarm.mall.model.domain.market.ActivityScopeConfig;
import net.summerfarm.mall.model.dto.market.activity.ActivityInfoDTO;
import net.summerfarm.mall.model.dto.market.activity.ActivityItemScopeDTO;
import net.summerfarm.mall.model.dto.market.activity.ActivityScopeDTO;
import net.summerfarm.mall.model.dto.market.activity.ActivityScopeQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface ActivityBasicInfoMapper {
    
    int deleteByPrimaryKey(Long id);

    
    int insert(ActivityBasicInfo record);

    
    int insertSelective(ActivityBasicInfo record);

    
    ActivityBasicInfo selectByPrimaryKey(Long id);

    
    int updateByPrimaryKeySelective(ActivityBasicInfo record);

    
    int updateByPrimaryKey(ActivityBasicInfo record);

    /**
     * 获取生效中的活动
     * @return
     */
    List<ActivityInfoDTO> selectByScope(@Param("scopes") List<ActivityScopeDTO> scopes, @Param("types") List<Integer> types);

//    List<ActivityPageRespDTO> listByQuery(ActivityBasicQueryDTO queryDTO);

//    List<ActivityItemConfig> listByScope(@Param("list") List<ActivityScopeQueryDTO> list, @Param("type") Integer type, @Param("status") Integer status);

    List<ActivityItemScopeDTO> listActivity(@Param("list") List<ActivityScopeQueryDTO> list);

}