package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.FollowUpEvaluation;

public interface FollowUpEvaluationMapper {
    int deleteByPrimaryKey(Long id);

    int insert(FollowUpEvaluation record);

    int insertSelective(FollowUpEvaluation record);

    FollowUpEvaluation selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(FollowUpEvaluation record);

    int updateByPrimaryKey(FollowUpEvaluation record);

    FollowUpEvaluation selectByFollowRecordId(Integer id);
}