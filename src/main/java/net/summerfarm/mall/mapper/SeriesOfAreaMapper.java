package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.SeriesOfArea;
import net.summerfarm.mall.model.vo.SeriesOfAreaVO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface SeriesOfAreaMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(SeriesOfArea record);

    int insertSelective(SeriesOfArea record);

    SeriesOfArea selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(SeriesOfArea record);

    int updateByPrimaryKey(SeriesOfArea record);

    List<SeriesOfAreaVO> selectByModel(SeriesOfArea query);

    List<SeriesOfArea> selectBySeriesIdsAndSeriesTypeTypeAndAreaNo(@Param("seriesIds") Collection<Integer> seriesIds,
                                                                   @Param("seriesType") Integer seriesType,
                                                                   @Param("areaNo") Integer areaNo);

    SeriesOfArea selectBySeriesIdAndSeriesTypeAndAreaNo(@Param("seriesId") Long seriesId, @Param("seriesType") Integer seriesType,
                                                        @Param("areaNo") Integer areaNo);
}