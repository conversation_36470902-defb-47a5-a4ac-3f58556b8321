package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.OrderExportRecord;

public interface OrderExportRecordMapper {

    int deleteByPrimaryKey(Long id);

    int insert(OrderExportRecord record);

    int insertSelective(OrderExportRecord record);

    OrderExportRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(OrderExportRecord record);

    int updateByPrimaryKey(OrderExportRecord record);
}