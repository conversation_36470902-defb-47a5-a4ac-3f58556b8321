package net.summerfarm.mall.mapper;


import net.summerfarm.mall.model.domain.SampleApply;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface SampleApplyMapper {

    /**
     * 样品单详情
     *
     * @param sampleId sampleId
     * @return 样品单详情
     */
    SampleApply selectSampleById(Integer sampleId);

    /**
     * 计数数据交货日期和状态列表
     *
     * @param deliveryTime 配送日期
     * @param statusList   状态列表
     * @return int
     */
    int countDataByDeliveryDateAndStatusList(@Param("deliveryTime") LocalDate deliveryTime,
                                             @Param("statusList") List<Integer> statusList);

    /**
     * 列表数据交货日期和状态
     *
     * @param deliveryTime 配送日期
     * @param statusList   状态列表
     * @param pageIndex    分页指数
     * @param pageSize     分页大小
     * @return {@link List}<{@link SampleApply}>
     */
    List<SampleApply> listDataByDeliveryDateAndStatusList(@Param("deliveryTime") LocalDate deliveryTime,
                                                          @Param("statusList") List<Integer> statusList,
                                                          @Param("pageIndex") Integer pageIndex,
                                                          @Param("pageSize")Integer pageSize);

    /**
     * 样品单自提
     *
     * @param deliveryDate 自提日期
     * @param sampleId sampleId
     * @return 样品单自提
     */
    int sampleApplySelfPickup(@Param("deliveryDate") LocalDate deliveryDate,@Param("sampleId") Integer sampleId);

    /**
     * 修改样品信息
     * @param update 样品
     * @return 影响条数
     */
    int updateById(SampleApply update);
}
