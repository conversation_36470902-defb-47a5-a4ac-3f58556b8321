package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.CardRule;
import net.summerfarm.mall.model.vo.CardRuleVO;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CardRuleMapper {

    //查询有效的规则
    List<CardRule> selectValidRule(CardRule cardRule);

    CardRule selectByPrimaryKey(Integer id);

    CardRuleVO select(Integer id);

}
