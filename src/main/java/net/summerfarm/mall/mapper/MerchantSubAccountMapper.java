package net.summerfarm.mall.mapper;


import net.summerfarm.mall.model.domain.MerchantSubAccount;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public interface MerchantSubAccountMapper {
    int insert(MerchantSubAccount record);

    int insertSelective(MerchantSubAccount record);

    int updateByPrimaryKeySelective(MerchantSubAccount subRecord);

    /**
     * 查询店铺下未删除账号
     *
     * @param mId    店铺id
     * @param status 状态：0、待审核 1、审核通过
     * @return 子账号列表
     */
    List<MerchantSubAccount> selectByMId(@Param("mId") Long mId, @Param("status") Integer status);

    /**
     * 查询子账号
     *
     * @param query 查询条件
     * @return 子账号
     */
    @Deprecated
    MerchantSubAccount selectOne(Map<String, String> query);

    /**
     * 查询子账号信息
     *
     * @param query 查询条件
     * @return
     */
    List<MerchantSubAccount> selectAccountInfo(MerchantSubAccount query);

    /**
     * 移除子账号
     *
     * @param accountId 子账号id
     * @return
     */
    int deleteByPrimaryKey(Long accountId);

    /**
     * 更新账号状态
     *
     * @param accountId 子账号id
     * @param status    状态
     * @return
     */
    int updateStatus(@Param("accountId") Long accountId, @Param("status") Integer status);

    /**
     * 更新红包金额
     *
     * @param mId        店铺id
     * @param accountId  子账号id
     * @param cashAmount 红包金额
     * @param now        时间
     * @return
     */
    int updateCashAmount(@Param("mId") Long mId, @Param("accountId") Long accountId, @Param("cashAmount") BigDecimal cashAmount, @Param("cashUpdateTime") LocalDateTime now);

    /**
     * 查询账号（含已删除）
     *
     * @param query 查询条件
     * @return
     */
    MerchantSubAccount selectIgnoreDel(Map<String, String> query);

    /**
     * 更新子账号ℹ
     * @param mId mId
     * @param openId openid
     * @param registerTime registerTime
     * @param contact contact
     * @return
     */
    int updateUnPassByOpenId(@Param("mId") Long mId, @Param("openId") String openId, @Param("registerTime") LocalDateTime registerTime,@Param("contact") String contact);

    /**
     * 删除店铺子账号
     * @param mId
     * @return
     */
    int deleteByMId(Long mId);

    /**
     * 查询店长账号
     * @param mId
     * @return
     */
    MerchantSubAccount selectMangerByMId(@Param("mId") Long mId);


    int updateLoginTime( @Param("openId") String openId, @Param("loginTime") LocalDateTime loginTime);


    /**
     * 联系账户id列表
     *
     * @param accountIds 帐户id
     * @return {@link List}<{@link MerchantSubAccount}>
     */
    List<MerchantSubAccount> listContactByAccountIds(@Param("accountIds") List<Long> accountIds);

    /**
     * 查询子账号
     *
     * @param merchantSubAccount 查询条件
     * @return 子账号
     */
    MerchantSubAccount selectByEntity(MerchantSubAccount merchantSubAccount);

    /**
     * 查询子账号
     *
     * @param mId 查询条件
     * @return 子账号
     */
    List<MerchantSubAccount> selectAllAccountByMId(Long mId);

    /**
     * 批量更新子账户
     *
     * @param updateAccountList 新增条件
     * @return
     */
    int batchUpdateByPrimaryKey(@Param("list") List<MerchantSubAccount> updateAccountList);

    List<MerchantSubAccount> selectNoUnionIdLimit(@Param("offset") int offset, @Param("pageSize") int pageSize);


    Long selectCountByMAreaNosUnionid(@Param("areaCodes") List<Integer> areaCodes,@Param("unionid")  String unionid);
}