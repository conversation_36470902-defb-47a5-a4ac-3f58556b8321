package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.DeliveryPlanExtend;

import java.util.List;

public interface DeliveryPlanExtendMapper {
    int deleteByPrimaryKey(Long id);

    int insert(DeliveryPlanExtend record);

    int insertSelective(DeliveryPlanExtend record);

    DeliveryPlanExtend selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DeliveryPlanExtend record);

    int updateByPrimaryKey(DeliveryPlanExtend record);

    /**
     * 批量插入
     * @param deliveryPlanExtendList
     * @return
     */
    int insertBatch(List<DeliveryPlanExtend> deliveryPlanExtendList);
}