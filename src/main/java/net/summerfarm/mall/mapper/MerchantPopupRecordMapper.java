package net.summerfarm.mall.mapper;

import java.time.LocalDateTime;

import org.apache.ibatis.annotations.Param;

import net.summerfarm.mall.model.domain.MerchantPopupRecord;

public interface MerchantPopupRecordMapper {
    int insertSelective(MerchantPopupRecord record);

    int updateCountAndLastPopupTimeById(@Param("updatedCount") Integer updatedCount,
                                        @Param("updatedLastPopupTime") LocalDateTime updatedLastPopupTime,
                                        @Param("id") Long id);

    MerchantPopupRecord selectOneByMIdAndTypeId(@Param("mId") Long mId, @Param("typeId") Long typeId);

}