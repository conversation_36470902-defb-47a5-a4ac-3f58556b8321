package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.DiscountCard;

import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DiscountCardMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(DiscountCard record);

    int insertSelective(DiscountCard record);

    DiscountCard selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(DiscountCard record);

    int updateByPrimaryKey(DiscountCard record);

    List<DiscountCard>  selectDiscountCard(DiscountCard record);

    List<DiscountCard> listByIds(@Param("ids") List<Integer> ids);

    List<DiscountCard> selectAllDiscountCard();
}