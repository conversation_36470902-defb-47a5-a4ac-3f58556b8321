package net.summerfarm.mall.mapper;


import net.summerfarm.mall.model.domain.CouponBlackAndWhite;

import java.util.Collection;
import java.util.List;

public interface CouponBlackAndWhiteMapper {
    int deleteByPrimaryKey(Long id);

    int insert(CouponBlackAndWhite record);

    int insertSelective(CouponBlackAndWhite record);

    CouponBlackAndWhite selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CouponBlackAndWhite record);

    int updateByPrimaryKey(CouponBlackAndWhite record);

    List<CouponBlackAndWhite> getAllByEntity(CouponBlackAndWhite couponBlackAndWhite);

    List<Integer> pageCouponIds();

    /**
     * 根据优惠券ID列表查找对应的黑白名单记录。
     * <p>
     * <strong>强烈警告：此方法仅用于内存缓存初始化。返回的 SKU 字段是由多个 SKU 拼接而成的字符串，
     * 不可直接用于 SKU 匹配。必须将其转换为 List&lt;String&gt; 后才能使用。</strong>
     * </p>
     *
     * @param couponIds 要查找的优惠券ID列表。
     * @return 与提供的优惠券ID匹配的 CouponBlackAndWhite 对象列表（使用GROUP_CONCAT拼接而成）。
     */
    List<CouponBlackAndWhite> findByCouponIds(Collection<Integer> couponIds);
}
