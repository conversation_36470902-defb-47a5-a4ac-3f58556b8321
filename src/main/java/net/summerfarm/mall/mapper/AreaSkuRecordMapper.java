package net.summerfarm.mall.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import net.summerfarm.mall.model.domain.AreaSkuRecord;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;

@Repository
public interface AreaSkuRecordMapper {
    int insertSelective(AreaSkuRecord areaSkuRecord);

    /**
     * 最早上架时间
     * @param areaNo 城市编号
     * @param sku sku
     * @return 时间
     */
    LocalDateTime selectFirstOnSale(@Param("areaNo") Integer areaNo, @Param("sku") String sku);

    List<AreaSkuRecord> listFirstOnSaleBySkus(@Param("areaNo") Integer areaNo, @Param("list") List<String> skus);
}
