package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.CompanyAccount;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CompanyAccountMapper {
    CompanyAccount selectByPrimaryKey(Integer id);

    CompanyAccount selectByAreaNo(Integer areaNo);

    List<CompanyAccount> selectByChannel(int channel);

    CompanyAccount selectByMId(String mId);

    List<CompanyAccount> selectByAppId(String weixAppId);

    List<CompanyAccount> selectByMchxAppId(String weixAppId);
}
