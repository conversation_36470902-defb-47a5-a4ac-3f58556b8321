package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.SortCombinationTab;

import java.util.List;

public interface SortCombinationTabMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(SortCombinationTab record);

    int insertSelective(SortCombinationTab record);

    SortCombinationTab selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(SortCombinationTab record);

    int updateByPrimaryKey(SortCombinationTab record);

    List<SortCombinationTab> selectByCombinationId(Integer combinationId);
}