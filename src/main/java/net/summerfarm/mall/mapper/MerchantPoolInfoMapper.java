package net.summerfarm.mall.mapper;

import java.util.List;
import net.summerfarm.mall.model.domain.MerchantPoolInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface MerchantPoolInfoMapper {

    MerchantPoolInfo selectById(@Param("id") Long id);

    List<String> listCreators(String creator);

    List<MerchantPoolInfo> listByIds(@Param("list") List<Long> ids);

}