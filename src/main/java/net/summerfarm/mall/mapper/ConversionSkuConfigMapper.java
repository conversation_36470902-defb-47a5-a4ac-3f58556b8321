package net.summerfarm.mall.mapper;


import net.summerfarm.mall.model.domain.ConversionSkuConfig;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2021/10/18  11:11
 */
@Repository
public interface ConversionSkuConfigMapper {


    /**
     * 查询信息
     * @Author: ct
     * @param config 信息配置
     * @return
     **/
    List<ConversionSkuConfig> selectConfig(ConversionSkuConfig config);

    /**
     * 查询详情
     * @Author: ct
     * @param id
     * @return
     **/
    ConversionSkuConfig selectConfigDetail(Integer id);

}
