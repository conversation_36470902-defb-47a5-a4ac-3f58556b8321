package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.WarehouseStorageCenter;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository(value = "warehouseStorageMapper")
public interface WarehouseStorageCenterMapper {

    List<WarehouseStorageCenter> listByWarehouseNoList(@Param("list") List<Integer> list);
}