package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.AfterSaleOrder;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import net.summerfarm.mall.model.vo.OrderItemVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface AfterSaleOrderMapper {

    int insertSelective(AfterSaleOrder record);

    int countByMId(@Param("mId") Long mId, @Param("accountId") Long accountId);

    int countView(@Param("mId") Long mId, @Param("accountId") Long accountId);

    int updateStatus(@Param("afterSaleOrderNo") String afterSaleOrderNo, @Param("status") int status);

    AfterSaleOrderVO selectByAfterSaleOrderNo(String afterSaleOrderNo);

    List<AfterSaleOrder> selectDistinct(@Param("mId") Long mId, @Param("accountId") Long accountId);

    int update(AfterSaleOrder afterSaleOrder);

    /**
     * 查询补充凭证状态超过24h的售后单
     */
    List<String> selectTimeOutProof();


    int updateByAfterSaleOrderNo(AfterSaleOrder updateKeys);


    BigDecimal selectSumPrice (@Param("mId")Long mid,@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
    * 获取获取售后数量
    */
    List<AfterSaleOrderVO> selectByOrderNoAndSku(@Param("orderNo") String orderNo, @Param("sku") String sku);

    /**
    * 查询发起售后数量 0,1,2
    */
    List<AfterSaleOrderVO> selectAfterSale(AfterSaleOrder aso);

    /**
     * 查询成功售后数量 0,1,2
     */
    List<AfterSaleOrderVO> selectSuccessAfterSale(AfterSaleOrder aso);


    List<AfterSaleOrderVO> selectNew(AfterSaleOrder aso);

    /**
     * 极速售后单信息
     */
    List<AfterSaleOrderVO> selectSpeedAfterSaleOrder(@Param("beginTime") LocalDateTime beginTime, @Param("endTime") LocalDateTime endTime, @Param("mId") Long mId, @Param("status")Integer status);


    /**
    * 查询售后信息 没有区分状态
    */
    List<AfterSaleOrder> selectAfterOrder(String orderNo);

    /**
    * 查询退款中的售后单
    */
    List<AfterSaleOrder> selectAfterOrderDeposit();

    /**
     * 查询已成功未到货售后数量
     */
    Integer selectNotStockSuccessQuantity(String orderNo);

    /**
     * 查询除退货退款，换货，补发的售后数量
     */
    Integer selectSuccessQuantity(String orderNo);

    /**
     * 查询所有成功的售后数量
     */
    Integer selectAllSuccessQuantity(String orderNo);

    /**
     * 查询售后数量和售后单位,还有金额
     * @param orderNo
     * @param sku
     * @param deliveryPlanId
     * @param suitId
     * @return
     */
    List<AfterSaleOrderVO> selectAfterQuantityByOrderNo(@Param("orderNo")String orderNo,@Param("sku")String sku,@Param("deliveryPlanId")Integer deliveryPlanId,@Param("suitId")Integer suitId,@Param("afterSaleOrderNo")String afterSaleOrderNo,@Param("productType")Integer productType);

    /**
     * 查询退货退款/退货录入账单记录
     * @param orderNo
     * @param sku
     * @param deliveryPlanId
     * @param suitId
     * @return
     */
    List<AfterSaleOrderVO> selectAfterByHandle(@Param("orderNo")String orderNo,@Param("sku")String sku,@Param("deliveryPlanId")Integer deliveryPlanId,@Param("suitId")Integer suitId,@Param("productType")Integer productType);

    /**
     * 根据订单号+组合包ID查询
     * @param orderNo
     * @param suitId
     * @param afterSaleOrderNo
     * @return
     */
    List<AfterSaleOrderVO> selectByOrderNoAndSuitId(String orderNo, Integer suitId,String afterSaleOrderNo);

    /**
     * 统计
     * @param dpId
     * @return
     */
    int countBydeliveryPlanId(@Param("dpId") Integer dpId);

    /**
     * 根据订单号查询换货补发的成功数量
     * @param orderNo
     * @return
     */
    Integer selectHBAfterByOrderNo(String orderNo);

    /**
     * 查询未到货成功的售后单
     * @param orderNo
     * @return
     */
    List<AfterSaleOrderVO> selectNotAfter(String orderNo);

    /**
     * 查询订单对应的售后单是否有退运费的售后
     * @param orderNo
     * @param afterSaleOrderNo
     * @return
     */
    Integer selectByDeliveryFeeCount(String orderNo,String afterSaleOrderNo);

    /**
     *
     * @param orderNo
     * @return
     */
    int selectNotQuantityByOrderNo(@Param("orderNo")String orderNo);

    /**
     * 查询未到货成功的售后单
     * @param afterSaleOrder
     * @return
     */
    List<AfterSaleOrderVO> selectSuccessByAfterSaleOrder(AfterSaleOrder afterSaleOrder);

    /**
     * 查询换货、补发的售后单数量
     * @param orderNo
     * @return
     */
    Integer selectExchangeByOrderNo(String orderNo);

    /**
     * 根据售后订单号查询售后相关信息
     *
     * @param afterSaleOrderNo 后出售订单号
     * @return {@link AfterSaleOrderVO}
     */
    AfterSaleOrderVO selectVoByAfterSaleOrderNo(@Param("afterSaleOrderNo") String afterSaleOrderNo);

    /**
     * 批量插入信息
     * @param afterSaleOrders 插入信息
     * @return 数量
     */
    int saveBatchAfterSaleOrder(List<AfterSaleOrder> afterSaleOrders);

    /**
     * 查询售后信息 包含待审核，待审批，完成
     */
    List<AfterSaleOrder> selectAfterOrderReissue(OrderItemVO orderItem);

    /**
     * 获取用户发起售后的数量
     *
     */
    int afterSaleCountByMId(@Param("mId") Long mId, @Param("accountId") Long accountId);

    /**
     * 查询是否重复
     * @param afterSaleOrderNo 售后单号
     * @return 数量
     */
    int countByAfterSaleOrderNo(String afterSaleOrderNo);

    /**
     * 根据商户编号获取未完成的售后单--待审核，待审批记录
     * @param mId 客户编号
     * @return 数量
     */
    Integer getUnfilledAfterSaleOrderByMid(Long mId);

    /**
     * 根据sku获取部分退款信息
     * @param afterSaleOrder
     * @return AfterSaleOrderVO
     */
    List<AfterSaleOrderVO> selectRefundSuccessInfo(AfterSaleOrder afterSaleOrder);

    List<AfterSaleOrderVO> selectByOrderNoNew(AfterSaleOrder querySaleOrder);

    Integer queryByOrderNoQuantity(@Param("orderNo") String orderNo, @Param("afterTime") LocalDateTime afterTime, @Param("sku") String sku);

    Integer queryAfterOrderQuantity(@Param("addTime") LocalDateTime addTime, @Param("sku") String sku, @Param("deliveryDate") LocalDate deliveryDate);

    List<AfterSaleOrderVO> selectAfterSaleReissueOrder(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("sku") String sku, @Param("mId") Long mId);

    /**
     * 查询售后订单sku信息
     *
     * @param afterSaleOrderNo afterSaleOrderNo
     * @return {@link List}<{@link AfterSaleOrderVO}>
     */
    AfterSaleOrderVO queryByAfterSaleOrderNo(@Param("afterSaleOrderNo") String afterSaleOrderNo);

    /**
     * 查询订单所有售后单
     * @param orderNo
     * @return
     */
    List<AfterSaleOrderVO> selectAfterSaleOrderByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 查询自动售后单
     * @param orderNo
     * @param sku
     * @return
     */
    AfterSaleOrder selectAutoAfterSaleByOrderNoAndSku(String orderNo, String sku);
}