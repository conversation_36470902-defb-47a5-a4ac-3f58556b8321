package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.DeliveryEvaluation;
import net.summerfarm.mall.model.domain.OrderItem;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * DeliveryEvaluationMapper继承基类
 */
@Repository
public interface DeliveryEvaluationMapper  {
    int deleteByPrimaryKey(Long id);

    int insert(DeliveryEvaluation record);

    int insertSelective(DeliveryEvaluation record);

    DeliveryEvaluation selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DeliveryEvaluation record);

    int updateByPrimaryKey(DeliveryEvaluation record);

    List<DeliveryEvaluation> selectList(DeliveryEvaluation record);

    List<DeliveryEvaluation> selectListByDeliveryPlanId(@Param("deliveryIds") List<Integer> deliveryIds);

    /**
     * 批量插入
     * @param deliveryEvaluation
     * @return
     */
    int insertBatch(List<DeliveryEvaluation> deliveryEvaluation);
}