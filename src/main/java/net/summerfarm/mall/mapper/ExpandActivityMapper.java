package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.ExpandActivity;
import net.summerfarm.mall.model.vo.ExpandActivityVO;
import org.apache.ibatis.annotations.Param;


public interface ExpandActivityMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ExpandActivity record);

    int insertSelective(ExpandActivity record);

    ExpandActivity selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ExpandActivity record);

    int updateByPrimaryKey(ExpandActivity record);

    /**
     * 查询当前地区生效中活动
     * @param largeAreaNo 查询参数
     * @return 拓展购买活动
     */
    ExpandActivityVO selectEnableActivity(@Param("largeAreaNo")Integer largeAreaNo);
}