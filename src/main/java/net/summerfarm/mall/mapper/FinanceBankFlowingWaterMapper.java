package net.summerfarm.mall.mapper;



import net.summerfarm.mall.model.domain.FinanceBankFlowingWater;

import java.util.List;

public interface FinanceBankFlowingWaterMapper {
    /**
     * 根据id查询信息
     * @param id
     * @return
     */
    FinanceBankFlowingWater selectByPrimaryKey(Long id);

    /**
     * 修改
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(FinanceBankFlowingWater record);
}