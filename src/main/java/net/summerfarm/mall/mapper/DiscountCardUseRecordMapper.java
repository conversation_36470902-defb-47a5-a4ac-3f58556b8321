package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.DiscountCardUseRecord;

import java.util.List;

public interface DiscountCardUseRecordMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(DiscountCardUseRecord record);

    int insertSelective(DiscountCardUseRecord record);

    int insertBatch(List<DiscountCardUseRecord> record);

    DiscountCardUseRecord selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(DiscountCardUseRecord record);

    int updateByPrimaryKey(DiscountCardUseRecord record);

    List<DiscountCardUseRecord> selectByOrderNo(String orderNo);

    void deleteByOrderNo(String orderNo);

    Integer getValidCountsByMid(Long mId);
}