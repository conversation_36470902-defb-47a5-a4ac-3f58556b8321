package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.AreaStore;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AreaStoreMallMapper {

    //修改虚拟库存和虚拟变值
    int updateOnlineQuantityAndChange(@Param("onlineChange") Integer onlineChange, @Param("changeChange") Integer changeChange, @Param("sku") String sku, @Param("storeNo") Integer storeNo);

    int updateLockStock(@Param(value = "lockQuantity") Integer lockQuantity, @Param(value = "sku") String sku, @Param(value = "storeNo") Integer storeNo);

    int updateSaleLockStock(@Param(value = "lockQuantity") Integer lockQuantity, @Param(value = "sku") String sku, @Param(value = "storeNo") Integer storeNo,@Param(value = "reserveQuantity") Integer reserveQuantity);



    AreaStore selectByStoreNoAndSku(@Param(value = "storeNo") Integer storeNo, @Param(value = "sku") String sku);

    AreaStore selectByOrderNoNew(@Param("orderNo") String orderNo);

    AreaStore selectByStoreNoAndSkuNew(@Param(value = "storeNo") Integer storeNo, @Param(value = "sku") String sku);

    /**
     * 库存查询
     * @param sku
     * @param areaNo 仓库
     * @return
     */
    int onlineQuantity(@Param("sku") String sku,@Param("areaNo") Integer areaNo);


    List<AreaStore> selectByStoreNoAndSkus(@Param("sku") Integer areaNo, @Param("skus") List<String> skus);

    AreaStore selectWithOutDataPermission(AreaStore areaStore);

    AreaStore selectOne(AreaStore query);

    List<AreaStore> selectByAreaNoSku(@Param("areaNo") Integer areaNo, @Param("skus") List<String> skus);

}
