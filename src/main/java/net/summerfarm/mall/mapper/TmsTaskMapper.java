package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.TmsTask;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2020/7/14  10:42
 */
@Repository
public interface TmsTaskMapper {

    TmsTask selectTmsTask(@Param("storeNo") Integer storeNo, @Param("deliverTime") LocalDate deliverTime, @Param("path") String path);


}
