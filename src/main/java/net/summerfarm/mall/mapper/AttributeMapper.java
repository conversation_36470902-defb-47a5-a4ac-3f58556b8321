package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.Attribute;
import org.springframework.stereotype.Repository;

@Repository
public interface AttributeMapper {
    int deleteByPrimaryKey(Integer attrId);

    int insert(Attribute record);

    int insertSelective(Attribute record);

    Attribute selectByPrimaryKey(Integer attrId);

    int updateByPrimaryKeySelective(Attribute record);

    int updateByPrimaryKey(Attribute record);
}