package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.bo.coupon.CouponReceiveCountBO;
import net.summerfarm.mall.model.domain.CouponReceiveLog;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface CouponReceiveLogMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(CouponReceiveLog record);

    int insertSelective(CouponReceiveLog record);

    CouponReceiveLog selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(CouponReceiveLog record);

    int updateByPrimaryKey(CouponReceiveLog record);

    int selectByCoupon(@Param("id") Integer id, @Param("couponSenderId") Integer couponSenderId, @Param("mId") Long mId);

    /**
     * 批量插入卡券领取记录
     *
     * @param couponReceiveLogs 卡券领取记录数据集
     * @return 变更记录数
     */
    int insertList(@Param("couponReceiveLogs") Collection<CouponReceiveLog> couponReceiveLogs);

    /**
     * 根据给定的senderSetUpId集合按照senderSetUpId以及couponId分组统计
     *
     * @param senderSetupIds senderSetUpId集合
     * @param mId            商户id
     */
    List<CouponReceiveCountBO> countBySenderSetupIdAndMId(@Param("senderSetupIds") Collection<Integer> senderSetupIds, @Param("mId") Long mId);
}