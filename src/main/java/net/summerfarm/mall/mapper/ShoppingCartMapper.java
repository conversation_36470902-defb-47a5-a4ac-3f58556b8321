package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.ShoppingCart;
import net.summerfarm.mall.model.vo.ShoppingCartVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ShoppingCartMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ShoppingCart record);

    int insertSelective(ShoppingCart record);

    ShoppingCart selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ShoppingCart record);

    int updateByPrimaryKey(ShoppingCart record);

    ShoppingCart selectByEntity(ShoppingCart cart);

    Integer getSumQuantity(@Param("mId") Long mId, @Param("accountId") Long accountId,
                           @Param("sku") String sku, @Param("parentSku") String parentSku);

    void updateQuantityByEntity(ShoppingCart shoppingCart);

    int batchDelete(@Param("ids") List<Long> ids);

    List<ShoppingCartVO> getShoppingCarts(@Param("mId") Long mId, @Param("accountId") Long accountId,
                                          @Param("areaNo") Integer areaNo,
                                          @Param("adminId") Integer adminId, @Param("direct") Integer direct);

    int deleteByEntity(ShoppingCart toDel);

    void batchDeleteByEntity(@Param("list") List<ShoppingCart> delete);

    List<ShoppingCart> selectAll(@Param("mId") Long mId, @Param("accountId") Long accountId);

    int batchUpdateQuantity(@Param("list") List<ShoppingCart> updateQuantityList);

    int updateQuantityById(@Param("id") Long id, @Param("quantity") Integer quantity);

    /**
     * 根据客户ID查询所有购物车信息
     * @param shoppingCart
     */
    List<ShoppingCartVO> getAll(ShoppingCart shoppingCart);
}