package net.summerfarm.mall.mapper;

import java.util.List;
import net.summerfarm.mall.model.domain.MchPopupOtification;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;

/**
 * <AUTHOR>
 */
public interface MchPopupOtificationMapper {
    int deleteByPrimaryKey(Long id);

    int insert(MchPopupOtification record);

    /**
     * 新增用户展示过的弹窗
     * @param record
     * @return
     */
    int insertSelective(MchPopupOtification record);

    /**
     * 查询用户每日弹窗数据
     * @param bannerId
     * @param recognitionTime
     * @param mId
     * @return
     */
    MchPopupOtification selectByBannerId(@Param("bannerId") Integer bannerId, @Param("recognitionTime") LocalDate recognitionTime, @Param("mId") Long mId);

    int updateByPrimaryKeySelective(MchPopupOtification record);

    int updateByPrimaryKey(MchPopupOtification record);

    List<MchPopupOtification> listByDate(@Param("recognitionTime") LocalDate recognitionTime, @Param("mId") Long mId);
}