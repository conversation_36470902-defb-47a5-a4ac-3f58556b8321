package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.FinancialInvoice;
import net.summerfarm.mall.model.vo.invoice.FinancialInvoiceVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/16 15:55
 */
@Repository
public interface FinancialInvoiceMapper {
	/**
	 * 获取发票信息
	 * @param mId 商户id
	 * @return 发票信息
	 */
	List<FinancialInvoiceVO> selectByMId(@Param("mId") Long mId);
}
