package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.InterestRateConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface InterestRateConfigMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(InterestRateConfig record);

    int insertSelective(InterestRateConfig record);

    InterestRateConfig selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(InterestRateConfig record);

    int updateByPrimaryKey(InterestRateConfig record);

    /**
     * 查询当前地区生效中活动
     * @param areaNo 查询参数
     * @param skuList 查询参数
     * @return InterestRateConfigs
     */
    List<InterestRateConfig> selectBySkuList(@Param("skuList")List<String> skuList,@Param("areaNo")Integer areaNo);
}