package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.MasterOrderPreferential;

/**
* <AUTHOR>
* @description 针对表【master_order_preferential(主订单优惠明细表)】的数据库操作Mapper
* @createDate 2023-10-11 14:08:08
* @Entity net.summerfarm.mall.model.domain.MasterOrderPreferential
*/
public interface MasterOrderPreferentialMapper {

    int deleteByPrimaryKey(Long id);

    int insert(MasterOrderPreferential record);

    int insertSelective(MasterOrderPreferential record);

    MasterOrderPreferential selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MasterOrderPreferential record);

    int updateByPrimaryKey(MasterOrderPreferential record);

}
