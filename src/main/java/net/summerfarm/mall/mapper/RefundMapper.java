package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.Refund;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

@Repository
public interface RefundMapper {

    List<Refund> selectByOrderNo(String orderNo);

    int insertSelective(Refund record);

    int updateByPrimaryKeySelective(Refund record);

    List<Refund> selectByAfterSaleOrderNo(String afterSaleOrderNo);

    List<Refund> selectByAllAfterSaleOrderNo(String afterSaleOrderNo);

    List<Refund> selectByAfterSaleOrderNoSuccess(String afterSaleOrderNo);

    int updateSelectiveByAfterSaleOrderNo(Refund updateKeys);

    Refund selectById(Integer refundId);

    Refund selectByRefundNo(String refundNo);

    List<Refund> selectByOrderNoList(ArrayList<String> orderList);

    List<Refund> selectLikeOrderNo(String orderNo);
}