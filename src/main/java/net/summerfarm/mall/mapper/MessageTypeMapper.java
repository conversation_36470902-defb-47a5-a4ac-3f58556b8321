package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.MessageType;
import org.springframework.stereotype.Repository;

@Repository
public interface MessageTypeMapper {
    int deleteByPrimaryKey(Integer messageTypeId);

    int insert(MessageType record);

    int insertSelective(MessageType record);

    MessageType selectByPrimaryKey(Integer messageTypeId);

    int updateByPrimaryKeySelective(MessageType record);

    int updateByPrimaryKey(MessageType record);
}