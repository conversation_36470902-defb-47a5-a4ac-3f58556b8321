package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.FenceDelivery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 围栏配送日志mapper
 */
@Mapper
public interface FenceDeliveryMapper {
    /**
     * 根据id删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 新增围栏配送日志
     * @param record 围栏配送日志信息
     * @return
     */
    int insert(FenceDelivery record);
    /**
     * 新增围栏配送日志
     * @param record 围栏配送日志信息
     * @return
     */
    int insertSelective(FenceDelivery record);

    /**
     * 根据id查看围栏配送日志
     * @param id
     * @return
     */
    FenceDelivery selectByPrimaryKey(Long id);

    /**
     * 更新围栏配送日志
     * @param record 围栏配送日志信息
     * @return
     */
    int updateByPrimaryKeySelective(FenceDelivery record);
    /**
     * 更新围栏配送日志
     * @param record 围栏配送日志信息
     * @return
     */
    int updateByPrimaryKey(FenceDelivery record);

    /**
     * 根据围栏id查找配送规则
     * @param fenceId 围栏id
     * @return 配送规则
     */
    FenceDelivery selectByFenceId(@Param("fenceId") Integer fenceId);
}