package net.summerfarm.mall.mapper;

import java.util.List;
import net.summerfarm.mall.model.domain.MerchantPoolDetail;
import net.summerfarm.mall.model.dto.market.circle.PoolDetailDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface MerchantPoolDetailMapper {

    List<Long> selectByMId(@Param("mId") Long mId, @Param("list") List<PoolDetailDTO> detailList);

    List<MerchantPoolDetail> getDetailByMId(@Param("mId") Long mId);

    int checkMerchantInMerchantPool(@Param("mId") Long mId, @Param("poolInfoId") Long poolInfoId, @Param("version") Integer version);

    Integer countByMidPoolInfoID(@Param("mId") Long mId, @Param("poolInfoId") Long poolInfoId);

}