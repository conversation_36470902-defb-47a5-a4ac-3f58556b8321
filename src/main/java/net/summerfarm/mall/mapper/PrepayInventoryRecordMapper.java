package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.PrepayInventoryRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PrepayInventoryRecordMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(PrepayInventoryRecord record);

    int insertSelective(PrepayInventoryRecord record);

    PrepayInventoryRecord selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(PrepayInventoryRecord record);

    int updateByPrimaryKey(PrepayInventoryRecord record);

    List<PrepayInventoryRecord> selectRecordList(@Param("adminId") Integer adminId, @Param("orderNo") String orderNo, @Param("sku") String sku);

    List<PrepayInventoryRecord> selectRecordListByOrderNo(String orderNo);

    List<PrepayInventoryRecord> selectByOrderNoAndSku(@Param("orderNo") String orderNo, @Param("sku") String sku);

    List<PrepayInventoryRecord> selectRecordListNew(@Param("orderNo") String orderNo, @Param("sku") String sku, @Param("valid") boolean valid);
}