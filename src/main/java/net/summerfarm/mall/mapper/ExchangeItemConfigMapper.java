package net.summerfarm.mall.mapper;

import java.util.List;
import net.summerfarm.mall.model.domain.ExchangeItemConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface ExchangeItemConfigMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ExchangeItemConfig record);

    int insertSelective(ExchangeItemConfig record);

    ExchangeItemConfig selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ExchangeItemConfig record);

    List<ExchangeItemConfig> selectByScopeId(@Param("scopeConfigId") Long scopeConfigId);

    ExchangeItemConfig selectBySku(@Param("scopeConfigId") Long scopeConfigId, @Param("sku") String sku);

}