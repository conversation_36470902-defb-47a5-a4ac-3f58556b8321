package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.Coupon;
import net.summerfarm.mall.model.domain.CouponSenderSetup;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

@Mapper
public interface CouponSenderSetupMapper {
    List<Coupon> selectCouponById(Integer id);

    int deleteByPrimaryKey(Integer id);

    int insert(CouponSenderSetup record);

    int insertSelective(CouponSenderSetup record);

    CouponSenderSetup selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(CouponSenderSetup record);

    int updateByPrimaryKey(CouponSenderSetup record);

    List<CouponSenderSetup> selectSenderSetup(LocalDateTime now);

    /**
     * 根据卡券id和状态查询对应的卡券发放设置
     *
     * @param couponId 卡券id
     * @param status   状态
     * @return 卡券发放设置信息
     */
    CouponSenderSetup selectByCouponIdAndStatus(@Param("couponId") Integer couponId,
                                                @Param("status") Integer status,
                                                @Param("senderType") Integer senderType);

    List<CouponSenderSetup> selectByAll(CouponSenderSetup couponSenderSetup);

    /**
     * 根据发放配置id查询卡券信息
     * @param ids 卡发发放id集合
     * @return 卡券信息
     */
    List<Coupon> selectCouponByIdIn(@Param("ids") Collection<Integer> ids);
}