package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.DeliveryPlanRemarkSnapshot;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DeliveryPlanRemarkSnapshotMapper {
    int deleteByPrimaryKey(Long id);

    int insert(DeliveryPlanRemarkSnapshot record);

    int insertSelective(DeliveryPlanRemarkSnapshot record);

    DeliveryPlanRemarkSnapshot selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DeliveryPlanRemarkSnapshot record);


    List<DeliveryPlanRemarkSnapshot> selectByTypeBusinessIds(@Param("type") Integer type, @Param("businessIds")List<String> businessIds);

    int deleteByTypeBusinessIds(@Param("type") Integer type, @Param("businessIds")List<String> businessIds);

    int addList(@Param("adds") List<DeliveryPlanRemarkSnapshot> adds);
}