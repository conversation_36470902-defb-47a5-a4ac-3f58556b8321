package net.summerfarm.mall.mapper;

import java.util.List;
import net.summerfarm.mall.model.domain.market.ActivitySkuDetail;
import net.summerfarm.mall.model.dto.market.activity.ActivitySkuDetailDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface ActivitySkuDetailMapper {
    
    int deleteByPrimaryKey(Long id);

    
    int insert(ActivitySkuDetail record);

    
    int insertSelective(ActivitySkuDetail record);

    
    ActivitySkuDetail selectByPrimaryKey(Long id);

    
    int updateByPrimaryKeySelective(ActivitySkuDetail record);

    
    int updateByPrimaryKey(ActivitySkuDetail record);

    int insertBatch(@Param("list") List<ActivitySkuDetail> list, @Param("itemConfigId") Long itemConfigId);

    ActivitySkuDetail selectBySku(@Param("itemConfigId") Long itemConfigId, @Param("sku") String sku);

    List<ActivitySkuDetail> selectByItemConfig(@Param("itemConfigId") Long itemConfigId);

    int updateDelFlag(@Param("itemConfigId") Long itemConfigId, @Param("sku") String sku);

    List<ActivitySkuDetail> listByItemConfigsAndSku(@Param("configList") List<Long> list, @Param("sku") String sku);

    List<ActivitySkuDetail> listByItemConfigsSkus(@Param("configList") List<Long> list, @Param("skus") List<String> skus);

    int updateDelFlagBatch(@Param("ids")List<Long> ids);

    List<ActivitySkuDetailDTO> listByBasicInfoIds(@Param("list") List<Long> basicInfoIds, @Param("sku") String sku);

    ActivitySkuDetailDTO selectByBasicInfoId(@Param("basicInfoId") Long basicInfoId, @Param("sku") String sku);

    List<ActivitySkuDetailDTO> listByItemConfigs(@Param("configList") List<Long> list);

    int updateActivityStock(@Param("itemConfigId") Long itemConfigId, @Param("sku") String sku, @Param("quantity") Integer purchaseQuantity);

    List<ActivitySkuDetailDTO> listByBasicInfoIdsAndSkus(@Param("ids") List<Long> basicInfoIds, @Param("skus") List<String> skus);

    /**
    * @description 归还库存
    * @params [itemConfigId, sku, purchaseQuantity]
    * @return int
    * <AUTHOR>
    * @date  2024/8/21 13:51
    */
    int returnActivityStock(@Param("itemConfigId") Long itemConfigId, @Param("sku") String sku, @Param("quantity") Integer purchaseQuantity);
}