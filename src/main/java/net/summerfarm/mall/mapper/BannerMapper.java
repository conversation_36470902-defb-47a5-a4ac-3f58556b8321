package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.Banner;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BannerMapper {

    Banner selectByPrimaryKey(Integer id);

    List<Banner> select(@Param("type") String type, @Param("showRule") Integer showRule, @Param("areaNo") Integer areaNo);

    List<Banner> selectByPrimaryKeys(@Param("ids") List<Integer> ids);

    Banner selectFirstByIds(@Param("ids") List<Integer> ids);

    List<Banner> listByQuery(@Param("showRule") Integer showRule, @Param("areaNo") Integer areaNo);
}