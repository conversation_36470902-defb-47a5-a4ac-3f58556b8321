package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.AdminSkin;

public interface AdminSkinMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AdminSkin record);

    int insertSelective(AdminSkin record);

    AdminSkin selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AdminSkin record);

    int updateByPrimaryKey(AdminSkin record);

    AdminSkin selectByAdminId(Integer adminId);
}