package net.summerfarm.mall.mapper;

import java.util.List;
import net.summerfarm.mall.model.domain.market.TagLaunchInfo;
import net.summerfarm.mall.model.dto.market.malltag.MallTagDTO;
import net.summerfarm.mall.model.dto.market.malltag.TagScopeDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface TagLaunchInfoMapper {

    TagLaunchInfo selectByPrimaryKey(Long id);

    List<MallTagDTO> listAll(@Param("scopes") List<TagScopeDTO> scopes, @Param("skus") List<String> skus);

}