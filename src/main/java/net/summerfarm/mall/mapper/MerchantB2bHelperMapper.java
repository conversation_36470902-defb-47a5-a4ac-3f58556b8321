package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.merchant.MerchantB2bHelper;
import org.apache.ibatis.annotations.Param;

public interface MerchantB2bHelperMapper {
    int deleteByPrimaryKey(Long id);

    int insert(MerchantB2bHelper record);

    int insertSelective(MerchantB2bHelper record);

    MerchantB2bHelper selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MerchantB2bHelper record);

    int updateByPrimaryKey(MerchantB2bHelper record);

    MerchantB2bHelper selectByMidAccountId(@Param("mId") Long merchantId,@Param("accountId") Long accountId);
}