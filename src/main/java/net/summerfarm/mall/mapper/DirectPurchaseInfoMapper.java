package net.summerfarm.mall.mapper;


import net.summerfarm.mall.model.domain.DirectPurchaseInfo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface DirectPurchaseInfoMapper {

    /**
     * 查询直发采购数据
     * @param id
     * @return
     */
    DirectPurchaseInfo selectById(Integer id);

    /**
     * 按交货日期统计数据
     *
     * @param deliveryTime 配送日期
     * @return int
     */
    int countDataByDeliveryDate(@Param("deliveryTime") LocalDate deliveryTime);

    List<DirectPurchaseInfo> listDataByDeliveryDate(@Param("deliveryTime") LocalDate deliveryTime,
                                                    @Param("pageIndex") Integer pageIndex,
                                                    @Param("pageSize")Integer pageSize);
}
