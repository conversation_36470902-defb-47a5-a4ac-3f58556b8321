package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.ExpandActivityTag;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ExpandActivityTagMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ExpandActivityTag record);

    int insertSelective(ExpandActivityTag record);

    ExpandActivityTag selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ExpandActivityTag record);

    int updateByPrimaryKey(ExpandActivityTag record);

    ExpandActivityTag selectOrderByMid(@Param("mId")Long mId, @Param("expandId")Long expandId);

    ExpandActivityTag selectByMid(@Param("mId")Long mId, @Param("expandId")Long expandId);

}