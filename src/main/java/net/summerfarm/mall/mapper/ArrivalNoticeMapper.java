package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.ArrivalNotice;
import org.springframework.stereotype.Repository;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by wjd on 2017/12/20.
 */

@Repository
public interface ArrivalNoticeMapper {

    void insert(ArrivalNotice arrivalNotice);

    List<ArrivalNotice> select(ArrivalNotice arrivalNotice);

    List<ArrivalNotice> selectBySku(ArrivalNotice arrivalNotice);

    int updateById(Integer id);

    /**
     * 根据sku列表，用户id，门店id查询该门店是否关注了到货通知
     * @param skuList sku列表
     * @param mid 用户id
     * @param storeNo 门店id
     * @return 到货通知列表
     */
    List<ArrivalNotice> selectBySkuListAndMidAndStoreNo(
        @Param("skuList") List<String> skuList,
        @Param("mid") Long mid,
        @Param("storeNo") Integer storeNo
    );
}
