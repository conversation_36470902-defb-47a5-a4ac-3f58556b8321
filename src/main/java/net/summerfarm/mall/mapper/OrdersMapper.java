package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.CategoryOrder;
import net.summerfarm.mall.model.domain.DeliveryPlan;
import net.summerfarm.mall.model.domain.FruitSales;
import net.summerfarm.mall.model.domain.Orders;
import net.summerfarm.mall.model.dto.order.DeliveryEvaluationDTO;
import net.summerfarm.mall.model.dto.order.OrderDeliveryDTO;
import net.summerfarm.mall.model.input.CheckInput;
import net.summerfarm.mall.model.vo.OrderVO;
import net.summerfarm.mall.model.vo.ProductInfoVO;
import net.summerfarm.mall.model.vo.TimingDeliveryVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Repository
public interface OrdersMapper {

    int insertSelective(Orders record);

    int updateByOrderNoSelective(Orders record);

    int countOrders(Long mId);

    int countOrderByDate(@Param("mId") Long mId, @Param("startDate")LocalDate startDate, @Param("endDate") LocalDate endDate);

    int countOrderByMid(Long mId);

    Orders selectOne(@Param("orderNo") String orderNo);

    OrderVO selectOrderVO(String orderNo);

    Orders selectByOrderNo(String orderNo);

    /**
     * 根据订单号查询订单(加锁)
     * @param orderNo
     * @return
     */
    Orders selectByOrderNoForUpdate(String orderNo);

    List<OrderVO> selectOrders(Map selectKeys);

    List<TimingDeliveryVO> selectTimingOrder(@Param("mId") Long mId, @Param("status") Short status);

    List<OrderVO> selectOrderList(@Param("mId") Long mId, @Param("orderStatus") Short orderStatus);

    List<OrderVO> selectOrder(String orderNo);

    int countOrder(@Param("mId") Long mId, @Param("status") Integer status, @Param("type") Integer type);

    int countOutTimes(@Param("mId") Long mId, @Param("monthStart") LocalDateTime monthStart);

    /**
     * 变更订单状态
     * @param orderNo 订单编号
     * @param aimStatus 目标状态
     * @param originalStatus 订单原状态
     * @return
     */
    int updateStatus(@Param("orderNo") String orderNo, @Param("aimStatus") int aimStatus, @Param("originalStatus") int originalStatus);

    /**
     * 变更订单自提标志
     * @param orderNo 订单编号
     * @return
     */
    int updateOutStock(@Param("orderNo") String orderNo);

    /**
     * 查询区间时间内消费总额
     * @param mId
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return
     */
    BigDecimal selectTotalPriceByMonth(@Param("mId") Long mId, @Param("beginTime") LocalDateTime beginTime, @Param("endTime") LocalDateTime endTime);

    List<Orders> unpaidOrders(@Param("status")Integer status,@Param("outTimes")Integer outTimes);

    List<String> selectByMid(@Param("mid") Long mid, @Param("type") Integer type,@Param("startDate")LocalDate startDate, @Param("endDate") LocalDate endDate);

    List<String> selectOrdersByMid(@Param("mid") Long mid,@Param("startDate")LocalDate startDate, @Param("endDate") LocalDate endDate);



    /**
    * 查询一段时间内的一天下多单且第一单有运费大于0的订单
    */
    List<DeliveryPlan> queryOrderByDelivery(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
    * 查询订单信息
    */
    List<OrderVO> queryOrderByContactId(@Param("contactId") Long contactId, @Param("deliveryTime") LocalDate deliveryTime);


    //查询每天第一单且第一单运费为0的订单过滤使用了券和运费卡 本部仓
    List<OrderVO> selectNoDeliveryOrder(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    OrderVO selectByOrderyNo(String orderNo);

    List<Orders> selectOrderByMid(@Param("mId") Long mId ,@Param("areaNo") int areaNo, @Param("year") String year);

    List<CategoryOrder> selectOrderByCategory(@Param("mId") Long mId ,@Param("areaNo") int areaNo,@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 查询区间时间内 消费总额 订单状态为 2，3，6
     * @param mId
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return
     */
    BigDecimal selectSumTotalPrice(@Param("mId") Long mId, @Param("beginTime") LocalDateTime beginTime, @Param("endTime") LocalDateTime endTime,@Param("mSize") String mSize);

    /**
    * 查询区间时间内 未到货售后总金额
    */
    BigDecimal selectSumAfterPrice(@Param("mId") Long mId, @Param("beginTime") LocalDateTime beginTime, @Param("endTime") LocalDateTime endTime,@Param("mSize") String mSize);

    Orders selectUnPayDiscountOrder(@Param("mId") Long mId, @Param("discountCardId") Integer id);

    int selectSumOrders(@Param("mId") Long mId,@Param("status") Integer status);

    List<Long> selectPeriodMid(@Param("sTime") LocalDate sTime,@Param("eTime") LocalDate eTime);

    List<FruitSales> selectIsFruit(String orderNo);

    FruitSales selectIsFruitByItemId(Long orderItemId);
    /**
     * 查询待支付充值订单
     * @param mId mId
     * @param configId 充值档位id
     * @param customAmount 自定义充值金额
     * @return 订单信息
     */
    Orders selectUnPayRechargeOrder(@Param("mId") Long mId, @Param("configId") Integer configId, @Param("customAmount") BigDecimal customAmount);

    /**
     * 未支付虚拟商品订单
     * @return 订单信息
     */
    List<Orders> unpaidVirtualOrders();

    /**
     * 查询最近X天该客户购买过的sku
     * @param collocationIdList sku集合
     * @param mId 登录人客户id
     * @param recentConsumeTime N天前的时间
     * @return
     */
    List<String> selectIsFirstConsume(@Param("list") List<String> collocationItemList, @Param("mId") Long mId, @Param("recentConsumeTime") LocalDateTime recentConsumeTime);

    /**
     * 查看已购sku
     * @param productSkuInfoVOS sku集合
     * @param mId 用户ID
     * @return 已购sku
     */
    List<String> selectSkuOrderd(@Param("productSkuInfoVOS") List<ProductInfoVO> productSkuInfoVOS,@Param("mId") Long mId);

    /**
     * 检查是否是茶百道门店下安佳淡奶油订单
     * @param input
     * @return
     */
    Boolean checkIsCBD(CheckInput input);

    /**
     * 一定时间内首次下单
     * @param mId mId
     * @param sku sku
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 是否首次下单
     */
    boolean hasOrderInPeriod(@Param("mId") Long mId, @Param("sku") String sku, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 一定时间内首次下单
     * @param mId mId
     * @param skuList skuList
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 是否首次下单
     */
    Set<String> hasOrderInPeriodForSkuList(@Param("mId") Long mId, @Param("list") List<String> skuList, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 当天是否首次下单
     * @param mId mId
     * @param orderNo orderNo
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 是否首次下单
     */
    boolean hasOrderByMid(@Param("mId") Long mId, @Param("orderNo") String orderNo, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 用户t+1是否有省心送配送计划
     * @param contactIds contactIds
     * @param localDate 指定配送时间
     * @return 是否首次下单
     */
    boolean hasDeliveryPlanByTime(@Param("contactIds") List<Long> contactIds, @Param("localDate") LocalDate localDate);

    List<Orders> listTimeOutOrders();

    int countOrdersByType(@Param("mId") Long mId);

    List<OrderDeliveryDTO> orderDeliveryList(@Param("mId") Long mId, @Param("deliveryTime") LocalDate deliveryTime);

    Integer selectAreaNoByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 通过订单号列表查询订单
     *
     * @param orderNoList 订单号列表
     * @return {@link List}<{@link Orders}>
     */
    List<Orders> listOrderByOrderNoList(@Param("orderNoList") List<String> orderNoList);

    /**
     * 根据商户编号获取未完成订单数量-待配送、待收货
     * @param mId 客户编号
     * @return 数量
     */
    Integer getUnfilledOrderByMid(Long mId);

    List<TimingDeliveryVO> listWarningTimingOrder(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    List<TimingDeliveryVO> listTimingOrder(@Param("mId") Long mId, @Param("status") Integer status);

    /**
     * 查询省心送未完成的订单
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 订单列表
     */
    List<String> getNeedReturnTimingOrder(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 查询用户评价列表
     * @param deliveryDate deliveryDate
     * @param mId mId
     * @param deliveryEvaluationStatus deliveryEvaluationStatus
     * @return DeliveryPlan
     */
    List<DeliveryEvaluationDTO> selectDeliveryEvaluationList(@Param("deliveryDate") LocalDate deliveryDate, @Param("deliveryEvaluationStatus") Integer deliveryEvaluationStatus, @Param("mId") Long mId);


    /**
     * 根据mid查询省心送未完成的订单
     * @param oldOrderTime 开始时间
     * @param mId mId
     * @return 订单列表
     */
    List<String> getNeedReturnTimingOrderForView(@Param("oldOrderTime") LocalDateTime oldOrderTime,@Param("mId") Long mId);

    /**
     * 超过48小时配送计划表未设置配送计划的订单
     * @param mId mId
     * @return 订单列表
     */
    List<String> selectUnscheduledOrders(@Param("mId") Long mId);

    /**
     * 查询省心送退款短信触达订单
     * @param orderTime orderTime
     * @return 订单列表
     */
    List<String> getNeedReturnTimingOrderForSms(@Param("orderTime") LocalDateTime orderTime);

    OrderVO exitByMidSkuOder(@Param("mId")Long mId, @Param("skus")List<String> skus);

    List<OrderVO> listAllTimeOutError(@Param("orderTime") LocalDateTime orderTime);

    /**
     * 刷数据查询所有未完成的省心送订单
     *
     * @return {@link List}<{@link Orders}>
     */
    List<Orders> selectAllUncompletedOrders();

    Set<Long> getOrderRecordBySpu(@Param("mIdList") List<Long> mIdList, @Param("pdNo") String sampleSpu);

    List<Long> selectContactIdListByOrderTimeAndMId(@Param("mId") Long mId,
                                                    @Param("startOrderTime") LocalDateTime startOrderTime,
                                                    @Param("endOrderTime") LocalDateTime endOrderTime);


    /**
     * 根据订单号查询销售主体
     * @param orderNos
     * @return
     */
    List<String> querySellingEntityByNos(@Param("orderNos") Collection<String> orderNos);

}
