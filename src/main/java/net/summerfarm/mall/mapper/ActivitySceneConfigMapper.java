package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.market.ActivitySceneConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface ActivitySceneConfigMapper {
    
    int deleteByPrimaryKey(Long id);

    
    int insert(ActivitySceneConfig record);

    
    int insertSelective(ActivitySceneConfig record);

    
    ActivitySceneConfig selectByPrimaryKey(Long id);

    
    int updateByPrimaryKeySelective(ActivitySceneConfig record);

    
    int updateByPrimaryKey(ActivitySceneConfig record);

    ActivitySceneConfig selectByInfoId(@Param("basicInfoId") Long basicInfoId);
}