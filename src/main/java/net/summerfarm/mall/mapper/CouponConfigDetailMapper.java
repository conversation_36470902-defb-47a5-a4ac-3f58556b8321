package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.CouponConfigDetail;

import java.util.List;

public interface CouponConfigDetailMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(CouponConfigDetail record);

    int insertSelective(CouponConfigDetail record);

    CouponConfigDetail selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(CouponConfigDetail record);

    int updateByPrimaryKey(CouponConfigDetail record);

    List<CouponConfigDetail> selectByConfigId(Integer configId);
}