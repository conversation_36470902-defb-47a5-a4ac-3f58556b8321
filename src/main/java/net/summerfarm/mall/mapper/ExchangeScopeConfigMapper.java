package net.summerfarm.mall.mapper;

import java.util.List;
import net.summerfarm.mall.model.domain.ExchangeScopeConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface ExchangeScopeConfigMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ExchangeScopeConfig record);

    int insertSelective(ExchangeScopeConfig record);

    ExchangeScopeConfig selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ExchangeScopeConfig record);

    List<ExchangeScopeConfig> listByBaseInfoIds(@Param("list") List<Long> baseInfoIds);

    ExchangeScopeConfig selectById(@Param("id") Long id);

}