package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.InvoiceConfig;
import net.summerfarm.mall.model.vo.invoice.InvoiceConfigVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @title: InvoiceConfigMapper
 * @date 2021/11/1716:05
 */
@Repository
public interface InvoiceConfigMapper {

    /**
     * 查询用户工商名称
     *
     * @param id mid
     * @return InvoiceConfig
     */
    InvoiceConfig selectBusiness(Long id);

    /**
     * 查询工商名称
     *
     * @param id
     * @return InvoiceConfig
     */
    InvoiceConfig selectById(Long id);

    /**
     * 查询大客户工商信息是否已经存在
     *
     * @param invoiceConfig 大客户抬头配置和普通门店客户抬头配置
     * @return InvoiceConfig
     */
    InvoiceConfig selectAdminId(InvoiceConfig invoiceConfig);

    /**
     * 插入品牌抬头配置
     *
     * @param invoiceConfig 大客户抬头配置
     * @return int
     */
    int insertAdmin(InvoiceConfig invoiceConfig);

    /**
     * 插入门店抬头配置
     *
     * @param invoiceConfig 普通门店客户抬头配置
     * @return int
     */
    int insertMerchant(InvoiceConfig invoiceConfig);

    /**
     * 重置默认标志
     *
     * @param mId m id
     * @return int
     */
    int resetDefaultFlag(@Param("mId") Long mId);

    /**
     * 修改门店发票抬头
     *
     * @param invoiceConfig
     * @return
     */
    int updateMerchant(InvoiceConfig invoiceConfig);

    /**
     * 修改门店发票抬头
     *
     * @param invoiceConfig
     * @return
     */
    int update(InvoiceConfig invoiceConfig);

    /**
     * 根据入参的参数获取到对应的抬头配置
     *
     * @param adminId:     大客户id
     * @param mId          门店id
     * @param invoiceTitle 发票标题
     * @param taxNumber    税务号码
     * @return {@link List}<{@link InvoiceConfig}>
     */
    List<InvoiceConfig> selectByType(@Param("adminId")Integer adminId, @Param("mId")Long mId,@Param("invoiceTitle")String invoiceTitle,@Param("taxNumber")String taxNumber);


    /**
     * 更新默认标志
     *
     * @param invoiceId   发票编号
     * @param defaultFlag 默认国旗
     * @return int
     */
    int updateDefaultFlag(@Param("id")Long invoiceId,@Param("defaultFlag") Integer defaultFlag);

    /**
     * 删除抬头
     *
     * @param id id
     */
    void deleteById(@Param("id") Long id);
}
