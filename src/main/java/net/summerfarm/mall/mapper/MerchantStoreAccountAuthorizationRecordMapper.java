package net.summerfarm.mall.mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

import net.summerfarm.mall.model.domain.merchant.MerchantStoreAccountAuthorizationRecord;

/**
* @description: ${description}
* @author: George
* @date: 2025-01-13
**/
public interface MerchantStoreAccountAuthorizationRecordMapper {
    int deleteByPrimaryKey(Long id);

    int insert(MerchantStoreAccountAuthorizationRecord record);

    int insertSelective(MerchantStoreAccountAuthorizationRecord record);

    MerchantStoreAccountAuthorizationRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MerchantStoreAccountAuthorizationRecord record);

    int updateByPrimaryKey(MerchantStoreAccountAuthorizationRecord record);

    MerchantStoreAccountAuthorizationRecord selectAllByAccountId(@Param("accountId")Long accountId);

    /**
     * 根据openId查询
     * @param openid
     * @return
     */
    MerchantStoreAccountAuthorizationRecord selectByOpenId(String openid);
}