package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.MessageOrder;
import org.springframework.stereotype.Repository;

@Repository
public interface MessageOrderMapper {
    int deleteByPrimaryKey(Long smId);

    int insert(MessageOrder record);

    int insertSelective(MessageOrder record);

    MessageOrder selectByPrimaryKey(Long smId);

    int updateByPrimaryKeySelective(MessageOrder record);

    int updateByPrimaryKeyWithBLOBs(MessageOrder record);

    int updateByPrimaryKey(MessageOrder record);
}