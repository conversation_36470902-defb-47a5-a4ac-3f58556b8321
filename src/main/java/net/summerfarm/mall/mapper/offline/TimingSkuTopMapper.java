package net.summerfarm.mall.mapper.offline;

import java.util.List;
import net.summerfarm.mall.model.domain.offline.TimingSkuTop;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface TimingSkuTopMapper {

    TimingSkuTop selectByPrimaryKey(Long id);

    List<TimingSkuTop> listByAreaNo(@Param("areaNo") Integer areaNo, @Param("dateFlag") Integer dateFlag);
}