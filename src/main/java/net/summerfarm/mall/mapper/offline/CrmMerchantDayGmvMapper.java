package net.summerfarm.mall.mapper.offline;

import net.summerfarm.mall.model.domain.CrmMerchantDayGmv;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2022/4/18  14:26
 */
@Repository
public interface CrmMerchantDayGmvMapper{
        /**
         * 根据id查询
         * @param id id
         * @return 商户每日gmv信息
         */
    List<CrmMerchantDayGmv> selectByPrimaryKey(Long id);
}
