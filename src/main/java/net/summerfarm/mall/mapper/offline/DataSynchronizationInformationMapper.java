package net.summerfarm.mall.mapper.offline;

import net.summerfarm.mall.model.domain.DataSynchronizationInformation;
import org.springframework.stereotype.Repository;

@Repository
public interface DataSynchronizationInformationMapper {
    int deleteByPrimaryKey(Long id);

    int insert(DataSynchronizationInformation record);

    int insertSelective(DataSynchronizationInformation record);

    DataSynchronizationInformation selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DataSynchronizationInformation record);

    int updateByPrimaryKey(DataSynchronizationInformation record);

    /**
     * 根据表名获取数据更新信息
     * @param tableName 表名
     * @return 数据更新信息
     */
    DataSynchronizationInformation selectByTableName(String tableName);
}