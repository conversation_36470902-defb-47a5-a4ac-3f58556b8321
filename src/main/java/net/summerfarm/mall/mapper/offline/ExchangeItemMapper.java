package net.summerfarm.mall.mapper.offline;

import java.util.List;
import net.summerfarm.mall.model.domain.ExchangeItem;
import net.summerfarm.mall.model.dto.market.exchange.ExchangeItemDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface ExchangeItemMapper {

    List<ExchangeItem> selectByMid(@Param("mId") Long mId, @Param("dateFlag") Integer dateFlag);

    List<ExchangeItem> selectByQuery(@Param("itemDTO") ExchangeItemDTO exchangeItemDTO);

}