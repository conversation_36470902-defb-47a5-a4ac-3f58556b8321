package net.summerfarm.mall.mapper.offline;

import net.summerfarm.mall.model.domain.ExpandActivityProducts;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ExpandActivityProductsMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ExpandActivityProducts record);

    int insertSelective(ExpandActivityProducts record);

    ExpandActivityProducts selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ExpandActivityProducts record);

    int updateByPrimaryKey(ExpandActivityProducts record);

    /**
     * 活动商品枚举-流失风险商品
     */
    List<String> selectByChurnRisk(@Param("mId")Long mId,@Param("dateFlag")Integer dateFlag);

    /**
     * 活动商品枚举-召回商品
     */
    List<String> selectByRecall(@Param("mId")Long mId,@Param("dateFlag")Integer dateFlag);

    /**
     * 活动商品枚举-拉新商品
     */
    List<String> selectByPullNew(@Param("mId")Long mId,@Param("dateFlag")Integer dateFlag);
}