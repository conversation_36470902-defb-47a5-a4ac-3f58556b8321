package net.summerfarm.mall.mapper.offline;

import net.summerfarm.mall.model.domain.CommonlyRecommended;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CommonlyRecommendedMapper {
    int deleteByPrimaryKey(Long id);

    int insert(CommonlyRecommended record);

    int insertSelective(CommonlyRecommended record);

    CommonlyRecommended selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CommonlyRecommended record);

    int updateByPrimaryKey(CommonlyRecommended record);

    /**
     * 获取常用推荐商品
     * @param mId 用户ID
     * @param dateFlag 同步时间标记
     * @return 常用推荐商品
     */
    List<CommonlyRecommended> selectCommonlyRecommended(@Param("mId")Long mId, @Param("dateFlag")Integer dateFlag);
}