package net.summerfarm.mall.mapper;


import net.summerfarm.mall.model.domain.ContactOperateLog;

import java.util.List;

public interface ContactOperateLogMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ContactOperateLog record);

    int insertSelective(ContactOperateLog record);

    ContactOperateLog selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ContactOperateLog record);

    int updateByPrimaryKey(ContactOperateLog record);

    List<ContactOperateLog> selectByContactId(Long contactId);
}