package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.CirclePeopleRule;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CirclePeopleRuleMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(CirclePeopleRule record);

    int insertSelective(CirclePeopleRule record);

    CirclePeopleRule selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(CirclePeopleRule record);

    int updateByPrimaryKey(CirclePeopleRule record);

    int selectAct(@Param("id") Integer id,@Param("mId") Long mId,@Param("type") Integer type);

    List<Integer> selectActMid(@Param("ids") List<Integer> ids,@Param("mId") Long mId,@Param("type") Integer type);

    List<CirclePeopleRule> selectByRuleIdType(@Param("ids") List<Integer> ids,@Param("type") Integer type);
}