package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.FrontCategoryToArea;

public interface FrontCategoryToAreaMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(FrontCategoryToArea record);

    int insertSelective(FrontCategoryToArea record);

    FrontCategoryToArea selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(FrontCategoryToArea record);

    int updateByPrimaryKey(FrontCategoryToArea record);
}