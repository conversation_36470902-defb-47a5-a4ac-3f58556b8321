package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.SortCombination;
import org.apache.ibatis.annotations.Param;

public interface SortCombinationMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(SortCombination record);

    int insertSelective(SortCombination record);

    SortCombination selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(SortCombination record);

    int updateByPrimaryKey(SortCombination record);

    SortCombination selectByAreaNo(@Param("type") Integer type, @Param("areaNo") Integer areaNo);
}