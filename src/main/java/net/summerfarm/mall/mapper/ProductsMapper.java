package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.Products;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository
public interface ProductsMapper {

    Products selectByPrimaryKey(Long pdId);

    /**
     * 用户可见商品pdId
     * @param areaNo 城市编号
     * @param adminId 大客户id
     * @param direct 1、账期 2、现结
     * @param typeAdminId 代仓所属大客户id
     * @param msize  单店/大客户
     * @return
     */
    List<Long> accessiblePdId(@Param("areaNo") Integer areaNo, @Param("adminId") Integer adminId, @Param("direct") Integer direct,@Param(value = "typeAdminId") Integer typeAdminId,@Param(value = "msize") String msize);

    /**
     * 查询商品数据
     * @param sku
     * @return
     */
    Products queryBySku(String sku);

    /**
     * 根据sku 批量查询
     * @return
     */
    List<Products> listBySkuList(@Param ("skuList") List<String> skuList);

    /**
     * 根据pdId 批量查询
     * @param pdIds
     * @return
     */
    List<Products> selectByPdIds(@Param("pdIds") Collection<Long> pdIds);
}
