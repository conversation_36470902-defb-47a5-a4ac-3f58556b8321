package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.LuckyDrawActivityPrize;

import java.util.List;


public interface LuckyDrawActivityPrizeMapper {

    int deleteByPrimaryKey(Long id);

    int insert(LuckyDrawActivityPrize record);

    int insertSelective(LuckyDrawActivityPrize record);

    LuckyDrawActivityPrize selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(LuckyDrawActivityPrize record);

    int updateByPrimaryKey(LuckyDrawActivityPrize record);

    List<LuckyDrawActivityPrize> selectByActivityId(Long activityId);

    List<LuckyDrawActivityPrize> selectByEquityPackageId(Long equityPackageId);
}