package net.summerfarm.mall.constant;

import java.util.Arrays;
import java.util.List;

/**
 * @Package: net.summerfarm.mall.constant
 * @Description:
 * @author: <EMAIL>
 * @Date: 2022-04-25
 */
public interface Constants {

    interface AreaStoreConstant{
        // 默认在线库存
        Integer defaultOnlineQuantity = 0;
    }

    interface adminIdConstant{
        // 正式环境喜茶大客户
        Integer PRO_XICHA_ADMIN_ID = 1507;
        // 测试环境鲜沐科技大客户
        Integer TEST_ADMIN_ID = 1026;
    }

    interface DeliveryConstant{
        // 配送完成
        Integer DELIVERY_FINISH = 6;
    }

    interface RedisConstant {
        Long EXPIRE_TIME = 30L;
    }

    /**
     * 鲜沐tenantId
     */
    Long XIANMU_TENANT_ID = 1L;


    String NO_CONTACT = "NO_CONTACT";


    /**
     * 防止爬虫商品默认库存数值
     */
    Integer ONLINE_QUANTITY = 10000;

    List<String> kgList = Arrays.asList ("kg","KG","Kg","kG");

    /**
     * REVIEW_NOT_PASS = 核验未通过
     */
    public static final String VERIFY_NOT_PASS = "VERIFY_NOT_PASS";

    /**
     * INCONSISTENCY_IN_AREA_NO = 运营区域不一致
     */
    public static final String INCONSISTENCY_IN_AREA_NO = "INCONSISTENCY_IN_AREA_NO";



    /**
     * 是否鲜沐租户
     * @param tenantId
     * @return
     */
    static Boolean isXm(Long tenantId) {
        if (tenantId != null && tenantId > XIANMU_TENANT_ID) {
            return false;
        }
        return true;
    }
}
