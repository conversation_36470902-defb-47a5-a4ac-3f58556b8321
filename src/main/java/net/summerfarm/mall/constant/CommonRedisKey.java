package net.summerfarm.mall.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-mall
 * @description
 * @date 2024/1/10 13:49:39
 */
public interface CommonRedisKey {

    /**
     * SaaS根据areaNo查询围栏下面对应的省市区缓存prefixKey
     */
    String DUBBO_ADDRESS_INFO = "dubbo_address_Info:";


    /**
     * 售后相关prefixKey
     */
    interface AfterSaleLock{
        String AFTER_SALE = "lock:after-sale:";
    }

    /**
     * 配送相关prefixKey
     */
    interface DeliveryPlanLock{
        String DELIVERY_PLAN = "lock:delivery-plan:";
    }

    /**
     * 订单相关prefixKey
     */
    interface OrderLock{
        String ORDER_CONFIRM_RECEIPT = "lock:order_confirm_receipt:";
    }


    /**
     * 分布式锁相关prefixKey
     */
    interface Lock{
        String RECEIVE_COUPON = "coupon_receive:";
        String PAY_NOTIFY_FULL_RETURN = "lock:pay_notify_full_return:";
        String ORDER_MISSING_WEIGHT = "lock:order_missing_weight:";
    }

    /**
     * 缓存相关prefixKey
     */
    interface Cache{
        String CACHE_CATEGORY = "CACHE:CATEGORY:";
        String CACHE_AREA = "CACHE:AREA:";

        String CACHE_TOPIC_PAGE_DETAIL = "cache:topic_page_detail:";
        String CACHE_DEFAULT_CONTACT = "cache:default_contact:";

        String CACHE_CONFIG = "cache:config_value:";

        String CACHE_MERCHANT_POOL_INFO = "cache:merchant_pool_info:";

        String CACHE_CORE_PRODUCT_BASE_PRICE_INFO = "cache:core_product_base_price_info:";
    }
}
