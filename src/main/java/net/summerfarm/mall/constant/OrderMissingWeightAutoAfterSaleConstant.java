package net.summerfarm.mall.constant;

import net.summerfarm.enums.AfterSaleHandleType;
import net.summerfarm.mall.enums.AfterSaleDeliveryedEnum;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2024-09-20
 **/
public class OrderMissingWeightAutoAfterSaleConstant {

    /**
     * 商品数量不符
     */
    public static final String AMOUNT_INCONSISTENCY = "商品数量不符";

    /**
     * 单位克
     */
    public static final String GRAMS_UNIT = "g";

    /**
     * 申请备注
     */
    public static final String APPLY_REMARK = "称重后自动退差价";

    /**
     * 已到货/未到货
     */
    public static final Integer BROKEN_DELIVERY_TYPE = AfterSaleDeliveryedEnum.BROKEN.getType();

    /**
     * 退款处理类型
     */
    public static final Integer REFUND_HANDLE_TYPE = AfterSaleHandleType.REFUND.getType();

    /**
     * 正常品
     */
    public static final Integer SUIT_ID = 0;

    /**
     * 普通售后
     */
    public static final Integer NORMAL_AFTER_SALE = 0;

    /**
     * 申请售后分类
     */
    public static final String APPLY_SECONDARY_REMARK = "买手&供应商（POP）,少称";
}
