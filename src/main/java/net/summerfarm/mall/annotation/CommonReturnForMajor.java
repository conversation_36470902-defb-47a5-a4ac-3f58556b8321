package net.summerfarm.mall.annotation;

import javax.xml.bind.annotation.XmlType;
import java.lang.annotation.*;

/**
 * 大客户请求统一返回注解
 * <AUTHOR>
 */
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface CommonReturnForMajor {
    /**
     * 返回空数据：t、成功的空数据 f、返回error
     * @return
     */
    boolean successEmpty() default true;

    /**
     * 错误消息
     * @return
     */
    String errorMsg() default "";
}
