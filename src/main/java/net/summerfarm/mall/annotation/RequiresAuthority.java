package net.summerfarm.mall.annotation;

import net.summerfarm.mall.enums.MemberEnum;

import java.lang.annotation.*;

/**
 * @Package: net.summerfarm.common.annotation
 * @Description: 权限注解
 * @author: <EMAIL>
 * @Date: 2016/10/13
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequiresAuthority {
    MemberEnum[] membershipGrade() default MemberEnum.ORDINARY;
}
