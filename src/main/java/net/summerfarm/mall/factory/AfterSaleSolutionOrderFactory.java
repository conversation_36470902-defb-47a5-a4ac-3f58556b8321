package net.summerfarm.mall.factory;

import net.summerfarm.enums.AfterSaleHandleType;
import net.summerfarm.enums.OrderTypeEnum;
import net.summerfarm.mall.after.service.AfterSaleOrderHandleTypeSolution;
import net.summerfarm.mall.after.service.AfterSaleOrderSkuTypeSolution;
import net.summerfarm.mall.after.service.AfterSaleOrderTypeSolution;
import net.summerfarm.mall.after.service.impl.*;
import net.summerfarm.mall.after.template.*;
import net.summerfarm.mall.after.template.impl.AuditAfterSolution;
import net.summerfarm.mall.after.template.impl.CreatAfterSolution;
import net.summerfarm.mall.after.template.impl.HandleAfterSolution;
import net.summerfarm.mall.enums.InventoryTypeEnum;
import net.summerfarm.mall.enums.OrderTypeAfterSolutionEnum;
import net.summerfarm.mall.enums.SkuTypeAfterSolutionEnum;
import net.summerfarm.mall.mapper.InventoryMapper;
import net.summerfarm.mall.model.domain.Inventory;
import net.summerfarm.mall.model.domain.Orders;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022.07.01
 * 工厂策略类
 */
@Component
public class AfterSaleSolutionOrderFactory {
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private NormalOrderAfterSolution normalOrderAfterSolution;
    @Resource
    private TimingOrderAfterSolution timingOrderAfterSolution;
    @Resource
    private SkuAgentSolution skuAgentSolution;
    @Resource
    private BlockIncomingBillsHandleTypeAfterSolution blockIncomingBillsHandleTypeAfterSolution;
    @Resource
    private BlockRefundsHandleTypeAfterSolution blockRefundsHandleTypeAfterSolution;
    @Resource
    private CouponHandleTypeAfterSolution couponHandleTypeAfterSolution;
    @Resource
    private DeliveryGoodsHandleTypeAfterSolution deliveryGoodsHandleTypeAfterSolution;
    @Resource
    private EntryBillHandleTypeAfterSolution entryBillHandleTypeAfterSolution;
    @Resource
    private ExchangeGoodsHandleTypeAfterSolution exchangeGoodsHandleTypeAfterSolution;
    @Resource
    private RefundEntryBillHandleTypeAfterSolution refundEntryBillHandleTypeAfterSolution;
    @Resource
    private RefundHandleTypeAfterSolution refundHandleTypeAfterSolution;
    @Resource
    private RefuseBillHandleTypeAfterSolution refundUseBillHandleTypeAfterSolution;
    @Resource
    private ReturnGoodsHandleTypeAfterSolution returnGoodsHandleTypeAfterSolution;
    @Resource
    private RefuseHandleTypeAfterSolution refuseHandleTypeAfterSolution;
    @Resource
    private ReturnShippingHandleTypeAfterSolution refundShippingHandleTypeAfterSolution;
    @Resource
    private ReturnShippingBillHandleTypeAfterSolution returnShippingBillHandleTypeAfterSolution;
    @Resource
    private AfterSaleOrderAuditHandler afterSaleOrderAuditHandler;
    @Resource
    private AfterSaleOrderCreatHandler afterSaleOrderCreatHandler;
    @Resource
    private AfterSaleOrderHandleHandler afterSaleOrderHandleHandler;

    private Map<Integer, AfterSaleOrderHandleTypeSolution> handleTypeSolutionMap = new HashMap<>();
    private Map<Integer, AfterSaleOrderSkuTypeSolution> skuTypeSolutionMap = new HashMap<>();
    private Map<Integer, AfterSaleOrderTypeSolution> orderTypeSolutionMap = new HashMap<>();

    /**
     * 初始化
     */
    @PostConstruct
    public void init() {
        handleTypeSolutionMap.put(AfterSaleHandleType.REFUND.getType(), refundHandleTypeAfterSolution);
        handleTypeSolutionMap.put(AfterSaleHandleType.COUPON.getType(), couponHandleTypeAfterSolution);
        handleTypeSolutionMap.put(AfterSaleHandleType.ENTRY_BILL.getType(), entryBillHandleTypeAfterSolution);
        handleTypeSolutionMap.put(AfterSaleHandleType.REFUND_GOODS.getType(), returnGoodsHandleTypeAfterSolution);
        handleTypeSolutionMap.put(AfterSaleHandleType.REFUND_ENTRY_BILL.getType(), refundEntryBillHandleTypeAfterSolution);
        handleTypeSolutionMap.put(AfterSaleHandleType.EXCHANGE_GOODS.getType(), exchangeGoodsHandleTypeAfterSolution);
        handleTypeSolutionMap.put(AfterSaleHandleType.DELIVERY_GOODS.getType(), deliveryGoodsHandleTypeAfterSolution);
        handleTypeSolutionMap.put(AfterSaleHandleType.REFUSE.getType(), refuseHandleTypeAfterSolution);
        handleTypeSolutionMap.put(AfterSaleHandleType.REFUSE_BILL.getType(), refundUseBillHandleTypeAfterSolution);
        handleTypeSolutionMap.put(AfterSaleHandleType.BLOCK_REFUNDS.getType(), blockRefundsHandleTypeAfterSolution);
        handleTypeSolutionMap.put(AfterSaleHandleType.BLOCK_INCOMING_BILLS.getType(), blockIncomingBillsHandleTypeAfterSolution);
        handleTypeSolutionMap.put(AfterSaleHandleType.RETURN_SHIPPING.getType(), refundShippingHandleTypeAfterSolution);
        handleTypeSolutionMap.put(AfterSaleHandleType.RETURN_SHIPPING_BILL.getType(), returnShippingBillHandleTypeAfterSolution);

        skuTypeSolutionMap.put(SkuTypeAfterSolutionEnum.AGENT_PRODUCT.getType(), skuAgentSolution);

        orderTypeSolutionMap.put(OrderTypeAfterSolutionEnum.NORMAL.getType(), normalOrderAfterSolution);
        orderTypeSolutionMap.put(OrderTypeAfterSolutionEnum.TIMING.getType(), timingOrderAfterSolution);
    }

    public CreatAfterSolution getCreatSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders){
        BaseCreatAfterSaleSolution afterSaleSolution = getBaseCreatAfterSaleSolution(afterSaleOrderVO, orders);

        CreatAfterSolution creatAfterSolution = new CreatAfterSolution();
        creatAfterSolution.setHandleTypeSolution(afterSaleSolution.getHandleTypeSolution());
        creatAfterSolution.setOrderTypeSolution(afterSaleSolution.getOrderTypeSolution());
        creatAfterSolution.setSkuTypeSolution(afterSaleSolution.getSkuTypeSolution());
        creatAfterSolution.setAfterSaleOrderCreatHandler(afterSaleOrderCreatHandler);
        return creatAfterSolution;
    }

    public HandleAfterSolution getHandleSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders){
        BaseHandleAfterSaleSolution afterSaleSolution = getBaseHandleAfterSaleSolution(afterSaleOrderVO, orders);

        HandleAfterSolution handleAfterSolution = new HandleAfterSolution();
        handleAfterSolution.setHandleTypeSolution(afterSaleSolution.getHandleTypeSolution());
        handleAfterSolution.setOrderTypeSolution(afterSaleSolution.getOrderTypeSolution());
        handleAfterSolution.setSkuTypeSolution(afterSaleSolution.getSkuTypeSolution());
        handleAfterSolution.setAfterSaleOrderHandleHandler(afterSaleOrderHandleHandler);
        return handleAfterSolution;
    }

    public AuditAfterSolution getAuditSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders){
        BaseAuditAfterSaleSolution afterSaleSolution = getBaseAuditAfterSaleSolution(afterSaleOrderVO, orders);

        AuditAfterSolution auditAfterSolution = new AuditAfterSolution();
        auditAfterSolution.setHandleTypeSolution(afterSaleSolution.getHandleTypeSolution());
        auditAfterSolution.setOrderTypeSolution(afterSaleSolution.getOrderTypeSolution());
        auditAfterSolution.setSkuTypeSolution(afterSaleSolution.getSkuTypeSolution());
        auditAfterSolution.setAfterSaleOrderAuditHandler(afterSaleOrderAuditHandler);
        return auditAfterSolution;
    }

    private BaseCreatAfterSaleSolution getBaseCreatAfterSaleSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {
        BaseCreatAfterSaleSolution baseAfterSaleSolution = new BaseCreatAfterSaleSolution();
        //设置订单校验类型
        if(orders.getType().equals(OrderTypeEnum.TIMING.getId())){
            baseAfterSaleSolution.setOrderTypeSolution(orderTypeSolutionMap.get(OrderTypeEnum.TIMING.getId()));
        }else {
            baseAfterSaleSolution.setOrderTypeSolution(orderTypeSolutionMap.get(OrderTypeAfterSolutionEnum.NORMAL.getType()));
        }

        //根据SKU进行校验
        Inventory inventory = inventoryMapper.selectBySku(afterSaleOrderVO.getSku());
        if (inventory != null){
            if (inventory.getType().equals(InventoryTypeEnum.AGENT_PRODUCT.getType())){
                baseAfterSaleSolution.setSkuTypeSolution(skuTypeSolutionMap.get(SkuTypeAfterSolutionEnum.AGENT_PRODUCT.getType()));
            }else {
                baseAfterSaleSolution.setSkuTypeSolution(skuTypeSolutionMap.get(SkuTypeAfterSolutionEnum.SELF_OPERATED_GOODS.getType()));
            }
        }

        //根据售后服务类型进行处理
        baseAfterSaleSolution.setHandleTypeSolution(handleTypeSolutionMap.get(afterSaleOrderVO.getHandleType()));

        return baseAfterSaleSolution;
    }

    private BaseHandleAfterSaleSolution getBaseHandleAfterSaleSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {
        BaseHandleAfterSaleSolution baseAfterSaleSolution = new BaseHandleAfterSaleSolution();
        //设置订单校验类型
        if(orders.getType().equals(OrderTypeEnum.TIMING.getId())){
            baseAfterSaleSolution.setOrderTypeSolution(orderTypeSolutionMap.get(OrderTypeEnum.TIMING.getId()));
        }else {
            baseAfterSaleSolution.setOrderTypeSolution(orderTypeSolutionMap.get(OrderTypeAfterSolutionEnum.NORMAL.getType()));
        }

        //根据SKU进行校验
        Inventory inventory = inventoryMapper.selectBySku(afterSaleOrderVO.getSku());
        if (inventory != null){
            if (inventory.getType().equals(InventoryTypeEnum.AGENT_PRODUCT.getType())){
                baseAfterSaleSolution.setSkuTypeSolution(skuTypeSolutionMap.get(SkuTypeAfterSolutionEnum.AGENT_PRODUCT.getType()));
            }else {
                baseAfterSaleSolution.setSkuTypeSolution(skuTypeSolutionMap.get(SkuTypeAfterSolutionEnum.SELF_OPERATED_GOODS.getType()));
            }
        }

        //根据售后服务类型进行处理
        baseAfterSaleSolution.setHandleTypeSolution(handleTypeSolutionMap.get(afterSaleOrderVO.getHandleType()));

        return baseAfterSaleSolution;
    }

    private BaseAuditAfterSaleSolution getBaseAuditAfterSaleSolution(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {
        BaseAuditAfterSaleSolution baseAfterSaleSolution = new BaseAuditAfterSaleSolution();
        //设置订单校验类型
        if(orders.getType().equals(OrderTypeEnum.TIMING.getId())){
            baseAfterSaleSolution.setOrderTypeSolution(orderTypeSolutionMap.get(OrderTypeEnum.TIMING.getId()));
        }else {
            baseAfterSaleSolution.setOrderTypeSolution(orderTypeSolutionMap.get(OrderTypeAfterSolutionEnum.NORMAL.getType()));
        }

        //根据SKU进行校验
        Inventory inventory = inventoryMapper.selectBySku(afterSaleOrderVO.getSku());
        if (inventory != null){
            if (inventory.getType().equals(InventoryTypeEnum.AGENT_PRODUCT.getType())){
                baseAfterSaleSolution.setSkuTypeSolution(skuTypeSolutionMap.get(SkuTypeAfterSolutionEnum.AGENT_PRODUCT.getType()));
            }else {
                baseAfterSaleSolution.setSkuTypeSolution(skuTypeSolutionMap.get(SkuTypeAfterSolutionEnum.SELF_OPERATED_GOODS.getType()));
            }
        }

        //根据售后服务类型进行处理
        baseAfterSaleSolution.setHandleTypeSolution(handleTypeSolutionMap.get(afterSaleOrderVO.getHandleType()));

        return baseAfterSaleSolution;
    }
}
