package net.summerfarm.mall.factory;

import net.summerfarm.mall.contexts.*;
import net.summerfarm.mall.enums.AfterSaleWorkflowTypeEnum;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> ct
 * create at:  2019/12/9  17:55
 */
@Deprecated
// @Component
public class AfterSaleWorkflowFactory {

    @Resource
    private NotNeedWorkflow notNeedWorkflow;
    @Resource
    private OutOfDateWorkflow outOfDateWorkflow;
    @Resource
    private ExchangeGoodsWorkflow exchangeGoodsWorkflow;
    @Resource
    private WholeOrderWorkflow wholeOrderWorkflow;

    private Map<Integer, AfterSaleWorkflow> afterSaleWorkflow;


    // @PostConstruct
    @Deprecated
    public void init(){
        afterSaleWorkflow = new HashMap<>();

        afterSaleWorkflow.put(AfterSaleWorkflowTypeEnum.NOT_NEED.getType(),notNeedWorkflow);

        afterSaleWorkflow.put(AfterSaleWorkflowTypeEnum.BROKEN.getType(),outOfDateWorkflow);

        afterSaleWorkflow.put(AfterSaleWorkflowTypeEnum.EXCHANGE.getType(),exchangeGoodsWorkflow);

        afterSaleWorkflow.put(AfterSaleWorkflowTypeEnum.INTERCEPT.getType(),wholeOrderWorkflow);

    }

    public AfterSaleWorkflow getWorkflow(int type){
        return afterSaleWorkflow.get(type);
    }




}
