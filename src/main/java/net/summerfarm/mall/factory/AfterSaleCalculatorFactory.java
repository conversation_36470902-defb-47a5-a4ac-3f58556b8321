package net.summerfarm.mall.factory;


import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.contexts.*;
import net.summerfarm.mall.service.AfterSaleCalculator;
import net.summerfarm.mall.wechat.enums.AfterSaleCalculatorType;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> ct
 * create at:  2019/12/6  15:36
 */
@Component
@Slf4j
public class AfterSaleCalculatorFactory {

    private Map<String, AfterSaleCalculator> calculatorStrategies;

    @Resource
    AfterSaleCalculator notNeedCalculator;
    @Resource
    NotNeedTimingCalculator notNeedTimingCalculator;
    @Resource
    BrokenTiMingCalculator brokenTiMingCalculator;
    @Resource
    BrokenCalculator brokenCalculator;

    //初始化策略类 饿汉式
    @PostConstruct
    public void init(){

        calculatorStrategies = new HashMap<>();

        calculatorStrategies.put(AfterSaleCalculatorType.NOT_NEED.getType(), notNeedCalculator);

        calculatorStrategies.put(AfterSaleCalculatorType.BROKEN.getType(),brokenCalculator);

        calculatorStrategies.put(AfterSaleCalculatorType.TIMING_NOT_NEED.getType(),notNeedTimingCalculator);

        calculatorStrategies.put(AfterSaleCalculatorType.TIMING_BROKEN.getType(),brokenTiMingCalculator);

    }


    //根据key获取计算售后金额策略
    public AfterSaleCalculator getAfterSaleCalculator(String key){
        AfterSaleCalculator afterSaleCalculator = calculatorStrategies.get(key);
        log.info("getAfterSaleCalculator:{}", afterSaleCalculator.getClass());
        return afterSaleCalculator;
    }
}
