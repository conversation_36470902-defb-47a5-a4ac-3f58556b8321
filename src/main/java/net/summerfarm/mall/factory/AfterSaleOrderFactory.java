package net.summerfarm.mall.factory;

/**
 * <AUTHOR> ct
 * create at:  2019/12/6  11:13
 */

import net.summerfarm.mall.contexts.*;
import net.summerfarm.mall.service.AfterSaleStrategy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> ct
 * create at:  2019/12/6  10:31
 *
 * 获取策略 工厂类
 */
// @Component
@Deprecated
public class AfterSaleOrderFactory {
    @Resource
    NotNeedAfterSaleOrder notNeedAfterSaleOrder;
    @Resource
    BrokenAfterSaleOrder brokenAfterSaleOrder;
    @Resource
    OutOfDateAfterSaleOrder outOfDateAfterSaleOrder;
    @Resource
    RefundAfterSaleOrder refundAfterSaleOrder;
    @Resource
    ExchangeGoodsAfterSaleOrder exchangeGoodsAfterSaleOrder;
    @Resource
    RefuseAfterSaleOrder refuseAfterSaleOrder;
    @Resource
    WholeOrderAfterSaleOrder wholeOrderAfterSaleOrder;

    private Map<Integer, AfterSaleStrategy> afterSaleStrategies;

    //初始化策略类
    // @PostConstruct
    private void init(){

        afterSaleStrategies = new HashMap<>();

        afterSaleStrategies.put(0,notNeedAfterSaleOrder);

        afterSaleStrategies.put(1,brokenAfterSaleOrder);

        afterSaleStrategies.put(2,outOfDateAfterSaleOrder);

        afterSaleStrategies.put(3,refundAfterSaleOrder);

        afterSaleStrategies.put(4,exchangeGoodsAfterSaleOrder);

        afterSaleStrategies.put(7,refuseAfterSaleOrder);

        afterSaleStrategies.put(8,wholeOrderAfterSaleOrder);

    }

    public AfterSaleStrategy getAfterSaleStrategy(int type ){
        return afterSaleStrategies.get(type);
    }

}