package net.summerfarm.mall.contexts;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description mq topic 常用常量
 * @createTime 2021年12月03日 11:29:00
 */
public class MQTopicConstant {

    /**
     * 重试次数5
     */
    public static final Integer RETRY_COUNT_FIVE = 5;

    /**
     * 重试间隔时间
     */
    public static final Long RETRY_TIME_ONE_MINUTE = 60 * 1000L;

    /**
     * 延时消息
     */
    public static final String MALL_DELAY_LIST = "mall-delay-list";

    /**
     * 延时消息
     */
    public static final String MALL_LIST = "mall-list";

    /**
     * 支付延迟查询消息
     */
    public static final String MALL_PAYMENT_DELAY_LIST = "mall-payment-counter-check-list";

    /**
     * 支付、退款延时查询 group
     */
    public static final String GID_MALL_COUNTER_CHECK = "GID_mall_counter_check";

    /**
     * 支付分区顺序消息
     */
    public static final String MALL_PAYMENT_LIST = "mall-payment-list";

    /**
     * 延时消息group
     */
    public static final String GID_MALL_DELAY = "GID_mall_delay";

    /**
     * 支付、退款成功回调通知group
     */
    public static final String GID_MALL_PAYMENT = "GID_mall_payment";

    /**
     * 支付成功回调仅处理参与满返订单
     */
    public static final String GID_MALL_PAYMENT_FULL_RETURN = "GID_mall_payment_full_return";

    /**
     * 选择器表达式
     */
    public static final String SELECTOR_EXPRESSION = "*";


}
