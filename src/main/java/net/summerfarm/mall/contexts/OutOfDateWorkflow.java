package net.summerfarm.mall.contexts;

import com.alibaba.fastjson.JSONObject;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.enums.AfterSaleHandleType;
import net.summerfarm.enums.AfterSaleOrderStatus;
import net.summerfarm.enums.OrderTypeEnum;
import net.summerfarm.mall.common.mq.MQData;
import net.summerfarm.mall.common.mq.MType;
import net.summerfarm.mall.common.util.DateUtils;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.enums.CouponActivityScopeEnum;
import net.summerfarm.mall.enums.CouponGroupingEnum;
import net.summerfarm.mall.enums.CouponReceiveTypeEnum;
import net.summerfarm.mall.enums.OrderStatusEnum;
import net.summerfarm.mall.factory.AfterSaleCalculatorFactory;
import net.summerfarm.mall.mapper.AfterSaleDeliveryPathMapper;
import net.summerfarm.mall.mapper.AfterSaleOrderMapper;
import net.summerfarm.mall.mapper.AfterSaleProofMapper;
import net.summerfarm.mall.mapper.CouponMapper;
import net.summerfarm.mall.mapper.DeliveryPlanMapper;
import net.summerfarm.mall.mapper.InventoryMapper;
import net.summerfarm.mall.mapper.MerchantCouponMapper;
import net.summerfarm.mall.mapper.OrderItemMapper;
import net.summerfarm.mall.mapper.OrdersMapper;
import net.summerfarm.mall.mapper.PrepayInventoryRecordMapper;
import net.summerfarm.mall.mapper.RefundMapper;
import net.summerfarm.mall.model.bo.coupon.CouponSenderBO;
import net.summerfarm.mall.model.domain.AfterSaleDeliveryPath;
import net.summerfarm.mall.model.domain.AfterSaleOrder;
import net.summerfarm.mall.model.domain.AfterSaleOrderCalculator;
import net.summerfarm.mall.model.domain.AfterSaleProof;
import net.summerfarm.mall.model.domain.Coupon;
import net.summerfarm.mall.model.domain.Inventory;
import net.summerfarm.mall.model.domain.Orders;
import net.summerfarm.mall.model.domain.PrepayInventoryRecord;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import net.summerfarm.mall.model.vo.DeliveryPlanVO;
import net.summerfarm.mall.service.ActivityService;
import net.summerfarm.mall.service.AfterSaleCalculator;
import net.summerfarm.mall.service.AfterSaleOrderService;
import net.summerfarm.mall.service.OrderService;
import net.summerfarm.mall.service.strategy.coupon.CouponSenderContext;
import net.summerfarm.mall.wechat.enums.AfterSaleCalculatorType;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> ct
 * create at:  2019/12/9  15:00
 * 未到货售后 缺货其他 审核 审批
 */
@Component
public class OutOfDateWorkflow extends AfterSaleWorkflow {

    @Resource
    private CouponMapper couponMapper;
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private OrderService orderService;
    @Resource
    private AfterSaleProofMapper afterSaleProofMapper;
    @Resource
    private AfterSaleOrderMapper afterSaleOrderMapper;
    @Resource
    private MerchantCouponMapper merchantCouponMapper;
    @Resource
    private  AfterSaleWorkflowAction afterSaleWorkflowAction;
    @Resource
    private RefundMapper refundMapper;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private AfterSaleOrderService afterSaleOrderService;
    @Resource
    private PrepayInventoryRecordMapper prepayInventoryRecordMapper;
    @Resource
    private AfterSaleDeliveryPathMapper afterSaleDeliveryPathMapper;
    @Resource
    private AfterSaleCalculatorFactory afterSaleCalculatorFactory;
    @Resource
    private OrderItemMapper orderItemMapper;
    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;
    @Resource
    private ActivityService activityService;
    @Resource
    private CouponSenderContext couponSenderContext;


    private static final Logger logger = LoggerFactory.getLogger(OutOfDateWorkflow.class);


    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    @Deprecated
    AjaxResult handle(AfterSaleOrderVO afterSaleOrderVO) {


        //修改售后记录
        AfterSaleOrder updateKeys = new AfterSaleOrder();
        BigDecimal handleNum = afterSaleOrderVO.getHandleNum();
        String afterSaleOrderNo = afterSaleOrderVO.getAfterSaleOrderNo();
        //查询sku是否为代仓商品, 是售后可以为0元
        Inventory inventoryVO = inventoryMapper.selectBySku(afterSaleOrderVO.getSku());
        AfterSaleOrderVO afterSaleOrder = afterSaleOrderMapper.selectByAfterSaleOrderNo(afterSaleOrderNo);
        Orders orders1 = ordersMapper.selectByOrderNo(afterSaleOrderVO.getOrderNo());
        //-1拒绝，0返券，1补发，2退款'
        if(afterSaleOrderVO.getHandleType() != -1 && handleNum.compareTo(afterSaleOrder.getHandleNum()) > 0){
            BigDecimal couponMoney = checkoutHandleNum(afterSaleOrderVO);
            if(Objects.isNull(couponMoney)){
                return AjaxResult.getErrorWithMsg("售后金额计算异常");
            }
            if(handleNum.compareTo(couponMoney) > 0 ){
                return AjaxResult.getErrorWithMsg("更改后的售后金额大于可售后金额");
            }
        }
        Integer handleType = afterSaleOrderVO.getHandleType();
        if (handleType == -1) {
            //记录
            logger.info("管理员：拒绝了{}售后申请", afterSaleOrderVO.getAfterSaleOrderNo());
            updateKeys.setStatus(AfterSaleOrderStatus.FAIL.getStatus());
            if ("凭证不符合要求,请重新上传照片".equals(afterSaleOrderVO.getHandleRemark())) {
                updateKeys.setStatus(AfterSaleOrderStatus.RE_COMMIT.getStatus());
            }
            afterSaleWorkflowAction.checkoutProof(afterSaleOrderVO);
            AfterSaleDeliveryPath updatePath = new AfterSaleDeliveryPath();
            updatePath.setAfterSaleNo(afterSaleOrderNo);
            updatePath.setStatus(0);
            afterSaleDeliveryPathMapper.updateAfterSaleDeliveryPath(updatePath);
            //成功返券
        } else if (handleType == 0) {
            if ((afterSaleOrderVO.getHandleNum() == null || afterSaleOrderVO.getHandleNum().compareTo(BigDecimal.ZERO) <= 0) && Objects.equals(inventoryVO.getType(),0)) {
                return AjaxResult.getErrorWithMsg("售后金额不能小于零");
            }
            logger.info("管理员：{}{}通过了{}售后申请", afterSaleOrderVO.getAfterSaleOrderNo());

            //修改售后记录
            updateKeys.setStatus(AfterSaleOrderStatus.IN_HAND.getStatus());
        }
        //退款
        else if (handleType.intValue() == 2 || handleType.intValue() == 4 || handleType.intValue() == 9) {
            updateKeys.setStatus(AfterSaleOrderStatus.IN_HAND.getStatus());
            afterSaleWorkflowAction.insertRefund(afterSaleOrderVO);
            //录入账单
        } else if(Objects.equals(3,handleType)){

            List<PrepayInventoryRecord> records = prepayInventoryRecordMapper.selectByOrderNoAndSku(afterSaleOrderVO.getOrderNo(), afterSaleOrderVO.getSku());
            if (CollectionUtils.isEmpty(records) && (afterSaleOrderVO.getHandleNum() == null || afterSaleOrderVO.getHandleNum().compareTo(BigDecimal.ZERO) <= 0) && Objects.equals(inventoryVO.getType(),0)) {
                return AjaxResult.getErrorWithMsg("售后金额不能小于等于零");
            }
            updateKeys.setStatus(AfterSaleOrderStatus.IN_HAND.getStatus());

            //退货录入账单
        } else if(Objects.equals(handleType, AfterSaleHandleType.REFUND_ENTRY_BILL.getType()) ||
                Objects.equals(handleType, AfterSaleHandleType.REFUSE_BILL.getType())
        ){
            updateKeys.setStatus(AfterSaleOrderStatus.IN_HAND.getStatus());
        } else {
            return AjaxResult.getErrorWithMsg("不正确的处理状态");
        }

        //只更改状态售后的
        updateKeys.setAfterSaleOrderNo(afterSaleOrderVO.getAfterSaleOrderNo());
        updateKeys.setView(0);
        afterSaleOrderMapper.updateByAfterSaleOrderNo(updateKeys);
        //获取最后一个
        List<AfterSaleProof> afterSaleProofs = afterSaleProofMapper.select(afterSaleOrderVO.getAfterSaleOrderNo());
        //根据id 修改proof
        AfterSaleProof updateProof = afterSaleWorkflowAction.conventAfterSaleProof(afterSaleOrderVO);
        updateProof.setStatus(updateKeys.getStatus());
        updateProof.setHandleNum(afterSaleOrderVO.getHandleNum());
        updateProof.setHandleType(afterSaleOrderVO.getHandleType());
        updateProof.setId(afterSaleProofs.get(afterSaleProofs.size() - 1).getId());
        updateProof.setAfterSaleType(afterSaleOrderVO.getAfterSaleType());
        updateProof.setRefundType(afterSaleOrderVO.getRefundType());
        updateProof.setQuantity(afterSaleOrderVO.getQuantity());
        //拒绝handler type不处理
        if (updateProof.getHandleType() == -1) {
            updateProof.setHandleType(null);
        }
        afterSaleProofMapper.updateById(updateProof);
        //activityService.afterSaleActivityQuantity(afterSaleOrderVO);
        //如果是返券自动审批通过
        if(Arrays.asList(new Integer[]{0,3,5}).contains(handleType)){
            AfterSaleOrderVO afterSale = new AfterSaleOrderVO();
            afterSale.setStatus(1);
            afterSale.setAfterSaleOrderNo(afterSaleOrderNo);
            afterSale.setAuditer(afterSaleOrderVO.getHandler());
            AjaxResult audit = audit(afterSale);
            return audit;
        }
        return AjaxResult.getOK();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    @Deprecated
    AjaxResult audit(AfterSaleOrderVO afterSaleOrderVO) {
        String afterSaleOrderNo = afterSaleOrderVO.getAfterSaleOrderNo();
        Integer status = afterSaleOrderVO.getStatus();
        //退款状态 成功
        Integer isSuccess = AfterSaleOrderStatus.SUCCESS.getStatus();
        AfterSaleOrderVO afterSaleOrder = afterSaleOrderMapper.selectByAfterSaleOrderNo(afterSaleOrderNo);
        //处于待审核状态才可以进入下一步
        if (AfterSaleOrderStatus.IN_HAND.getStatus() != afterSaleOrder.getStatus()) {
            return AjaxResult.getError("售后状态错误");

        }
        //获取最后一个
        List<AfterSaleProof> afterSaleProofs = afterSaleProofMapper.select(afterSaleOrderNo);
        //根据id 修改proof
        AfterSaleProof updateProof = new AfterSaleProof();
        AfterSaleOrder updateAfterOrder = new AfterSaleOrder();
        updateProof.setAuditer(afterSaleOrderVO.getAuditer());
        updateProof.setId(afterSaleProofs.get(afterSaleProofs.size() - 1).getId());

        //审核不通过
        if (status == 0) {
            //更改审核订单列表
            updateAfterOrder.setStatus(AfterSaleOrderStatus.WAIT_HANDLE.getStatus());
            //更新凭证状态
            updateProof.setStatus(AfterSaleOrderStatus.WAIT_HANDLE.getStatus());

            afterSaleWorkflowAction.notPass(afterSaleOrder);

            //审核成功
        } else if (status == 1) {

            Orders orders = ordersMapper.selectByOrderyNo(afterSaleOrder.getOrderNo());
            BigDecimal handleNum = BigDecimal.ZERO;
            int handleQuantity = 0;
            AfterSaleOrder query = new AfterSaleOrder();
            query.setOrderNo(afterSaleOrder.getOrderNo());
            query.setDeliveryId(afterSaleOrder.getDeliveryId());
            List<AfterSaleOrderVO> afterSaleOrders = afterSaleOrderService.selectAfterSaleOrderVO(query);

            for (AfterSaleOrderVO check : afterSaleOrders) {
                if (check.getStatus() == 2 && Objects.equals(check.getDeliveryId(),afterSaleOrder.getDeliveryId())) {
                    handleQuantity = check.getQuantity() + handleQuantity;
                    handleNum = handleNum.add(check.getHandleNum());
                }
            }
            boolean max = afterSaleWorkflowAction.maxMoney(afterSaleOrderNo, updateProof,handleNum,true);
            if (!max){
                return AjaxResult.getError("AFTER_SALE_FAILED", "最大可售后金额为" + orders.getTotalPrice().subtract(handleNum));
            }

            //成功 返券
            if (afterSaleOrder.getHandleType() == 0 && (afterSaleOrder.getType() == 0 || afterSaleOrder.getType() == 3 ||
                    (afterSaleOrder.getType() == 1) && RequestHolder.getMerchantSubject() != null)) {
                Coupon coupon = couponMapper.selectByCode(Global.AFTER_SALE_COUPON_FRONT + afterSaleOrder.getHandleNum());
                if (coupon == null) {
                    coupon = new Coupon();
                    coupon.setAddTime(LocalDateTime.now());
                    coupon.setCode(Global.AFTER_SALE_COUPON_FRONT + afterSaleOrder.getHandleNum());
                    coupon.setMoney(afterSaleOrder.getHandleNum());
                    coupon.setName("售后补偿券");
                    coupon.setType((byte) 0);
                    coupon.setThreshold(afterSaleOrder.getHandleNum().add(BigDecimal.valueOf(0.01)));
                    coupon.setVaildTime(180);
                    coupon.setGrouping(CouponGroupingEnum.AFTER_SALE.ordinal());
                    coupon.setAgioType(1);
                    coupon.setActivityScope(CouponActivityScopeEnum.OTHER.ordinal());
                    coupon.setStatus(1);
                    coupon.setAutoCreated(1);
                    //默认不限领取次数和不限领取张数
                    coupon.setQuantityClaimed(0);
                    coupon.setGrantLimit(0);
                    coupon.setCreator("系统默认");
                    couponMapper.insertNewCoupon(coupon);
                }

                //发放优惠券
                CouponSenderBO couponSenderBO = new CouponSenderBO();
                couponSenderBO.setCoupon(coupon);
                couponSenderBO.setMId(afterSaleOrder.getmId());
                couponSenderBO.setReceiveType(CouponReceiveTypeEnum.OTHER.getCode());
                couponSenderContext.sendActivityCoupon(couponSenderBO);
            }

            //成功  退款
            if ((afterSaleOrder.getHandleType() == 2 || afterSaleOrder.getHandleType() == 4 || afterSaleOrder.getHandleType() == 9) && (afterSaleOrder.getType() == 0 || afterSaleOrder.getType() == 3)) {

                try {
                    AjaxResult refund = afterSaleOrderService.refund(afterSaleOrderNo);
                    if(!AjaxResult.isSuccess(refund)){
                        return AjaxResult.getErrorWithMsg(refund.getMsg());
                    }
                } catch (Exception e) {
                    logger.info("退款失败", e);
                    //退款状态改为失败
                    isSuccess = AfterSaleOrderStatus.FAIL.getStatus();
                }
            }

            //大客户帐期门店 已到货 录入账单不做任何操作,只记录。 未到货 拍多拍错不想要 缺货 返库存,其他不返库存
            if(afterSaleOrder.getHandleType() == 3 && (afterSaleOrder.getType() == 0 || afterSaleOrder.getType() == 3)){
                //未到货
                if(Objects.equals(afterSaleOrder.getDeliveryed(),0)){
                    afterSaleOrderService.afterSaleEntryBill(afterSaleOrderNo);
                }
            }

            //大客户帐期门店 拒收 录入账单不做任何操作,只记录。
            if(afterSaleOrder.getHandleType() == 10 && (afterSaleOrder.getType() == 0 || afterSaleOrder.getType() == 3)){
                afterSaleOrderService.afterSaleEntryBill(afterSaleOrderNo);
            }

            updateAfterOrder.setStatus(isSuccess);
            updateProof.setStatus(isSuccess);

            MQData mqData = new MQData();
            mqData.setType(MType.AFTER_SALE_ORDER_NO.name());
            mqData.setData(afterSaleOrder.getAfterSaleOrderNo());
            mqProducer.send(RocketMqMessageConstant.MANAGE_LIST, null, JSONObject.toJSONString(mqData));



        } else {
            return AjaxResult.getErrorWithMsg("不正确的审核状态");
        }
        //商城极速售后直接发券，改为待审核，  后台提交的更改状态为完成
        if( Objects.equals(afterSaleOrderVO.getType(),1) && RequestHolder.getMerchantSubject() != null){
            updateAfterOrder.setStatus(AfterSaleOrderStatus.WAIT_HANDLE.getStatus());
            updateProof.setStatus(AfterSaleOrderStatus.WAIT_HANDLE.getStatus());
        }
        updateAfterOrder.setView(0);
        updateAfterOrder.setAfterSaleOrderNo(afterSaleOrderNo);
        afterSaleOrderMapper.updateByAfterSaleOrderNo(updateAfterOrder);
        updateProof.setAuditer(afterSaleOrderVO.getAuditer());
        updateProof.setAuditeRemark(afterSaleOrderVO.getAuditeRemark());
        updateProof.setAuditetime(LocalDateTime.now());
        afterSaleProofMapper.updateById(updateProof);

        Orders orders = ordersMapper.selectByOrderyNo(afterSaleOrder.getOrderNo());
        if(OrderTypeEnum.TIMING.getId().equals(orders.getType())){
            handleTimingOrder(orders);
        }

        return AjaxResult.getOK();
    }

    private void handleTimingOrder(Orders orders) {
        String orderNo = orders.getOrderNo();
        Integer afterSaleQuantity = afterSaleOrderService.getAfterSaleSuccessQuanlity(orderNo);

        List<DeliveryPlanVO> plans = deliveryPlanMapper.selectByOrderNo(orderNo);
        int deliveredQuantity = plans.stream().filter(deliveryPlanVO -> !deliveryPlanVO.getDeliveryTime().isAfter(LocalDate.now())).mapToInt(dp -> dp.getQuantity()).sum();
        Integer quantity = orderItemMapper.selectTimingOrderQuantity(orderNo);

        logger.info("orderNo:{}, deliveredQuanlity:{}, afterSaleQuanlity:{},quantity:{}", orderNo, deliveredQuantity, afterSaleQuantity, quantity);
        //配送计划的数量加上售后的数量等于订单下单的数量
        if( deliveredQuantity + afterSaleQuantity == quantity){
            if (deliveredQuantity == 0) {
                ordersMapper.updateStatus(orderNo, OrderStatusEnum.DRAWBACK.getId(), orders.getStatus());
            } else {
                int number = deliveryPlanMapper.countByOrderNo(orderNo);
                //如果待收货数量配送计划只有一个，改变订单状态，否则等其自动收货
                if (Objects.equals(number,1)){
                    //改变配送计划的订单状态
                    List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanMapper.selectByDeliveryPlan(orderNo);
                    for (DeliveryPlanVO deliveryPlanVO : deliveryPlanVOS) {
                        deliveryPlanMapper.deliveryedById(deliveryPlanVO.getId());
                    }
                    orderItemMapper.updateStatusByOrderNo(orderNo, OrderStatusEnum.RECEIVED.getId());
                    //改变订单状态，插入自动确认收货时间
                    orders.setConfirmTime(DateUtils.localDate2Date(LocalDate.now()));
                    //省心送订单变更为已经收货
                    ordersMapper.updateByOrderNoSelective(orders);
                    ordersMapper.updateStatus(orderNo, OrderStatusEnum.RECEIVED.getId(), orders.getStatus());
                    orderService.updateScore(orderNo, orders.getmId());
                    //orderService.pushOrderConfirm(orderNo);
                }
            }

        }

    }

    @Deprecated
    private BigDecimal checkoutHandleNum(AfterSaleOrderVO afterSaleOrderVO){

        AfterSaleCalculator calculator = afterSaleCalculatorFactory.getAfterSaleCalculator(AfterSaleCalculatorType.BROKEN.getType());
        if(afterSaleOrderVO.getDeliveryId() != null){
            calculator = afterSaleCalculatorFactory.getAfterSaleCalculator(AfterSaleCalculatorType.TIMING_BROKEN.getType());
        }
        AjaxResult ajaxResult = calculator.calcRefund(afterSaleOrderVO);
        if(!AjaxResult.isSuccess(ajaxResult)){
            return null;
        }
        AfterSaleOrderCalculator afterCalculator =  (AfterSaleOrderCalculator)ajaxResult.getData();
        BigDecimal couponMoney = afterCalculator.getCouponMoney();
        return couponMoney;
    }

}
