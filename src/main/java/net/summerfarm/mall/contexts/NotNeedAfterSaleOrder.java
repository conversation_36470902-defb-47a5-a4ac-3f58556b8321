package net.summerfarm.mall.contexts;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.BaseDateUtils;
import net.summerfarm.enums.AfterSaleHandleType;
import net.summerfarm.enums.AfterSaleOrderStatus;
import net.summerfarm.enums.OrderTypeEnum;
import net.summerfarm.mall.common.util.DateUtils;
import net.summerfarm.mall.common.util.PreCutOffOrderUtil;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.enums.AfterSaleWorkflowEnum;
import net.summerfarm.mall.factory.AfterSaleCalculatorFactory;
import net.summerfarm.mall.factory.AfterSaleWorkflowFactory;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import net.summerfarm.mall.model.vo.DeliveryPlanVO;
import net.summerfarm.mall.service.AfterSaleCalculator;
import net.summerfarm.mall.service.AfterSaleStrategy;
import net.summerfarm.mall.wechat.enums.AfterSaleCalculatorType;
import net.summerfarm.warehouse.model.domain.WarehouseLogisticsCenter;
import net.summerfarm.warehouse.service.WarehouseLogisticsService;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

import static net.summerfarm.common.AjaxResult.DEFAULT_SUCCESS;


/**
 * <AUTHOR> ct
 * create at:  2019/12/6  10:45
 * 普通售后   未到货售后
 * 拍多拍错不想要 提交售后
 */
// @Component
@Deprecated
public class NotNeedAfterSaleOrder implements AfterSaleStrategy {

    @Resource
    private AfterSaleOrderAction afterSaleOrderAction;
    @Resource
    private AfterSaleCalculatorFactory afterSaleCalculatorFactory;
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;
    // @Resource
    private AfterSaleWorkflowFactory afterSaleWorkflowFactory;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private PrepayInventoryRecordMapper prepayInventoryRecordMapper;
    @Resource
    private PreCutOffOrderUtil preCutOffOrderUtil;
    @Resource
    private AdminMapper adminMapper;
    @Resource
    private AreaMapper areaMapper;
    @Resource
    private WarehouseLogisticsService warehouseLogisticsService;

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    @Deprecated
    public AjaxResult afterSaleOrder(AfterSaleOrderVO afterSaleOrderVO) {
        //进入前拼装好 需要的信息

        AfterSaleOrder updateAfterSaleOrder = new AfterSaleOrder();
        AfterSaleProof updateAfterSaleProof = new AfterSaleProof();
        BigDecimal totalRefund = BigDecimal.ZERO;

        //商城提交售后封装 mid
        Boolean isManage = afterSaleOrderVO.getmId() != null ? Boolean.TRUE : Boolean.FALSE;

        // 未到货拍多拍错一定是退款,账期门店 为录入账单
        afterSaleOrderVO.setHandleType(AfterSaleHandleType.REFUND.getType());
        if(!isManage && RequestHolder.isMajorDirect()){
            afterSaleOrderVO.setHandleType(AfterSaleHandleType.ENTRY_BILL.getType());
        }
        //后台提交售后

        afterSaleOrderAction.assembleAfterSaleOrderVO(afterSaleOrderVO);

        Orders order = ordersMapper.selectByOrderNo(afterSaleOrderVO.getOrderNo());

        AjaxResult result = afterSaleOrderAction.chcekAfterSaleOrder(order, afterSaleOrderVO);
        String afterSaleOrderNo = afterSaleOrderAction.createAfterSaleOrderNo(afterSaleOrderVO.getmId(), afterSaleOrderVO.getAccountId());
        afterSaleOrderVO.setAfterSaleOrderNo(afterSaleOrderNo);
        if(!Objects.equals(result.getCode(),DEFAULT_SUCCESS)){
            return result;
        }

        List<DeliveryPlanVO> deliveryPlans = deliveryPlanMapper.selectByOrderNo(order.getOrderNo());
        if (CollectionUtils.isEmpty(deliveryPlans) || deliveryPlans.size() > 1) {
            return AjaxResult.getErrorWithMsg("配送计划不正常");
        }
        DeliveryPlanVO deliveryPlanVO = deliveryPlans.get(0);
        //商城发起未到货售后
        if (RequestHolder.getMerchantSubject() != null) {
            Integer adminId = RequestHolder.getAdminId();
            AjaxResult deliveryResult = checkoutTime(adminId, deliveryPlanVO);
            if(!AjaxResult.isSuccess(deliveryResult)){
               return deliveryResult;
            }
        }

        //只能是 未到货 普通售后
        updateAfterSaleOrder.setType(0);
        afterSaleOrderVO.setDeliveryed(AfterSaleOrder.DELIVERY_NOT_RECEIVED);
        updateAfterSaleOrder.setDeliveryed(AfterSaleOrder.DELIVERY_NOT_RECEIVED);
        updateAfterSaleOrder.setStatus(AfterSaleOrderStatus.WAIT_HANDLE.getStatus());
        updateAfterSaleProof.setStatus(AfterSaleOrderStatus.WAIT_HANDLE.getStatus());
        updateAfterSaleOrder.setAfterSaleOrderNo(afterSaleOrderNo);
        updateAfterSaleProof.setHandleType(afterSaleOrderVO.getHandleType());
        updateAfterSaleProof.setAfterSaleOrderNo(afterSaleOrderNo);

        /** {@link NotNeedCalculator} */
        AfterSaleCalculator calculator = afterSaleCalculatorFactory.getAfterSaleCalculator(AfterSaleCalculatorType.NOT_NEED.getType());
        AjaxResult ajaxResult = calculator.calcRefund(afterSaleOrderVO);
        if(!AjaxResult.isSuccess(ajaxResult)){
            return ajaxResult;
        }
    
        AfterSaleOrderCalculator afterCalculator =  (AfterSaleOrderCalculator)ajaxResult.getData();
        BigDecimal couponMoney = afterCalculator.getCouponMoney();

        if(afterSaleOrderVO.getHandleNum() != null
                && afterSaleOrderVO.getHandleNum().compareTo(BigDecimal.ZERO) > 0 ){
            updateAfterSaleProof.setHandleNum(afterSaleOrderVO.getHandleNum());
        } else {
            updateAfterSaleProof.setHandleNum(couponMoney);
        }
        Integer skuType = afterCalculator.getSkuType();
        afterSaleOrderVO.setHandleNum(couponMoney);
        Merchant merchant = merchantMapper.selectOneByMid(order.getmId());
        String sku = afterSaleOrderVO.getSku();
        List<PrepayInventoryRecord> prepayInventoryRecords = prepayInventoryRecordMapper.selectRecordList(merchant.getAdminId(), afterSaleOrderVO.getOrderNo(), sku);
        //代仓商品的实付是0元,预付款商品实付0元 ,退款支持0元
        if (CollectionUtils.isEmpty(prepayInventoryRecords) && couponMoney.compareTo(BigDecimal.ZERO) == 0 && Objects.equals(skuType,"0")) {
            return AjaxResult.getErrorWithMsg("退款金额异常");
        }
        if (couponMoney.add(totalRefund).compareTo(order.getTotalPrice()) > 0 ) {
            return AjaxResult.getError("TOTAL_REFUND", "金额错误");
        }

        afterSaleOrderAction.assembleAfterSaleOrder(afterSaleOrderVO,updateAfterSaleOrder);
        afterSaleOrderAction.assembleAfterSalePoof(afterSaleOrderVO,updateAfterSaleProof);


        AfterSaleWorkflow workflow = afterSaleWorkflowFactory.getWorkflow(AfterSaleWorkflowEnum.HANDLE.getType());
        //后台省心送未到货售后 到待审批
        if(Objects.equals(order.getType(), OrderTypeEnum.TIMING.getId()) && isManage){
            //审核
            afterSaleOrderVO.setHandler(afterSaleOrderVO.getApplyer());
            AjaxResult handleResult = workflow.workflow(AfterSaleWorkflow.HANDLE, afterSaleOrderVO);
            if(!Objects.equals(handleResult.getCode(),"SUCCESS")){
                return AjaxResult.getErrorWithMsg(handleResult.getMsg());
            }
        }
        //商城订单普通售后
        if(Objects.equals(order.getType(),OrderTypeEnum.NORMAL.getId())){
            //审核
            afterSaleOrderVO.setHandler(afterSaleOrderVO.getApplyer());
            AjaxResult handleResult = workflow.workflow(AfterSaleWorkflow.HANDLE, afterSaleOrderVO);
            if(!Objects.equals(handleResult.getCode(),"SUCCESS")){
                return AjaxResult.getErrorWithMsg(handleResult.getMsg());
            }
            //审批 审批成功
            afterSaleOrderVO.setType(0);
            afterSaleOrderVO.setStatus(1);
            afterSaleOrderVO.setAuditer(afterSaleOrderVO.getApplyer());
            AjaxResult auditResult = workflow.workflow(AfterSaleWorkflow.AUDIT, afterSaleOrderVO);
            if(!Objects.equals(auditResult.getCode(),"SUCCESS")){
                return AjaxResult.getErrorWithMsg(auditResult.getMsg());
            }
        }
        return AjaxResult.getOK();

    }

    /**
    * 计算是否能发起未到货售后
     * @param deliveryPlanVO
     * @param adminId
    */
    public AjaxResult checkoutTime(Integer adminId,DeliveryPlanVO deliveryPlanVO){

        LocalDate deliveryTime = deliveryPlanVO.getDeliveryTime().minusDays(1);
        LocalDateTime localDateTime = LocalDateTime.of(LocalDate.now(),Global.CLOSING_ORDER_TIME);

        //大客户截单 信息
        if(Objects.nonNull(adminId)){
            Admin admin = adminMapper.selectByPrimaryKey(adminId);
            String closeOrderTime = admin.getCloseOrderTime();
            if(!StringUtils.isEmpty(closeOrderTime)){
                LocalDateTime afterTime = LocalDateTime.of(deliveryTime, LocalTime.parse(closeOrderTime, BaseDateUtils.DEFAULT_LOCAL_TIME));
                if(LocalDateTime.now().isAfter(afterTime)){
                    return AjaxResult.getErrorWithMsg("配送日前一天的" + closeOrderTime + "前可选");
                }
            }
        }
        //获取当前配送仓的截单时间
        Integer orderStoreNo = deliveryPlanVO.getOrderStoreNo();
        WarehouseLogisticsCenter center = warehouseLogisticsService.selectByStoreNo(orderStoreNo);
        if(Objects.nonNull(center)){
            String closeTime = center.getCloseTime();
            //获取城配仓对应是否有加单
            List<Area> areas = areaMapper.selectSupportAddOrder(orderStoreNo);
            //截单时间
            localDateTime = deliveryTime.atTime(LocalTime.parse(closeTime, DateTimeFormatter.ofPattern(DateUtils.DEFULT_LOCAL_TIME)));
            //存在加单往后延长30分钟
            if(!CollectionUtils.isEmpty(areas)){
                localDateTime = localDateTime.plusMinutes(Global.ADD_TIME);
            }
        }
        //时间校验
        if(LocalDateTime.now().isAfter(localDateTime)){
            LocalTime localTime = localDateTime.toLocalTime();
            String format = localTime.format(DateTimeFormatter.ofPattern("HH点mm分"));
            return AjaxResult.getErrorWithMsg("配送日前一天的" + format +"前可选");
        }
        return AjaxResult.getOK();
    }



}
