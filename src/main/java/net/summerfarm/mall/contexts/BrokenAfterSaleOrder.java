package net.summerfarm.mall.contexts;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.enums.AfterSaleHandleType;
import net.summerfarm.enums.AfterSaleOrderStatus;
import net.summerfarm.mall.enums.AfterSaleWorkflowEnum;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.factory.AfterSaleCalculatorFactory;
import net.summerfarm.mall.factory.AfterSaleWorkflowFactory;
import net.summerfarm.mall.mapper.InventoryMapper;
import net.summerfarm.mall.mapper.OrdersMapper;
import net.summerfarm.mall.mapper.PrepayInventoryRecordMapper;
import net.summerfarm.mall.mapper.ProductsMapper;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import net.summerfarm.mall.service.AfterSaleCalculator;
import net.summerfarm.mall.service.AfterSaleStrategy;
import net.summerfarm.mall.service.MemberService;
import net.summerfarm.mall.wechat.enums.AfterSaleCalculatorType;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

import static com.github.javaparser.utils.Log.info;

/**
 * <AUTHOR> ct
 * create at:  2019/12/6  16:59
 * 已到货 商品品质问题，商品数量等 提交售后
 */
// @Component
@Slf4j
public class BrokenAfterSaleOrder implements AfterSaleStrategy {

    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private ProductsMapper productsMapper;
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private MemberService memberService;
    @Resource
    private AfterSaleOrderAction afterSaleOrderAction;

    @Resource
    AfterSaleCalculatorFactory afterSaleCalculatorFactory;
    
    // @Resource
    private AfterSaleWorkflowFactory afterSaleWorkflowFactory;
    @Resource
    private PrepayInventoryRecordMapper prepayInventoryRecordMapper;


    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    @Deprecated
    public AjaxResult afterSaleOrder(AfterSaleOrderVO afterSaleOrderVO) {

        Orders order = ordersMapper.selectByOrderNo(afterSaleOrderVO.getOrderNo());


        Inventory inventory = inventoryMapper.selectBySku(afterSaleOrderVO.getSku());
        if(Objects.equals(inventory.getType(), 1) && Objects.equals(afterSaleOrderVO.getDeliveryed(),1)){
            return AjaxResult.getErrorWithMsg("代仓商品不支持已到货售后");

        }
        //商城提交售后封装 mid
        Boolean isManage = afterSaleOrderVO.getmId() != null ? Boolean.TRUE : Boolean.FALSE;

        //如果是大客户预付商品 售后方式为 录入账单
        List<PrepayInventoryRecord> prepayInventoryRecords = prepayInventoryRecordMapper.selectByOrderNoAndSku(order.getOrderNo(), afterSaleOrderVO.getSku());
        //账期门店 为录入账单
        if((!isManage && RequestHolder.isMajorDirect()) || !CollectionUtils.isEmpty(prepayInventoryRecords)){
            afterSaleOrderVO.setHandleType(AfterSaleHandleType.ENTRY_BILL.getType());
        }
        afterSaleOrderAction.assembleAfterSaleOrderVO(afterSaleOrderVO);
        Integer grade = memberService.calculGrade(afterSaleOrderVO.getmId());
        AjaxResult result = afterSaleOrderAction.chcekAfterSaleOrder(order, afterSaleOrderVO);
        if(!Objects.equals(result.getCode(),"SUCCESS")){
            return result;
        }
        String afterSaleOrderNo = afterSaleOrderAction.createAfterSaleOrderNo(afterSaleOrderVO.getmId(), afterSaleOrderVO.getAccountId());

        afterSaleOrderVO.setAfterSaleOrderNo(afterSaleOrderNo);
        AfterSaleCalculator calculator = afterSaleCalculatorFactory.getAfterSaleCalculator(AfterSaleCalculatorType.BROKEN.getType());
        //省心送计算金额
        if(order.getType() == 1 ){
            calculator = afterSaleCalculatorFactory.getAfterSaleCalculator(AfterSaleCalculatorType.TIMING_BROKEN.getType());
        }
        AjaxResult ajaxResult = calculator.calcRefund(afterSaleOrderVO);
        if(!AjaxResult.isSuccess(ajaxResult)){
            return ajaxResult;
        }

        AfterSaleOrderCalculator afterCalculator =  (AfterSaleOrderCalculator)ajaxResult.getData();
        BigDecimal couponMoney = afterCalculator.getCouponMoney();

        AfterSaleOrder updateAfterSaleOrder = new AfterSaleOrder();
        AfterSaleProof updateAfterSaleProof = new AfterSaleProof();
        updateAfterSaleOrder.setType(afterSaleOrderVO.getType());
        updateAfterSaleOrder.setStatus(AfterSaleOrderStatus.WAIT_HANDLE.getStatus());
        updateAfterSaleOrder.setDeliveryed(afterSaleOrderVO.getDeliveryed());
        updateAfterSaleOrder.setAfterSaleOrderNo(afterSaleOrderNo);
        updateAfterSaleProof.setStatus(AfterSaleOrderStatus.WAIT_HANDLE.getStatus());

        if(afterSaleOrderVO.getHandleType() != null){
            updateAfterSaleProof.setHandleType(afterSaleOrderVO.getHandleType());
        } else {
            updateAfterSaleProof.setHandleType(AfterSaleHandleType.COUPON.getType());
        }
        if(afterSaleOrderVO.getHandleNum() != null && afterSaleOrderVO.getHandleNum().compareTo(BigDecimal.ZERO) > 0 ){
            couponMoney = afterSaleOrderVO.getHandleNum();
        }
        afterSaleOrderVO.setHandleNum(couponMoney);
        updateAfterSaleProof.setHandleNum(couponMoney);

        updateAfterSaleProof.setAfterSaleOrderNo(afterSaleOrderNo);


        //极速售后 校验客户类型 大客户无法发起
        if(Objects.equals(afterSaleOrderVO.getType(),1) && !Objects.equals(order.getmSize(),Global.BIG_MERCHANT)){
            if (couponMoney.compareTo(BigDecimal.valueOf(0)) == -1) {
                return AjaxResult.getErrorWithMsg("非水果类目不能极速售后");
            } else if (couponMoney.compareTo(BigDecimal.valueOf(0)) == 0) {
                return AjaxResult.getErrorWithMsg("极速售后金额必须大于0");
            } else if (couponMoney.compareTo(BigDecimal.valueOf(40)) == 1) {
                return AjaxResult.getErrorWithMsg("超出单次极速售后金额上限(40)");
            }
            BigDecimal refundAmount = memberService.getRefundAmountByGrade(grade);
            //当月已使用极速退款金额 计算剩余额度
            BigDecimal jsAmount = memberService.getUsedAmount(LocalDateTime.now(), afterSaleOrderVO.getmId(), grade);
            //剩余额度
            BigDecimal remainAmount = refundAmount.subtract(jsAmount == null ? BigDecimal.valueOf(0) : jsAmount);
            if(remainAmount.compareTo(couponMoney) < 0){
                return AjaxResult.getErrorWithMsg("极速售后余额不足");
            }
        }

        afterSaleOrderAction.assembleAfterSaleOrder(afterSaleOrderVO,updateAfterSaleOrder);

        afterSaleOrderAction.assembleAfterSalePoof(afterSaleOrderVO,updateAfterSaleProof);

        if(Objects.equals(afterSaleOrderVO.getType(),1)){
            //可以发起极速售后
            //审核 审批已到货售后
            //后台提交 调用 审核 审批 通过
            AfterSaleWorkflow workflow = afterSaleWorkflowFactory.getWorkflow(AfterSaleWorkflowEnum.AUDIT.getType());
            //审核
            afterSaleOrderVO.setHandler(afterSaleOrderVO.getApplyer());
            AjaxResult handleResult = workflow.workflow(0, afterSaleOrderVO);
            if(!Objects.equals("SUCCESS",handleResult.getCode())){
                return AjaxResult.getErrorWithMsg(handleResult.getMsg());
            }
            return AjaxResult.getOK();
        }
        //后台提交 调用 审核通过 退款，返券
        if((Objects.equals(updateAfterSaleProof.getHandleType(),AfterSaleHandleType.ENTRY_BILL.getType()) ||
                Objects.equals(updateAfterSaleProof.getHandleType(),AfterSaleHandleType.REFUND.getType())||
                Objects.equals(updateAfterSaleProof.getHandleType(),AfterSaleHandleType.COUPON.getType())) &&
                isManage && !Objects.equals(afterSaleOrderVO.getType(),1)){

            AfterSaleWorkflow workflow = afterSaleWorkflowFactory.getWorkflow(AfterSaleWorkflowEnum.AUDIT.getType());
            log.info("afterSaleWorkflowHandle:"+workflow.getClass());

            //审核
            afterSaleOrderVO.setHandler(afterSaleOrderVO.getApplyer());
            AjaxResult handleResult = workflow.workflow(0, afterSaleOrderVO);
            if(!Objects.equals("SUCCESS",handleResult.getCode())){
                return AjaxResult.getErrorWithMsg(handleResult.getMsg());
            }
        }
        return AjaxResult.getOK();
    }
}
