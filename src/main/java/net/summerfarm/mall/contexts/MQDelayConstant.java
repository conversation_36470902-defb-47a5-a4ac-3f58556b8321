package net.summerfarm.mall.contexts;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 延迟消息常用常量
 * @createTime 2021年11月29日 14:46:00
 */
public class MQDelayConstant {

    /**
     * 超时时间
     */
    public static final Long TIMEOUT = 3000L;

    /**
     * 延迟级别2
     * 延迟5秒
     */
    public static final Integer TWO_DELAY_LEVEL = 2;
    public static final Long TWO_DELAY_LEVEL_LONG = 5 * 1000L;

    /**
     * 延迟级别3
     * 延迟10秒
     */
    public static final Integer THREE_DELAY_LEVEL = 3;
    public static final Long THREE_DELAY_LEVEL_LONG = 10 * 1000L;


    /**
     * 延迟级别4
     * 延迟30秒
     */
    public static final Integer FOUR_DELAY_LEVEL = 4;
    public static final Long FOUR_DELAY_LEVEL_LONG = 30 * 1000L;

    /**
     * 延迟级别14
     * 延迟10分钟
     */
    public static final Integer FOURTEEN_DELAY_LEVEL = 14;
    public static final Long FOURTEEN_DELAY_LEVEL_LONG = 10 * 60 * 1000L;

    /**
     * 延迟级别16
     * 延迟30分钟
     */
    public static final Integer SIXTEEN_DELAY_LEVEL = 16;
    public static final Long SIXTEEN_DELAY_LEVEL_LONG = 30 * 60 * 1000L;


    /**
     * 延迟级别18
     * 延迟2小时
     */
    public static final Integer EIGHTEEN_DELAY_LEVEL = 18;
    public static final Long EIGHTEEN_DELAY_LEVEL_LONG = 2 * 60 * 60 * 1000L;

    /**
     * 延迟最大小时 2
     */
    public static final Integer MAX_HOURS_LEVEL = 2;
    public static final Long MAX_HOURS_LEVEL_LONG = 2 * 60 * 60 * 1000L;


    /**
     * 取消售后订单,延迟24小时
     */
    public static final Integer CANCEL_AFTER_SALES_DELAY_HOURS = 24;
    public static final Long CANCEL_AFTER_SALES_DELAY_HOURS_LONG = 24 * 60 * 60 * 1000L;


}
