package net.summerfarm.mall.contexts;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.enums.AfterSaleOrderStatus;
import net.summerfarm.enums.OrderTypeEnum;
import net.summerfarm.mall.factory.AfterSaleCalculatorFactory;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import net.summerfarm.mall.model.vo.ProductVO;
import net.summerfarm.mall.service.AfterSaleCalculator;
import net.summerfarm.mall.service.AfterSaleStrategy;
import net.summerfarm.mall.service.DeliveryService;
import net.summerfarm.mall.service.OrderService;
import net.summerfarm.mall.service.facade.OfcQueryFacade;
import net.summerfarm.mall.wechat.enums.AfterSaleCalculatorType;
import net.summerfarm.ofc.client.enums.OfcOrderSourceEnum;
import net.summerfarm.ofc.client.resp.DeliveryDateQueryResp;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

import static net.summerfarm.common.AjaxResult.DEFAULT_SUCCESS;

/**
 * <AUTHOR> ct
 * create at:  2020/12/4  14:24
 * 售后类型 退货退款 退货录入账单 待审核状态
 */
@Component
public class RefundAfterSaleOrder implements AfterSaleStrategy {

    @Resource
    DeliveryPlanMapper deliveryPlanMapper;
    @Resource
    InventoryMapper inventoryMapper;
    @Resource
    OrdersMapper ordersMapper;
    @Resource
    private AfterSaleOrderAction afterSaleOrderAction;
    @Resource
    AfterSaleCalculatorFactory afterSaleCalculatorFactory;
    @Resource
    AfterSaleDeliveryPathMapper afterSaleDeliveryPathMapper;
    @Resource
    AfterSaleDeliveryDetailMapper afterSaleDeliveryDetailMapper;
    @Resource
    MerchantMapper merchantMapper;
    @Resource
    OrderService orderService;
    @Resource
    AreaMapper areaMapper;
    @Resource
    ContactMapper contactMapper;
    @Resource
    private OfcQueryFacade getDeliveryDateFacade;

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    @Deprecated
    public AjaxResult afterSaleOrder(AfterSaleOrderVO afterSaleOrderVO) {

        //售后类型 退货退款 退货录入账单
        Integer handleType = afterSaleOrderVO.getHandleType();
        String orderNo = afterSaleOrderVO.getOrderNo();
        Orders orders = ordersMapper.selectByOrderNo(orderNo);
        Merchant merchant = merchantMapper.selectOneByMid(orders.getmId());
        afterSaleOrderVO.setAfterSaleUnit("件");
        //校验是否已经生成了退货退款售后
        String sku = afterSaleOrderVO.getSku();
        //获取下单城市信息
        Integer areaNo = merchant.getAreaNo();
        Area area = areaMapper.selectByAreaNo(areaNo);
        ProductVO productVO = inventoryMapper.queryBySku(sku);
        Inventory inventory = inventoryMapper.selectBySku(sku);
        BigDecimal handleNum = afterSaleOrderVO.getHandleNum();
        BigDecimal recoveryNum = afterSaleOrderVO.getRecoveryNum() == null ? BigDecimal.ZERO : afterSaleOrderVO.getRecoveryNum();
        //校验数量，金额校验 回收费金额+ 退款金额 <= 订单项金额(或配送计划金额)
        if(recoveryNum.compareTo(handleNum) > 0){
            return AjaxResult.getErrorWithMsg("退款金额 + 回收费 > 订单项金额");
        }
        Integer type = inventory.getType();
        // 代仓商品不支持
        if(type.intValue() == 1){
            return AjaxResult.getErrorWithMsg("代仓商品不支持此类型售后");
        }
        //现结代下单不支持
        if(orders.getType().equals(OrderTypeEnum.HELP.getId()) && Objects.equals(merchant.getDirect(),2)){
            return AjaxResult.getErrorWithMsg("现结代下单不可售后");
        }
        //实际退款金额 = handleNum - 回收费金额
        handleNum = handleNum.subtract(recoveryNum);
        afterSaleOrderVO.setHandleNum(handleNum);
        AjaxResult result = afterSaleOrderAction.chcekAfterSaleOrder(orders, afterSaleOrderVO);
        String afterSaleOrderNo = afterSaleOrderAction.createAfterSaleOrderNo(afterSaleOrderVO.getmId(), afterSaleOrderVO.getAccountId());
        afterSaleOrderVO.setAfterSaleOrderNo(afterSaleOrderNo);
        AfterSaleCalculator calculator = afterSaleCalculatorFactory.getAfterSaleCalculator(AfterSaleCalculatorType.BROKEN.getType());
        if(afterSaleOrderVO.getDeliveryId() != null){
            calculator = afterSaleCalculatorFactory.getAfterSaleCalculator(AfterSaleCalculatorType.TIMING_BROKEN.getType());
        }
        AjaxResult ajaxResult = calculator.calcRefund(afterSaleOrderVO);
        if(!AjaxResult.isSuccess(ajaxResult)){
            return ajaxResult;
        }
        AfterSaleOrderCalculator afterCalculator =  (AfterSaleOrderCalculator)ajaxResult.getData();
        BigDecimal couponMoney = afterCalculator.getCouponMoney();
        if(!Objects.equals(result.getCode(),DEFAULT_SUCCESS)){
            return result;
        }
        if(handleNum.compareTo(couponMoney) > 0){
            return AjaxResult.getErrorWithMsg("超过可售后金额");
        }
        //获取发起售后的数量
        Integer deliveryId = afterSaleOrderVO.getDeliveryId();
        //省心送id 存在说明是省心送未到货退款,获取配送计划的配送地址
        Long contactId = afterSaleOrderAction.getContactId(orderNo,deliveryId);
        //配送时间
        DeliveryDateQueryResp dateQueryResp = getDeliveryDateFacade.queryDeliveryDate(LocalDateTime.now(),afterSaleOrderVO.getmId(),contactId,null,null, OfcOrderSourceEnum.XM_AFTER_SALE,null);

        //售后单详情
        AfterSaleOrder afterSaleOrder = new AfterSaleOrder();
        afterSaleOrder.setAfterSaleOrderNo(afterSaleOrderNo);
        afterSaleOrder.setDeliveryed(AfterSaleOrder.DELIVERY_RECEIVED);
        afterSaleOrder.setStatus(AfterSaleOrderStatus.WAIT_HANDLE.getStatus());
        AfterSaleProof afterSaleProof = new AfterSaleProof();
        afterSaleProof.setStatus(AfterSaleOrderStatus.WAIT_HANDLE.getStatus());
        afterSaleProof.setHandleNum(afterSaleOrderVO.getHandleNum());
        afterSaleProof.setHandleType(afterSaleOrderVO.getHandleType());
        afterSaleOrderAction.assembleAfterSaleOrder(afterSaleOrderVO,afterSaleOrder);
        afterSaleOrderAction.assembleAfterSalePoof(afterSaleOrderVO,afterSaleProof);

        //生成售后配送单信息
        Contact contact = contactMapper.selectByPrimaryKey(contactId);
        Integer storeNo = contact.getStoreNo() ;
        AfterSaleDeliveryPath insertPath = new AfterSaleDeliveryPath();
        insertPath.setConcatId(contactId);
        insertPath.setAfterSaleNo(afterSaleOrderVO.getAfterSaleOrderNo());
        insertPath.setDeliveryTime(dateQueryResp.getDeliveryDate());
        insertPath.setMId(orders.getmId());
        insertPath.setOutStoreNo(storeNo);
        insertPath.setStatus(1);
        insertPath.setType(1);
        afterSaleDeliveryPathMapper.insertAfterSaleDeliveryPath(insertPath);
        //生成售后配送单详情
        AfterSaleDeliveryDetail insertDetail = new AfterSaleDeliveryDetail();
        insertDetail.setAsDeliveryPathId(insertPath.getId());
        insertDetail.setPdName(productVO.getPdName());
        insertDetail.setQuantity(afterSaleOrderVO.getQuantity());
        insertDetail.setWeight(productVO.getWeight());
        insertDetail.setSku(sku);
        insertDetail.setType(1);
        insertDetail.setStatus(AfterSaleDeliveryDetail.EFFECTIVE_STATUS);
        afterSaleDeliveryDetailMapper.insertAfterSaleDeliveryDetail(insertDetail);
        return AjaxResult.getOK();
    }
}
