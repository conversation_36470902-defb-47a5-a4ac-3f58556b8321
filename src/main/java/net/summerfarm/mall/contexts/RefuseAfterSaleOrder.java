package net.summerfarm.mall.contexts;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.enums.AfterSaleHandleType;
import net.summerfarm.enums.AfterSaleOrderStatus;
import net.summerfarm.mall.enums.AfterSaleWorkflowEnum;
import net.summerfarm.mall.factory.AfterSaleCalculatorFactory;
import net.summerfarm.mall.factory.AfterSaleWorkflowFactory;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import net.summerfarm.mall.service.AfterSaleCalculator;
import net.summerfarm.mall.service.AfterSaleStrategy;
import net.summerfarm.mall.service.MemberService;
import net.summerfarm.mall.wechat.enums.AfterSaleCalculatorType;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> ct
 * create at:  2019/12/6  16:59
 * 已到货 商品品质问题，商品数量等 提交售后
 */
// @Component
public class RefuseAfterSaleOrder implements AfterSaleStrategy {

    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private ProductsMapper productsMapper;
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private MemberService memberService;
    @Resource
    private AfterSaleOrderAction afterSaleOrderAction;
    @Resource
    private MerchantMapper merchantMapper;

    @Resource
    AfterSaleCalculatorFactory afterSaleCalculatorFactory;
    
    // @Resource
    private AfterSaleWorkflowFactory afterSaleWorkflowFactory;
    @Resource
    private PrepayInventoryRecordMapper prepayInventoryRecordMapper;


    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    @Deprecated
    public AjaxResult afterSaleOrder(AfterSaleOrderVO afterSaleOrderVO) {

        Orders order = ordersMapper.selectByOrderNo(afterSaleOrderVO.getOrderNo());


        Inventory inventory = inventoryMapper.selectBySku(afterSaleOrderVO.getSku());
        if(Objects.equals(inventory.getType(), 1) && Objects.equals(afterSaleOrderVO.getDeliveryed(),1)){
            return AjaxResult.getErrorWithMsg("代仓商品不支持已到货售后");

        }

        //如果是大客户预付商品 售后方式为 录入账单
        List<PrepayInventoryRecord> prepayInventoryRecords = prepayInventoryRecordMapper.selectByOrderNoAndSku(order.getOrderNo(), afterSaleOrderVO.getSku());
        Merchant merchant = merchantMapper.selectOneByMid(order.getmId());
        //账期门店 为录入账单
        if ("大客户".equals(merchant.getSize()) && Objects.equals(1, merchant.getDirect()) || !CollectionUtils.isEmpty(prepayInventoryRecords)) {
            afterSaleOrderVO.setHandleType(AfterSaleHandleType.REFUSE_BILL.getType());
        }
        // 不存在商城提交
//        afterSaleOrderAction.assembleAfterSaleOrderVO(afterSaleOrderVO);
        AjaxResult result = afterSaleOrderAction.chcekAfterSaleOrder(order, afterSaleOrderVO);
        if(!Objects.equals(result.getCode(),"SUCCESS")){
            return result;
        }
        String afterSaleOrderNo = afterSaleOrderAction.createAfterSaleOrderNo(afterSaleOrderVO.getmId(), afterSaleOrderVO.getAccountId());

        afterSaleOrderVO.setAfterSaleOrderNo(afterSaleOrderNo);
        AfterSaleCalculator calculator = afterSaleCalculatorFactory.getAfterSaleCalculator(AfterSaleCalculatorType.BROKEN.getType());
        //省心送计算金额
        if(order.getType() == 1 ){
            calculator = afterSaleCalculatorFactory.getAfterSaleCalculator(AfterSaleCalculatorType.TIMING_BROKEN.getType());
        }

        AjaxResult ajaxResult = calculator.calcRefund(afterSaleOrderVO);
        if(!AjaxResult.isSuccess(ajaxResult)){
            return ajaxResult;
        }
        AfterSaleOrderCalculator afterCalculator =  (AfterSaleOrderCalculator)ajaxResult.getData();
        BigDecimal couponMoney = afterCalculator.getCouponMoney();

        AfterSaleOrder updateAfterSaleOrder = new AfterSaleOrder();
        AfterSaleProof updateAfterSaleProof = new AfterSaleProof();
        updateAfterSaleOrder.setType(afterSaleOrderVO.getType());
        updateAfterSaleOrder.setStatus(AfterSaleOrderStatus.WAIT_HANDLE.getStatus());
        updateAfterSaleOrder.setDeliveryed(afterSaleOrderVO.getDeliveryed());
        updateAfterSaleOrder.setAfterSaleOrderNo(afterSaleOrderNo);
        updateAfterSaleProof.setStatus(AfterSaleOrderStatus.WAIT_HANDLE.getStatus());

        if(afterSaleOrderVO.getHandleType() != null){
            updateAfterSaleProof.setHandleType(afterSaleOrderVO.getHandleType());
        } else {
            updateAfterSaleProof.setHandleType(AfterSaleHandleType.COUPON.getType());
        }
        BigDecimal handleNum = couponMoney;
        if(afterSaleOrderVO.getHandleNum() != null && afterSaleOrderVO.getHandleNum().compareTo(BigDecimal.ZERO) > 0 ){
            handleNum = afterSaleOrderVO.getHandleNum();
        }

        if(handleNum.compareTo(couponMoney) > 0){
            return AjaxResult.getErrorWithMsg("超过可售后金额");
        }

        BigDecimal recoveryNum = afterSaleOrderVO.getRecoveryNum() == null ? BigDecimal.ZERO : afterSaleOrderVO.getRecoveryNum();
        //校验数量，金额校验 回收费金额+ 退款金额 <= 订单项金额(或配送计划金额)
        if(recoveryNum.compareTo(handleNum) > 0){
            return AjaxResult.getErrorWithMsg("退款金额 + 回收费 > 订单项金额");
        }
        handleNum = handleNum.subtract(recoveryNum);
        afterSaleOrderVO.setHandleNum(handleNum);
        updateAfterSaleProof.setHandleNum(handleNum);

        updateAfterSaleProof.setAfterSaleOrderNo(afterSaleOrderNo);

        afterSaleOrderAction.assembleAfterSaleOrder(afterSaleOrderVO,updateAfterSaleOrder);

        afterSaleOrderAction.assembleAfterSalePoof(afterSaleOrderVO,updateAfterSaleProof);
        //后台提交 调用 审核通过 退款，返券
        boolean isManage = Objects.equals(afterSaleOrderVO.getIsManage(), true);
        if(isManage){
            AfterSaleWorkflow workflow = afterSaleWorkflowFactory.getWorkflow(AfterSaleWorkflowEnum.AUDIT.getType());
            //审核
            afterSaleOrderVO.setHandler(afterSaleOrderVO.getApplyer());
            AjaxResult handleResult = workflow.workflow(0, afterSaleOrderVO);
            if(!Objects.equals("SUCCESS",handleResult.getCode())){
                return AjaxResult.getErrorWithMsg(handleResult.getMsg());
            }
        }

        return AjaxResult.getOK();
    }
}
