package net.summerfarm.mall.contexts;

import com.alibaba.fastjson.JSONObject;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.enums.AfterSaleHandleType;
import net.summerfarm.enums.AfterSaleOrderStatus;
import net.summerfarm.enums.RefundStatusEnum;
import net.summerfarm.mall.enums.AfterHandleRemarkEnum;
import net.summerfarm.mall.common.mq.MQData;
import net.summerfarm.mall.common.mq.MType;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import net.summerfarm.mall.model.vo.MerchantCouponVO;
import net.summerfarm.mall.model.vo.OrderVO;
import net.summerfarm.mall.service.OrderRelationService;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR> ct
 * create at:  2019/12/9  15:07
 */
@Component
public class AfterSaleWorkflowAction {

    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private RefundMapper refundMapper;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private AfterSaleOrderMapper afterSaleOrderMapper;
    @Resource
    private AfterSaleProofMapper afterSaleProofMapper;
    @Resource
    private OrdersCouponMapper ordersCouponMapper;
    @Resource
    private PrepayInventoryRecordMapper prepayInventoryRecordMapper;
    @Resource
    private OrderRelationService orderRelationService;


    private static final Logger logger = LoggerFactory.getLogger(AfterSaleWorkflowAction.class);


    public void checkoutProof(AfterSaleOrderVO afterSaleOrderVO){

        if (AfterHandleRemarkEnum.CERTIFICATE.getDescription().equals(afterSaleOrderVO.getHandleRemark())) {
            afterSaleOrderVO.setStatus(AfterSaleOrderStatus.RE_COMMIT.getStatus());
            MQData mqData = new MQData();
            mqData.setType(MType.AFTER_SALE_ORDER_NO.name());
            mqData.setData(afterSaleOrderVO.getAfterSaleOrderNo());
            mqProducer.send(RocketMqMessageConstant.MANAGE_LIST, null, JSONObject.toJSONString(mqData));
        }
    }

    //插入退款记录
    @Deprecated
    public void insertRefund(AfterSaleOrderVO afterSaleOrderVO){
        //查询当前退款信息是否是预付商品
        List<PrepayInventoryRecord> records = prepayInventoryRecordMapper.selectByOrderNoAndSku(afterSaleOrderVO.getOrderNo(), afterSaleOrderVO.getSku());
        Inventory inventory = inventoryMapper.selectBySku(afterSaleOrderVO.getSku());
        if (CollectionUtils.isEmpty(records) && (afterSaleOrderVO.getHandleNum() == null || afterSaleOrderVO.getHandleNum().compareTo(BigDecimal.ZERO) <= 0) && Objects.equals(inventory.getType(),0)) {
            throw new DefaultServiceException(0,"售后金额不能小于零");
        }

        //修改售后记录
        //查询历史退款记录
        OrderVO orderVO = ordersMapper.selectByOrderyNo(afterSaleOrderVO.getOrderNo());
        BigDecimal totalFen = orderVO.getTotalPrice().multiply(BigDecimal.valueOf(100));
        BigDecimal refundFen = afterSaleOrderVO.getHandleNum().multiply(BigDecimal.valueOf(100));
        logger.info("refundFen={}",refundFen);
        // List<Refund> refunds = refundMapper.selectByOrderNo(afterSaleOrderVO.getOrderNo());
        String refundNo =  Global.createAfterSaleOrderNo(afterSaleOrderVO.getOrderNo()); // afterSaleOrderVO.getOrderNo() + (refunds.size() + 1);
        Refund refund = new Refund(afterSaleOrderVO.getOrderNo(), afterSaleOrderVO.getAfterSaleOrderNo(), refundNo, totalFen, refundFen);
        if (inventory!=null){
            if(Objects.equals(inventory.getType(),1) || !CollectionUtils.isEmpty(records)){
                refund.setRefundChannel("鲜沐卡");
            }
        }
        refund.setCouponId(afterSaleOrderVO.getCouponId());
        refundMapper.insertSelective(refund);
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public void notPass(AfterSaleOrderVO afterSaleOrderVO){
        //更新凭证状态
        if (Objects.equals(afterSaleOrderVO.getHandleType(),AfterSaleHandleType.REFUND.getType())) {
            Refund updateKeys = new Refund();
            updateKeys.setStatus((byte) RefundStatusEnum.REFUSE.ordinal());
            updateKeys.setAfterSaleOrderNo(afterSaleOrderVO.getAfterSaleOrderNo());
            refundMapper.updateSelectiveByAfterSaleOrderNo(updateKeys);
        }
        MQData mqData = new MQData();
        mqData.setType(MType.AFTER_SALE_ORDER_NO.name());
        mqData.setData(afterSaleOrderVO.getAfterSaleOrderNo());
        mqProducer.send(RocketMqMessageConstant.MANAGE_LIST, null, JSONObject.toJSONString(mqData));
    }


    public AfterSaleProof conventAfterSaleProof(AfterSaleOrderVO afterSaleOrderVO) {
        AfterSaleProof afterSaleProof = new AfterSaleProof();
        afterSaleProof.setAfterSaleOrderNo(afterSaleOrderVO.getAfterSaleOrderNo());
        afterSaleProof.setProofPic(afterSaleOrderVO.getProofPic());
        afterSaleProof.setHandleNum(afterSaleOrderVO.getHandleNum());
        afterSaleProof.setHandler(afterSaleOrderVO.getHandler());
        afterSaleProof.setAuditer(afterSaleOrderVO.getAuditer());
        afterSaleProof.setApplyRemark(afterSaleOrderVO.getApplyRemark());
        afterSaleProof.setQuantity(afterSaleOrderVO.getQuantity());
        afterSaleProof.setHandleRemark(afterSaleOrderVO.getHandleRemark());
        afterSaleProof.setExtraRemark(afterSaleOrderVO.getExtraRemark());
        afterSaleProof.setAfterSaleType(afterSaleOrderVO.getAfterSaleType());
        afterSaleProof.setRefundType(afterSaleOrderVO.getRefundType());
        afterSaleProof.setHandletime(LocalDateTime.now());
        afterSaleProof.setHandleType(afterSaleOrderVO.getHandleType());
        afterSaleProof.setHandleSecondaryRemark(afterSaleOrderVO.getHandleSecondaryRemark());
        return afterSaleProof;
    }


    public boolean maxMoney( String afterSaleOrderNo ,AfterSaleProof updateProof , BigDecimal handleNum,Boolean afterCoupon ){
        AfterSaleOrderVO afterSaleOrder = afterSaleOrderMapper.selectByAfterSaleOrderNo(afterSaleOrderNo);
        Orders orders = ordersMapper.selectByOrderyNo(afterSaleOrder.getOrderNo());
        BigDecimal totalPrice = orders.getTotalPrice();
        if(afterCoupon){
            HashMap<String, String> map = new HashMap<>();
            Map<String, OrderRelation> orderNoMap = orderRelationService.queryMasterOrderNoByOrderNo(Collections.singletonList(afterSaleOrder.getOrderNo()));
            if (CollectionUtils.isEmpty(orderNoMap)){
                map.put("orderNo",afterSaleOrder.getOrderNo());
            }else {
                map.put("orderNo",orderNoMap.get(afterSaleOrder.getOrderNo()).getMasterOrderNo());
            }
            List<MerchantCouponVO> merchantCouponVOS = ordersCouponMapper.select(map);
            if(!CollectionUtils.isEmpty(merchantCouponVOS)){
                MerchantCouponVO merchantCouponVO = merchantCouponVOS.get(0);
                Integer grouping = merchantCouponVO.getGrouping();
                if(Objects.equals(grouping,1)){
                    BigDecimal money = merchantCouponVO.getMoney();
                    totalPrice = totalPrice.add(money);
                }
            }
        }
        if (handleNum.add(afterSaleOrder.getHandleNum()).compareTo(totalPrice) > 0) {
            afterSaleOrder.setStatus(0);
            afterSaleOrderMapper.updateByAfterSaleOrderNo(afterSaleOrder);
            updateProof.setAuditeRemark("最大可售后金额为" + totalPrice.subtract(handleNum));
            updateProof.setAuditetime(LocalDateTime.now());
            updateProof.setStatus(0);
            afterSaleProofMapper.updateById(updateProof);
            return  false;
        }
        return true;
    }



}
