package net.summerfarm.mall.contexts;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.enums.AfterSaleHandleType;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import net.summerfarm.mall.service.AfterSaleCalculator;
import net.summerfarm.mall.service.AfterSaleOrderService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> ct
 * create at:  2019/12/6  15:13
 * 省心送订单 已到货退款
 */
@Component
public class BrokenTiMingCalculator implements AfterSaleCalculator {

    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private AfterSaleOrderMapper afterSaleOrderMapper;
    @Resource
    private OrderItemMapper orderItemMapper;
    @Resource
    private AfterSaleOrderService afterSaleOrderService;
    @Resource
    private OrdersMapper ordersMapper;
    @Override
    @Deprecated
    public AjaxResult calcRefund(AfterSaleOrderVO afterSaleOrderVO) {
        Integer deliveryId = afterSaleOrderVO.getDeliveryId();
        String sku = afterSaleOrderVO.getSku();
        Integer expectQuantity = afterSaleOrderVO.getQuantity();
        String orderNo = afterSaleOrderVO.getOrderNo();
        Integer handleType = afterSaleOrderVO.getHandleType();
        Orders orders = ordersMapper.selectByOrderNo(orderNo);
        Inventory inventory = inventoryMapper.selectBySku(sku);

        if(Objects.equals(handleType, AfterSaleHandleType.EXCHANGE_GOODS.getType()) ||
                Objects.equals(handleType, AfterSaleHandleType.DELIVERY_GOODS.getType())){
            AfterSaleOrderCalculator calculator = new AfterSaleOrderCalculator();
            calculator.setSkuType(inventory.getType());
            calculator.setCouponMoney(BigDecimal.ZERO);
            return AjaxResult.getOK(calculator);
        }
        DeliveryPlan deliveryPlan = deliveryPlanMapper.queryById(deliveryId);
        if(deliveryPlan == null){
            return AjaxResult.getError("配送计划不存在");
        }
        Integer amount = deliveryPlan.getQuantity();

        BigDecimal maxAfterSaleQuantity = BigDecimal.valueOf(inventory.getAfterSaleQuantity());
        BigDecimal quantity = BigDecimal.valueOf(expectQuantity);
        if (AfterSaleHandleType.REFUND_GOODS.getType().equals(afterSaleOrderVO.getHandleType()) ||
                AfterSaleHandleType.REFUSE_BILL.getType().equals(afterSaleOrderVO.getHandleType()) ||
                AfterSaleHandleType.REFUSE.getType().equals(afterSaleOrderVO.getHandleType())
        ) {
            quantity = maxAfterSaleQuantity.multiply(quantity);
        }
        AfterSaleOrder select = new AfterSaleOrder();
        select.setOrderNo(orderNo);
        select.setSku(sku);
        select.setSuitId(0);
        List<AfterSaleOrderVO> afterSaleOrderVOS = afterSaleOrderService.selectAfterSaleByStatus(select);
        Integer afterSaleQuantity = 0;
        //省心送售后 已到货售后金额 查询该配送计划已经已到货售后数量
        for (AfterSaleOrderVO afterSale : afterSaleOrderVOS) {
            Integer handleTypeVO = afterSale.getHandleType();
            //换货 补发 不计算
            if(Objects.equals(handleTypeVO, AfterSaleHandleType.EXCHANGE_GOODS.getType()) || Objects.equals(handleTypeVO, AfterSaleHandleType.DELIVERY_GOODS.getType())){
                continue;
            }
            //审核时修改售后数量 售后编号相同 跳过
            if(Objects.equals(afterSale.getAfterSaleOrderNo(),afterSaleOrderVO.getAfterSaleOrderNo())){
                continue;
            }
            if (afterSaleOrderVO.getDeliveryed() == 1 && Objects.equals(afterSale.getDeliveryId() , deliveryId)) {
                afterSaleQuantity += afterSaleOrderVO.getQuantity();
                //退货退款金额计算 按件
            } else if(Objects.equals(handleTypeVO,AfterSaleHandleType.REFUND_GOODS.getType()) ||
                    Objects.equals(handleTypeVO,AfterSaleHandleType.REFUSE.getType()) ||
                    Objects.equals(handleTypeVO,AfterSaleHandleType.REFUSE_BILL.getType())
            ){
                Integer quantities = afterSaleOrderVO.getQuantity() * maxAfterSaleQuantity.intValue();
                afterSaleQuantity += quantities;
            }
        }
        //有BUG，先将预售省心送过滤一下
        if (orders.getOrderSaleType() == 0){
            //售后总数量不能大与最大可售后数量
            if (quantity.intValue() + afterSaleQuantity > maxAfterSaleQuantity.intValue() * amount) {
                return AjaxResult.getError(ResultConstant.OVER_AFTER_SALE_QUANTITY);
            }
        }
        MathContext mc = new MathContext(6, RoundingMode.HALF_EVEN);
        BigDecimal afterSalePercent = quantity.divide(maxAfterSaleQuantity, mc);
        OrderItem orderItem = orderItemMapper.selectOne(orderNo, sku, 0, null);
        BigDecimal couponMoney = afterSalePercent.multiply(orderItem.getPrice()).setScale(2,BigDecimal.ROUND_DOWN);
        AfterSaleOrderCalculator calculator = new AfterSaleOrderCalculator();
        calculator.setSkuType(inventory.getType());
        calculator.setCouponMoney(couponMoney);
        return AjaxResult.getOK(calculator);
    }

}
