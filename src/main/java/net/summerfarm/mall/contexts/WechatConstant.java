package net.summerfarm.mall.contexts;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 微信常用常量
 * @createTime 2021年12月03日 15:40:00
 */
public class WechatConstant {

    /**
     *
     */
    public static final int WECHAT_SUCCESS_CODE = 0;

    /**
     * 微信token锁
     */
    public static final String MALL_WECHAT_ACCESS_TOKEN_LOCK="MALL_WECHAT_ACCESS_TOKEN_LOCK" + ":" + SpringContextUtil.getActiveProFile();

    /**
     * 微信token
     */
    public static final String MALL_WECHAT_ACCESS_TOKEN="MALL_WECHAT_ACCESS_TOKEN"+ ":" + SpringContextUtil.getActiveProFile();

    /**
     * 微信小程序token
     */
    public static final String MALL_WECHAT_LITE_ACCESS_TOKEN="MALL_LITE_ACCESS_TOKEN"+ ":" + SpringContextUtil.getActiveProFile();

    /**
     * 微信ticket
     */
    public static final String MALL_WECHAT_JSAPI_TICKET="MALL_WECHAT_JSAPI_TICKET" + ":" + SpringContextUtil.getActiveProFile();

    public static final String MALL_NOTIFY_ACTIVITY_URL = "https://h5.summerfarm.net/home.html?name=Follow&tab=follow&part=wechat";




}
