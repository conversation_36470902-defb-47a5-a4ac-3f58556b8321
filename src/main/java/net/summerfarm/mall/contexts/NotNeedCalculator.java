package net.summerfarm.mall.contexts;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.enums.AfterSaleHandleType;
import net.summerfarm.enums.AfterSaleOrderStatus;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import net.summerfarm.mall.model.vo.MerchantCouponVO;
import net.summerfarm.mall.model.vo.OrderItemVO;
import net.summerfarm.mall.service.AfterSaleCalculator;
import net.summerfarm.mall.service.AfterSaleOrderService;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> ct
 * create at:  2019/12/6  14:04
 * 未到货退款 拍多拍错不想要,缺货，其他 普通订单计算售后金额
 */
@Component
public class NotNeedCalculator implements AfterSaleCalculator {

    @Resource
    private OrderItemMapper orderItemMapper;
    @Resource
    private AfterSaleOrderMapper afterSaleOrderMapper;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private MerchantCouponMapper merchantCouponMapper;
    @Resource
    private AfterSaleOrderService afterSaleOrderService;

    @Override
    @Deprecated
    public AjaxResult calcRefund(AfterSaleOrderVO afterSaleOrderVO) {

        String orderNo = afterSaleOrderVO.getOrderNo();
        String sku = afterSaleOrderVO.getSku();
        Integer suitId = afterSaleOrderVO.getSuitId();
        BigDecimal couponMoney = BigDecimal.ZERO;
        Integer expectQuantity = afterSaleOrderVO.getQuantity();
        Orders orders = ordersMapper.selectByOrderNo(orderNo);
        String couponId = "";
        //查询出购买数量
        OrderItem orderItem = orderItemMapper.selectOne(orderNo, sku, suitId, null);
        Inventory inventory = inventoryMapper.selectBySku(sku);
        AfterSaleOrder selectA = new AfterSaleOrder();
        selectA.setOrderNo(orderNo);
        selectA.setSku(sku);
        selectA.setSuitId(suitId);
        selectA.setStatus(AfterSaleOrderStatus.SUCCESS.getStatus());
        List<AfterSaleOrderVO> afterSaleOrderVOS = afterSaleOrderService.selectAfterSaleByStatus(selectA);
        Integer afterSaleQuantity = 0;
        if (!CollectionUtils.isEmpty(afterSaleOrderVOS) && afterSaleOrderVOS.size() >0) {
            for (AfterSaleOrderVO  afterSale : afterSaleOrderVOS) {
                Integer handleType = afterSale.getHandleType();
                //补发 和 换货不计算在内
                if( Objects.equals(handleType, AfterSaleHandleType.EXCHANGE_GOODS.getType()) || Objects.equals(handleType, AfterSaleHandleType.DELIVERY_GOODS.getType())){
                    continue;
                }
                //状态为不为成功,则跳过
                if(!Objects.equals(afterSale.getStatus(),AfterSaleOrderStatus.SUCCESS.getStatus())){
                    continue;
                }
                //审核时修改售后数量 售后编号相同 跳过
                if(Objects.equals(afterSale.getAfterSaleOrderNo(),afterSaleOrderVO.getAfterSaleOrderNo())){
                    continue;
                }
                //未到货退款 已到货售后 退货退款 退货录入账单
                if (Objects.equals(afterSale.getDeliveryed(),AfterSaleOrder.DELIVERY_NOT_RECEIVED) || Objects.equals(handleType,AfterSaleHandleType.REFUND_ENTRY_BILL.getType())
                        || Objects.equals(handleType,AfterSaleHandleType.REFUND_GOODS.getType())) {
                    afterSaleQuantity += afterSale.getQuantity();
                    //已到货售后 返券 退款
                 } else {
                    afterSaleQuantity = afterSaleQuantity + afterSale.getQuantity()/inventory.getAfterSaleQuantity() + 1;
                }
            }
        }
        //售后总数量不能大与最大可售后数量
        if (expectQuantity + afterSaleQuantity >  orderItem.getAmount()) {
            return AjaxResult.getError(ResultConstant.OVER_AFTER_SALE_QUANTITY);
        }

        //计算应该返回的金额 不包含精准送
        List<OrderItem> items = orderItemMapper.selectOrderItemUsable(orderNo);
        if (CollectionUtils.isEmpty(items)) {
            couponMoney = BigDecimal.ZERO;
        } else if (items.size() == 1) {

            Integer notDeliveryQuantity = 0;
            BigDecimal afterSaleMoney = BigDecimal.ZERO;
            //SKU单价
            BigDecimal price = items.get(0).getPrice();
            //总共付的钱-已经售后的钱
            AfterSaleOrder selectData = new AfterSaleOrder();

            selectData.setmId(orders.getmId());
            selectData.setOrderNo(orderNo);
            selectData.setStatus(AfterSaleOrderStatus.SUCCESS.getStatus());
            List<AfterSaleOrderVO> selectList = afterSaleOrderService.selectAfterSaleOrderVO(selectData);
            if (!CollectionUtils.isEmpty(selectList)) {
                for (AfterSaleOrderVO queryAfterOrder : selectList) {
                    //查询已售后金额
                    afterSaleMoney = afterSaleMoney.add(queryAfterOrder.getHandleNum());
                    if (queryAfterOrder.getDeliveryed() == AfterSaleOrder.DELIVERY_NOT_RECEIVED
                            && sku.equals(queryAfterOrder.getSku())
                            && suitId.equals(queryAfterOrder.getSuitId())) {
                        notDeliveryQuantity = notDeliveryQuantity + queryAfterOrder.getQuantity();
                    }
                }
            }
            //数量
            Integer amount = items.get(0).getAmount();
            //已售后数量+此次售后数量
            if (notDeliveryQuantity + expectQuantity == amount) {
                couponMoney = orders.getTotalPrice().subtract(afterSaleMoney);
            } else {
                couponMoney = price.multiply(new BigDecimal(expectQuantity));
            }
            Map select = new HashMap();
            select.put("orderNo", orderNo);
            List<MerchantCouponVO> merchantCoupons = merchantCouponMapper.select(select,true);
            if (!CollectionUtils.isEmpty(merchantCoupons)) {
                for (MerchantCouponVO couponVO : merchantCoupons) {
                    couponId = couponId + couponVO.getId() + Global.SEPARATING_SYMBOL;
                }
            }

        } else if (items.size() > 1) {
            List<OrderItemVO> suitItems = orderItemMapper.selectOrderItemSuitId(orderNo, null, suitId,null);

            if (suitId <= 0) {
                //应该只有一个
                List<OrderItemVO> orderItemVOS = orderItemMapper.selectOrderItemSuitId(orderNo, sku, suitId,afterSaleOrderVO.getProductType());
                couponMoney = orderItemVOS.get(0).getPrice().multiply(BigDecimal.valueOf(expectQuantity));
            }

        }
        AfterSaleOrderCalculator calculator = new AfterSaleOrderCalculator();
        calculator.setCouponMoney(couponMoney);
        calculator.setSkuType(inventory.getType());
        if (StringUtils.isNotBlank(couponId)) {
            calculator.setCouponId(couponId.substring(0, couponId.length() - 1));
        }
        return AjaxResult.getOK(calculator);
    }
}
