package net.summerfarm.mall.contexts;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;

/**
 * <AUTHOR> ct
 * create at:  2019/12/9  14:59
 * 已到货售后
 */
public class BrokenWorkflow extends AfterSaleWorkflow {
    @Override
    @Deprecated
    AjaxResult handle(AfterSaleOrderVO afterSaleOrderVO) {

        return null;
    }

    @Override
    @Deprecated
    AjaxResult audit(AfterSaleOrderVO afterSaleOrderVO) {
        return null;
    }
}
