package net.summerfarm.mall.contexts;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import net.summerfarm.mall.service.AfterSaleCalculator;
import net.summerfarm.mall.service.AfterSaleOrderService;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2019/12/6  14:53
 * 省心送订单 未到货售后计算金额
 */
@Component
public class NotNeedTimingCalculator implements AfterSaleCalculator {

    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;
    @Resource
    private OrderItemMapper orderItemMapper;
    @Resource
    private AfterSaleOrderMapper afterSaleOrderMapper;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private AfterSaleOrderService afterSaleOrderService;

    @Override
    @Deprecated
    public AjaxResult calcRefund(AfterSaleOrderVO afterSaleOrderVO) {

        String orderNo = afterSaleOrderVO.getOrderNo();
        Integer notDeliveryQuantity = 0;
        Orders orders = ordersMapper.selectByOrderNo(orderNo);
        List<OrderItem> items = orderItemMapper.selectOrderItemUsable(orderNo);
        Inventory inventory = inventoryMapper.selectBySku(afterSaleOrderVO.getSku());

        Integer amount = items.get(0).getAmount();
        BigDecimal price = items.get(0).getPrice();
        AfterSaleOrder selectData = new AfterSaleOrder();
        selectData.setmId(orders.getmId());
        selectData.setOrderNo(orderNo);
        List<AfterSaleOrderVO> selectList = afterSaleOrderService.selectAfterSaleByStatus(selectData);

        //查询省心送定单已经未到货退款的数量
        if (!CollectionUtils.isEmpty(selectList)) {
            for (AfterSaleOrderVO afterSale : selectList) {

                if (afterSale.getDeliveryed() == 0 ) {
                    notDeliveryQuantity = notDeliveryQuantity + afterSale.getQuantity();
                }
            }
        }

        //省心送已配送数量
        int afterQuantity = 0 ;
        //省心送未到货售后数量 = 订单数量 - 已配送数量 - 未到货售后数量
        Integer quantity = deliveryPlanMapper.selectDeliveredQuantity4Follow(orderNo);
        afterQuantity = quantity == null ? afterQuantity : quantity ;
        amount = amount - afterQuantity - notDeliveryQuantity;
        BigDecimal couponMoney = price.multiply(new BigDecimal(amount));
        AfterSaleOrderCalculator calculator = new AfterSaleOrderCalculator();
        calculator.setCouponMoney(couponMoney);
        calculator.setSkuType(inventory.getType());

        return AjaxResult.getOK(calculator);
    }
}
