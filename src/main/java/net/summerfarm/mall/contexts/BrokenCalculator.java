package net.summerfarm.mall.contexts;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.enums.AfterSaleHandleType;
import net.summerfarm.enums.OrderTypeEnum;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import net.summerfarm.mall.model.vo.MerchantCouponVO;
import net.summerfarm.mall.service.AfterSaleCalculator;
import net.summerfarm.mall.service.AfterSaleOrderService;
import net.summerfarm.mall.service.OrderRelationService;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.*;

/**
 * <AUTHOR> ct
 * create at:  2019/12/6  14:32
 * 普通订单 已到货退款计算金额
 */
@Component
public class BrokenCalculator implements AfterSaleCalculator {

    @Resource
    private OrderItemMapper orderItemMapper;
    @Resource
    private AfterSaleOrderMapper afterSaleOrderMapper;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private OrdersCouponMapper ordersCouponMapper;
    @Resource
    private AfterSaleOrderService afterSaleOrderService;
    @Resource
    private OrderRelationService orderRelationService;


    @Override
    @Deprecated
    public AjaxResult calcRefund(AfterSaleOrderVO afterSaleOrderVO) {

        String orderNo = afterSaleOrderVO.getOrderNo();
        String sku = afterSaleOrderVO.getSku();
        Integer suitId = afterSaleOrderVO.getSuitId();
        Integer handleType = afterSaleOrderVO.getHandleType();
        BigDecimal quantity = BigDecimal.valueOf(afterSaleOrderVO.getQuantity());
        //查询出购买数量
        Inventory inventory = inventoryMapper.selectBySku(sku);

        if(Objects.equals(handleType, AfterSaleHandleType.EXCHANGE_GOODS.getType()) ||
                Objects.equals(handleType, AfterSaleHandleType.DELIVERY_GOODS.getType())){
            AfterSaleOrderCalculator calculator = new AfterSaleOrderCalculator();
            calculator.setCouponMoney(BigDecimal.ZERO);
            calculator.setSkuType(inventory.getType());
            return AjaxResult.getOK(calculator);
        }
        OrderItem orderItem = orderItemMapper.selectOne(orderNo, sku, suitId, null);
        if (Objects.isNull(orderItem)) {
            return AjaxResult.getErrorWithMsg("找不到订单项");
        }
        BigDecimal price = orderItem.getPrice();
        List<OrderItem> orderItems = orderItemMapper.selectOrderItem(orderNo);

        Integer amount = orderItem.getAmount();
        //返券,重新计算单价使用了售后券 ，商城发起已到货售后 无售后类型
        if(Objects.equals(handleType,AfterSaleHandleType.COUPON.getType()) || Objects.isNull(handleType)){
            HashMap<String, String> map = new HashMap<>();
            Map<String, OrderRelation> orderNoMap = orderRelationService.queryMasterOrderNoByOrderNo(Collections.singletonList(orderNo));
            if (CollectionUtils.isEmpty(orderNoMap)){
                map.put("orderNo",orderNo);
            }else {
                map.put("orderNo",orderNoMap.get(orderNo).getMasterOrderNo());
            }
            List<MerchantCouponVO> merchantCouponVOS = ordersCouponMapper.select(map);
            if(!CollectionUtils.isEmpty(merchantCouponVOS)){
                MerchantCouponVO merchantCouponVO = merchantCouponVOS.get(0);
                Integer grouping = merchantCouponVO.getGrouping();
                if(Objects.equals(grouping,1)){
                    BigDecimal money = merchantCouponVO.getMoney();
                    BigDecimal totalPrice = BigDecimal.ZERO;
                    for (OrderItem item : orderItems) {
                        totalPrice = totalPrice.add(item.getPrice().multiply(BigDecimal.valueOf(item.getAmount())));
                    }
                    //平摊sku单价上
                    BigDecimal discountFee = money.multiply(orderItem.getPrice().divide(totalPrice,2, BigDecimal.ROUND_DOWN));
                    orderItem.setPrice(price.add(discountFee));
                }
            }

        }

        BigDecimal maxAfterSaleQuantity = BigDecimal.valueOf(inventory.getAfterSaleQuantity());
        if(maxAfterSaleQuantity.compareTo(BigDecimal.ZERO) == 0){
            return AjaxResult.getErrorWithMsg("afterSaleQuantity 不能为0");
        }

        // 退货退款 退货录入账单 单位为件 数量 = 件 * 售后单位
        if(Objects.equals(handleType,AfterSaleHandleType.REFUND_GOODS.getType())
                || Objects.equals(handleType,AfterSaleHandleType.REFUND_ENTRY_BILL.getType())
                || Objects.equals(handleType,AfterSaleHandleType.REFUSE.getType())
                || Objects.equals(handleType,AfterSaleHandleType.REFUSE_BILL.getType())
        ){
            quantity = maxAfterSaleQuantity.multiply(quantity);
        }

        Orders orders = ordersMapper.selectByOrderNo(orderNo);
        //普通售后
        AfterSaleOrder select = new AfterSaleOrder();
        select.setOrderNo(orderNo);
        select.setSku(sku);
        select.setSuitId(suitId);
        select.setType(0);
        List<AfterSaleOrderVO> afterSaleOrderVOS = afterSaleOrderService.selectAfterSaleByStatus(select);
        //极速售后
        AfterSaleOrder fastSaleOrder = new AfterSaleOrder();
        fastSaleOrder.setType(1);
        fastSaleOrder.setOrderNo(orderNo);
        fastSaleOrder.setSku(sku);
        fastSaleOrder.setSuitId(suitId);

        List<AfterSaleOrderVO> fastOrderVOS = afterSaleOrderService.selectAfterSaleOrderVO(fastSaleOrder);
        afterSaleOrderVOS.addAll(fastOrderVOS);
        Integer afterSaleQuantity = 0;
        if (!CollectionUtils.isEmpty(afterSaleOrderVOS) && Objects.equals(orders.getType(), OrderTypeEnum.NORMAL.getId())) {
            for (AfterSaleOrderVO afterSale : afterSaleOrderVOS) {
                Integer selectHandleType = afterSale.getHandleType();
                // 审批，审核失败 补发 和 换货不计算在内
                if(Objects.equals(selectHandleType, AfterSaleHandleType.EXCHANGE_GOODS.getType())
                        || Objects.equals(selectHandleType, AfterSaleHandleType.DELIVERY_GOODS.getType())){
                    continue;
                }
                //审核时修改售后数量 售后编号相同 跳过
                if(Objects.equals(afterSale.getAfterSaleOrderNo(),afterSaleOrderVO.getAfterSaleOrderNo())){
                    continue;
                }

                //未到货售后 或 售后类型为 退货退款 退货录入账单
                if (afterSale.getDeliveryed() == 0
                        || Objects.equals(selectHandleType,AfterSaleHandleType.REFUND_GOODS.getType())
                        || Objects.equals(selectHandleType,AfterSaleHandleType.REFUND_ENTRY_BILL.getType())
                        || Objects.equals(selectHandleType, AfterSaleHandleType.REFUSE_BILL)
                        || Objects.equals(selectHandleType, AfterSaleHandleType.REFUSE)
                ) {
                    afterSaleQuantity = afterSaleQuantity + afterSale.getQuantity() * maxAfterSaleQuantity.intValue();
                } else {
                    afterSaleQuantity += afterSale.getQuantity();
                }
            }
        }


        //售后总数量不能大与最大可售后数量
        if (quantity.intValue() + afterSaleQuantity > maxAfterSaleQuantity.intValue() * amount) {
            return AjaxResult.getError(ResultConstant.OVER_AFTER_SALE_QUANTITY);
        }
        MathContext mc = new MathContext(6, RoundingMode.HALF_EVEN);
        BigDecimal afterSalePercent = quantity.divide(maxAfterSaleQuantity, mc);
        BigDecimal couponMoney = afterSalePercent.multiply(orderItem.getPrice()).setScale(2,BigDecimal.ROUND_DOWN);
        AfterSaleOrderCalculator calculator = new AfterSaleOrderCalculator();
        calculator.setCouponMoney(couponMoney);
        calculator.setSkuType(inventory.getType());

        return AjaxResult.getOK(calculator);
    }

}
