package net.summerfarm.mall.contexts;

import com.google.common.base.Splitter;
import net.summerfarm.common.delayqueue.DelayQueueItem;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.URLUtils;
import net.summerfarm.mall.common.util.DateUtils;
import net.summerfarm.mall.model.domain.Category;
import net.summerfarm.mall.payments.common.enums.BasePayTypeEnum;

import org.apache.commons.lang3.RandomStringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.DelayQueue;

/**
 * @Package: com.manageSystem.contexts
 * @Description: 系统常用参数
 * @author: <EMAIL>
 * @Date: 2016/7/27
 */
public class Global {
    private static final Logger logger = LoggerFactory.getLogger(Global.class);
    /**
     * 市
     */
    public static final String NO_AREA_CITY = "NO_AREA_CITY";
    /**
     * 超级管理员
     */
    public static final String SA = "SUPER_ADMIN";

    /**
     *大客户提前截单
     */
    public static Integer CLOSE_ORDER_TYPE = 1;

    /**
     * 茶百道adminId
     */
    public static final String CBD_ADMIN_ID = "CBD_admin_id";

    /**
     * 分隔符号
     */
    public static final String SEPARATING_SYMBOL = ",";

    /**
     * 默认分隔器
     */
    public static final Splitter DEFAULT_SPLITTER = Splitter.on(SEPARATING_SYMBOL);

    public static final Splitter OMIT_EMPTY_SPLITTER = Splitter.on(SEPARATING_SYMBOL).omitEmptyStrings();

    public static final int DAY_OF_WEEK_NUM = 7;

    /**
     * 商户id
     */
    public static final String MERCHANT_ID = "merchantId";

    /**
     * 子账号id
     */
    public static final String ACCOUNT_ID = "accountId";

    /**
     * 微信openID
     */
    public static final String OPEN_ID = "openId";
    /**
     * phone
     */
    public static final String PHONE = "phone";

    /**
     * 微信unionID
     */
    public static final String UNION_ID = "unionid";

    /**
     * 小程序openID
     */
    public static final String MP_OPEN_ID = "mpOpenId";

    /**
     * 登录方式
     */
    public static final String LOGIN_WAY = "loginWay";
    /**
     * 微信公众号
     */
    public static final String WECHAT_GZH = "wechat_gzh";
    /**
     * 微信小程序
     */
    public static final String WECHAT_MNG = "wechat_mng";

    /**
     *
     */
    public static final String MERCHANT_SUBJECT = "merchantSubject";

    public static final String AFTER_SALE_COUPON_FRONT = "after_sale";

    /**
     * 商城域名
     */
    public static String DOMAIN_NAME = "https://h5.summerfarm.net";

    /**
     * POP商城域名
     */
    public static String POP_DOMAIN_NAME = "https://konglong.cosfo.cn";

    /**
     * 顶级域名
     */
    public static String TOP_DOMAIN_NAME = URLUtils.getDomainName(Global.DOMAIN_NAME);;

    public static final String BIG_MERCHANT = "大客户";

    public static final String COMMON_MERCHANT = "单店";


    public static final Integer NAN_JING = 17;

    public static final String TIME_FRAMES = "timeFrames";

    /**
     * 不支持10点前精准送
     */
     public static final String NOT_SUPPORT_TEN_TIME_FRARM = "notSupportTenTimeFrames";

    /**优惠券到期类型 固定时间到期*/
    public static final  Integer FIX_DATE_COUPON_TYPE = 0;

    /**优惠券到期类型 指定时间点到期*/
    public static final  Integer APPOINT_DATE_COUPON_TYPE = 1;

    /**临时使用 券id */
    public static final  String TEMPORARY_COUPON_ID = "coupon_id";

    /**
     * 短信服务商切换，1为叮咚云，其他则用阿里云
     */
    public static final int SMS_MODE = 2;

    public static final LocalTime NJ_CLOSING_ORDER_TIME = LocalTime.of(22, 00, 00);

    public static final LocalTime TWENTY_THREE_CLOSING_TIME = LocalTime.of(23, 00, 00);

    public static final LocalTime NINE_CLOSING_TIME = LocalTime.of(21, 00, 00);

    /**
    * 22:30截单
    */
    public static final Integer TYPE_NORMAL = 0;

    /**
    * 22 截单
    */
    public static final Integer TYPE_TWENTY_TWO = 1;

    /**
    * 23点截单
    */
    public static final Integer TYPE_TWENTY_THREE = 2;

    /**
     * 23点截单
     */
    public static final Integer TYPE_NINE = 3;

    public static final Integer TI_MING_PURCHASE = 5;

    /**
     * 省心送调拨计算
     */
    public static final Integer TI_MING_ALLOCATION = 15;

    /**
    * 上海城市编号
    */
    public static final Integer SH_AREA_NO =2750;

    /**
     * 特殊编号
    */
    public static final String AL = "上海艾伦";

    public  static final String AL_STORE_NO = "上海艾伦编号";

    //业务相关参数

    /**
     * 延迟队列
     */
    public static final BlockingQueue<DelayQueueItem> delayQueue = new DelayQueue<>();


    public static final Map<Integer, String> areaMap = new HashMap<>();


    /**
     * 精准送运费
     */
    public static final BigDecimal TIME_FRAME_DELIVERY_FEE = BigDecimal.valueOf(30);

    /**
     * 超时加单费用
     */
    public static final BigDecimal OUT_TIMES_FEE = BigDecimal.valueOf(5);

    /**
     * 每日截单时间-1 <= orderTIme < 每日截单时间
     */
    public static final LocalTime CLOSING_ORDER_TIME = LocalTime.of(22, 00, 00);

    /**
     * 每日截单时间-1 <= orderTIme < 每日截单时间
     */
    public static final LocalTime NAN_JING_CLOSING_ORDER_TIME = LocalTime.of(22, 00, 00);

    /**
     * 每日截单时间-1 <= orderTIme < 每日截单时间
     */
    public static final LocalTime TWENTY_THREE_CLOSING_ORDER_TIME = LocalTime.of(23, 00, 00);

    /**
     * 每日截单时间-1 <= orderTIme < 每xianmu日截单时间
     */
    public static final LocalTime BIG_CLOSING_ORDER_TIME = LocalTime.of(20, 00, 00);

    /**
     * 大客户截单
     */
    public static final LocalTime CBD_BIG_CLOSING_ORDER_TIME = LocalTime.of(18, 00, 00);

    /**
     * 每个自然日结束时间
     */
    public static final LocalTime END_DAY_TIME = LocalTime.of(23, 59, 59);

    /**
     * 春节配送时间
     */
    public static final LocalDate SPRING_FESTIVAL_DATE = LocalDate.of(2021, 02, 15);

    /**
     * ten o'clock in the morning
     */
    public static final LocalTime MORNING_TEN_CLOCK = LocalTime.of(10, 00, 00);

    /**
     * thirty minutes
     */
    public static final Long THIRTY_MINUTES = 30L;


    public static final String CLOSING_ORDER_TIME_STR = "22:00:00";

    public static final Integer ADD_TIME = 30;

    /**
     * 普通订单业务编号
     */
    public static final String NORMAL_ORDER_CODE = "01";

    /**
     * 普通订单业务编号
     */
    public static final String POP_ORDER_CODE = "PO";

    /**
     * 团购订单业务编号
     */
    public static final String TIMING_ORDER_CODE = "02";

    /**
     * 补运费
     */
    public static final String DELIVERY_FEE_ORDER_CODE = "03";

    /**
     * 代下单
     */
    public static final String SUBSTITUTED_TAKE_ORDER_CODE = "04";

    /**
     * 普通订单子订单业务编码
     */
    public static final String NORMAL_MASTER_ORDER_CODE = "M01";

    /**
     * 普通订单子订单业务编码
     */
    public static final String POP_MASTER_ORDER_CODE = "MPO";

    /**
     * 安佳淡奶油SKU
     */
    public static  final  String AJD_SKU = "56217";
    /**
     * 奶油黄金卡
     */
    public static final String VIRTUAL_GOODS_ORDER_CODE = "10";
    /**
     * 直发采购订单
     */
    public static final String DIRECT_PURCHASE_ORDER_CODE = "11";
    /**
     * 秒杀订单
     */
    public static final String PANIC_ORDER_CODE = "12";
    /**
     * 精准送sku
     */
    public static final String TIME_FRAME_FEE_SKU = "DF001TF0001";

    /**
     * 精准送SKU名称
     */
    public static final String TIME_FRAME_FEE_SKU_NAME = "精准送";

    public static final String AUDIT_URL ="";

    public static final int DELIVERY_FEE_CATEGORY = 3;

    public static final String WECHAT_PAY = "微信支付";

    public static final String MINI_PROGRAM_PAY = "小程序支付";

    public static final String BOC_PAY = "中银支付";

    public static final String XIANMU_CARD = "鲜沐卡";

    public static final String CMD_PAY = "招行支付";

    public static final String B2B_WECHAT_FIRE_FACE = "微信B2B支付";

    public static final String DIN_PAY = "智付间联";

    public static final String CHECK_CHAR = "^[ !。？，,.?A-z0-9\\u4e00-\\u9fa5]*$";

    /**
     * 数据库常量值
     */
    public static Map<String, String> CONFIG = new HashMap<>();

    //精准送 配置表key
    public static final String TIME_FRAME = "timeFrame";

    //普通配送 配置表key
    public static final String DELIVERY = "delivery";

    /**
     * 品类树形数据
     */
    public static Map<Integer, List<Category>> CATEGORY_TREE = new HashMap<>();

    /**
     * 乳制品
     */
    public static Set<Integer> DAIRY_SET = new HashSet<>();

    /**
     * 冻结库存标识
     */
    public static String LOCK = "lock";

    /**
     * 自提订单不支持自动确认收货
     */
    public static String OWN_ORDER_NOT_AUTO_OVER = "自提订单不支持自动确认收货";

    /**
     * 客服是否在线
     */
    public static boolean CUSTOMER_SERVICE_OFFLINE = false;


    public static final String AUDIT_WECHAT_USER_QR = "AUDIT_WECHAT_USER_QR";

    public static final String OFFICIAL_WECHAT_USER_QR_URL = "https://wework.qpic.cn/wwpic/636482_7FdlfZS9QkqfvAa_1696760038/0";
    /**
     * 加密私钥
     */
    public static final String PRI_KEY = "-----BEGIN PRIVATE KEY-----\n" +
            "MIIBVQIBADANBgkqhkiG9w0BAQEFAASCAT8wggE7AgEAAkEAywgONpWQipLoNPdr\n" +
            "KdZp+zaqOnMh7Oe6xoXmcDeTY8PLj22KyvI2Mnck8uJjxEjJmt0FiUzE1eSIn9RO\n" +
            "61o6YwIDAQABAkBkefMWvkPO4E78T2mARfUi9nWeAXZLmI6NSpy08jwFxXEbPQiV\n" +
            "v+k3MdVcc4XyMCFBtVV7wBEi5esD1wrt39nRAiEA5/6S77Ng3AGwtURnsF+iduO1\n" +
            "EykAiydRsik2gqWaUEsCIQDgCkQVgF6ljc3P36mnfA9w8JUPkIVVXxzDncYqYjjf\n" +
            "SQIhALIYjaZ0F0xuA8D0NYGBuU/5uL1vwZoR4jaaSt160f8HAiEApYdQOGapBz6X\n" +
            "Mp/49DLYIcTPDTnlGWd1lB4s/MGnbWkCIHOeTAjGZ6yLTSBwR8TD7wCaNKt+uces\n" +
            "nwhB1jRGLgFq\n" +
            "-----END PRIVATE KEY-----\n";

    public static boolean HEARTBEAT_FLAG = true;

    /**
     * 当前时间的截单开始时间
     * 若当前时间>=截单时间，return: {当前日期}
     * 当前时间<截单时间，return：{(当前日期-1)}
     *
     * @return
     */
    public static LocalDateTime getStartTime(LocalTime closeTime) {
        LocalDateTime closingTime = LocalDateTime.of(LocalDate.now(), closeTime);
        return LocalDateTime.now().isBefore(closingTime) ? closingTime.minusDays(1) : closingTime;
    }

    /**
     * 当前时间的截单开始时间 大客户计算配送计划时间
     * 若当前时间>=截单时间，return: {当前日期}
     * 当前时间<截单时间，return：{(当前日期-1)}
     *
     * @return
     */
    public static LocalDateTime getBigMerchantStartTime() {
        LocalDateTime closingTime = LocalDateTime.of(LocalDate.now(), BIG_CLOSING_ORDER_TIME);
        return LocalDateTime.now().isBefore(closingTime) ? closingTime.minusDays(1) : closingTime;
    }

    /**
     * 指定时间的截单开始时间
     *
     * @return
     */
    public static LocalDateTime getStartTimeByDate(Date date) {
        LocalDate localDate = DateUtils.date2LocalDate(date);
        LocalDateTime localDateTime = DateUtils.date2LocalDateTime(date);
        LocalDateTime closingTime = LocalDateTime.of(localDate, CLOSING_ORDER_TIME);
        return localDateTime.isBefore(closingTime) ? closingTime.minusDays(1) : closingTime;
    }

    /**
     * 当前时间 月份开始时间
     *
     * @return
     */
    public static LocalDateTime getMonthStartTime(LocalDateTime now) {
        LocalDateTime start = null;
        LocalDate firstday = LocalDate.of(now.toLocalDate().getYear(), now.toLocalDate().getMonth(), 1);
        LocalDate lastDay = now.toLocalDate().with(TemporalAdjusters.lastDayOfMonth());
        if (now.toLocalDate().isEqual(lastDay) && now.toLocalTime().isAfter(CLOSING_ORDER_TIME)) {
            start = lastDay.atTime(CLOSING_ORDER_TIME);
        } else {
            start = firstday.minusDays(1).atTime(CLOSING_ORDER_TIME);
        }
        return start;
    }

    /**
     * 当前时间 月份开始时间
     *
     * @return
     */
    public static LocalDateTime getMonthEndTime(LocalDateTime now) {
        LocalDateTime end = null;
        LocalDate lastDay = now.toLocalDate().with(TemporalAdjusters.lastDayOfMonth());
        if (now.toLocalDate().isEqual(lastDay) && now.toLocalTime().isAfter(CLOSING_ORDER_TIME)) {
            end = lastDay.plusDays(1).with(TemporalAdjusters.lastDayOfMonth()).atTime(CLOSING_ORDER_TIME);
        } else {
            end = lastDay.atTime(CLOSING_ORDER_TIME);
        }
        return end;
    }

    /**
     * 当前时间 年份开始时间
     * @param now
     * @return
     */
    public static LocalDateTime getYearStartTime(LocalDateTime now){
        LocalDateTime start = null;
        LocalDate lastDay = now.toLocalDate().with(TemporalAdjusters.lastDayOfYear());
        if (now.toLocalDate().isEqual(lastDay) && now.toLocalTime().isAfter(CLOSING_ORDER_TIME)) {
            start = lastDay.atTime(CLOSING_ORDER_TIME);
        } else {
            start = now.toLocalDate().with(TemporalAdjusters.firstDayOfYear()).minusDays(1).atTime(CLOSING_ORDER_TIME);
        }
        return start;
    }

    private static DateTimeFormatter ORDER_NO_FORMATER = DateTimeFormatter.ofPattern("MMddHHmmSS");

    /**
     * 生成订单号
     *
     * 该方法根据传入的订单代码和当前时间生成一个唯一的订单号。
     * 订单号由以下部分组成：
     * 1. 传入的订单代码(如01普通订单，02省心送订单，PO POP订单等)
     * 2. 当前年份(保留后2位)
     * 3. 6位随机字母数字组合（大写）
     * 4. 当前月份、日期和小时/分钟/毫秒（格式：MMddHHmmSS）
     *
     * @param orderCode 订单代码，用作订单号的前缀
     * @return 生成的唯一订单号,长度20位
     */
    public static String createOrderNo(String orderCode) {
        LocalDateTime now = LocalDateTime.now();
        String year = String.valueOf(now.getYear()).substring(2);
        String randomString = RandomStringUtils.randomAlphanumeric(6).toUpperCase();
        String monthDay = now.format(ORDER_NO_FORMATER);
        return orderCode + year + randomString + monthDay;
    }

    /**
     * 生成售后订单号
     *
     * 该方法根据传入的原订单号生成一个唯一的售后订单号。
     * 售后订单号由以下部分组成：
     * 1. 原订单号
     * 2. 6位随机字母数字组合（大写）
     *
     * @param orderNo 原订单号
     * @return 生成的唯一售后订单号
     */
    public static String createAfterSaleOrderNo(String orderNo) {
        String randomString = RandomStringUtils.randomAlphanumeric(6).toUpperCase();
        return orderNo + randomString;
    }

    public static void main(String[] args) {
        // 测试createOrderNo方法
        String orderCode = "PO";
        String orderNo = createOrderNo(orderCode);
        System.out.println(new Date() + "生成的订单号: " + orderNo);

        // 生成多个订单号以验证唯一性
        Set<String> orderNos = new HashSet<>();
        final int numsToTest = 10_0000; // 基本上可以保证在极短时间内生成10万笔订单而不重复。
        for (int i = 0; i < numsToTest; i++) {
            orderNos.add(createOrderNo(orderCode));
        }
        System.out.println("生成的" + numsToTest + "个订单号中唯一的数量: " + orderNos.size());
    }


    public  static String getErrorInfoFromException(Exception e) {
        try {
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            pw.flush();
            sw.flush();
            return "\r\n" + sw + "\r\n";
        } catch (Exception e2) {
            return "ErrorInfoFromException";
        }
    }

    public static LocalDateTime getNJStartTime() {
        LocalDateTime closingTime = LocalDateTime.of(LocalDate.now(), NAN_JING_CLOSING_ORDER_TIME);
        return LocalDateTime.now().isBefore(closingTime) ? closingTime.minusDays(1) : closingTime;
    }

    public static LocalDateTime getTwentyThreeStartTime() {
        LocalDateTime closingTime = LocalDateTime.of(LocalDate.now(),TWENTY_THREE_CLOSING_ORDER_TIME );
        return LocalDateTime.now().isBefore(closingTime) ? closingTime.minusDays(1) : closingTime;
    }

    public static LocalDateTime getNineThreeStartTime() {
        LocalDateTime closingTime = LocalDateTime.of(LocalDate.now(),NINE_CLOSING_TIME );
        return LocalDateTime.now().isBefore(closingTime) ? closingTime.minusDays(1) : closingTime;
    }

    /**
     * 根据支付订单号计算订单号
     * @param payOrderNo 支付订单号
     * @return 订单号
     */
    public static String getOrderNo(String payOrderNo) {
        return payOrderNo.split("_")[0];
    }

    /**
     * 判断支付类型
     * @param payTypeStr 支付类型
     * @return
     */
    public static BasePayTypeEnum getPayTypeEnum(String payTypeStr){
        switch (payTypeStr) {
            case WECHAT_PAY:
            case MINI_PROGRAM_PAY:
                return BasePayTypeEnum.WEIX;
            case XIANMU_CARD:
                return BasePayTypeEnum.XIANMU;
            case CMD_PAY:
                return BasePayTypeEnum.CMB;
            case B2B_WECHAT_FIRE_FACE:
                return BasePayTypeEnum.B2B_WECHAT_FIRE_FACE;
            case DIN_PAY:
                return BasePayTypeEnum.DIN_PAY;
            default:
                throw new DefaultServiceException(1, "支付类型异常");
        }
    }

    /**
     * 异常订单处理
     */
    public static String ORDER_ABNORMAL_HANDLE = "异常订单处理";

    /**
     * 左括号
     */
    public static String LEFT_BRACKET = "(";

    /**
     * 左括号
     */
    public static String RIGHT_BRACKET = ")";

    /**
     *
     */
    public static String SLASH = "/";

    /**
     *中划线
     */
    public static String STRIKETHROUGH = "-";

    /**
     *乘号
     */
    public static String TIMES_SIGN = "*";

    /**
     * 计算均价单位
     */
    public static List<String> avgUnitList = Arrays.asList ("kg","kG","Kg","KG","斤");

    /**
     * 默认配送完成时间
     */
    public static final String DEFAULT_COMPLETE_DELIVERY_TIME = "14:00";

    /**
     * 处理销售说明
     * @param weight
     * @return
     */
    public static String subSaleInstructions(String weight) {
        if (Objects.isNull(weight)) {
            return null;
        }
        if (!weight.endsWith(RIGHT_BRACKET)) {
            return weight;
        }
        if (!weight.contains(LEFT_BRACKET)) {
            return weight;
        }

        int index = weight.lastIndexOf(LEFT_BRACKET);
        return weight.substring(0, index);
    }

    /**
     * 默认的运费规则
     */
    public static final String DEFAULT_DELIVERY_FEE_RULE = "{10}";

    /**
     * 大客户18点截单时间
     */
    public static final String EIGHTEEN_PM = "18:00:00";

    /**
     * 大客户20点截单时间
     */
    public static final String TWENTY_PM = "20:00:00";

    /**
     * 微信推送相关topic
     */
    public static final String TOPIC_CRM_WX_OFFICIAL_PUSH = "topic_crm_wx_push";
    /**
     * 微信推送相关tag
     */
    public static final String TAG_CRM_WX_FEEDBACK = "tag_crm_wx_feedback_push";

    /**
     * 微信推送相关group :GID_tms_purchase_order_create
     */
    public static final String GID_CRM_WX_FEEDBACK = "GID_crm_wx_feedback_push";

    /**
     * 加购人群包ID配置KEY
     */
    public static final String ADD_PURCHASE_POOL_INFO_ID = "ADD_PURCHASE_POOL_INFO_ID";

    //商城缓存key
    public static final String CACHE_SKU = "CACHE:SKU:";
    public static final String CACHE_SPU = "CACHE:SPU:";
    public static final String CACHE_SORT_COMBINATION_TAB ="CACHE:SORT_COMBINATION_TAB:";
    public static final String CACHE_FENCE_INFO ="CACHE:FENCE_INFO:";
    public static final String CACHE_MERCHANT_INFO ="CACHE:MERCHANT_INFO:";
    public static final String CACHE_MERCHANT_SUB_ACCOUNT ="CACHE:MERCHANT_SUB_ACCOUNT:";
    public static final String CACHE_PRODUCTS_PROPERTY ="CACHE:CACHE_PRODUCTS_PROPERTY:";
    public static final String CACHE_PRODUCT_PRICE ="CACHE:CACHE_PRODUCT_PRICE:";
    public static final String CACHE_CATEGORY = "CACHE:CATEGORY:";
    public static final String CACHE_AREA = "CACHE:AREA:";
    public static final String CACHE_FRONT_CATEGORY = "CACHE:CACHE_FRONT_CATEGORY:";
    public static final String CACHE_M_COUPON = "CACHE:CACHE_M_COUPON:";
    public static final String CACHE_M_POOL = "CACHE:CACHE_M_POOL:";
    public static final String CACHE_AREA_SKU = "CACHE:CACHE_AREA_SKU:";
    public static final String CACHE_WAREHOUSE_INVENTORY_MAPPING  = "CACHE:WAREHOUSE_INVENTORY_MAPPING:";
    public static final String CACHE_CONFIG  = "CACHE:CONFIG:";
    public static final String CACHE_COUPON = "CACHE:COUPON:";

    /**
     * 鲜果属性排序规则
     */
    public static final List<String> FRUIT_PROPERTY_SORT = Arrays.asList("品种","口味","熟度","产地",
            "品牌","储藏温度","储藏区域","外包装","商品形态","湿度","肉类品种","蔬菜品种","是否含糖","面筋含量","乳脂含量","使用方法",
            "成分","每100g含蛋白质","每100g乳脂含量","其他");

    /**
     * 非鲜果属性排序规则
     */
    public static final List<String> NON_FRUIT_PROPERTY_SORT = Arrays.asList("品牌","储藏温度","储藏区域",
            "乳脂含量","每100g乳脂含量","每100g含蛋白质","肉类品种","蔬菜品种","面筋含量","口味","品种","产地",
            "成分","商品形态", "使用方法","外包装","湿度","是否含糖","其他","熟度");
    /**
     * 省心送订单自动售后天数
     */
    public static final Integer TIMING_ORDER_AFTER_DAY = 90;

    /**
     * 省心送订单弹窗天数
     */
    public static final Integer TIMING_ORDER_AFTER_VIEW_DAY = 30;

    /**
     * 省心送订单自动售后剩余1天短信触达
     */
    public static final Integer TIMING_ORDER_AFTER_SMS_FIRST = 1;

    /**
     * 省心送订单自动售后剩余3天短信触达
     */
    public static final Integer TIMING_ORDER_AFTER_SMS_SECOND = 3;

    /**
     * 省心送订单自动售后剩余7天短信触达
     */
    public static final Integer TIMING_ORDER_AFTER_SMS_THIRD = 7;

    /**
     * 省心送订单自动售后剩余14天短信触达
     */
    public static final Integer TIMING_ORDER_AFTER_SMS_FOURTH = 14;

    /**
     * 省心送订单自动售后剩余30天短信触达
     */
    public static final Integer TIMING_ORDER_AFTER_SMS_FIFTH = 30;

    /**
     * 省心送订单自动售后开始时间
     */
    public static final LocalDateTime TIMING_ORDER_AFTER_START_TIME = LocalDateTime.of(LocalDate.of(2023,1,1), LocalTime.MIN);

    /**
     * 省心送订单发布时间点
     */
    public static final LocalDateTime TIMING_ORDER_OLD_TIME = LocalDateTime.of(LocalDate.of(2024,1,10), LocalTime.of(14, 00, 00));

    /**
     * b2b餐饮类型  B2B_RETAIL_TYPE
     */
    public static final String B2B_RETAIL_TYPE = "餐饮店";
    /**
     *  mall b2b灰度key
     */
    public static final String MALL_B2B_GRAY = "mall:b2b:gray";

    public static final String MALL_B2B_COUPON_KET = "MALL_B2B_COUPON_KET";

    public static final String ACTIVITY_CACHE_SWITCH = "ACTIVITY_CACHE_SWITCH";

    /**
     * POP客户售后推迟时间 单位：分钟
     */
    public static final String POP_AFTER_SALE_DELAY_TIME = "pop_after_sale_delay_time";

    /**
     * POP客户订单滚动售后规则时间 单位：小时
     */
    public static final String POP_AFTER_SALE_ROLLING_TIME = "pop_after_sale_rolling_time";

    /**
     * POP大客户的ID
     */
    public static final String POP_ADMIN_ID ="pop_admin_id";

    /**
     * POP商城公众号关注推送文案
     */
    public static final String POP_PUBLIC_CONCERNED_ABOUT ="pop_public_concerned_about";

    /**
     * POP商城原始公众号ID
     */
    public static final String POP_PUBLIC_ORIGINAL_ID ="pop_public_original_id";

    /**
     * POP商城配送完成时间
     */
    public static final String POP_COMPLETE_DELIVERY_TIME = "pop_complete_delivery_time";

    /**
     * 到手价、标签展示所有商品需要屏蔽优惠券类型
     */
    public static final String HIDE_COUPON_GROUP = "hide_coupon_group";

    /**
     * 大客户是否放开营销（默认否）
     */
    public static final String MAJOR_MARKETING_RESTRICT ="major_marketing_restrict";

    /**
     * 样品spu
     */
    public static final String SAMPLE_SPU ="sample_spu";

    /**
     * 前端通用错误弹窗code码
     */
    public static final String TIPS_ERROR_CODE ="TIPS_ERROR";
}
