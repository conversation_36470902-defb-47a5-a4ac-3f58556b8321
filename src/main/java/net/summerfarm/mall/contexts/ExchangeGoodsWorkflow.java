package net.summerfarm.mall.contexts;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.enums.AfterSaleOrderStatus;
import net.summerfarm.mall.enums.DistOrderSourceEnum;
import net.summerfarm.mall.enums.SaleStockChangeTypeEnum;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import net.summerfarm.mall.model.vo.DeliveryPlanVO;
import net.summerfarm.mall.service.OrderService;
import net.summerfarm.mall.service.QuantityChangeRecordService;
import net.summerfarm.mall.service.facade.WmsAreaStoreFacade;
import net.summerfarm.mall.service.facade.dto.AreaStoreLockReq;
import net.summerfarm.mall.service.facade.dto.AreaStoreQueryReq;
import net.summerfarm.mall.service.facade.dto.AreaStoreQueryRes;
import net.summerfarm.mall.service.facade.dto.OrderLockSkuDetailReqDTO;
import net.summerfarm.wnc.client.enums.SourceEnum;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR> ct
 * create at:  2021/6/8  14:49
 */
@Component
public class ExchangeGoodsWorkflow extends AfterSaleWorkflow {

    @Resource
    AfterSaleWorkflowAction afterSaleWorkflowAction;
    @Resource
    AfterSaleOrderAction afterSaleOrderAction;
    @Resource
    AfterSaleProofMapper afterSaleProofMapper;
    @Resource
    AfterSaleOrderMapper afterSaleOrderMapper;
    @Resource
    AfterSaleDeliveryPathMapper afterSaleDeliveryPathMapper;
    @Resource
    AfterSaleDeliveryDetailMapper afterSaleDeliveryDetailMapper;
    @Resource
    MerchantMapper merchantMapper;
    @Resource
    OrderService orderService;
    @Resource
    QuantityChangeRecordService quantityChangeRecordService;
    @Resource
    DeliveryPlanMapper deliveryPlanMapper;
    @Resource
    private WmsAreaStoreFacade areaStoreFacade;


    @Override
    @Transactional(propagation = Propagation.REQUIRED,rollbackFor = Exception.class)
    @Deprecated
    AjaxResult handle(AfterSaleOrderVO afterSaleOrderVO) {
        Integer status = afterSaleOrderVO.getHandleType();
        //更新状态
        String afterSaleOrderNo = afterSaleOrderVO.getAfterSaleOrderNo();
        String sku = afterSaleOrderVO.getSku();
        String orderNo = afterSaleOrderVO.getOrderNo();
        Integer quantity = afterSaleOrderVO.getQuantity();
        AfterSaleProof updateProof = new AfterSaleProof();
        AfterSaleOrder updateAfterOrder = new AfterSaleOrder();
        AfterSaleOrderVO result = afterSaleOrderMapper.selectByAfterSaleOrderNo(afterSaleOrderNo);
        afterSaleOrderVO.setmId(result.getmId());
        Merchant merchant = merchantMapper.selectOneByMid(result.getmId());
        //不同意
        if(Objects.equals(status,-1)){
            //更改审核订单列表
            updateAfterOrder.setStatus(AfterSaleOrderStatus.WAIT_HANDLE.getStatus());
            //更新凭证状态
            updateProof.setStatus(AfterSaleOrderStatus.WAIT_HANDLE.getStatus());
            afterSaleWorkflowAction.notPass(afterSaleOrderVO);

            //通过 扣库存处理冻结
        } else {
            //更改审核订单列表
            updateAfterOrder.setStatus(AfterSaleOrderStatus.SUCCESS.getStatus());
            //更新凭证状态
            updateProof.setStatus(AfterSaleOrderStatus.SUCCESS.getStatus());
            //省心送id 存在说明是省心送未到货退款,获取配送计划的配送地址
            Long contactId = afterSaleOrderAction.getContactId(orderNo,afterSaleOrderVO.getDeliveryId());
            /*orderService.updateStock(sku, merchant.getAreaNo(), -quantity,afterSaleOrderVO.getHandler(), SaleStockChangeTypeEnum.REFUNDS, orderNo, recordMap,false,contactId);
            quantityChangeRecordService.insert(recordMap);*/
            //改用新模型获取库存配送信息
            List<String> skuList = Collections.singletonList(sku);
            AreaStoreQueryReq areaStoreQueryReq = new AreaStoreQueryReq();
            areaStoreQueryReq.setContactId(contactId);
            areaStoreQueryReq.setSkuCodeList(skuList);
            areaStoreQueryReq.setMId(result.getmId());
            areaStoreQueryReq.setSource(DistOrderSourceEnum.XM_AFTER_SALE.getCode());
            Map<String, AreaStoreQueryRes> storeQueryResMap = areaStoreFacade.getInfo(areaStoreQueryReq);
            AreaStoreQueryRes queryStore = storeQueryResMap.get(sku);
            //库存扣减 改用新模型
            AreaStoreLockReq storeLockReq = new AreaStoreLockReq();
            storeLockReq.setOrderNo(afterSaleOrderVO.getAfterSaleOrderNo());
            storeLockReq.setOperatorNo(afterSaleOrderVO.getAfterSaleOrderNo());
            storeLockReq.setIdempotentNo(afterSaleOrderVO.getAfterSaleOrderNo());
            storeLockReq.setOrderType(net.summerfarm.enums.SaleStockChangeTypeEnum.REFUNDS.getTypeName());
            storeLockReq.setContactId(contactId);
            List<OrderLockSkuDetailReqDTO> orderLockSkuDetailReqDTOS = new ArrayList<>();
            OrderLockSkuDetailReqDTO orderLockSkuDetailReqDTO = new OrderLockSkuDetailReqDTO();
            orderLockSkuDetailReqDTO.setSkuCode(sku);
            orderLockSkuDetailReqDTO.setOccupyQuantity(quantity);
            orderLockSkuDetailReqDTOS.add(orderLockSkuDetailReqDTO);
            storeLockReq.setOrderLockSkuDetailReqDTOS(orderLockSkuDetailReqDTOS);
            storeLockReq.setMerchantId(afterSaleOrderVO.getmId());
            storeLockReq.setSource(SourceEnum.XM_AFTER_SALE.getValue());
            storeLockReq.setOperatorName(afterSaleOrderVO.getHandler());
            areaStoreFacade.storeLock(storeLockReq);

            afterSaleOrderVO.setQueryStore(queryStore);
            AfterSaleDeliveryPath deliveryPath = afterSaleOrderAction.createDeliveryPath(afterSaleOrderVO);
            afterSaleDeliveryPathMapper.insertAfterSaleDeliveryPath(deliveryPath);
            //生成售后配送单详情
            List<AfterSaleDeliveryDetail> details = new ArrayList<>();
            //补货详情
            AfterSaleDeliveryDetail deliveryDetail = afterSaleOrderAction.createDeliveryDetail(sku, quantity, deliveryPath.getId());
            details.add(deliveryDetail);
            afterSaleDeliveryDetailMapper.insertAfterSaleDeliveryDetailList(details);

        }
        //更新审核人
        updateProof.setAuditer(afterSaleOrderVO.getHandler());
        updateProof.setHandler(afterSaleOrderVO.getHandler());
        updateProof.setHandleRemark(afterSaleOrderVO.getHandleRemark());
        updateProof.setExtraRemark(afterSaleOrderVO.getExtraRemark());
        updateProof.setAfterSaleOrderNo(afterSaleOrderNo);
        updateProof.setAuditetime(LocalDateTime.now());
        updateProof.setHandletime(LocalDateTime.now());
        afterSaleProofMapper.updateByAfterSale(updateProof);
        updateAfterOrder.setView(0);
        updateAfterOrder.setAfterSaleOrderNo(afterSaleOrderNo);
        afterSaleOrderMapper.updateByAfterSaleOrderNo(updateAfterOrder);
        return AjaxResult.getOK();
    }

    @Override
    @Deprecated
    AjaxResult audit(AfterSaleOrderVO afterSaleOrderVO) {
        return null;
    }
}
