package net.summerfarm.mall.contexts;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import net.summerfarm.mall.service.CustomWorkFlow;

/**
 * <AUTHOR> ct
 * create at:  2019/12/9  14:44
 */
@Deprecated
abstract public  class AfterSaleWorkflow implements CustomWorkFlow {

    public static final int HANDLE = 0;

    public static final int AUDIT = 1;
    @Override
    public AjaxResult workflow(Integer type,AfterSaleOrderVO afterSaleOrderVO) {

        AjaxResult handle = null;
        switch (type){
            case HANDLE :
                handle = handle(afterSaleOrderVO);
                break;
            case AUDIT :
                handle = audit(afterSaleOrderVO);
                break;
            default:
                break;
        }
        return handle;
    }

    abstract AjaxResult handle(AfterSaleOrderVO afterSaleOrderVO);

    abstract AjaxResult audit(AfterSaleOrderVO afterSaleOrderVO);


}
