package net.summerfarm.mall.contexts;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.enums.AfterSaleHandleType;
import net.summerfarm.enums.AfterSaleOrderStatus;
import net.summerfarm.enums.OrderTypeEnum;
import net.summerfarm.mall.common.mq.MQData;
import net.summerfarm.mall.common.mq.MType;
import net.summerfarm.mall.enums.OrderStatusEnum;
import net.summerfarm.mall.factory.AfterSaleCalculatorFactory;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.vo.*;
import net.summerfarm.mall.service.AfterSaleOrderService;
import net.summerfarm.mall.service.OrderService;
import net.summerfarm.mall.wechat.templatemessage.InterceptSuccessMsg;
import net.summerfarm.mall.wechat.templatemessage.TemplateMsgSender;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date
 */
@Component
@Slf4j
public class WholeOrderWorkflow extends AfterSaleWorkflow {

    @Resource
    private AfterSaleDeliveryPathMapper afterSaleDeliveryPathMapper;
    @Resource
    private AfterSaleCalculatorFactory afterSaleCalculatorFactory;
    @Resource
    private AfterSaleOrderMapper afterSaleOrderMapper;
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private OrderItemMapper orderItemMapper;
    @Resource
    private OrderService orderService;
    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;
    @Resource
    private RefundMapper refundMapper;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private AfterSaleProofMapper afterSaleProofMapper;
    @Resource
    private AfterSaleOrderService afterSaleOrderService;
    @Resource
    private AfterSaleWorkflowAction afterSaleWorkflowAction;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private PrepayInventoryRecordMapper prepayInventoryRecordMapper;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private TemplateMsgSender templateMsgSender;
    @Resource
    private DiscountCardUseRecordMapper discountCardUseRecordMapper;
    @Resource
    private DiscountCardToMerchantMapper discountCardToMerchantMapper;
    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    @Deprecated
    AjaxResult handle(AfterSaleOrderVO afterSaleOrderVO) {

        //修改售后记录
        AfterSaleOrder updateKeys = new AfterSaleOrder();
        BigDecimal handleNum = afterSaleOrderVO.getHandleNum();
        String afterSaleOrderNo = afterSaleOrderVO.getAfterSaleOrderNo();
        String s = JSON.toJSONString(afterSaleOrderVO);
        log.info("整单拦截审核："+s);
        AfterSaleOrderVO afterSaleOrder = afterSaleOrderMapper.selectByAfterSaleOrderNo(afterSaleOrderNo);
        Orders order = ordersMapper.selectByOrderNo(afterSaleOrder.getOrderNo());
        //省心送订单退款金额必须为0
        if (afterSaleOrder.getDeliveryId() != null){
            if (handleNum.compareTo(new BigDecimal(0)) != 0){
                return AjaxResult.getErrorWithMsg("省心送退款金额不为0");
            }
        }
        //-1拒绝，2退款'
        if(afterSaleOrderVO.getHandleType() != -1 && handleNum.compareTo(afterSaleOrder.getHandleNum()) > 0
                && !order.getType().equals(OrderTypeEnum.TIMING.getId())){
            BigDecimal couponMoney = checkoutHandleNum(order);
            if(Objects.isNull(couponMoney)){
                return AjaxResult.getErrorWithMsg("售后金额计算异常");
            }
            if(handleNum.compareTo(couponMoney) > 0 ){
                return AjaxResult.getErrorWithMsg("更改后的售后金额大于可售后金额");
            }
        }
        Integer handleType = afterSaleOrderVO.getHandleType();
        if (handleType == -1) {
            //记录
            log.info("管理员：拒绝了{}售后申请", afterSaleOrderVO.getAfterSaleOrderNo());
            updateKeys.setStatus(AfterSaleOrderStatus.FAIL.getStatus());
            if ("凭证不符合要求,请重新上传照片".equals(afterSaleOrderVO.getHandleRemark())) {
                updateKeys.setStatus(AfterSaleOrderStatus.RE_COMMIT.getStatus());
            }
            //校验错误类型
            afterSaleWorkflowAction.checkoutProof(afterSaleOrderVO);
            //不需要对库存进行任何操作，拦截退单是库存先进行相关操作，再生成售后单
        }
        //退款
        else if (Objects.equals(AfterSaleHandleType.BLOCK_REFUNDS.getType(),handleType)) {
            updateKeys.setStatus(AfterSaleOrderStatus.IN_HAND.getStatus());
            //省心送订单不退款
            if (!order.getType().equals(OrderTypeEnum.TIMING.getId())){
                afterSaleWorkflowAction.insertRefund(afterSaleOrderVO);
            }
            //录入账单
        } else if(Objects.equals(AfterSaleHandleType.BLOCK_INCOMING_BILLS.getType(),handleType)){

            //省心送订单不需要录入账单
            if (!order.getType().equals(OrderTypeEnum.TIMING.getId())){

            }
            updateKeys.setStatus(AfterSaleOrderStatus.IN_HAND.getStatus());
        }  else {
            return AjaxResult.getErrorWithMsg("不正确的处理状态");
        }

        //只更改状态售后的
        updateKeys.setAfterSaleOrderNo(afterSaleOrderVO.getAfterSaleOrderNo());
        updateKeys.setRecoveryType(afterSaleOrderVO.getRecoveryType());
        updateKeys.setView(0);
        afterSaleOrderMapper.updateByAfterSaleOrderNo(updateKeys);
        //获取最后一个
        List<AfterSaleProof> afterSaleProofs = afterSaleProofMapper.select(afterSaleOrderVO.getAfterSaleOrderNo());
        //根据id 修改proof
        AfterSaleProof updateProof = afterSaleWorkflowAction.conventAfterSaleProof(afterSaleOrderVO);
        updateProof.setStatus(updateKeys.getStatus());
        updateProof.setHandleNum(afterSaleOrderVO.getHandleNum());
        updateProof.setHandleType(afterSaleOrderVO.getHandleType());
        updateProof.setId(afterSaleProofs.get(afterSaleProofs.size() - 1).getId());
        updateProof.setAfterSaleType(afterSaleOrderVO.getAfterSaleType());
        updateProof.setRefundType(afterSaleOrderVO.getRefundType());
        updateProof.setQuantity(afterSaleOrderVO.getQuantity());
        //拦截订单审核会传过来
        updateProof.setRecoveryNum(afterSaleOrderVO.getRecoveryNum());
        //拒绝handler type不处理
        if (updateProof.getHandleType() == -1) {
            updateProof.setHandleType(null);
        }
        afterSaleProofMapper.updateById(updateProof);

        return AjaxResult.getOK();

    }

    @Override
    @Deprecated
    AjaxResult audit(AfterSaleOrderVO afterSaleOrderVO) {

        String afterSaleOrderNo = afterSaleOrderVO.getAfterSaleOrderNo();
        int status = afterSaleOrderVO.getStatus();
        AfterSaleOrderVO queryAfterOrder = afterSaleOrderMapper.selectByAfterSaleOrderNo(afterSaleOrderNo);
        //处于待审核状态才可以进入下一步
        if (!Objects.equals(AfterSaleOrderStatus.IN_HAND.getStatus(),queryAfterOrder.getStatus())) {
            return AjaxResult.getError("售后状态错误");
        }
        //获取最后一个
        List<AfterSaleProof> afterSaleProofs = afterSaleProofMapper.select(afterSaleOrderNo);
        //根据id 修改proof
        AfterSaleProof updateProof = new AfterSaleProof();
        AfterSaleOrder updateAfterOrder = new AfterSaleOrder();
        updateProof.setAuditer(afterSaleOrderVO.getAuditer());
        updateProof.setId(afterSaleProofs.get(afterSaleProofs.size() - 1).getId());
        //审核不通过
        if (status == 0) {
            //更改审核订单列表
            updateAfterOrder.setStatus(AfterSaleOrderStatus.WAIT_HANDLE.getStatus());
            //更新凭证状态
            updateProof.setStatus(AfterSaleOrderStatus.WAIT_HANDLE.getStatus());
            afterSaleWorkflowAction.notPass(afterSaleOrderVO);
        }
        //审核成功
        AjaxResult refundResult = AjaxResult.getOK();
        if(status == 1){
            //省心送返还配送次数
            if (queryAfterOrder.getDeliveryId() != null){
                DeliveryPlan plan = deliveryPlanMapper.selectById(queryAfterOrder.getDeliveryId());
                OrderVO orders = ordersMapper.selectByOrderyNo(plan.getOrderNo());
                if (orders == null ) {
                    return AjaxResult.getErrorWithMsg("修改记录不存在");
                }
                if (orders.getType() == 1) {
                    plan.setOrderNo(plan.getOrderNo());
                    plan.setUpdateTime(LocalDateTime.now());
                    plan.setStatus(11);
                    deliveryPlanMapper.updateById(plan);
                } else {
                    return AjaxResult.getErrorWithMsg("不正确的的订单类型");
                }

                updateProof.setStatus(AfterSaleOrderStatus.SUCCESS.getStatus());
                updateAfterOrder.setStatus(AfterSaleOrderStatus.SUCCESS.getStatus());

                //普通订单
            }else {
                //成功  退款
                BigDecimal handleNum = BigDecimal.ZERO;
                Boolean refundIsSuccess = true;
                AfterSaleOrderVO afterSaleOrder = afterSaleOrderMapper.selectByAfterSaleOrderNo(afterSaleOrderNo);
                AfterSaleOrder querySaleAfterOrder = new AfterSaleOrder();
                querySaleAfterOrder.setOrderNo(afterSaleOrder.getOrderNo());
                querySaleAfterOrder.setDeliveryed(afterSaleOrder.getDeliveryed());

                //发起退款
                if ( AfterSaleHandleType.BLOCK_REFUNDS.getType().equals(afterSaleOrderVO.getHandleType()) && (afterSaleOrderVO.getType() == 0 || afterSaleOrderVO.getType() == 3)) {
                    try {
                        refundResult = afterSaleOrderService.refund(afterSaleOrderNo);

                        //更改黄金卡使用次数,使用记录
                        List<DiscountCardUseRecord> useRecords = discountCardUseRecordMapper.selectByOrderNo(afterSaleOrder.getOrderNo());
                        if (!CollectionUtils.isEmpty(useRecords)) {
                            Map<Integer, List<DiscountCardUseRecord>> collect = useRecords.stream().collect(Collectors.groupingBy(DiscountCardUseRecord::getDiscountCardMerchantId));
                            collect.forEach((cardId, records) -> {
                                Integer discountCardMerchantId = records.get(0).getDiscountCardMerchantId();
                                DiscountCardToMerchant discountCardToMerchant = discountCardToMerchantMapper.selectByPrimaryKey(discountCardMerchantId);
                                int sum = records.stream().mapToInt(DiscountCardUseRecord::getUseTimes).sum();
                                DiscountCardToMerchant update = new DiscountCardToMerchant();
                                update.setId(discountCardMerchantId);
                                update.setUsedTimes(discountCardToMerchant.getUsedTimes() - sum);
                                update.setUpdateTime(LocalDateTime.now());
                                discountCardToMerchantMapper.updateByPrimaryKeySelective(update);
                            });
                        }

                        refundIsSuccess = AjaxResult.isSuccess(refundResult);

                    } catch (Exception e) {
                        if(e instanceof DefaultServiceException && Objects.equals("资金确认中，请稍后重新发起退款。",e.getMessage())){
                            throw new DefaultServiceException(0, "资金确认中，请稍后重新发起退款。");
                        }
                        refundIsSuccess = false;
                        log.info("退款失败 err：", e);
                        throw new DefaultServiceException(0, "资金确认中，请稍后重新发起退款。");
                    }
                }
                //大客户帐期门店 录入账单不做任何操作,只记录
                if(AfterSaleHandleType.BLOCK_INCOMING_BILLS.getType().equals(afterSaleOrderVO.getHandleType()) && (afterSaleOrderVO.getType() == 0 || afterSaleOrderVO.getType() == 3)){
                    //未到货

                    //修改订单项状态
                    List<OrderItemVO> orderItemVOS = orderItemMapper.selectOrderItemVO(queryAfterOrder.getOrderNo());
                    if (!CollectionUtils.isEmpty(orderItemVOS)){
                        for (OrderItemVO orderItemVO : orderItemVOS) {
                            orderItemMapper.updateStatusById(orderItemVO.getId(), OrderStatusEnum.DRAWBACK.getId());
                        }
                    }

                }

                if(refundIsSuccess){
                    updateProof.setStatus(AfterSaleOrderStatus.SUCCESS.getStatus());
                    updateAfterOrder.setStatus(AfterSaleOrderStatus.SUCCESS.getStatus());
                } else {
                    updateProof.setStatus(AfterSaleOrderStatus.FAIL.getStatus());
                    updateAfterOrder.setStatus(AfterSaleOrderStatus.FAIL.getStatus());
                }
            }
        }
        //更新审核人
        updateProof.setAuditer(afterSaleOrderVO.getAuditer());
        updateProof.setAuditeRemark(afterSaleOrderVO.getAuditeRemark());
        updateProof.setAuditetime(LocalDateTime.now());
        afterSaleProofMapper.updateById(updateProof);
        updateAfterOrder.setView(0);
        updateAfterOrder.setAfterSaleOrderNo(afterSaleOrderNo);
        afterSaleOrderMapper.updateByAfterSaleOrderNo(updateAfterOrder);

        Orders orders = ordersMapper.selectByOrderyNo(queryAfterOrder.getOrderNo());
        if(OrderTypeEnum.TIMING.getId().equals(orders.getType())){
            handleTimingOrder(orders);
        }

        //修改状态之后再进行消息发送，否则无法知道具体的auditeTime
        if (status == 1){
            //发送微信消息
            notifySuccess(queryAfterOrder.getOrderNo());
            //发送钉钉消息
            MQData mqData = new MQData();
            mqData.setType(MType.INTERCEPT_ORDER.name());
            mqData.setData(queryAfterOrder.getOrderNo());
            mqProducer.send(RocketMqMessageConstant.MALL_LIST, null, JSON.toJSONString(mqData));
        }


        return refundResult;
    }

    private BigDecimal checkoutHandleNum(Orders orderVO){
        BigDecimal couponMoney = new BigDecimal(0);
        if (!orderVO.getType().equals(OrderTypeEnum.TIMING.getId())){
            return couponMoney = orderVO.getTotalPrice();
        }
        return couponMoney;
    }


    private void handleTimingOrder(Orders orders) {
        String orderNo = orders.getOrderNo();
        Integer afterSaleQuantity = afterSaleOrderService.getAfterSaleSuccessQuanlity(orderNo);

        List<DeliveryPlanVO> plans = deliveryPlanMapper.selectByOrderNo(orderNo);
        int deliveredQuantity = plans.stream().filter(deliveryPlanVO -> !deliveryPlanVO.getDeliveryTime().isAfter(LocalDate.now())).mapToInt(dp -> dp.getQuantity()).sum();
        Integer quantity = orderItemMapper.selectTimingOrderQuantity(orderNo);

        log.info("orderNo:{}, deliveredQuanlity:{}, afterSaleQuanlity:{},quantity:{}", orderNo, deliveredQuantity, afterSaleQuantity, quantity);
        if( deliveredQuantity + afterSaleQuantity == quantity){
            if (deliveredQuantity == 0) {
                ordersMapper.updateStatus(orderNo, OrderStatusEnum.DRAWBACK.getId(), orders.getStatus());
            } else {
                orderItemMapper.updateStatusByOrderNo(orderNo, OrderStatusEnum.RECEIVED.getId());
                ordersMapper.updateStatus(orderNo, OrderStatusEnum.RECEIVED.getId(), orders.getStatus());
                orderService.updateScore(orderNo, orders.getmId());
                //orderService.pushOrderConfirm(orderNo);
            }
        }
    }
    public void notifySuccess(String orderNo){
        OrderVO orderVO = ordersMapper.selectByOrderyNo(orderNo);
        List<AfterSaleProof> afterSaleProofs = afterSaleProofMapper.selectByOrderNo(orderNo, null);
        afterSaleProofs = afterSaleProofs.stream().filter(afterSaleProof -> afterSaleProof.getHandleType().equals(AfterSaleHandleType.BLOCK_REFUNDS.getType())).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(afterSaleProofs)){
            AfterSaleProof afterSaleProof = afterSaleProofs.get(0);

            //发送微信消息
            Merchant merchant = merchantMapper.selectByPrimaryKey(orderVO.getmId());
            String msg = InterceptSuccessMsg.templateMessage(merchant.getOpenid(), orderNo, afterSaleProof.getRefundType(), LocalDateTime.now());
            log.info("订单拦截成功微信消息发送"+msg);
            templateMsgSender.sendTemplateMsg(msg);
        }

    }

}
