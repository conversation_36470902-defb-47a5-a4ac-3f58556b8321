package net.summerfarm.mall.contexts;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.enums.AfterSaleHandleType;
import net.summerfarm.enums.AfterSaleOrderStatus;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.enums.AfterSaleWorkflowEnum;
import net.summerfarm.mall.factory.AfterSaleCalculatorFactory;
import net.summerfarm.mall.factory.AfterSaleWorkflowFactory;
import net.summerfarm.mall.mapper.MerchantMapper;
import net.summerfarm.mall.mapper.OrdersMapper;
import net.summerfarm.mall.mapper.PrepayInventoryRecordMapper;
import net.summerfarm.mall.model.domain.AfterSaleOrder;
import net.summerfarm.mall.model.domain.AfterSaleOrderCalculator;
import net.summerfarm.mall.model.domain.AfterSaleProof;
import net.summerfarm.mall.model.domain.Merchant;
import net.summerfarm.mall.model.domain.Orders;
import net.summerfarm.mall.model.domain.PrepayInventoryRecord;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import net.summerfarm.mall.service.AfterSaleCalculator;
import net.summerfarm.mall.service.AfterSaleStrategy;
import net.summerfarm.mall.wechat.enums.AfterSaleCalculatorType;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> ct
 * create at:  2019/12/6  17:00
 * 未到货售后 缺货 其他 提交售后
 */
// @Component
public class OutOfDateAfterSaleOrder implements AfterSaleStrategy {

    @Resource
    private AfterSaleOrderAction afterSaleOrderAction;

    @Resource
    private AfterSaleCalculatorFactory afterSaleCalculatorFactory;

    @Resource
    private OrdersMapper ordersMapper;

    // @Resource
    private AfterSaleWorkflowFactory afterSaleWorkflowFactory;

    @Resource
    private PrepayInventoryRecordMapper prepayInventoryRecordMapper;

    @Resource
    private MerchantMapper merchantMapper;

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    @Deprecated
    public AjaxResult afterSaleOrder(AfterSaleOrderVO afterSaleOrderVO) {

        //是否是从后台提交售后
        Boolean isBackstage = afterSaleOrderVO.getmId() == null ? Boolean.FALSE : Boolean.TRUE;
        AfterSaleCalculator calculator;
        Orders orders = ordersMapper.selectByOrderNo(afterSaleOrderVO.getOrderNo());

        //商城提交售后封装 mid
        if(RequestHolder.getMerchantSubject() != null){
            afterSaleOrderVO.setHandleType(AfterSaleHandleType.REFUND.getType());
        }
        //商城 未到货 普通订单缺货其他一定是退款,账期门店 为录入账单
        if(!isBackstage && RequestHolder.getMerchantSubject() != null && RequestHolder.isMajorDirect()){
            afterSaleOrderVO.setHandleType(AfterSaleHandleType.ENTRY_BILL.getType());
        }

        afterSaleOrderAction.assembleAfterSaleOrderVO(afterSaleOrderVO);
        AjaxResult result = afterSaleOrderAction.chcekAfterSaleOrder(orders, afterSaleOrderVO);

        if(!Objects.equals(result.getCode(),"SUCCESS")){
            return result;
        }
        //只能是 未到货 普通售后 省心送提交售后计算金额
        if(orders.getType() == 1){
            calculator = afterSaleCalculatorFactory.getAfterSaleCalculator(AfterSaleCalculatorType.TIMING_NOT_NEED.getType());
        } else {
            calculator = afterSaleCalculatorFactory.getAfterSaleCalculator(AfterSaleCalculatorType.NOT_NEED.getType());
        }
        String afterSaleOrderNo = afterSaleOrderAction.createAfterSaleOrderNo(afterSaleOrderVO.getmId(), afterSaleOrderVO.getAccountId());
        afterSaleOrderVO.setAfterSaleOrderNo(afterSaleOrderNo);
        if(!Objects.equals(result.getCode(),"SUCCESS")){
            return result;
        }

        AjaxResult ajaxResult = calculator.calcRefund(afterSaleOrderVO);
        if(!AjaxResult.isSuccess(ajaxResult)){
            return ajaxResult;
        }

        AfterSaleOrderCalculator afterCalculator =  (AfterSaleOrderCalculator)ajaxResult.getData();
        BigDecimal couponMoney = afterCalculator.getCouponMoney();
        Integer skuType = afterCalculator.getSkuType();

        Merchant merchant = merchantMapper.selectOneByMid(orders.getmId());
        String sku = afterSaleOrderVO.getSku();
        List<PrepayInventoryRecord> prepayInventoryRecords = prepayInventoryRecordMapper.selectRecordList(merchant.getAdminId(), afterSaleOrderVO.getOrderNo(), sku);
        //代仓商品的实付是0元,预付款商品实付0元 ,退款支持0元
        if (CollectionUtils.isEmpty(prepayInventoryRecords) && couponMoney.compareTo(BigDecimal.ZERO) == 0 && Objects.equals(skuType,"0")) {
            return AjaxResult.getErrorWithMsg("退款金额异常");
        }
        AfterSaleOrder updateAfterSaleOrder = new AfterSaleOrder();
        AfterSaleProof updateAfterSaleProof = new AfterSaleProof();

        updateAfterSaleOrder.setType(0);
        updateAfterSaleOrder.setDeliveryed(0);
        updateAfterSaleOrder.setStatus(AfterSaleOrderStatus.WAIT_HANDLE.getStatus());
        updateAfterSaleProof.setStatus(AfterSaleOrderStatus.WAIT_HANDLE.getStatus());
        updateAfterSaleProof.setHandleType(afterSaleOrderVO.getHandleType());
        updateAfterSaleOrder.setAfterSaleOrderNo(afterSaleOrderNo);
        updateAfterSaleProof.setAfterSaleOrderNo(afterSaleOrderNo);

        if(afterSaleOrderVO.getHandleNum() != null
                && afterSaleOrderVO.getHandleNum().compareTo(BigDecimal.ZERO) > 0 ){
            updateAfterSaleProof.setHandleNum(afterSaleOrderVO.getHandleNum());
        } else {
            updateAfterSaleProof.setHandleNum(couponMoney);
        }


        afterSaleOrderAction.assembleAfterSaleOrder(afterSaleOrderVO,updateAfterSaleOrder);
        afterSaleOrderAction.assembleAfterSalePoof(afterSaleOrderVO,updateAfterSaleProof);

        // 切仓库存不足售后归还虚拟库存
        if (Objects.equals(afterSaleOrderVO.getRefundType(), "切仓库存不足")){
            afterSaleOrderAction.releaseLockQuantity(afterSaleOrderVO);
        }

        //自动通过 调用审核,审批 审核人审批人人都是该用户
            if ((Objects.equals(afterSaleOrderVO.getRefundType(), "缺货") || Objects.equals(afterSaleOrderVO.getRefundType(), "切仓库存不足")) && isBackstage) {
            //后台提交 调用 审核 审批 通过
            AfterSaleWorkflow workflow = afterSaleWorkflowFactory.getWorkflow(AfterSaleWorkflowEnum.HANDLE.getType());
            //审核
            afterSaleOrderVO.setHandler(afterSaleOrderVO.getApplyer());
            AjaxResult handleResult = workflow.workflow(0, afterSaleOrderVO);
            if(!Objects.equals("SUCCESS",handleResult.getCode())){
                return AjaxResult.getErrorWithMsg(handleResult.getMsg());
            }
            //审批 审批成功
            afterSaleOrderVO.setType(0);
            afterSaleOrderVO.setStatus(1);
            afterSaleOrderVO.setAuditer(afterSaleOrderVO.getApplyer());
            AjaxResult auditResult = workflow.workflow(1, afterSaleOrderVO);
            if(!Objects.equals("SUCCESS",auditResult.getCode())){
                return AjaxResult.getErrorWithMsg(auditResult.getMsg());
            }

        }

        if(Objects.equals(afterSaleOrderVO.getRefundType(),"其他") && isBackstage){
            //后台提交 调用 审核 审批 通过
            AfterSaleWorkflow workflow = afterSaleWorkflowFactory.getWorkflow(AfterSaleWorkflowEnum.HANDLE.getType());
            //审核
            afterSaleOrderVO.setHandler(afterSaleOrderVO.getApplyer());
            AjaxResult handleResult = workflow.workflow(0, afterSaleOrderVO);
            if(!Objects.equals("SUCCESS",handleResult.getCode())){
                return AjaxResult.getErrorWithMsg(handleResult.getMsg());
            }
        }

        return AjaxResult.getOK();
    }
}
