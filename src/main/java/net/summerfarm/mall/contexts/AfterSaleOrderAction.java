package net.summerfarm.mall.contexts;

import cn.hutool.core.util.RandomUtil;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.common.util.UnsafeUtil;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.enums.AfterSaleHandleType;
import net.summerfarm.enums.AfterSaleOrderStatus;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.enums.DistOrderSourceEnum;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import net.summerfarm.mall.model.vo.DeliveryPlanVO;
import net.summerfarm.mall.model.vo.ProductVO;
import net.summerfarm.mall.service.*;
import net.summerfarm.mall.service.facade.WmsAreaStoreFacade;
import net.summerfarm.mall.service.facade.dto.AreaStoreQueryRes;
import net.summerfarm.mall.service.facade.dto.AreaStoreUnLockReq;
import net.summerfarm.mall.service.facade.dto.OrderUnLockSkuDetailReqDTO;
import net.summerfarm.warehouse.service.WarehouseInventoryService;
import net.xianmu.common.user.UserInfoHolder;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> ct
 * create at:  2019/12/6  11:17
 */
@Component
public class AfterSaleOrderAction {

    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private AfterSaleOrderMapper afterSaleOrderMapper;
    @Resource
    private AfterSaleProofMapper afterSaleProofMapper;
    @Resource
    private MemberService memberService;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private ProductsMapper productsMapper;
    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;
    @Resource
    private AreaMapper areaMapper;
    @Resource
    private AfterSaleOrderService afterSaleOrderService;
    @Resource
    private OrderService orderService;
    @Resource
    private ContactMapper contactMapper;
    @Resource
    private OrderItemMapper orderItemMapper;
    @Resource
    private WarehouseInventoryService warehouseInventoryService;
    @Resource
    private AreaStoreMallMapper areaStoreMallMapper;
    @Resource
    private QuantityChangeRecordService quantityChangeRecordService;
    @Resource
    private DeliveryService deliveryService;
    @Resource
    private WmsAreaStoreFacade areaStoreFacade;

    private static final Logger logger = LoggerFactory.getLogger(AfterSaleOrderAction.class);

    public AjaxResult chcekAfterSaleOrder(Orders orders, AfterSaleOrderVO afterSaleOrderVO){

        Merchant merchant = merchantMapper.selectOneByMid(afterSaleOrderVO.getmId());

        //代表整单退查所有的SKU
        if (afterSaleOrderVO.getSku() == null){
            List<OrderItem> orderItems = orderItemMapper.selectOrderItem(orders.getOrderNo());
            for (OrderItem orderItem : orderItems) {
                Inventory inventory = inventoryMapper.selectBySku(orderItem.getSku());
                Products products = productsMapper.selectByPrimaryKey(inventory.getPdId());

                if (Objects.isNull(afterSaleOrderVO.getmId()) && inAfterSaleTime(afterSaleOrderVO.getOrderNo(), products.getAfterSaleTime(),orders,afterSaleOrderVO.getDeliveryId())) {
                    return AjaxResult.getError(ResultConstant.OUT_OF_AFTER_SALE_TIME);
                }
            }
        }else {

            Inventory inventory = inventoryMapper.selectBySku(afterSaleOrderVO.getSku());
            Products products = productsMapper.selectByPrimaryKey(inventory.getPdId());


            if (Objects.isNull(afterSaleOrderVO.getmId()) && inAfterSaleTime(afterSaleOrderVO.getOrderNo(), products.getAfterSaleTime(),orders,afterSaleOrderVO.getDeliveryId())) {
                return AjaxResult.getError(ResultConstant.OUT_OF_AFTER_SALE_TIME);
            }
        }
        if (orders.getStatus() != 2 && orders.getStatus() != 3 && orders.getStatus() != 6) {
            return AjaxResult.getErrorWithMsg("订单状态异常");
        }

        /*if (!orders.getAreaNo().equals(merchant.getAreaNo())) {
            return AjaxResult.getErrorWithMsg("当前区域和订单所属区域不匹配，不可售后");
        }*/
        if (!Objects.equals(orders.getmSize(), merchant.getSize())) {
            return AjaxResult.getErrorWithMsg("已不可发起售后");
        }
        return AjaxResult.getOK();
    }

    public String createAfterSaleOrderNo(Long mId ,Long accountId) {
        int afterSaleTimes = afterSaleOrderMapper.countByMId(mId, accountId);
        StringBuffer head = new StringBuffer(mId + RandomUtil.randomInt(1000,10000) + "");
        String mid = StringUtils.orderRandomNum();
        String tail = afterSaleTimes + 1 + "";
        while (tail.length() < 4) {
            tail = 0 + tail;
        }
        int monthValue = LocalDate.now().getMonthValue();
        String month = String.valueOf(monthValue);
        if(monthValue < 10){
            month = "0" + month;
        }
        head.reverse().append(mid).append(month).append(tail);
        return head.toString();
    }


    /**
    * 拼装afterSaleOrder
    */
    public void assembleAfterSaleOrder(AfterSaleOrderVO afterSaleOrderVO, AfterSaleOrder insertAfterSaleOrder){

        Integer grade = memberService.calculGrade(afterSaleOrderVO.getmId());
        insertAfterSaleOrder.setmId(afterSaleOrderVO.getmId());
        insertAfterSaleOrder.setAfterSaleOrderStatus(afterSaleOrderVO.getAfterSaleOrderStatus());
        insertAfterSaleOrder.setDeliveryId(afterSaleOrderVO.getDeliveryId());
        insertAfterSaleOrder.setAccountId(afterSaleOrderVO.getAccountId());
        insertAfterSaleOrder.setAddTime(LocalDateTime.now());
        insertAfterSaleOrder.setOrderNo(afterSaleOrderVO.getOrderNo());
        insertAfterSaleOrder.setSku(afterSaleOrderVO.getSku());
        insertAfterSaleOrder.setAfterSaleUnit(afterSaleOrderVO.getAfterSaleUnit());
        insertAfterSaleOrder.setIsFull((afterSaleOrderVO.getType() != null && afterSaleOrderVO.getType() == 3) ? Boolean.TRUE : Boolean.FALSE);
        insertAfterSaleOrder.setSuitId(afterSaleOrderVO.getSuitId());
        insertAfterSaleOrder.setGrade(grade);
        insertAfterSaleOrder.setAfterSaleOrderNo(afterSaleOrderVO.getAfterSaleOrderNo());
        insertAfterSaleOrder.setRecoveryType(afterSaleOrderVO.getRecoveryType());
        //计算售后了几次
        int times = 1;
        AfterSaleOrder select = new AfterSaleOrder();
        select.setOrderNo(afterSaleOrderVO.getOrderNo());
        select.setSku(afterSaleOrderVO.getSku());
        select.setSuitId(afterSaleOrderVO.getSuitId());
        List<AfterSaleOrderVO> afterSaleOrders = afterSaleOrderService.selectAfterSaleOrderVO(select);
        if (!CollectionUtils.isEmpty(afterSaleOrders)) {
            times = afterSaleOrders.size() + 1;
        }
        insertAfterSaleOrder.setTimes(times);
        insertAfterSaleOrder.setView(1);
        insertAfterSaleOrder.setAfterSaleRemark(afterSaleOrderVO.getAfterSaleRemark());
        insertAfterSaleOrder.setAfterSaleRemarkType(afterSaleOrderVO.getAfterSaleRemarkType());
        afterSaleOrderMapper.insertSelective(insertAfterSaleOrder);



    }

    /**
     * 省心送自动售后生成售后单
     */
    public void assembleTimingAfterSaleOrder(AfterSaleOrderVO afterSaleOrderVO, AfterSaleOrder insertAfterSaleOrder){

        Integer grade = memberService.calculGrade(afterSaleOrderVO.getmId());
        insertAfterSaleOrder.setmId(afterSaleOrderVO.getmId());
        insertAfterSaleOrder.setAfterSaleOrderStatus(0);
        insertAfterSaleOrder.setDeliveryId(afterSaleOrderVO.getDeliveryId());
        insertAfterSaleOrder.setDeliveryed(afterSaleOrderVO.getDeliveryed());
        insertAfterSaleOrder.setType(afterSaleOrderVO.getType());
        insertAfterSaleOrder.setStatus(AfterSaleOrderStatus.SUCCESS.getStatus());
        insertAfterSaleOrder.setAccountId(afterSaleOrderVO.getAccountId());
        insertAfterSaleOrder.setAddTime(LocalDateTime.now());
        insertAfterSaleOrder.setOrderNo(afterSaleOrderVO.getOrderNo());
        insertAfterSaleOrder.setSku(afterSaleOrderVO.getSku());
        insertAfterSaleOrder.setAfterSaleUnit(afterSaleOrderVO.getAfterSaleUnit());
        insertAfterSaleOrder.setIsFull(Boolean.FALSE);
        insertAfterSaleOrder.setSuitId(afterSaleOrderVO.getSuitId());
        insertAfterSaleOrder.setGrade(grade);
        insertAfterSaleOrder.setAfterSaleOrderNo(afterSaleOrderVO.getAfterSaleOrderNo());
        insertAfterSaleOrder.setRecoveryType(0);
        //计算售后了几次
        int times = 1;
        AfterSaleOrder select = new AfterSaleOrder();
        select.setOrderNo(afterSaleOrderVO.getOrderNo());
        select.setSku(afterSaleOrderVO.getSku());
        select.setSuitId(afterSaleOrderVO.getSuitId());
        List<AfterSaleOrderVO> afterSaleOrders = afterSaleOrderService.selectAfterSaleOrderVO(select);
        if (!CollectionUtils.isEmpty(afterSaleOrders)) {
            times = afterSaleOrders.size() + 1;
        }
        insertAfterSaleOrder.setTimes(times);
        insertAfterSaleOrder.setView(1);
        //insertAfterSaleOrder.setAfterSaleRemark(afterSaleOrderVO.getAfterSaleRemark());
        //insertAfterSaleOrder.setAfterSaleRemarkType(afterSaleOrderVO.getAfterSaleRemarkType());
        insertAfterSaleOrder.setProductType(afterSaleOrderVO.getProductType());
        afterSaleOrderMapper.insertSelective(insertAfterSaleOrder);



    }

    /**
     * 拼装afterSalePoof
     */
    public void assembleAfterSalePoof(AfterSaleOrderVO afterSaleOrderVO, AfterSaleProof insertAfterSaleProof){

        insertAfterSaleProof.setStatus(insertAfterSaleProof.getStatus());
        insertAfterSaleProof.setAfterSaleType(afterSaleOrderVO.getAfterSaleType());
        insertAfterSaleProof.setQuantity(afterSaleOrderVO.getQuantity());
        insertAfterSaleProof.setRefundType(afterSaleOrderVO.getRefundType());
        insertAfterSaleProof.setProofPic(afterSaleOrderVO.getProofPic());
        insertAfterSaleProof.setApplyRemark(afterSaleOrderVO.getApplyRemark());
        insertAfterSaleProof.setApplyer(afterSaleOrderVO.getApplyer());
        insertAfterSaleProof.setRecoveryNum(afterSaleOrderVO.getRecoveryNum());
        insertAfterSaleProof.setAfterSaleOrderNo(afterSaleOrderVO.getAfterSaleOrderNo());
        insertAfterSaleProof.setHandleRemark(afterSaleOrderVO.getHandleRemark());
        insertAfterSaleProof.setExtraRemark(afterSaleOrderVO.getExtraRemark());
        afterSaleProofMapper.insert(insertAfterSaleProof);
    }

    /**
     * 省心送自动售后生成售后单
     */
    public void assembleTimingAfterSalePoof(AfterSaleOrderVO afterSaleOrderVO, AfterSaleProof insertAfterSaleProof){

        insertAfterSaleProof.setStatus(AfterSaleOrderStatus.SUCCESS.getStatus());
        insertAfterSaleProof.setAfterSaleType("其他");
        insertAfterSaleProof.setRefundType("其他");
        insertAfterSaleProof.setHandleNum(afterSaleOrderVO.getTimingAfterSaleMoney());
        insertAfterSaleProof.setQuantity(afterSaleOrderVO.getQuantity());
        insertAfterSaleProof.setApplyRemark(afterSaleOrderVO.getApplyRemark());
        insertAfterSaleProof.setApplyer("系统发起");
        insertAfterSaleProof.setRecoveryNum(BigDecimal.ZERO);
        insertAfterSaleProof.setAfterSaleOrderNo(afterSaleOrderVO.getAfterSaleOrderNo());
        insertAfterSaleProof.setHandleType(afterSaleOrderVO.getHandleType());
        insertAfterSaleProof.setHandletime(LocalDateTime.now());
        insertAfterSaleProof.setHandleRemark(afterSaleOrderVO.getHandleRemark());
        insertAfterSaleProof.setAuditeRemark(afterSaleOrderVO.getAuditeRemark());
        insertAfterSaleProof.setHandler(afterSaleOrderVO.getAuditer());
        insertAfterSaleProof.setAuditer(afterSaleOrderVO.getAuditer());
        insertAfterSaleProof.setApplySecondaryRemark("其他");
        afterSaleProofMapper.insert(insertAfterSaleProof);
    }

    /**
     * 是否在售后时间
     *
     * @param afterSaleTime
     */
    public Boolean inAfterSaleTime(String orderNo, int afterSaleTime, Orders orders,Integer deliveryId) {
        List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanMapper.selectByOrderNo(orderNo);
        //省心送可以没有配送计划,非省心送订单没有查询到配送计划则不可售后
        if(CollectionUtils.isEmpty(deliveryPlanVOS)){
            return !Objects.equals(orders.getType(), 1);
        }
        DeliveryPlanVO deliveryPlanVO = deliveryPlanVOS.get(0);
        if(deliveryId != null){
            for (DeliveryPlanVO deliveryPlan : deliveryPlanVOS) {
                if(Objects.equals(deliveryId,deliveryPlan.getId())){
                    deliveryPlanVO = deliveryPlan;
                }
            }
        }
        LocalDateTime start = deliveryPlanVO.getDeliveryTime().atTime(0, 0);
        LocalDateTime now = LocalDateTime.now();
        return now.isAfter(start.plusHours(afterSaleTime));
    }

    /**
    * 商城提交售后统一拼装
    */
   public void assembleAfterSaleOrderVO(AfterSaleOrderVO afterSaleOrderVO){
       if(afterSaleOrderVO.getmId() == null){
           Merchant merchant = merchantMapper.selectOneByMid(RequestHolder.getMId());
           afterSaleOrderVO.setmId(RequestHolder.getMId());
           afterSaleOrderVO.setAccountId(RequestHolder.getAccountId());
           afterSaleOrderVO.setApplyer(merchant.getMname());
       }
   }

   /**
   * 开始生成补货信息
   */
    public AfterSaleDeliveryPath createDeliveryPath(AfterSaleOrderVO afterSaleOrderVO){

        //生成售后配送单信息
        AreaStoreQueryRes areaStoreQueryRes = afterSaleOrderVO.getQueryStore();
        String orderNo = afterSaleOrderVO.getOrderNo();
        Integer handleType = afterSaleOrderVO.getHandleType();
        Integer type =  handleType.equals(AfterSaleHandleType.EXCHANGE_GOODS.getType()) ? 2 : 0;
        Integer deliveryId = afterSaleOrderVO.getDeliveryId();
        Orders orders = ordersMapper.selectByOrderNo(orderNo);
        Area area = areaMapper.selectByMId(orders.getmId());
        //获取配送计划对应地址
        Long contactId = getContactId(orderNo, deliveryId);
        Contact contact = contactMapper.selectByPrimaryKey(contactId);
        Integer storeNo =  contact.getStoreNo();
        ////生成配送信息
        AfterSaleDeliveryPath insertPath = new AfterSaleDeliveryPath();
        insertPath.setConcatId(contactId);
        insertPath.setAfterSaleNo(afterSaleOrderVO.getAfterSaleOrderNo());
        insertPath.setDeliveryTime(areaStoreQueryRes.getDeliveryTime());
        insertPath.setMId(orders.getmId());
        insertPath.setStatus(1);
        insertPath.setType(type);
        insertPath.setOutStoreNo(storeNo);
        return insertPath;
    }
    /**
    * 生成补货详情信息(不包含回收)
    */
    public AfterSaleDeliveryDetail createDeliveryDetail(String sku,Integer quantity,Integer pathId){

        ProductVO productVO = inventoryMapper.queryBySku(sku);

        AfterSaleDeliveryDetail deliveryDetail = new AfterSaleDeliveryDetail();
        deliveryDetail.setAsDeliveryPathId(pathId);
        deliveryDetail.setPdName(productVO.getPdName());
        deliveryDetail.setQuantity(quantity);
        deliveryDetail.setWeight(productVO.getWeight());
        deliveryDetail.setSku(sku);
        deliveryDetail.setType(1);
        deliveryDetail.setType(AfterSaleDeliveryDetail.DELIVERY_TYPE);
        deliveryDetail.setStatus(AfterSaleDeliveryDetail.EFFECTIVE_STATUS);
        return deliveryDetail;
    }


    /**
    * 获取配送地址
    */
    public  Long getContactId(String orderNo,Integer deliveryId){
        List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanMapper.selectByOrderNoNoStatus(orderNo);
        if(CollectionUtils.isEmpty(deliveryPlanVOS)){
            throw  new DefaultServiceException("为获取到订单配送计划");
        }
        Long contactId = deliveryPlanVOS.get(0).getContactId();
        //省心送
        if(deliveryId != null){
            DeliveryPlan deliveryPlan = deliveryPlanMapper.queryById(deliveryId);
            contactId = deliveryPlan.getContactId();
        }
        return contactId;
    }

    /**
     * 释放冻结库存
     * @param afterSaleOrderVO
     */
    public void releaseLockQuantity(AfterSaleOrderVO afterSaleOrderVO) {
        //Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
        List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanMapper.selectByOrderNo(afterSaleOrderVO.getOrderNo());
        if (CollectionUtils.isEmpty(deliveryPlanVOS)) {
            logger.error("订单:{},未找到配送计划", afterSaleOrderVO.getOrderNo());
            return;
        }
        DeliveryPlanVO deliveryPlanVO = deliveryPlanVOS.get(NumberUtils.INTEGER_ZERO);
        /*WarehouseInventoryMappingVO mappingVO = warehouseInventoryService.selectVoByUniqueIndex(deliveryPlanVO.getOrderStoreNo(), afterSaleOrderVO.getSku());
        areaStoreMallMapper.updateSaleLockQuantity(-afterSaleOrderVO.getQuantity(), afterSaleOrderVO.getSku(), mappingVO.getWarehouseNo());
        AreaStore areaStore = areaStoreMallMapper.selectByStoreNoAndSku(mappingVO.getWarehouseNo(), afterSaleOrderVO.getSku());
        //使用库存仓库编号
        Integer warehouseNo = areaStore.getAreaNo();
        try {
            int onlineChange = afterSaleOrderVO.getQuantity(), changeChange = 0;
            // 还库存时需要先还虚拟变值，然后再加虚拟
            if (afterSaleOrderVO.getQuantity() > 0 && areaStore.getChange() > 0) {
                changeChange = areaStore.getChange() >= afterSaleOrderVO.getQuantity() ? -afterSaleOrderVO.getQuantity() : -areaStore.getChange();
                onlineChange = areaStore.getChange() >= afterSaleOrderVO.getQuantity() ? 0 : afterSaleOrderVO.getQuantity() - areaStore.getChange();
            }

            areaStoreMallMapper.updateOnlineQuantityAndChange(onlineChange, changeChange, afterSaleOrderVO.getSku(), warehouseNo);
            // 库存变动记录
            quantityChangeRecord(recordMap, warehouseNo, afterSaleOrderVO.getSku(), afterSaleOrderVO.getHandler(), Global.ORDER_ABNORMAL_HANDLE, afterSaleOrderVO.getOrderNo(), areaStore.getOnlineQuantity(), QuantityChangeRecord.oldOnlineQuantityOffset);

            quantityChangeRecordService.insert(recordMap);
        } catch (
                DataIntegrityViolationException e) {
            throw new DefaultServiceException(0, afterSaleOrderVO.getSku() + "在" + warehouseNo + "线上库存不足");
        } catch (
                UncategorizedSQLException e) {
            logger.error(collectExceptionStackMsg(e));
            throw new DefaultServiceException(1, "系统错误,请重新提交.");
        }*/

        Orders orders = ordersMapper.selectByOrderNo(afterSaleOrderVO.getOrderNo());
        if (orders == null) {
            logger.error("订单:{},未找到", afterSaleOrderVO.getOrderNo());
            return;
        }

        //库存释放 新模型
        AreaStoreUnLockReq areaStoreUnLockReq = new AreaStoreUnLockReq();
        areaStoreUnLockReq.setContactId(deliveryPlanVO.getContactId());
        areaStoreUnLockReq.setOrderType(net.summerfarm.enums.SaleStockChangeTypeEnum.ORDER_ABNORMAL_HANDLE.getTypeName());
        areaStoreUnLockReq.setOrderNo(afterSaleOrderVO.getOrderNo());
        areaStoreUnLockReq.setIdempotentNo(afterSaleOrderVO.getAfterSaleOrderNo());
        areaStoreUnLockReq.setOperatorNo(afterSaleOrderVO.getAfterSaleOrderNo());
        List<OrderUnLockSkuDetailReqDTO> orderReleaseSkuDetailReqDTOS = new ArrayList<>();
        OrderUnLockSkuDetailReqDTO orderUnLockSkuDetailReqDTO = new OrderUnLockSkuDetailReqDTO();
        orderUnLockSkuDetailReqDTO.setSkuCode(afterSaleOrderVO.getSku());
        orderUnLockSkuDetailReqDTO.setReleaseQuantity(afterSaleOrderVO.getQuantity());
        orderReleaseSkuDetailReqDTOS.add(orderUnLockSkuDetailReqDTO);
        areaStoreUnLockReq.setOrderReleaseSkuDetailReqDTOS(orderReleaseSkuDetailReqDTOS);
        areaStoreUnLockReq.setMerchantId(afterSaleOrderVO.getmId());
        areaStoreUnLockReq.setSource(DistOrderSourceEnum.getDistOrderSourceByOrderType(orders.getType()));
        areaStoreUnLockReq.setOperatorName(afterSaleOrderVO.getHandler());
        areaStoreFacade.storeUnLock(areaStoreUnLockReq);

    }

    private void quantityChangeRecord(Map<String, QuantityChangeRecord> recordMap, Integer areaNo, String sku, String recorder, String typeName, String recordNo, Integer oldQuantity, long offset) {
        QuantityChangeRecord record = recordMap.get(sku + ":" + areaNo);
        if (record == null) {
            record = new QuantityChangeRecord(areaNo, sku, recorder, typeName, recordNo);
        }
        if (StringUtils.isBlank(recorder)) {
            if (Objects.nonNull(UserInfoHolder.getUser())) {
                record.setRecorder(UserInfoHolder.getUser().getUsername());
            } else {
                record.setRecorder("系统默认");
            }
        }
        UnsafeUtil.unsafe.compareAndSwapObject(record, offset, null, oldQuantity);
        recordMap.put(sku + ":" + areaNo, record);
    }
}
