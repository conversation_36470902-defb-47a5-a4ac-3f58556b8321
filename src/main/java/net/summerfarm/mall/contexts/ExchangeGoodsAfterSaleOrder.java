package net.summerfarm.mall.contexts;

import com.google.common.collect.Lists;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.enums.AfterSaleOrderStatus;
import net.summerfarm.mall.enums.DistOrderSourceEnum;
import net.summerfarm.mall.enums.SaleStockChangeTypeEnum;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import net.summerfarm.mall.model.vo.OrderItemVO;
import net.summerfarm.mall.service.AfterSaleStrategy;
import net.summerfarm.mall.service.OrderService;
import net.summerfarm.mall.service.QuantityChangeRecordService;
import net.summerfarm.mall.service.facade.WmsAreaStoreFacade;
import net.summerfarm.mall.service.facade.dto.AreaStoreLockReq;
import net.summerfarm.mall.service.facade.dto.AreaStoreQueryReq;
import net.summerfarm.mall.service.facade.dto.AreaStoreQueryRes;
import net.summerfarm.mall.service.facade.dto.OrderLockSkuDetailReqDTO;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR> ct
 * create at:  2020/12/9  10:51
 *
 * 换货,补发
 */
@Component
public class ExchangeGoodsAfterSaleOrder implements AfterSaleStrategy {

    @Resource
    AfterSaleDeliveryDetailMapper afterSaleDeliveryDetailMapper;
    @Resource
    AfterSaleDeliveryPathMapper afterSaleDeliveryPathMapper;
    @Resource
    QuantityChangeRecordService quantityChangeRecordService;
    @Resource
    AfterSaleOrderAction afterSaleOrderAction;
    @Resource
    MerchantMapper merchantMapper;
    @Resource
    DeliveryPlanMapper deliveryPlanMapper;
    @Resource
    OrderItemMapper orderItemMapper;
    @Resource
    InventoryMapper inventoryMapper;
    @Resource
    OrderService orderService;
    @Resource
    OrdersMapper ordersMapper;
    @Resource
    AreaMapper areaMapper;
    @Resource
    private WmsAreaStoreFacade areaStoreFacade;

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult afterSaleOrder(AfterSaleOrderVO afterSaleOrderVO) {

        //售后类型
        Integer handleType = afterSaleOrderVO.getHandleType();
        Integer suitId = afterSaleOrderVO.getSuitId();
        String orderNo = afterSaleOrderVO.getOrderNo();
        String sku = afterSaleOrderVO.getSku();
        Integer quantity = afterSaleOrderVO.getQuantity();
        Long mId = afterSaleOrderVO.getmId();
        Merchant merchant = merchantMapper.selectOneByMid(mId);
        if(mId == null){
          return AjaxResult.getErrorWithMsg("获取用户信息失败");
        }

        Orders orders = ordersMapper.selectByOrderNo(afterSaleOrderVO.getOrderNo());
        if (orders == null) {
            return AjaxResult.getErrorWithMsg("获取订单信息失败");
        }
        //换货补发 只是校验数量是否大于订单数量
        //省心送订单
        Integer deliveryId = afterSaleOrderVO.getDeliveryId();
        afterSaleOrderVO.setAfterSaleUnit("件");
        if(Objects.nonNull(deliveryId)){
            DeliveryPlan deliveryPlan = deliveryPlanMapper.queryById(deliveryId);
            Integer deliveryQuantity = deliveryPlan.getQuantity();
            if(afterSaleOrderVO.getQuantity() > deliveryQuantity ){
                return AjaxResult.getErrorWithMsg("售后数量大于配送计划数量");
            }
        } else {
            List<OrderItemVO> orderItemVOS = orderItemMapper.selectOrderItemSuitId(orderNo, sku, suitId,afterSaleOrderVO.getProductType());
            if(CollectionUtils.isEmpty(orderItemVOS) ||  quantity > orderItemVOS.get(0).getAmount()){
                return AjaxResult.getErrorWithMsg("售后数量大于购买数量");
            }
        }
        //售后单详情 直接到审批成功
        String afterSaleOrderNo = afterSaleOrderAction.createAfterSaleOrderNo(afterSaleOrderVO.getmId(), afterSaleOrderVO.getAccountId());
        afterSaleOrderVO.setAfterSaleOrderNo(afterSaleOrderNo);
        AfterSaleOrder afterSaleOrder = new AfterSaleOrder();
        afterSaleOrder.setDeliveryed(AfterSaleOrder.DELIVERY_RECEIVED);
        afterSaleOrder.setAfterSaleOrderNo(afterSaleOrderNo);
        afterSaleOrder.setStatus(AfterSaleOrderStatus.SUCCESS.getStatus());
        AfterSaleProof afterSaleProof = new AfterSaleProof();
        afterSaleProof.setStatus(AfterSaleOrderStatus.SUCCESS.getStatus());
        afterSaleProof.setAuditer(afterSaleOrderVO.getHandler());
        afterSaleProof.setApplyer(afterSaleOrderVO.getHandler());
        afterSaleProof.setAuditetime(LocalDateTime.now());
        afterSaleProof.setHandleType(handleType);
        afterSaleProof.setHandleNum(afterSaleOrderVO.getHandleNum());
        afterSaleOrderAction.assembleAfterSaleOrder(afterSaleOrderVO,afterSaleOrder);
        afterSaleOrderAction.assembleAfterSalePoof(afterSaleOrderVO,afterSaleProof);

        //配送地址id
        Long contactId = afterSaleOrderAction.getContactId(orderNo,afterSaleOrderVO.getDeliveryId());
        //改用新模型获取库存配送信息
        List<String> skuList = Collections.singletonList(sku);
        AreaStoreQueryReq areaStoreQueryReq = new AreaStoreQueryReq();
        areaStoreQueryReq.setContactId(contactId);
        areaStoreQueryReq.setSkuCodeList(skuList);
        areaStoreQueryReq.setMId(mId);
        areaStoreQueryReq.setSource(DistOrderSourceEnum.getDistOrderAfterSaleSource(merchant.getBusinessLine()));
        Map<String, AreaStoreQueryRes> storeQueryResMap = areaStoreFacade.getInfo(areaStoreQueryReq);
        AreaStoreQueryRes queryStore = storeQueryResMap.get(sku);
        afterSaleOrderVO.setQueryStore(queryStore);
        AfterSaleDeliveryPath insertPath = afterSaleOrderAction.createDeliveryPath(afterSaleOrderVO);
        afterSaleDeliveryPathMapper.insertAfterSaleDeliveryPath(insertPath);
        //生成售后配送单详情
        List<AfterSaleDeliveryDetail> details = new ArrayList<>();
        //补货详情
        AfterSaleDeliveryDetail deliveryDetail = afterSaleOrderAction.createDeliveryDetail(sku, quantity, insertPath.getId());
        details.add(deliveryDetail);
        List<ExchangeGoods> exchangeGoodList = afterSaleOrderVO.getExchangeGoodList();
        if(!CollectionUtils.isEmpty(exchangeGoodList)){
            details.addAll(Lists.transform(exchangeGoodList, x -> handleDetail(x, insertPath.getId())));
        }

        //补发，换货冻结库存
        afterSaleDeliveryDetailMapper.insertAfterSaleDeliveryDetailList(details);

        /*Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
        orderService.updateStock(sku, merchant.getAreaNo(), -quantity,afterSaleOrderVO.getHandler(), SaleStockChangeTypeEnum.REFUNDS, orderNo, recordMap,false,contactId);
        quantityChangeRecordService.insert(recordMap);*/

        //库存扣减 改用新模型
        AreaStoreLockReq storeLockReq = new AreaStoreLockReq();
        storeLockReq.setOrderNo(afterSaleOrderVO.getAfterSaleOrderNo());
        storeLockReq.setOperatorNo(afterSaleOrderVO.getAfterSaleOrderNo());
        storeLockReq.setIdempotentNo(afterSaleOrderVO.getAfterSaleOrderNo());
        storeLockReq.setOrderType(net.summerfarm.enums.SaleStockChangeTypeEnum.REFUNDS.getTypeName());
        storeLockReq.setContactId(contactId);
        List<OrderLockSkuDetailReqDTO> orderLockSkuDetailReqDTOS = new ArrayList<>();
        OrderLockSkuDetailReqDTO orderLockSkuDetailReqDTO = new OrderLockSkuDetailReqDTO();
        orderLockSkuDetailReqDTO.setSkuCode(sku);
        orderLockSkuDetailReqDTO.setOccupyQuantity(quantity);
        orderLockSkuDetailReqDTOS.add(orderLockSkuDetailReqDTO);
        storeLockReq.setOrderLockSkuDetailReqDTOS(orderLockSkuDetailReqDTOS);
        storeLockReq.setMerchantId(afterSaleOrderVO.getmId());
        storeLockReq.setSource(DistOrderSourceEnum.getDistOrderAfterSaleSourceByOrderType(orders.getType()));
        storeLockReq.setOperatorName(afterSaleOrderVO.getHandler());
        areaStoreFacade.storeLock(storeLockReq);

        return AjaxResult.getOK();
    }

    /**
    * 换货单详情
    */
    private AfterSaleDeliveryDetail handleDetail(ExchangeGoods exchangeGoods,Integer id){
        AfterSaleDeliveryDetail deliveryDetail = new AfterSaleDeliveryDetail();
        deliveryDetail.setSku(exchangeGoods.getSku());
        deliveryDetail.setWeight(exchangeGoods.getWeight());
        deliveryDetail.setPdName(exchangeGoods.getPdName());
        deliveryDetail.setQuantity(exchangeGoods.getQuantity());
        deliveryDetail.setStatus(AfterSaleDeliveryDetail.EFFECTIVE_STATUS);
        deliveryDetail.setAsDeliveryPathId(id);
        deliveryDetail.setType(AfterSaleDeliveryDetail.RECOVERY_TYPE);
        return deliveryDetail;
    }
}
