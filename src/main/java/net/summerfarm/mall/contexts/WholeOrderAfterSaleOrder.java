package net.summerfarm.mall.contexts;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.enums.AfterSaleOrderStatus;
import net.summerfarm.enums.OrderTypeEnum;
import net.summerfarm.mall.common.util.PreCutOffOrderUtil;
import net.summerfarm.mall.factory.AfterSaleCalculatorFactory;
import net.summerfarm.mall.factory.AfterSaleWorkflowFactory;
import net.summerfarm.mall.mapper.AdminMapper;
import net.summerfarm.mall.mapper.AreaMapper;
import net.summerfarm.mall.mapper.DeliveryPlanMapper;
import net.summerfarm.mall.mapper.MerchantMapper;
import net.summerfarm.mall.mapper.OrdersMapper;
import net.summerfarm.mall.mapper.PrepayInventoryRecordMapper;
import net.summerfarm.mall.model.domain.AfterSaleOrder;
import net.summerfarm.mall.model.domain.AfterSaleProof;
import net.summerfarm.mall.model.domain.Orders;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import net.summerfarm.mall.service.AfterSaleStrategy;
import net.summerfarm.warehouse.service.WarehouseLogisticsService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Objects;

import static net.summerfarm.common.AjaxResult.DEFAULT_SUCCESS;

/**
 * 订单拦截，整单退
 * <AUTHOR> hyl
 * @date : 2022-05-12
 */
// @Component
public class WholeOrderAfterSaleOrder implements AfterSaleStrategy {

    @Resource
    private AfterSaleOrderAction afterSaleOrderAction;
    @Resource
    private AfterSaleCalculatorFactory afterSaleCalculatorFactory;
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;
    
    // @Resource
    private AfterSaleWorkflowFactory afterSaleWorkflowFactory;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private PrepayInventoryRecordMapper prepayInventoryRecordMapper;
    @Resource
    private PreCutOffOrderUtil preCutOffOrderUtil;
    @Resource
    private AdminMapper adminMapper;
    @Resource
    private AreaMapper areaMapper;
    @Resource
    private WarehouseLogisticsService warehouseLogisticsService;

    @Override
    public AjaxResult afterSaleOrder(AfterSaleOrderVO afterSaleOrderVO) {

        //进入前拼装好 需要的信息
        AfterSaleOrder updateAfterSaleOrder = new AfterSaleOrder();
        AfterSaleProof updateAfterSaleProof = new AfterSaleProof();

        Orders order = ordersMapper.selectByOrderNo(afterSaleOrderVO.getOrderNo());
        //校验是否可发起售后
        AjaxResult result = afterSaleOrderAction.chcekAfterSaleOrder(order, afterSaleOrderVO);
        if(!Objects.equals(result.getCode(),DEFAULT_SUCCESS)){
            return result;
        }

        //生成afterSaleOrderNo
        String afterSaleOrderNo = afterSaleOrderAction.createAfterSaleOrderNo(afterSaleOrderVO.getmId(), afterSaleOrderVO.getAccountId());
        afterSaleOrderVO.setAfterSaleOrderNo(afterSaleOrderNo);


        //设置信息
        updateAfterSaleOrder.setType(0);
        afterSaleOrderVO.setDeliveryed(AfterSaleOrder.DELIVERY_NOT_RECEIVED);
        updateAfterSaleOrder.setDeliveryed(AfterSaleOrder.DELIVERY_NOT_RECEIVED);
        updateAfterSaleOrder.setStatus(AfterSaleOrderStatus.WAIT_HANDLE.getStatus());
        updateAfterSaleOrder.setAfterSaleOrderNo(afterSaleOrderNo);

        updateAfterSaleProof.setStatus(AfterSaleOrderStatus.WAIT_HANDLE.getStatus());
        updateAfterSaleProof.setHandleType(afterSaleOrderVO.getHandleType());
        updateAfterSaleProof.setAfterSaleOrderNo(afterSaleOrderNo);
        //不需要计算退款，整单退款，省心送为0元
        if (order.getType().equals(OrderTypeEnum.TIMING.getId())){
            updateAfterSaleProof.setHandleNum(new BigDecimal(0));
        }else {
            updateAfterSaleProof.setHandleNum(order.getTotalPrice());
        }


        //拼装并修改相关类
        afterSaleOrderAction.assembleAfterSaleOrder(afterSaleOrderVO,updateAfterSaleOrder);
        afterSaleOrderAction.assembleAfterSalePoof(afterSaleOrderVO,updateAfterSaleProof);


        //暂无审核、审批通过状况，全为待审核。
        return AjaxResult.getOK();
    }
}
