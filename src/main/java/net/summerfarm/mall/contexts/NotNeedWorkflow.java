package net.summerfarm.mall.contexts;


import com.alibaba.fastjson.JSONObject;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.enums.AfterSaleHandleType;
import net.summerfarm.enums.AfterSaleOrderStatus;
import net.summerfarm.enums.OrderTypeEnum;
import net.summerfarm.mall.common.mq.MQData;
import net.summerfarm.mall.common.mq.MType;
import net.summerfarm.mall.enums.OrderStatusEnum;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import net.summerfarm.mall.model.vo.DeliveryPlanVO;
import net.summerfarm.mall.service.ActivityService;
import net.summerfarm.mall.service.AfterSaleOrderService;
import net.summerfarm.mall.service.OrderService;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR> ct
 * create at:  2019/12/9  14:54
 *
 * 审核，审批 未到货售后拍多拍错
 */
@Component
public class NotNeedWorkflow extends AfterSaleWorkflow {

    @Resource
    private AfterSaleOrderMapper afterSaleOrderMapper;
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private OrderItemMapper orderItemMapper;
    @Resource
    private OrderService orderService;
    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;
    @Resource
    private RefundMapper refundMapper;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private AfterSaleProofMapper afterSaleProofMapper;
    @Resource
    private AfterSaleOrderService afterSaleOrderService;
    @Resource
    private AfterSaleWorkflowAction afterSaleWorkflowAction;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private PrepayInventoryRecordMapper prepayInventoryRecordMapper;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private ActivityService activityService;

    private static final Logger logger = LoggerFactory.getLogger("NotNeedWorkflow");


    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    @Deprecated
    AjaxResult handle(AfterSaleOrderVO afterSaleOrderVO) {
        //后台校验库存相关数据
        String afterSaleOrderNo = afterSaleOrderVO.getAfterSaleOrderNo();
        AfterSaleOrderVO queryAfterOrder = afterSaleOrderMapper.selectByAfterSaleOrderNo(afterSaleOrderNo);
        String orderNo = afterSaleOrderVO.getOrderNo();
        Integer deliveryId = queryAfterOrder.getDeliveryId();
        String sku = afterSaleOrderVO.getSku();
        Orders orders = ordersMapper.selectByOrderNo(afterSaleOrderVO.getOrderNo());

        BigDecimal totalPrice = orders.getTotalPrice();
        //查询已提交的售后订单
        AfterSaleOrder afterSaleOrder = new AfterSaleOrder();
        afterSaleOrder.setOrderNo(orderNo);
        afterSaleOrder.setDeliveryId(deliveryId);
        List<AfterSaleOrderVO> afterSaleOrders = afterSaleOrderService.selectAfterSaleOrderVO(afterSaleOrder);

        BigDecimal handleNum = BigDecimal.ZERO;
        Integer afterQuantity = 0;
        for (AfterSaleOrderVO check : afterSaleOrders) {
            if (check.getStatus() == AfterSaleOrderStatus.SUCCESS.getStatus()
                    && Objects.equals(check.getDeliveryId(), deliveryId)) {
                afterQuantity = afterQuantity + check.getQuantity();
                handleNum = handleNum.add(check.getHandleNum());
            }

        }
        Integer quantity = 0;
        if(Objects.equals(orders.getType(), OrderTypeEnum.TIMING.getId())
                && !Objects.equals(afterSaleOrderVO.getHandleType(),-1)){
            quantity  = timingAfterSaleOrder(deliveryId,queryAfterOrder.getOrderNo(),sku);

            if(deliveryId == null && !Objects.equals(queryAfterOrder.getQuantity()+ afterQuantity,quantity)){
                return AjaxResult.getError("TOTAL_REFUND","当前未设置数量发生变更，请重新发起售后");
            }
            List<OrderItem> orderItems = orderItemMapper.selectList(orderNo,sku, 0);
            BigDecimal price = orderItems.get(0).getPrice();
            totalPrice = price.multiply(BigDecimal.valueOf(quantity));
            if (deliveryId != null && handleNum.add(afterSaleOrderVO.getHandleNum()).compareTo(totalPrice) > 0) {
                return AjaxResult.getError("TOTAL_REFUND", "超出可售后金额");
            }
        }
        //修改售后记录
        AfterSaleOrder updateKeys = new AfterSaleOrder();
        //查询sku是否为代仓商品, 是售后可以为0元
        Inventory inventory = inventoryMapper.selectBySku(queryAfterOrder.getSku());
        //-1拒绝,2退款'
        if (afterSaleOrderVO.getHandleType() == -1) {
            //记录
            updateKeys.setStatus(AfterSaleOrderStatus.FAIL.getStatus());
            if ("凭证不符合要求,请重新上传照片".equals(afterSaleOrderVO.getHandleRemark())) {
                updateKeys.setStatus(AfterSaleOrderStatus.RE_COMMIT.getStatus());
                MQData mqData = new MQData();
                mqData.setType(MType.AFTER_SALE_ORDER_NO.name());
                mqData.setData(afterSaleOrderVO.getAfterSaleOrderNo());
                mqProducer.send(RocketMqMessageConstant.MANAGE_LIST, null, JSONObject.toJSONString(mqData));
            }
            afterSaleWorkflowAction.checkoutProof(afterSaleOrderVO);

        }
        //退款
        else if (afterSaleOrderVO.getHandleType().intValue() == AfterSaleHandleType.REFUND.getType()) {
            updateKeys.setStatus(AfterSaleOrderStatus.IN_HAND.getStatus());
            afterSaleWorkflowAction.insertRefund(afterSaleOrderVO);
            //录入账单
        } else if(afterSaleOrderVO.getHandleType().intValue() == AfterSaleHandleType.ENTRY_BILL.getType()){
            Merchant merchant = merchantMapper.selectOneByMid(orders.getmId());
            Integer adminId = merchant.getAdminId();
            List<PrepayInventoryRecord> prepayInventoryRecords = prepayInventoryRecordMapper.selectRecordList(adminId, afterSaleOrderVO.getOrderNo(), sku);

            if (CollectionUtils.isEmpty(prepayInventoryRecords)
                && (afterSaleOrderVO.getHandleNum() == null || afterSaleOrderVO.getHandleNum().compareTo(BigDecimal.ZERO) <= 0)
                && Objects.equals(inventory.getType(),0)) {
                return AjaxResult.getErrorWithMsg("售后金额不能小于零");
            }
            updateKeys.setStatus(AfterSaleOrderStatus.IN_HAND.getStatus());
        } else {
            return AjaxResult.getErrorWithMsg("不正确的处理状态");
        }

        //只更改状态售后的
        updateKeys.setAfterSaleOrderNo(afterSaleOrderNo);
        updateKeys.setView(0);
        afterSaleOrderMapper.updateByAfterSaleOrderNo(updateKeys);
        //获取最后一个
        List<AfterSaleProof> afterSaleProofs = afterSaleProofMapper.select(afterSaleOrderNo);
        //根据id 修改proof 审核中
        afterSaleOrderVO.setStatus(1);
        AfterSaleProof updateProof = afterSaleWorkflowAction.conventAfterSaleProof(afterSaleOrderVO);
        updateProof.setStatus(updateKeys.getStatus());
        updateProof.setId(afterSaleProofs.get(afterSaleProofs.size() - 1).getId());
        updateProof.setAfterSaleType(afterSaleOrderVO.getAfterSaleType());
        updateProof.setRefundType(afterSaleOrderVO.getRefundType());
        updateProof.setHandler(afterSaleOrderVO.getHandler());
        //拒绝handler type不处理
        if (updateProof.getHandleType() == -1) {
            updateProof.setHandleType(null);
        }
        afterSaleProofMapper.updateById(updateProof);
        activityService.afterSaleActivityQuantity(afterSaleOrderVO);
        return AjaxResult.getOK();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    @Deprecated
    AjaxResult audit(AfterSaleOrderVO afterSaleOrderVO) {
        String afterSaleOrderNo = afterSaleOrderVO.getAfterSaleOrderNo();
        int status = afterSaleOrderVO.getStatus();
        AfterSaleOrderVO queryAfterOrder = afterSaleOrderMapper.selectByAfterSaleOrderNo(afterSaleOrderNo);
        //处于待审核状态才可以进入下一步
        if (!Objects.equals(AfterSaleOrderStatus.IN_HAND.getStatus(),queryAfterOrder.getStatus())) {
            return AjaxResult.getError("售后状态错误");
        }
        //获取最后一个
        List<AfterSaleProof> afterSaleProofs = afterSaleProofMapper.select(afterSaleOrderNo);
        //根据id 修改proof
        AfterSaleProof updateProof = new AfterSaleProof();
        AfterSaleOrder updateAfterOrder = new AfterSaleOrder();
        updateProof.setAuditer(afterSaleOrderVO.getAuditer());
        updateProof.setId(afterSaleProofs.get(afterSaleProofs.size() - 1).getId());
        //审核不通过
        if (status == 0) {
            //更改审核订单列表
            updateAfterOrder.setStatus(AfterSaleOrderStatus.WAIT_HANDLE.getStatus());
            //更新凭证状态
            updateProof.setStatus(AfterSaleOrderStatus.WAIT_HANDLE.getStatus());
            afterSaleWorkflowAction.notPass(afterSaleOrderVO);
        }
        //审核成功
        AjaxResult refundResult = AjaxResult.getOK();
        if(status == 1){
            //成功  退款
            BigDecimal handleNum = BigDecimal.ZERO;
            Boolean refundIsSuccess = true;
            int handleQuantity = 0;
            AfterSaleOrderVO afterSaleOrder = afterSaleOrderMapper.selectByAfterSaleOrderNo(afterSaleOrderNo);
            AfterSaleOrder querySaleAfterOrder = new AfterSaleOrder();
            querySaleAfterOrder.setOrderNo(afterSaleOrder.getOrderNo());
            querySaleAfterOrder.setDeliveryId(afterSaleOrder.getDeliveryId());
            List<AfterSaleOrderVO> afterSaleOrders = afterSaleOrderService.selectAfterSaleOrderVO(querySaleAfterOrder);

            for (AfterSaleOrderVO check : afterSaleOrders) {
                if (check.getStatus() == 2 && Objects.equals(check.getDeliveryId(),afterSaleOrder.getDeliveryId())) {
                    handleQuantity = check.getQuantity() + handleQuantity;
                    handleNum = handleNum.add(check.getHandleNum());
                }
            }
            Orders orders = ordersMapper.selectByOrderyNo(afterSaleOrder.getOrderNo());
            boolean max = afterSaleWorkflowAction.maxMoney(afterSaleOrderNo, updateProof,handleNum,false);
            if (!max){
                return AjaxResult.getError("AFTER_SALE_FAILED", "最大可售后金额为" + orders.getTotalPrice().subtract(handleNum));
            }

            if ( AfterSaleHandleType.REFUND.getType().equals(afterSaleOrderVO.getHandleType()) && (afterSaleOrderVO.getType() == 0 || afterSaleOrderVO.getType() == 3)) {
                try {
                    refundResult = afterSaleOrderService.refund(afterSaleOrderNo);
                    refundIsSuccess = AjaxResult.isSuccess(refundResult);

                } catch (Exception e) {
                    if(e instanceof DefaultServiceException && Objects.equals("资金确认中，请稍后重新发起退款。",e.getMessage())){
                        throw new DefaultServiceException(0, "资金确认中，请稍后重新发起退款。");
                    }
                    refundIsSuccess = false;
                    logger.info("退款失败 err：", e);
                    throw new DefaultServiceException(0, "资金确认中，请稍后重新发起退款。");
                }
            }
            //大客户帐期门店 已到货 录入账单不做任何操作,只记录。 未到货 拍多拍错不想要 缺货 返库存,其他不返库存
            if(AfterSaleHandleType.ENTRY_BILL.getType().equals(afterSaleOrderVO.getHandleType()) && (afterSaleOrderVO.getType() == 0 || afterSaleOrderVO.getType() == 3)){
                //未到货

                if(Objects.equals(queryAfterOrder.getDeliveryed(),AfterSaleOrder.DELIVERY_NOT_RECEIVED)){
                    afterSaleOrderService.afterSaleEntryBill(afterSaleOrderNo);
                }
            }

            if(refundIsSuccess){
                updateProof.setStatus(AfterSaleOrderStatus.SUCCESS.getStatus());
                updateAfterOrder.setStatus(AfterSaleOrderStatus.SUCCESS.getStatus());
            } else {
                updateProof.setStatus(AfterSaleOrderStatus.FAIL.getStatus());
                updateAfterOrder.setStatus(AfterSaleOrderStatus.FAIL.getStatus());
            }
        }
        //更新审核人
        updateProof.setAuditer(afterSaleOrderVO.getAuditer());
        updateProof.setAuditeRemark(afterSaleOrderVO.getAuditeRemark());
        updateProof.setAuditetime(LocalDateTime.now());
        afterSaleProofMapper.updateById(updateProof);
        updateAfterOrder.setView(0);
        updateAfterOrder.setAfterSaleOrderNo(afterSaleOrderNo);
        afterSaleOrderMapper.updateByAfterSaleOrderNo(updateAfterOrder);

        Orders orders = ordersMapper.selectByOrderyNo(queryAfterOrder.getOrderNo());
        if(OrderTypeEnum.TIMING.getId().equals(orders.getType())){
            handleTimingOrder(orders);
        }
        return refundResult;
    }
    private void handleTimingOrder(Orders orders) {
        String orderNo = orders.getOrderNo();
        Integer afterSaleQuantity = afterSaleOrderService.getAfterSaleSuccessQuanlity(orderNo);

        List<DeliveryPlanVO> plans = deliveryPlanMapper.selectByOrderNo(orderNo);
        int deliveredQuantity = plans.stream().filter(deliveryPlanVO -> !deliveryPlanVO.getDeliveryTime().isAfter(LocalDate.now())).mapToInt(dp -> dp.getQuantity()).sum();
        Integer quantity = orderItemMapper.selectTimingOrderQuantity(orderNo);

        logger.info("orderNo:{}, deliveredQuanlity:{}, afterSaleQuanlity:{},quantity:{}", orderNo, deliveredQuantity, afterSaleQuantity, quantity);
        if( deliveredQuantity + afterSaleQuantity == quantity){
            if (deliveredQuantity == 0) {
                ordersMapper.updateStatus(orderNo, OrderStatusEnum.DRAWBACK.getId(), orders.getStatus());
            } else {
                orderItemMapper.updateStatusByOrderNo(orderNo, OrderStatusEnum.RECEIVED.getId());
                ordersMapper.updateStatus(orderNo, OrderStatusEnum.RECEIVED.getId(), orders.getStatus());
                orderService.updateScore(orderNo, orders.getmId());
                //orderService.pushOrderConfirm(orderNo);
            }

        }

    }


    private Integer timingAfterSaleOrder(Integer deliveryId,String orderNo ,String sku){
        List<OrderItem> orderItems = orderItemMapper.selectList(orderNo, sku, 0);
        Integer totalQuantity = 0;
        if(deliveryId != null){
            DeliveryPlan deliveryPlan = deliveryPlanMapper.selectById(deliveryId);
            totalQuantity = deliveryPlan.getQuantity();
        } else {
            List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanMapper.selectByOrderNo(orderNo);
            for (DeliveryPlanVO deliveryPlanVO : deliveryPlanVOS) {
                Integer quantity = deliveryPlanVO.getQuantity();
                totalQuantity = quantity == null ? totalQuantity : totalQuantity+quantity;
            }
            totalQuantity = orderItems.get(0).getAmount() - totalQuantity;
        }

        return totalQuantity;
    }


}
