package net.summerfarm.mall.contexts;

/**
 * <AUTHOR>
 */
public class OfcRocketMqConstant {

    /**
     * OFC 相关主题
     */
    public interface Topic {

        /**
         * 对接上游配送通知
         */
        String TOPIC_OFC_DELIVERY = "topic_ofc_delivery";

        /**
         * 对接上游消息提醒
         */
        String TOPIC_OFC_NOTICE = "topic_ofc_notice";

        /**
         * 履约完成
         */
        String TOPIC_OFC_COMMON_TASK_FINISH = "topic_ofc_common_task_finish";
    }

    /**
     * OFC相关 tag
     */
    public interface Tag {
        /**
         * 生成订单缺损信息
         */
        String INIT_ORDER_DEFECT_MESSAGE = "tag_init_order_defect_message";

        /**
         * ofc发送生成补发单消息
         */
        String REISSUE_ORDER_MESSAGE = "tag_reissue_order_message";

        /**
         * 完成配送商城微信消息推送
         */
        String FINISH_DELIVERY_WECHAT_MESSAGE = "tag_finish_delivery_wechat_message";

        /**
         * 外部对接推送订单收货通知
         */
        String INIT_DELIVERY_PATH_SHORT_SKU = "tag_init_delivery_path_short_sku";

        /**
         * 刷新售后状态消息
         */
        String REFRESH_AFTER_SALE = "tag_refresh_after_sale";

        /**
         * 完成排线更新售后状态售后信息
         */
        String COMPLETE_WIRING_MESSAGE = "tag_complete_wiring_message";


        /**
         * 鲜沐订单 履约完成 消息tag
         */
        String FULFILLMENT_FINISH_ORDER = "tag_ofc_fulfillment_finish_xianmu_order";

        /**
         * 鲜沐省心送订单 履约完成 消息tag
         */
        String FULFILLMENT_FINISH_TIMING_ORDER = "tag_ofc_fulfillment_finish_xianmu_timing_order";

        /**
         * pop缺货微信消息推送
         */
        String PICK_STOCK_OUT_WECHAT_MESSAGE = "tag_pick_stock_out_wechat_message";
    }

    /**
     * OFC 消费组
     */
    public interface ConsumeGroup {
        String GID_OFC_TO_MALL_WECHAT_MESSAGE = "GID_ofc_to_mall_wechat_message";
        String GID_OFC_TO_MALL_SHORT_SKU = "GID_ofc_to_mall_short_sku";
        String GID_OFC_TO_MALL_ORDER_DEFECT = "GID_ofc_to_mall_order_defect";
        String GID_OFC_TO_MALL_OUTER_PUSH = "GID_ofc_to_mall_outer_push";
        String GID_OFC_TO_MALL_REISSUE_MESSAGE = "GID_ofc_to_mall_reissue_message";
        String GID_OFC_TO_MALL_REFRESH_AFTER_SALE = "GID_ofc_to_mall_refresh_after_sale";
        String GID_OFC_TO_MALL_COMPLETE_WIRING_MESSAGE = "GID_ofc_to_mall_complete_wiring_message";
        String GID_OFC_TO_MALL_ORDER_FULFILLMENT_FINISH = "GID_ofc_to_mall_order_fulfillment_finish";
    }

}
