package net.summerfarm.mall.listener;//package net.summerfarm.listener;
//
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.boot.context.event.ApplicationReadyEvent;
//import org.springframework.context.ApplicationListener;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//
///**
// * @Package: net.summerfarm.mall.listener
// * @Description: 监听器，在spring容器加载完成后调用
// * @author: <EMAIL>
// * @Date: 2017/6/3
// */
//@Component
//public class MallInitListener implements ApplicationListener<ApplicationReadyEvent> {
//
//    private static final Logger logger = LoggerFactory.getLogger(MallInitListener.class);
//
//
//    @Override
//    public void onApplicationEvent(ApplicationReadyEvent event) {
////        AjaxResult ajaxResult = actionService.findOne();
//        logger.info("结果为1111111111111111123345645644444444444466666666666666666666666");
//    }
//}
